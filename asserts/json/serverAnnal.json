[{"id": 1001, "name": "RareMergeEquip", "event": "重锻出符合条件的无极百炼装备", "type": 1, "cond": "1、重锻出7稀有百炼武器\r\n2、重锻出3稀有的7词条百炼衣服、戒指、腰带、手套、袖子\r\n3、重锻出2稀有及以上的7词条百炼头盔、鞋子\r\n4、重锻出7词条百炼手镯", "condArgs": "{\r\n[0]=7,\r\n[1]=3,\r\n[2]=2,\r\n[3]=3,\r\n[4]=3,\r\n[5]=3,\r\n[6]=2,\r\n[7]=3,\r\n[8]=3,\r\n[9]=2,\r\n}", "score": 10, "desc": "{{if e.first==1}}{{e.time}}，{{e.playerName}}重锻出了{{e.serverName}}服务器的第一件{{if e.rareCnt>0}}{{e.rareCnt}}稀有{{else}}{{end}}无极装备{{e.equipName}}，正所谓流星白羽腰间插，剑花秋莲光出匣！{{else}}{{e.time}}，{{e.playerName}}重锻出了{{if e.rareCnt>0}}{{e.rareCnt}}稀有{{else}}{{end}}无极装备{{e.equipName}}，从此身携无极兵，试看赤胆英雄风！{{end}}", "descComment": "时间e.time，玩家名字e.playerName，装备名e.equipName，是否全服第一个e.first，服务器名字e.serverName，稀有词条数量e.rareCnt", "scores": [10], "category": 1}, {"id": 1002, "name": "WearWujiEquip", "event": "穿戴12件无极装备", "type": 1, "cond": "穿戴满12件7词条的百炼装备（不含玉佩）", "condArgs": "{12}", "score": 60, "desc": "{{if e.first==1}}{{e.time}}，{{e.playerName}}成为{{e.serverName}}服务器第一位全身穿戴12件无极装备的{{if e.playerGender==0}}少侠{{else}}女侠{{end}}，正所谓遍身无极登穹宇，天下谁人不识君！{{else}}{{e.time}}，{{e.playerName}}全身已穿戴12件无极装备，从此倚剑扫千军，扬鞭临峰俯万仞！{{end}}", "descComment": "时间e.time，玩家名字e.playerName，是否全服第一个e.first，服务器名字e.serverName，性别e.playerGender（0男1女）", "scores": [60], "category": 1}, {"id": 1003, "name": "RareBingHunXiLian", "event": "鸣金洗出2稀有", "type": 1, "cond": "鸣金洗出稀有词条数量=2（原本就锁住两条的不算）", "condArgs": "{\r\n{69,2},\r\n{89,2},\r\n{109,2},\r\n{109,3},\r\n}", "score": "8;8;8;15", "desc": "{{if e.first==1}}{{e.time}}，{{e.playerName}}淬炼出了{{e.serverName}}服务器的第一颗{{e.rareCnt}}稀有鸣金{{e.itemName}}（{{e.rareNameList}}），正所谓开天匣光惊云龙，鸣金敲玉玲珑声！{{else}}{{e.time}}，{{e.playerName}}淬炼出了含有稀有词条{{e.rareNameList}}的鸣金，一时鹤帐金鳞开，罗帏绣幕透香风！{{e.itemName}}{{end}}", "descComment": "时间e.time，玩家名字e.playerName，是否全服第一个e.first，服务器名字e.serverName，鸣金名e.itemName，稀有词条名字e.rareNameList", "scores": [8, 8, 8, 15], "category": 1}, {"id": 1004, "name": "RareDiMaiMerge", "event": "天机切出大稀有+小稀有词条", "type": 1, "cond": "天机原本有小稀有切出大稀有或者原本有大稀有切出小稀有", "condArgs": "{1,1,2}", "score": "12;25", "desc": "{{if e.first==1}}{{e.time}}，{{e.playerName}}鉴定出了{{e.serverName}}服务器的第一个含有小稀有与大稀有的机关{{e.itemName}}（{{e.rareNameList}}），正所谓天机造化鬼神功，掷置黄金解龙枢！{{else}}{{e.time}}，{{e.playerName}}鉴定出了同时含有小稀有与大稀有{{e.rareNameList}}的机关{{e.itemName}}，从此运筹动经纬，神机参破转乾坤！{{end}}", "descComment": "时间e.time，玩家名字e.playerName，是否全服第一个e.first，服务器名字e.serverName，天机名e.itemName，稀有词条名字e.rareNameList", "scores": [12, 25], "category": 1}, {"id": 1005, "name": "BWDHTop1", "event": "本服比武大会冠军", "type": 1, "cond": "获得本服比武大会的冠军", "score": 2, "desc": "{{if e.first==1}}{{e.time}}，{{e.playerNameList}}获得了{{e.serverName}}服务器第一次比武大会冠军的战绩，江湖已闻金翎信，从此千营共山呼！{{else}}{{e.time}}，{{e.playerNameList}}获得了比武大会冠军的战绩，凯旋醉和金甲舞，旌旗雷鼓动山川！{{end}}", "descComment": "时间e.time，队伍所有玩家名字e.playerNameList，是否全服第一次比武e.first，服务器名字e.serverName", "scores": [2], "category": 1}, {"id": 1006, "name": "KFLPCTop1", "event": "跨服流派首席冠军", "type": 1, "cond": "获得跨服流派首席冠军", "score": 4, "desc": "{{if e.first==1}}{{e.time}}，{{e.playerName}}获得了{{e.serverName}}服务器第一次{{e.playerClass}}流派跨服流派首席冠军的成绩{{else}}{{e.time}}，{{e.playerName}}获得了{{e.playerClass}}流派跨服流派首席冠军的成绩{{end}}，获得称号“天下第一{{e.playerClass}}”，正是且登金鞍雕白羽，天下英雄共仰瞻！", "descComment": "时间e.time，玩家名字e.playerName，是否全服第一次跨服流拍首席e.first，服务器名字e.serverName，玩家职业e.playerClass", "scores": [4], "category": 1}, {"id": 1007, "name": "FirstBHLSTop1", "event": "第一个甲一帮会", "type": 1, "cond": "服务器的第一个甲一帮会", "firstOnly": 1, "desc": "{{e.time}}，{{e.guildName}}成为{{e.serverName}}服务器的第一个甲一帮会，披坚执锐，威震江湖！", "descComment": "时间e.time，帮会名字e.guildName，服务器e.serverName", "scores": [], "category": 1}, {"id": 1008, "name": "FirstGuildScaleUpgrade", "event": "第一个7级帮会", "type": 1, "cond": "服务器的第一个7级帮会", "condArgs": "{7}", "firstOnly": 1, "desc": "{{e.time}}，{{e.guildName}}成为{{e.serverName}}服务器的第一个7级帮会，秣马厉兵，逐鹿武林！", "descComment": "时间e.time，帮会名字e.guildName，服务器e.serverName", "scores": [], "category": 1}, {"id": 1009, "name": "FirstDragonHead", "event": "第一个龙首帮会", "type": 1, "cond": "服务器各自势力的龙首帮会", "firstOnly": 1, "desc": "{{e.time}}，{{e.guildName}}成为{{e.serverName}}服务器的第一个{{e.guildGForce}}势力的龙首帮会，万仞之巅，傲视群雄！", "descComment": "时间e.time，帮会名字e.guildName，服务器e.serverName", "scores": [], "category": 1}, {"id": 1010, "name": "FirstForceBattleLongShou", "event": "第一个天江城城主", "type": 1, "cond": "第一场天江城之战获胜方势力龙首", "firstOnly": 1, "score": 2, "desc": "{{e.time}}，{{e.playerName}}成为{{e.serverName}}服务器的首任天江城城主，金城汤池，谁与争锋？", "descComment": "时间e.time，玩家名字e.playerName，服务器e.serverName", "scores": [2], "category": 1}, {"id": 1011, "name": "FirstAddTitle", "event": "称号六合青龙之叶棋五/齐文六/赵画四获得者", "type": 1, "cond": "称号六合青龙之叶棋五/齐文六/赵画四获得者", "condArgs": "{52000009,52000011,52000012}", "firstOnly": 1, "score": 2, "desc": "{{e.time}}，{{e.playerName}}达成{{e.serverName}}服务器首次击败{{if e.titleId==52000009}}叶棋五{{elseif e.titleId==52000011}}齐文六{{elseif e.titleId==52000012}}赵画四{{end}}成就，获得称号“六合青龙之{{if e.titleId==52000009}}叶棋五{{elseif e.titleId==52000011}}齐文六{{elseif e.titleId==52000012}}赵画四{{end}}”，智勇无双，声名大显！", "descComment": "时间e.time，玩家名字e.playerName，服务器e.serverName，称号e.titleId", "scores": [2], "category": 1}, {"id": 1012, "name": "FirstGardenGradeUpgrade", "event": "第一个8级庄园", "type": 1, "cond": "第一个升到8级庄园的玩家", "condArgs": "{8}", "firstOnly": 1, "score": 1, "desc": "{{e.time}}，{{e.playerName}}拥有了{{e.serverName}}服务器的第一座8级庄园，雕梁画栋，墨池香润，翠碧莺啼，名园动江湖！", "descComment": "时间e.time，玩家名字e.playerName，服务器e.serverName", "scores": [1], "category": 1}, {"id": 1013, "name": "FirstTotalGameTime", "event": "第一个在线时长达到1000小时的玩家", "type": 1, "cond": "第一个在线时长达到1000小时的玩家", "condArgs": "{1000}", "firstOnly": 1, "score": 1, "desc": "{{e.time}}，{{e.playerName}}成为{{e.serverName}}服务器第一位在线时长达到1000小时的{{if e.playerGender==0}}少侠{{else}}女侠{{end}}，醉梦江湖时日久，归来仍旧是少年！", "descComment": "时间e.time，玩家名字e.playerName，服务器e.serverName，性别e.playerGender（0男1女）", "scores": [1], "category": 1}, {"id": 1014, "name": "EquipUnit2Compound", "event": "组合出无极7稀有武器", "type": 1, "cond": "组合出无极7稀有武器", "condArgs": "{\r\n[0]=7,\r\n}", "score": 10, "desc": "{{if e.first==1}}{{e.time}}，{{e.playerName}}组合出了{{e.serverName}}服务器的第一件无极7稀有武器{{e.equipName}}，正所谓流星白羽腰间插，剑花秋莲光出匣！{{else}}{{e.time}}，{{e.playerName}}组合出了无极7稀有武器{{e.equipName}}，从此身携无极兵，试看赤胆英雄风！{{end}}", "descComment": "时间e.time，玩家名字e.playerName，装备名e.equipName，是否全服第一个e.first，服务器名字e.serverName", "scores": [10], "category": 1}, {"id": 2001, "name": "MergeEquip", "event": "重锻出含有稀有词条的百炼装备", "type": 2, "cond": "稀有词条≥1", "condArgs": "{1}", "desc": "{{e.time}}，{{e.playerName}}重锻出了含有{{e.rareCnt}}条稀有词条的百炼装备{{e.equipName}}，可谓千锤百炼，神兵现世！", "descComment": "时间e.time，玩家名字e.playerName，装备名e.equipName，稀有词条数量e.rareCnt", "scores": [], "category": 2}, {"id": 2002, "name": "BingHunXiLian", "event": "鸣金洗出稀有", "type": 2, "cond": "稀有词条≥1", "condArgs": "{1}", "desc": "{{e.time}}，{{e.playerName}}淬炼出了含有{{e.rareCnt}}条稀有词条{{e.rareNameList}}的鸣金{{e.itemName}}，荡剑鸣金，气吞山河！", "descComment": "时间e.time，玩家名字e.playerName，鸣金名e.itemName，稀有词条数量e.rareCnt，稀有词条名字e.rareNameList", "scores": [], "category": 2}, {"id": 2003, "name": "JingMaiWash", "event": "经脉单页全橙", "type": 2, "cond": "单页全橙", "desc": "{{e.time}}，{{e.playerName}}将{{e.jingMaiName}}的全部穴位洗髓至橙色品质，已是炉火纯青，攻无不克！", "descComment": "时间e.time，玩家名字e.player<PERSON><PERSON>，单页经脉名字e.jing<PERSON>aiName", "scores": [], "category": 2}, {"id": 2004, "name": "DiMaiMerge", "event": "天机切出大稀有或小稀有", "type": 2, "cond": "切出小稀有或者大稀有", "condArgs": "{0,0}", "desc": "{{e.time}}，{{e.playerName}}合成出了含有{{e.rareNameList}}的机关{{e.itemName}}，天机神合，枢动乾坤！", "descComment": "时间e.time，玩家名字e.playerName，天机名e.itemName，稀有词条名字e.rareNameList", "scores": [], "category": 2}, {"id": 2005, "name": "BWDHTop4", "event": "本服比武大会4强", "type": 2, "cond": "第2~4名", "desc": "{{e.time}}，{{e.playerName}}带领队友在比武大会中取得了{{if e.rank==2}}亚军{{else}}四强{{end}}的成绩，可谓运筹帷幄，决胜千里！", "descComment": "时间e.time，队长名字e.playerName，名次e.rank", "scores": [], "category": 2}, {"id": 2006, "name": "KFLPCTop16", "event": "跨服流派首席16强", "type": 2, "cond": "第2~16名", "desc": "{{e.time}}，{{e.playerName}}获得了{{e.playerClass}}流派跨服流派首席{{if e.rank==2}}亚军{{elseif e.rank<=4}}四强{{elseif e.rank<=8}}八强{{else}}十六强{{end}}的成绩，意气凌云，威震江湖！", "descComment": "时间e.time，玩家名字e.playerName，名字e.rank，职业e.playerClass", "scores": [], "category": 2}, {"id": 2007, "name": "TianTiTop3", "event": "公平论武结算", "type": 2, "cond": "前三", "desc": "{{e.time}}，{{e.playerName}}在公平论武赛季结算中取得了第{{e.rank}}名的成绩，获得称号“独孤求败”，无敌皆寂寞，谁堪赐一败？", "descComment": "{{e.time}}时间，{{e.playerName}}玩家、{{e.rank}}排名", "scores": [], "category": 2}, {"id": 2008, "name": "BattleForceTop3", "event": "战场结算", "type": 2, "cond": "前三", "desc": "{{e.time}}，{{e.playerName}}在战场赛季结算中取得了第{{e.rank}}名的成绩，获得称号“天下兵马大元帅”，统帅天下群雄，一言山呼海应！", "descComment": "时间e.time，玩家名字e.playerName，名次e.rank", "scores": [], "category": 2}, {"id": 2009, "name": "WYCWithBaifa", "name1": "ItemDrop", "event": "舞阳城出白发", "type": 2, "cond": "第二或第三个BOSS开出白夜拂雪", "desc": "{{e.time}}，{{e.playerName}}带领队友在决战舞阳城副本中获得了珍贵的“白夜拂雪”，正所谓一念神魔间，白夜未至雪满头！", "descComment": "时间e.time，队长名字e.player<PERSON>ame", "scores": [], "category": 2}, {"id": 2010, "name": "WDGZWithBMW", "name1": "ItemDrop", "event": "无敌公子出轿子", "type": 2, "cond": "无敌公子开出轿子", "desc": "{{e.time}}，{{e.playerName}}带领队友在风雪铁牢关副本中击败无敌公子，获得了珍贵的“云海玉箫辇”，正所谓归来千乘护金辇，銮舆栖凤羡煞人！", "descComment": "时间e.time，队长名字e.player<PERSON>ame", "scores": [], "category": 2}, {"id": 2011, "name": "AchieveScoreUpdate", "name1": "ScoreUpdate", "event": "说英雄点数达到XX点", "type": 2, "cond": "达到10000 15000 20000 25000…… 每5000一个档", "condArgs": "{10000,5000}", "desc": "{{e.time}}，{{e.playerName}}说英雄点数达到{{e.score}}点，名就功成，煊赫英雄榜！", "descComment": "时间e.time，玩家名字e.player<PERSON>ame，达到的说英雄点数e.score", "scores": [], "category": 2}, {"id": 2012, "name": "GardenGradeUpgrade", "event": "庄园等级达到XX级", "type": 2, "cond": "达到8、9、10、11、12、13、14、15…… 每一级一个档", "condArgs": "{8}", "desc": "{{e.time}}，{{e.playerName}}庄园等级达到{{e.gardenGrade}}级，琼楼玉宇，月阁星院，夜枕满园春！", "descComment": "时间e.time，玩家名字e.player<PERSON>ame，达到庄园等级e.gardenGrade", "scores": [], "category": 2}, {"id": 2013, "name": "YiPinScoreUpdate", "name1": "ScoreUpdate", "event": "衣品值达到XX", "type": 2, "cond": "达到10W、15W、20W…… 每5W一个档", "condArgs": "{100000,50000}", "desc": "{{e.time}}，{{e.playerName}}衣品值达到了{{e.score}}点，正所谓天工织华服，云罗绣锦衣！", "descComment": "时间e.time，玩家名字e.player<PERSON>ame，达到衣品值e.score", "scores": [], "category": 2}, {"id": 2014, "name": "JunMaScoreUpdate", "name1": "ScoreUpdate", "event": "骏马值达到XX", "type": 2, "cond": "达到1W、2W、3W…… 每1W一个档", "condArgs": "{10000,10000}", "desc": "{{e.time}}，{{e.playerName}}骏马值达到了{{e.score}}点，银鞍照白马，飒沓如流星！", "descComment": "时间e.time，玩家名字e.player<PERSON>ame，达到骏马值e.score", "scores": [], "category": 2}, {"id": 2015, "name": "QingYuanUpdate", "event": "情缘值达到XX", "type": 2, "cond": "达到10W、15W、20W……每5W一个档", "condArgs": "{100000,50000}", "desc": "{{e.time}}，{{e.playerName}}与{{e.targetPlayerName}}的情缘值达到了{{e.score}}点，只愿君心似我心，春风十里寄柔情！", "descComment": "时间e.time，玩家1名字e.playerName，玩家2名字e.targetPlayerName，达到情缘值e.score", "scores": [], "category": 2}, {"id": 2016, "name": "SendFlowerTop3", "event": "周六结算时本周送花榜前三名", "type": 2, "cond": "前3名", "desc": "{{e.time}}，{{e.playerName}}在本周赠花排行榜中名列第{{if e.rank==1}}一{{elseif e.rank==2}}二{{else}}三{{end}}", "descComment": "时间e.time，e.player<PERSON><PERSON>送花榜前三的玩家（按第一第二第三排列）", "scores": [], "category": 2}, {"id": 2017, "name": "ReceiveFlowerTop3", "event": "周六结算时本周收花榜前三名", "type": 2, "cond": "前3名", "desc": "{{e.time}}，{{e.playerName}}在本周收花排行榜中名列第{{if e.rank==1}}一{{elseif e.rank==2}}二{{else}}三{{end}}", "descComment": "时间e.time，e.player<PERSON><PERSON>收花榜前三的玩家（按第一第二第三排列）", "scores": [], "category": 2}, {"id": 2018, "name": "<PERSON><PERSON>", "event": "第1、10、20、30……以此类推对结婚的情侣", "type": 2, "cond": "第1、10、20、30……以此类推对结婚的情侣", "desc": "{{e.time}}，{{e.playerName}}与{{e.targetPlayerName}}成为{{e.serverName}}服务器的第{{e.index}}对侠侣", "descComment": "时间e.time，玩家1名字e.playerName，玩家2名字e.targetPlayerName，e.serverName服务器名字，e.index第X对", "scores": [], "category": 2}, {"id": 3001, "name": "GuildScaleUpgrade", "name1": "GuildScaleUpdate", "event": "帮会升级", "type": 3, "cond": "升至6、7", "condArgs": "{6}", "desc": "{{e.time}}，{{e.guildName}}升至{{e.guildScale}}级帮会，厚积薄发，逐鹿江湖！", "descComment": "时间e.time，帮会名字e.guildName，升至等级e.guildScale", "scores": [], "category": 3}, {"id": 3002, "name": "GuildScaleDegrade", "name1": "GuildScaleUpdate", "event": "帮会降级", "type": 3, "cond": "降至5、6", "condArgs": "{5}", "desc": "{{e.time}}，{{e.guildName}}降至{{e.guildScale}}级帮会，韬光养晦，待时而动！", "descComment": "时间e.time，帮会名字e.guildName，降至等级e.guildScale", "scores": [], "category": 3}, {"id": 3003, "name": "GuildAllianceJoin", "name1": "GuildAllianceUpdate", "event": "帮会联盟", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.guildName}}与{{e.targetGuildName}}结为联盟，可谓志同道合，江湖携手，同舟共济！", "descComment": "时间e.time，帮会1名字e.guildName，帮会2名字e.targetGuildName", "scores": [], "category": 3}, {"id": 3004, "name": "GuildAllianceQuit", "name1": "GuildAllianceUpdate", "event": "帮会解除联盟", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.guildName}}与{{e.targetGuildName}}解除联盟，正所谓道不同，不相为谋！", "descComment": "时间e.time，帮会1名字e.guildName，帮会2名字e.targetGuildName", "scores": [], "category": 3}, {"id": 3005, "name": "KFGuildAllianceJoin", "name1": "KFGuildAllianceUpdate", "event": "帮会跨服联盟", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.serverName}}服务器的{{e.guildName}}与{{e.targetServerName}}服务器的{{e.targetGuildName}}结为联盟，可谓志同道合，江湖携手，同舟共济！", "descComment": "时间e.time，帮会1名字e.guildName，所属服务器e.serverName，帮会2名字e.targetGuildName，所属服务器e.targetServerName", "scores": [], "category": 3}, {"id": 3006, "name": "KFGuildAllianceQuit", "name1": "KFGuildAllianceUpdate", "event": "帮会跨服解除联盟", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.serverName}}服务器的{{e.guildName}}与{{e.targetServerName}}服务器的{{e.targetGuildName}}解除联盟，正所谓道不同，不相为谋！", "descComment": "时间e.time，帮会1名字e.guildName，所属服务器e.serverName，帮会2名字e.targetGuildName，所属服务器e.targetServerName", "scores": [], "category": 3}, {"id": 3007, "name": "GuildHostileSet", "name1": "GuildAllianceUpdate", "event": "帮会拉敌对", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.guildName}}将{{e.targetGuildName}}设置为敌对帮会，从此势不两立，有仇必报！", "descComment": "时间e.time，主动拉敌对帮会名字e.guildName，被拉敌对帮会名字e.targetGuildName", "scores": [], "category": 3}, {"id": 3008, "name": "GuildHostileCancel", "name1": "GuildAllianceUpdate", "event": "帮会解除敌对", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.guildName}}将{{e.targetGuildName}}从敌对帮会列表移除，快意江湖，一笑泯恩仇！", "descComment": "时间e.time，主动解除敌对帮会名字e.guildName，被解除敌对帮会名字e.targetGuildName", "scores": [], "category": 3}, {"id": 3009, "name": "KFGuildHostileSet", "name1": "KFGuildAllianceUpdate", "event": "帮会跨服拉敌对", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.serverName}}服务器的{{e.guildName}}将{{e.targetServerName}}服务器的{{e.targetGuildName}}设置为敌对帮会，从此势不两立，有仇必报！", "descComment": "时间e.time，主动拉敌对帮会名字e.guildName，所属服务器e.serverName，被拉敌对帮会名字e.targetGuildName，所属服务器e.targetServerName", "scores": [], "category": 3}, {"id": 3010, "name": "KFGuildHostileCancel", "name1": "KFGuildAllianceUpdate", "event": "帮会跨服解除敌对", "type": 3, "cond": "双方都是6、7级", "condArgs": "{6}", "desc": "{{e.time}}，{{e.serverName}}服务器的{{e.guildName}}将{{e.targetServerName}}服务器的{{e.targetGuildName}}从敌对帮会列表移除，快意江湖，一笑泯恩仇！", "descComment": "时间e.time，主动解除敌对帮会名字e.guildName，所属服务器e.serverName，被解除敌对帮会名字e.targetGuildName，所属服务器e.targetServerName", "scores": [], "category": 3}, {"id": 3011, "name": "GuildGForceChange", "event": "帮会转势力", "type": 3, "cond": "6、7级帮会", "condArgs": "{6}", "desc": "{{e.time}}，{{e.oldGuildGForce}}的{{e.guildName}}转势力至{{e.guildGForce}}，可谓待势乘时，应变而动！", "descComment": "时间e.time，帮会名字e.guildName，之前势力e.oldGuildGForce，之后势力e.guildGForce", "scores": [], "category": 3}, {"id": 3012, "name": "DragonHead", "event": "获得金风细雨楼龙首", "type": 3, "cond": "获得金风细雨楼龙首", "desc": "{{e.time}}，{{e.guildName}}成为{{e.guildGForce}}势力的龙首帮会，统率群侠，威震江湖！", "descComment": "时间e.time，帮会名字e.guildName", "scores": [], "category": 3}, {"id": 3013, "name": "BHLSTop1", "event": "获得甲一", "type": 3, "cond": "获得甲一帮会", "desc": "{{e.time}}，{{e.guildName}}帮会在本周帮会联赛中荣登甲组第一，傲视群雄，谁与争锋？", "descComment": "时间e.time，帮会名字e.guildName", "scores": [], "category": 3}]