type: object
required:
  - roleId
  - roleName
  - jobId
  - gender
  - subGender
  - headPaintId
  - bodyPaintId
properties:
  roleId:
    type: integer
    description: 角色ID
    example: 1234567890
    format: int64
  roleName:
    type: string
    description: 角色名称
    example: 星巫
  jobId:
    type: integer
    description: 职业ID
    example: 1
    format: int64
  gender:
    type: integer
    description: 性别
    example: 1
  subGender:
    type: integer
    description: 子性别
    example: 1
  headPaintId:
    type: number
    description: 头像ID
    example: 44731002
  bodyPaintId:
    type: number
    description: 立绘ID
    example: 44732002