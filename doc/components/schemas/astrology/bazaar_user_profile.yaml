type: object
required:
  - roleId
  - roleInfo
  - averageRating
  - totalReviews
properties:
  roleId:
    type: integer
    description: 角色ID
    example: 1234567890
    format: int64
  roleInfo:
    $ref: "../role/basic_role_info.yaml"
  userInfo:
    $ref: "./bazaar_user_info.yaml"
  averageRating:
    type: number
    description: 平均评分
    example: 4.5
    format: double
  totalReviews:
    type: integer
    description: 解惑被评分总次数
    example: 100
    format: int64