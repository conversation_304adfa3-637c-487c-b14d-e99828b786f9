type: object
required:
  - roleId
  - gender
  - astroLevel
  - astroType
properties:
  roleId:
    type: integer
    description: 角色ID
    example: 1234567890
    format: int64
  gender:
    type: integer
    description: 性别
    example: 1
    format: int32
  astroLevel:
    type: integer
    description: 星巫等级
    example: 1
    format: int32
  astroType:
    type: integer
    description: 星巫类型
    example: 1
    format: int32