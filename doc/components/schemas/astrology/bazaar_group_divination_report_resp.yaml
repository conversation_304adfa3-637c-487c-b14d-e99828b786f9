type: object
required:
  - reportDate
  - mostFocusedTopic
  - averageFortuneScore
  - mostCommentedQuestion
  - generatedAt
properties:
  reportDate:
    type: string
    description: 报告对应的日期
    example: '2023-05-24'
    format: date-time
  mostFocusedTopic:
    type: object
    required:
      - id
      - text
    properties:
      id:
        type: integer
        description: 话题ID
        example: 1234567890
      text:
        type: string
        description: 话题文本
        example: "爱情运势"
  averageFortuneScore:
    type: number
    description: 平均运势分数
    example: 75.5
  mostCommentedQuestion:
    type: string
    description: 最多回答的问惑
    example: 怎么才能让我的财运变好？
  generatedAt:
    type: string
    description: 报告生成时间
    example: 2023-05-24T20:00:00Z
    format: date-time