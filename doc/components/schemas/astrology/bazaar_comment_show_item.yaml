  type: object
  required:
    - id
    - postId
    - roleId
    - roleInfo
    - userInfo
    - text
    - createTime
  properties:
    id:
      type: integer
      description: 评论ID
      example: 1
      format: int64
    roleId:
      type: integer
      description: 角色ID
      example: 1
      format: int64
    roleInfo:
      $ref: "../role/basic_role_info.yaml"
    userInfo:
      $ref: "./bazaar_core_user_info.yaml"
    postId:
      type: integer
      description: 问惑ID
      example: 1
      format: int64
    text:
      type: string
      description: 评论内容
      example: 评论内容
    createTime:
      type: integer
      description: 创建时间
      example: 1747294491610
      format: int64
    rating:
      $ref: "./bazaar_rating.yaml"