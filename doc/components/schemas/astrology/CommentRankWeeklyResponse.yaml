type: object
required:
  - list
  - updateTime
  - weekStart
  - weekEnd
properties:
  list:
    type: array
    description: 周排行榜列表
    items:
      type: object
      required:
        - roleId
        - commentCount
        - rank
        - jobId
        - gender
        - subGender
        - headPaintId
        - bodyPaintId
      properties:
        roleId:
          type: integer
          description: 角色ID
          example: 123456
        roleName:
          type: string
          description: 角色名称
          example: "星巫大师"
        commentCount:
          type: integer
          description: 本周解惑次数
          example: 100
        rank:
          type: integer
          description: 周排名
          example: 1
        jobId:
          type: integer
          description: 职业ID
          example: 1
        gender:
          type: integer
          description: 性别
          example: 1
        subGender:
          type: integer
          description: 子性别
          example: 0
        headPaintId:
          type: integer
          description: 头部涂装ID
          example: 0
        bodyPaintId:
          type: integer
          description: 身体涂装ID
          example: 0
  updateTime:
    type: integer
    description: 周排行榜更新时间戳
    example: 1640995200000
  weekStart:
    type: integer
    description: 周开始时间戳
    example: 1640995200000
  weekEnd:
    type: integer
    description: 周结束时间戳
    example: 1641600000000
