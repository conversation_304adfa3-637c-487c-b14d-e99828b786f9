  type: object
  required:
    - id
    - question
    - dice
    - roleId
    - roleInfo
    - userInfo
    - topicId
    - commentCount
    - canComment
    - createTime
  properties:
    id:
      type: integer
      description: 问惑ID
      example: 1
      format: int64
    roleId:
      type: integer
      description: 角色ID
      example: 1
      format: int64
    topicId:
      type: integer
      description: 话题ID
      example: 1
      format: int64
    roleInfo:
      $ref: "../role/basic_role_info.yaml"
    userInfo:
      $ref: "./bazaar_core_user_info.yaml"
    question:
      type: string
      description: 用户选择或输入的占卜问题。
      example: "#今日困惑 考研初试分数怎么样?"
    dice:
      $ref: "./bazaar_dice.yaml"
    commentCount:
      type: integer
      description: 评论数
      example: 1
      format: int64
    canComment:
      type: boolean
      description: 是否可以评论, 当用户已经解析过该帖子并完成评论后时，该字段为false
      example: true
    createTime:
      type: integer
      description: 创建时间
      example: 1747294491610
      format: int64