allOf:
- $ref: "./bazaar_rating.yaml"
- type: object
  properties:
    id:
      type: integer
      description: 评分ID
      example: 1234567890
      format: int64
    roleId:
      type: integer
      description: 角色ID
      example: 1234567890
      format: int64
    roleInfo:
      $ref: "../role/basic_role_info.yaml"
    userInfo:
      $ref: "../astrology/bazaar_core_user_info.yaml"
    commentId:
      type: integer
      description: 解惑的评论ID
      example: 1234567890
      format: int64
    postId:
      type: integer
      description: 问题ID
      example: 1234567890
      format: int64