type: object
required:
  - gender
  - birthTime
  - birthPlace
  - currentPlace
  - astroLevel
  - astroType
properties:
  gender:
    type: number
    description: 玩家性别，e.g. 0男  1女
    enum: [0, 1]
    example: 0
  birthTime:
    type: string
    description: 出生日期，e.g. 2025-01-01
    example: 2025-01-01 00:00
    format: date-time
  birthPlace:
    $ref: ../common/location.yaml
  currentPlace:
    $ref: ../common/location.yaml
  astroLevel:
    type: integer
    description: 星巫等级
    example: 1
    format: int32
  astroType:
    type: integer
    description: 星巫类型(游戏配表决定)
    example: 1
    format: int32