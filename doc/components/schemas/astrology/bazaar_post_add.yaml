  type: object
  required:
    - question
    - dice
    - isSyncMoment
    - topicId
    - topicText
  properties:
    topicId:
      type: integer
      description: 话题ID
      example: 1
      format: int64
    topicText:
      type: string
      description: 话题文本
      example: "爱情运势"
    question:
      type: string
      description: 用户选择或输入的占卜问题。
      example: "#今日困惑 考研初试分数怎么样?"
    dice:
      $ref: "./bazaar_dice.yaml"
    isSyncMoment:
      type: boolean
      description: 是否同步到朋友圈
      example: true