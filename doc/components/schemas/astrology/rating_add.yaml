type: object
description: "用户评价"
required:
  - star
  - text
  - fromRoleId
  - toRoleId
properties:
  fromRoleId:
    type: integer
    description: "评价来源角色id"
    example: 1234567890
    format: int64
  toRoleId:
    type: integer
    description: "被评价的角色id"
    example: 1234567890
    format: int64
  star:
    type: integer
    format: int32
    description: "用户评分，1 到 5 之间的整数"
    minimum: 1
    maximum: 5
    example: 4
  text:
    type: string
    description: ""
    minLength: 0
    maxLength: 30
    example: "谢谢您的解析，非常准确！"