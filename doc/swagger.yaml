openapi: 3.0.2
info:
  contact:
    name: hzwangzhenhua
    email: <EMAIL>
  title: 逆水寒梦岛接口
  version: '1.0'
  license:
    name: MIT
  description: |
    <h2>逆水寒相关服务的API接口文档</h2>
    **CallByGameServer** 标记是指该接口需要游戏服务器调用， 会使用IP白名单来校验接口请求合法性
servers:
  - url: http://**************:88/nsh/md
    description: 测试环境(经典服)
  - url: http://**************:88/nsh/md
    description: 测试环境(怀旧服)
  - url: http://*************:88/nsh/md
    description: 测试环境(重生服)
  - url: https://rc.hi.163.com/nsh/md
    description: 预发布环境(经典服)
  - url: https://bt.hi.163.com/nsh/md
    description: 预发布环境(怀旧服)
  - url: https://rc2.hi.163.com/nsh/md
    description: 预发布环境(重生服)
  - url: https://nshssl.hi.163.com/nsh/md
    description: 正式环境(经典服)
  - url: https://nshhjf.hi.163.com/nsh/md
    description: 正式环境(怀旧服)
  - url: https://nshcsf.hi.163.com/nsh/md
    description: 正式环境(重生服)
  - url: https://nsh-md-server-stress.apps-sl.danlu.netease.com/nsh/md
    description: 逆水寒压测环境
  - url: http://localhost:9992/nsh/md
    description: 本地开发
tags:
  - name: test
    description: 测试
  - name: common_message
    description: 通用留言
  - name: common_message_admin
    description: 通用留言-后台管理
  - name: marriage_info
    description: |
      玩家的情缘信息
      [文档链接](https://confluence.leihuo.netease.com/pages/viewpage.action?pageId=*********)
  - name: marriage_photo_wall
    description: 情缘照片墙
  - name: club
  - name: clubAdmin
  - name: clubGM
  - name: clubMatchRank
  - name: clubSync
  - name: clubUE
  - name: clubWeb
  - name: comment
  - name: competitionAdmin
  - name: expression
  - name: fcm
  - name: fcm_ban_account
    description: 防沉迷封禁账号
  - name: gm_gb_super_aas_limit
    description: 计费对接的家长守护规则接口
  - name: follow
  - name: gm
  - name: honor
  - name: hotMoment
  - name: inform
  - name: like
  - name: message
  - name: moment
  - name: player
  - name: polemic
  - name: server
  - name: server_annal
    description: 服务器编年史
  - name: player_annal
    description: 个人编年史
  - description: 便利贴邮箱
    name: note_mail
  - name: transfer
  - name: wishlist
    description: 心愿单
  - name: wishlist_full_fill
    description: 心愿单实现记录
  - name: gm_week_renqi
    description: 周人气相关gm指令
  - name: gm_fcm
    description: 防沉迷相关gm指令
  - name: guild_photo_wall
    description: 帮会照片墙
  - name: guild_photo_wall_notification
    description: 帮会照片墙通知
  - name: guild_photo_wall_comment
    description: 帮会照片墙评论
  - name: guild_photo_wall_photo
    description: 帮会照片墙图片
  - name: guild_photo_wall_handpick
    description: 帮会照片墙精选墙
  - name: multi_garden_photo_wall
    description: 联居照片墙
  - name: multi_garden_photo_wall_notification
    description: 联居照片墙通知
  - name: multi_garden_photo_wall_comment
    description: 联居照片墙评论
  - name: multi_garden_photo_wall_photo
    description: 联居照片墙图片
  - name: multi_garden_photo_wall_handpick
    description: 联居照片墙精选墙
  - name: gm_server_list
    description: 服务器列表相关gm指令
  - name: gm_audit
    description: 审核相关服务
  - name: admin_moment_pick
    description: 动态精选管理接口
  - name: cloud_game_duration
    description: 云游戏时长相关
  - name: gm_cloud_game_duration_gm
    description: 云游戏时长gm指令相关
  - name: activity
    description: 提供给活动业务相关的接口
  - name: official_account
    description: 官方号相关管理接口
  - name: expansion_trial_scene
    description: 资料片前试玩场景
  - name: activity_take_photo
    description: 拍照活动
  - name: garden
    description: 庄园
  - name: force_event
    description: 势力大事本
  - name: bust_photo
    description: 角色半身像
  - name: garden_photo_unit
    description: |
      庄园特殊玩法图片存储 [需求单](https://cloud.pm.netease.com/v6/issues/106860)
  - name: damage_stat
    description: |
      伤害数据统计接口
      [对应需求单](https://cloud.pm.netease.com/v6/issues?c%5B%5D=id&c%5B%5D=tracker&c%5B%5D=parent&c%5B%5D=status&c%5B%5D=subject&c%5B%5D=fixed_version&c%5B%5D=assigned_to&c%5B%5D=start_date&c%5B%5D=due_date&c%5B%5D=done_ratio&f%5B%5D=status_id&f%5B%5D=assigned_to_id&op%5Bstatus_id%5D=%3D&op%5Bassigned_to_id%5D=%3D&v%5Bstatus_id%5D%5B%5D=1&v%5Bstatus_id%5D%5B%5D=2&v%5Bassigned_to_id%5D%5B%5D=me&page=1&per_page=40&node_node_display=-1&sort=id%3Adesc&set_filter=1&issue_id=104786)
  - name: skill_combo
    description: |
      技能组合推荐 [需求单](https://cloud.pm.netease.com/v6/issues/105072)
  - name: appearance_paint
    description: 立绘和头像
  - name: times_square_photo
    description: 天极岛时代广场图片
  - name: meme
    description: 表情包
  - name: audit
    description: 审核相关
  - name: astrology
    description: 百业-星巫
paths:
  /astrology/bazaar/post/add:
    post:
      summary: 星巫-问惑-发布
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarPostAdd
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/bazaar_post_add'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_post_add_resp'
  /astrology/bazaar/post/list:
    get:
      summary: 星巫-问惑-公共列表
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarPostList
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/common_page'
        - $ref: '#/components/parameters/common_page_size'
        - $ref: '#/components/parameters/astrology_topic_ids'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_post_list_resp'
  /astrology/bazaar/post/self_list:
    get:
      summary: 星巫-问惑-我的列表
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarPostSelfList
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/common_page'
        - $ref: '#/components/parameters/common_page_size'
        - $ref: '#/components/parameters/astrology_topic_ids'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_post_list_resp'
  /astrology/bazaar/post/interpret:
    get:
      summary: 星巫-问惑-AI占星骰子
      description: |
        发起解读，把对应问题和占卜结果发送AI, 并返回解读结果
        [伏羲提供的上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794)
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarPostInterpret
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/bazaar_post_id'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_post_interpret_resp'
  /astrology/bazaar/comment/add:
    post:
      summary: 星巫-问疑解答-新增
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarCommentAdd
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/bazaar_comment_add'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_comment_add_resp'
  /astrology/bazaar/comment/list:
    get:
      summary: 星巫-问疑解答-列表
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarPostCommentList
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/bazaar_post_id'
        - $ref: '#/components/parameters/common_page'
        - $ref: '#/components/parameters/common_page_size'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_comment_list_resp'
  /astrology/bazaar/rating/add:
    post:
      summary: 星巫-解惑评分-添加
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarRatingAdd
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/bazaar_rating_add'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_rating_add_resp'
  /astrology/rating/add:
    post:
      summary: 星巫-评分-添加 (服务端调用)
      description: 直接给用户评分, 不走巫师集会逻辑
      tags:
        - Astrology
      security: []
      operationId: ratingAdd
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/rating_add'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_rating_add_resp'
  /astrology/bazaar/hot_topic:
    get:
      summary: 星巫-热门话题
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarHotTopic
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_hot_topic_resp'
  /astrology/bazaar/group_divination_report:
    get:
      summary: 星巫-群体占卜报告
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarGroupDivinationReport
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/timestamp_in_query'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_group_divination_report_resp'
  /astrology/bazaar/user/register:
    post:
      summary: 星巫-用户-注册 (服务端调用)
      tags:
        - Astrology
      security: []
      operationId: bazaarUserRegister
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/bazaar_user_register_req'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_user_register_res'
  /astrology/bazaar/user/profile:
    get:
      summary: 星巫-用户资料-展示
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarUserProfile
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_user_profile'
  /astrology/server/bazaar/user/profile:
    get:
      summary: 星巫-用户资料-展示 (服务器调用)
      tags:
        - Astrology
      security: []
      operationId: bazaarUserProfileForServer
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_user_profile_for_server'
  /astrology/bazaar/user/profile/update:
    post:
      summary: 星巫-用户资料-更新 (服务端调用)
      description: 更新星巫用户资料，更新字段可选，不传对应字段不更新
      tags:
        - Astrology
      security: []
      operationId: bazaarUserProfileUpdate
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/bazaar_user_update_req'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/base_resp'
  /astrology/bazaar/user/rating/receive_list:
    get:
      summary: 星巫-用户-解惑收到的评分列表
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: bazaarUserRatingReceiveList
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/common_page'
        - $ref: '#/components/parameters/common_page_size'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_user_rating_list'
  /astrology/dice_result/interpret:
    post:
      summary: 星巫-占星骰子-AI解读
      description: 不走巫师集会逻辑, 根据问题和骰子结果，直接调用伏羲AI接口
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: diceResultInterpret
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - question
                - dice
              properties:
                question:
                  type: string
                dice:
                  $ref: '#/components/schemas/bazaar_dice'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/bazaar_post_interpret_resp'
  /astrology/horoscope/planetary_aspects:
    get:
      summary: 星巫-星盘-AI今日星象
      description: 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: horoscopePlanetaryAspects
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/timestamp_in_query'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/horoscope_planetary_aspects_resp'
  /astrology/horoscope/daily_forecast:
    get:
      summary: 星巫-星盘-AI今日运势
      description: 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
      tags:
        - Astrology
      security:
        - loginSkey: []
      operationId: horoscopeDailyForecast
      parameters:
        - $ref: '#/components/parameters/roleid_in_query'
        - $ref: '#/components/parameters/timestamp_in_query'
        - name: fortune
          in: query
          required: true
          description: 测运类型，包括：basic:基本运势、wealth:财运、career:事业与学业、love:情感
          schema:
            type: string
            enum:
              - basic
              - wealth
              - career
              - love
          example: career
        - name: timeInterval
          in: query
          required: true
          description: 时间区间。today:今日，week:本周（周日为起始）
          schema:
            type: string
            enum:
              - today
              - week
          example: today
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    $ref: '#/components/schemas/horoscope_daily_forecast_resp'
  /astrology/comment/rank:
    get:
      summary: 星巫-解惑次数排行榜 (服务端调用)
      description: 获取解惑次数排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
      tags:
        - Astrology
      security: []
      operationId: commentRank
      responses:
        '200':
          description: 成功获取排行榜
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                    example: 0
                  data:
                    $ref: '#/components/schemas/comment_rank_resp'
  /astrology/comment/rank_weekly:
    get:
      summary: 星巫-解惑次数周排行榜 (服务端调用)
      description: 获取解惑次数周排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
      tags:
        - Astrology
      security:
        - ipWhitelist: []
      operationId: commentRankWeekly
      responses:
        '200':
          description: 成功获取排行榜
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                    example: 0
                  data:
                    $ref: '#/components/schemas/comment_rank_resp'
  /common_message/add:
    post:
      tags:
        - common_message
      summary: 通用留言 - 添加留言
      description: 通用留言 - 添加留言
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                moduleId:
                  $ref: '#/components/schemas/commonMessageModuleId'
                roleid:
                  description: 角色id
                  type: integer
                  format: int64
                jobId:
                  description: 职业id
                  type: integer
                  format: int64
                  example: 1
                gender:
                  description: 性别
                  type: integer
                  example: 1
                subGender:
                  description: 子性别
                  type: integer
                  enum:
                    - 0
                    - 1
                  example: 1
                rolename:
                  description: 角色名
                  type: string
                  maxLength: 14
                  minLength: 1
                text:
                  description: 留言文本
                  type: string
                  maxLength: 50
                  minLength: 1
              description: 业务请求
              type: object
              required:
                - roleType
                - rolename
                - gender
                - subGender
                - text
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      id:
                        description: '[int64]留言id'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/recc_list:
    get:
      tags:
        - common_message
      summary: 通用留言 - 推荐列表
      description: 获取推荐的通用留言列表
      parameters:
        - name: roleid
          in: query
          description: 角色id
          required: true
          schema:
            description: 角色id
            type: integer
            format: int64
        - $ref: '#/components/parameters/commonMessageModuleId'
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      list:
                        items:
                          properties:
                            id:
                              description: 留言id
                              type: integer
                              format: int64
                            roleid:
                              description: 角色id
                              type: integer
                            gender:
                              description: 性别
                              type: integer
                              example: 1
                            subGender:
                              description: 子性别
                              type: integer
                              example: 1
                            jobId:
                              description: 职业
                              type: integer
                              example: 1
                            rolename:
                              description: 角色名
                              type: string
                              example: 张三
                            text:
                              description: 留言内容
                              type: string
                            createTime:
                              description: 创建时间
                              type: number
                              format: int64
                          required:
                            - id
                            - roleid
                            - rolename
                            - gender
                            - subGender
                            - jobId
                            - text
                            - createTime
                        description: 推荐留言列表
                        type: array
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/message/add:
    post:
      tags:
        - common_message_admin
      summary: 后台-通用留言-添加
      description: 后台-通用留言-添加
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                moduleId:
                  $ref: '#/components/schemas/commonMessageModuleId'
                rolename:
                  description: '[string]角色名'
                  type: string
                  maxLength: 14
                  minLength: 1
                text:
                  description: '[string]留言文本'
                  type: string
                  maxLength: 50
                  minLength: 1
              description: 业务请求
              type: object
              required:
                - moduleId
                - roleType
                - rolename
                - text
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      id:
                        description: '[int64]留言id'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/message/del:
    post:
      tags:
        - common_message_admin
      summary: 后台-通用留言-删除
      description: 后台-通用留言-删除
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                moduleId:
                  $ref: '#/components/schemas/commonMessageModuleId'
                ids:
                  items:
                    description: '[int64]子项信息'
                    type: integer
                    format: int64
                  description: '[slice]留言id'
                  type: array
                  maxItems: 20
                  minItems: 1
              description: 业务请求
              type: object
              required:
                - ids
                - moduleId
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      delCnt:
                        description: '[int]删除数量'
                        type: integer
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/message/list:
    get:
      tags:
        - common_message_admin
      summary: 后台-通用留言-列表
      description: 后台-通用留言-列表
      parameters:
        - $ref: '#/components/parameters/commonMessageModuleId'
        - name: roleid
          in: query
          description: '[int64]角色id'
          schema:
            description: '[int64]角色id'
            type: integer
            format: int64
        - name: kw
          in: query
          description: '[string]内容搜索关键字'
          schema:
            description: '[string]内容搜索关键字'
            type: string
            maxLength: 50
        - name: page
          in: query
          description: '[int]Page'
          required: true
          schema:
            description: '[int]Page'
            type: integer
            minimum: 1
        - name: page_size
          in: query
          description: '[int]PageSize'
          required: true
          schema:
            description: '[int]PageSize'
            type: integer
            maximum: 100
            minimum: 1
        - name: status
          in: query
          description: '[string]Status'
          required: true
          schema:
            description: '[string]Status'
            type: string
            enum:
              - all
              - show
              - hide
        - name: sort_by
          in: query
          description: '[string]SortBy'
          schema:
            description: '[string]SortBy'
            type: string
            enum:
              - role_type
              - show_status
              - create_time
        - name: sort_order
          in: query
          description: '[string]SortOrder'
          schema:
            description: '[string]SortOrder'
            type: string
            enum:
              - asc
              - desc
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      list:
                        items:
                          properties:
                            createTime:
                              description: '[int64]创建时间'
                              type: integer
                              format: int64
                            id:
                              description: '[int64]留言id'
                              type: integer
                              format: int64
                            roleType:
                              description: '[int]角色类型 0: 真玩家, 1: 普通假玩家, 2: NPC假玩家'
                              type: integer
                            roleid:
                              description: '[int64]角色id'
                              type: integer
                              format: int64
                            rolename:
                              description: '[string]角色名'
                              type: string
                            status:
                              description: '[int]状态 0:正常 -1:隐藏 -2:删除'
                              type: integer
                            text:
                              description: '[string]留言文本'
                              type: string
                          description: '[struct]子项信息'
                          type: object
                          required:
                            - roleid
                            - roleType
                            - rolename
                        description: '[slice]列表'
                        type: array
                      total:
                        description: '[int64]总数'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/message/update_status:
    post:
      tags:
        - common_message_admin
      summary: 后台-通用留言-更新显示状态
      description: 后台-通用留言-更新显示状态
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                moduleId:
                  $ref: '#/components/schemas/commonMessageModuleId'
                id:
                  description: '[int64]留言id'
                  type: integer
                  format: int64
                status:
                  description: '[int]0:正常 -1:隐藏'
                  type: integer
                  enum:
                    - 0
                    - -1
              description: 业务请求
              type: object
              required:
                - id
                - moduleId
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      id:
                        description: '[int64]留言id'
                        type: integer
                        format: int64
                      updateTime:
                        description: '[int64]更新时间'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/operator/add:
    post:
      tags:
        - common_message_admin
      summary: 后台-管理员-新增
      description: 后台-管理员-新增
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                fullName:
                  description: '[string]名字'
                  type: string
                  maxLength: 14
                  minLength: 1
                openId:
                  description: '[string]登录邮箱账号'
                  type: string
                  format: email
                  maxLength: 64
                  minLength: 1
                roleType:
                  description: '[int]角色类型 0: 普通运营, 1: 管理员'
                  type: integer
                  enum:
                    - 0
                    - 1
              description: 业务请求
              type: object
              required:
                - openId
                - fullName
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      id:
                        description: '[int64]id'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/operator/del:
    post:
      tags:
        - common_message_admin
      summary: 后台-管理员-删除
      description: 后台-管理员-删除
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                openId:
                  description: '[string]登录邮箱账号'
                  type: string
                  format: email
                  maxLength: 64
                  minLength: 1
              description: 业务请求
              type: object
              required:
                - openId
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      id:
                        description: '[int64]id'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/operator/list:
    get:
      tags:
        - common_message_admin
      summary: 后台-管理员-列表
      description: 后台-管理员-列表
      parameters:
        - name: page
          in: query
          description: '[int]Page'
          required: true
          schema:
            description: '[int]Page'
            type: integer
            minimum: 1
        - name: page_size
          in: query
          description: '[int]PageSize'
          required: true
          schema:
            description: '[int]PageSize'
            type: integer
            maximum: 100
            minimum: 1
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      list:
                        items:
                          properties:
                            createTime:
                              description: '[int64]创建时间'
                              type: integer
                              format: int64
                            fullName:
                              description: '[string]名字'
                              type: string
                            id:
                              description: '[int64]id'
                              type: integer
                              format: int64
                            openId:
                              description: '[string]登录邮箱账号'
                              type: string
                            roleType:
                              description: '[int]角色类型'
                              type: integer
                            updateTime:
                              description: '[int64]更新时间'
                              type: integer
                              format: int64
                          description: '[struct]子项信息'
                          type: object
                        description: '[slice]列表'
                        type: array
                      total:
                        description: '[int64]总数'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/operator/update:
    post:
      tags:
        - common_message_admin
      summary: 后台-管理员-更新
      description: 后台-管理员-更新
      requestBody:
        description: 请求body
        content:
          application/json:
            schema:
              properties:
                fullName:
                  description: '[string]名字'
                  type: string
                  maxLength: 14
                  minLength: 1
                openId:
                  description: '[string]登录邮箱账号'
                  type: string
                  format: email
                  maxLength: 64
                  minLength: 1
                roleType:
                  description: '[int]角色类型 0: 普通运营, 1: 管理员'
                  type: integer
                  enum:
                    - 0
                    - 1
              description: 业务请求
              type: object
              required:
                - openId
                - fullName
        required: true
      responses:
        '200':
          description: Http Status Code 200时的业务响应
          content:
            application/json:
              schema:
                properties:
                  code:
                    description: 业务响应Code
                    type: integer
                  data:
                    properties:
                      id:
                        description: '[int64]id'
                        type: integer
                        format: int64
                    description: 业务响应Data
                    type: object
                  message:
                    description: 业务响应Message
                    type: string
                description: 业务响应
                type: object
                required:
                  - code
                  - message
                  - data
  /common_message/admin/modules/list:
    get:
      tags:
        - common_message_admin
      summary: 后台-模块-列表
      description: 后台-模块-列表
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  data:
                    type: object
                    properties:
                      list:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 10001
                            name:
                              type: string
                              example: 吃饭奇遇
  /kafka/cloud_game_duration/on_buy_month_card_log:
    post:
      tags:
        - kafka
        - cloud_game_duration
      summary: 模拟日志消费-云游戏购买月卡
      description: 同步云游戏购买月卡的日志，处理消费逻辑
      requestBody:
        content:
          text/plain:
            schema:
              type: string
              example: |
                2025-02-13 14:14:29 gas[28056]:[2067273]INFO|PLAYER|[404353]#<EMAIL>,,101601001,,30,,01000000E903013000010000C58DAD67,,1001
      responses:
        '200':
          $ref: '#/components/responses/cloudGameNotifyBuyMonthCardRes'
  /meme/list:
    get:
      security:
        - loginSkey: []
      tags:
        - meme
      summary: 自定义表情包列表
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/MemeListRes'
  /meme/add:
    post:
      security:
        - loginSkey: []
      tags:
        - meme
      summary: 添加到自定义表情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/url'
        - $ref: '#/components/parameters/memeClientId'
      responses:
        '200':
          $ref: '#/components/responses/MemeAddRes'
  /meme/del:
    post:
      security:
        - loginSkey: []
      tags:
        - meme
      summary: 删除自定义表情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/memeClientId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /meme/return_pic:
    post:
      security:
        - loginSkey: []
      tags:
        - meme
      summary: 运营审核系统表情包图片审核地址回调接收地址
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditReturnPic'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /audit/send_pic_v2:
    post:
      tags:
        - audit
      summary: 发送图片到审核系统中
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditSendPic'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /audit/return_pic:
    post:
      security:
        - loginSkey: []
      tags:
        - audit
      summary: 运营审核系统图片审核地址回调接收地址
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditReturnPic'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /times_square_photo/list:
    get:
      security:
        - loginSkey: []
      tags:
        - times_square_photo
      description: 列出玩家时代广场图片
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/TimesSquarePhotoListRes'
  /times_square_photo/add:
    post:
      security:
        - loginSkey: []
      tags:
        - times_square_photo
      summary: 保存或更新玩家时代广场图片
      description: 同一个index上传url会覆盖， 游戏自己控制允许的数量，通过限制index的最大值
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TimesSquarePhoto'
      responses:
        '200':
          $ref: '#/components/responses/TimesSquarePhotoAddRes'
  /times_square_photo/del:
    post:
      security:
        - loginSkey: []
      tags:
        - times_square_photo
      summary: 删除玩家时代广场个人图片
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: index
          in: query
          description: 图片索引位置
          schema:
            type: number
            example: 1
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /appearance_paint/update:
    post:
      security:
        - loginSkey: []
      tags:
        - appearance_paint
      description: 更新立绘
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppearancePaint'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /appearance_paint/get:
    get:
      security:
        - loginSkey: []
      tags:
        - appearance_paint
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/AppearancePaintGetRes'
  /skill_combo/add:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
      summary: 上传技能组合
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SkillComboRecord'
      responses:
        '200':
          $ref: '#/components/responses/SkillComboAddRes'
  /skill_combo/del:
    post:
      tags:
        - skill_combo
      summary: 删除技能组合
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /skill_combo/get:
    get:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 获取技能组合
      responses:
        '200':
          $ref: '#/components/responses/SkillComboGetRes'
  /skill_combo/collect:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 收藏技能组合
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /skill_combo/cancel_collect:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 取消收藏技能组合
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /skill_combo/like:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 点赞技能组合
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /skill_combo/sync:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 同步技能组合
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /skill_combo/cancel_like:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 取消点赞技能组合
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /skill_combo/update:
    post:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboId'
      summary: 修改技能组合
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SkillComboRecord'
      responses:
        '200':
          $ref: '#/components/responses/SkillComboAddRes'
  /skill_combo/list:
    get:
      tags:
        - skill_combo
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/skillComboCategory'
        - $ref: '#/components/parameters/skillComboJobId'
        - name: sort_by
          description: 排序字段 hot=>热度 like=>点赞
          required: true
          in: query
          schema:
            type: string
            enum:
              - hot
              - like
        - name: type
          required: true
          description: |
            列表类别
            |val|desc|
            |--|--|
            |1|推荐套路（所有的都入选)|
            |2|我收藏的套路|
            |3|我上传的套路|
          in: query
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
        - name: kw
          description: 搜索关键字
          in: query
          required: false
          schema:
            type: string
        - name: region
          in: query
          required: true
          description: 技能推荐要按照服务器分组分开, 同属一个推荐区域使用同一个region
          schema:
            type: number
            example: 1
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      summary: 技能组合列表
      responses:
        '200':
          $ref: '#/components/responses/SkillComboListRes'
  /damage_stat/add:
    post:
      tags:
        - damage_stat
      summary: 新增伤害统计数据
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DamageStatRecord'
      responses:
        '200':
          $ref: '#/components/responses/DamageStatAddRes'
  /damage_stat/list:
    get:
      tags:
        - damage_stat
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/gamePlayId'
        - $ref: '#/components/parameters/bossId'
      summary: 玩家某一副本下某个boss下的伤害统计数据列表
      description: |
        1. 列表保留最新20条
        2. 计算历史最高秒伤和最高治疗
      responses:
        '200':
          $ref: '#/components/responses/DamageStatListRes'
  /damage_stat/share_info:
    get:
      tags:
        - damage_stat
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/damageStatShareId'
      summary: 通过分享id获取某一个伤害统计数据
      responses:
        '200':
          $ref: '#/components/responses/DamageStatShareInfoRes'
  /bust_photo/add:
    post:
      tags:
        - bust_photo
      summary: 半身像上传
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/bustPhotoIndex'
        - $ref: '#/components/parameters/bustPhotoUrl'
      responses:
        '200':
          $ref: '#/components/responses/BustPhotoAddRes'
  /bust_photo/get:
    get:
      tags:
        - bust_photo
      summary: 角色半身像获取指定位置
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetId'
        - $ref: '#/components/parameters/bustPhotoIndex'
      responses:
        '200':
          $ref: '#/components/responses/BustPhotoGetRes'
  /bust_photo/get_batch:
    get:
      tags:
        - bust_photo
      summary: 批量获取角色半身像
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/targetIds'
        - $ref: '#/components/parameters/bustPhotoIndex'
      responses:
        '200':
          $ref: '#/components/responses/BustPhotoListRes'
  /bust_photo/server/get_batch:
    get:
      tags:
        - bust_photo
      summary: 批量获取角色半身像(服务器调用)
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/targetIds'
        - $ref: '#/components/parameters/bustPhotoIndex'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          $ref: '#/components/responses/BustPhotoListRes'
  /bust_photo/list:
    get:
      tags:
        - bust_photo
      summary: 角色半身像列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetId'
      responses:
        '200':
          $ref: '#/components/responses/BustPhotoListRes'
  /garden_photo_unit/add:
    post:
      tags:
        - garden_photo_unit
      summary: 庄园特点玩法图片上传
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GardenPhotoUnitAddBody'
      responses:
        '200':
          $ref: '#/components/responses/GardenPhotoUnitAddRes'
  /garden_photo_unit/get:
    get:
      tags:
        - garden_photo_unit
      summary: 庄园特点玩法图片获取
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetId'
        - $ref: '#/components/parameters/itemTemplateId'
      responses:
        '200':
          $ref: '#/components/responses/GardenPhotoUnitGetRes'
  /garden_photo_unit/list:
    get:
      tags:
        - garden_photo_unit
      security:
        - loginSkey: []
      summary: 庄园特点玩法图片列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetId'
      responses:
        '200':
          $ref: '#/components/responses/GardenPhotoUnitListRes'
  /force_event/add:
    post:
      tags:
        - force_event
      summary: 新增势力大事 (游戏服务器调用)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForceEventAdd'
      responses:
        '200':
          $ref: '#/components/responses/ForceEventAddRes'
  /force_event/remark:
    post:
      tags:
        - force_event
      summary: 史官评论势力大事 (游戏服务器调用)
      parameters:
        - $ref: '#/components/parameters/forceEventId'
        - $ref: '#/components/parameters/forceEventRemarkRoleId'
        - $ref: '#/components/parameters/forceEventRemarkRoleName'
        - $ref: '#/components/parameters/forceEventContent'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /force_event/list:
    get:
      tags:
        - force_event
      summary: 势力大事列表
      parameters:
        - $ref: '#/components/parameters/roleIdRqd'
        - $ref: '#/components/parameters/forceId'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/timeStampOpt'
        - $ref: '#/components/parameters/roleType'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/ForceEventListRes'
  /force_event/del:
    post:
      tags:
        - force_event
      summary: 删除势力大事 (游戏服务器调用)
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/forceId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /force_event/like:
    post:
      tags:
        - force_event
      summary: 点赞势力大事
      parameters:
        - $ref: '#/components/parameters/roleIdRqd'
        - $ref: '#/components/parameters/forceEventId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /force_event/cancel_like:
    post:
      tags:
        - force_event
      summary: 取消点赞势力大事
      parameters:
        - $ref: '#/components/parameters/roleIdRqd'
        - $ref: '#/components/parameters/forceEventId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /health_check:
    get:
      tags:
        - test
      summary: 健康检查
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage_info/update:
    post:
      tags:
        - marriage_info
      summary: 更新情缘信息 (CallByGameServer)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarriageInfo'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage_info/transfer:
    post:
      tags:
        - marriage_info
      summary: 通知情缘id转服后变更 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/marriageOldId'
        - $ref: '#/components/parameters/marriageNewId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage_info/show:
    get:
      tags:
        - marriage_info
      summary: 获取情缘信息
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/marriageInfoId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/MarriageInfo'
  /official_accounts/moments/export_detail:
    post:
      tags:
        - official_account
      summary: 导出官方号动态下详情(点赞评论转发数据)
      parameters:
        - $ref: '#/components/parameters/momentId'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /gm/meme/list:
    get:
      tags:
        - gm_meme
      summary: 查看某一个玩家的列表
      parameters:
        - $ref: '#/components/parameters/roleId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /gm/meme/audit:
    get:
      tags:
        - gm_meme
      summary: 审核玩家的一张表情包图片
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/url'
        - $ref: '#/components/parameters/auditStatus'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /gm/cloud_game_duration/expire_daily_duration:
    post:
      tags:
        - gm_cloud_game_duration
      summary: 将非参数指定的日期的当日时长都清空
      description: 用来模拟当日时长第二天凌晨会过期的规则
      parameters:
        - $ref: '#/components/parameters/cloudGameDurationTodayStr'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /gm/cloud_game_duration/month_card/merge_server:
    post:
      tags:
        - gm_cloud_game_duration
      summary: 云游戏月卡时长需要合并
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: 合并时间
              required:
                - time
                - merges
              properties:
                time:
                  type: string
                  example: 2023-04-07 09:00
                merges:
                  description: 合并服务器信息
                  type: array
                  items:
                    type: object
                    required:
                      - from
                      - to
                    properties:
                      from:
                        type: number
                        description: 被合并的服务器id
                        example: 342
                      to:
                        type: number
                        description: 合并指向的服务器id
                        example: 341
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /kafka/cloud_game_duration/on_add_yuanbao_log:
    post:
      tags:
        - kafka
        - cloud_game_duration
      summary: 模拟日志消费-云游戏充值元宝
      description: 同步云游戏充值元宝的日志，处理消费逻辑
      requestBody:
        content:
          text/plain:
            schema:
              type: string
              example: |
                2024-10-21 15:51:02 gas[36576]:[Development Build]INFO|PLAYER|[404135]57256700001,,<EMAIL>,,1000,,57256700001z1e0f3,,false
      responses:
        '200':
          $ref: '#/components/responses/CloudGameNotifyYuanBaoChargeRes'
  /cloud_game_duration/show:
    get:
      tags:
        - cloud_game_duration
      summary: 展示当前urs账号云游戏时长信息以及月卡信息
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/cloudGameUserType'
        - $ref: '#/components/parameters/GameServerTimestamp'
      responses:
        '200':
          $ref: '#/components/responses/CloudGameDurationShowRes'
  /cloud_game_duration/month_card/show:
    get:
      tags:
        - cloud_game_duration
      summary: 展示当前urs账号服务器下月卡信息 (ip白名单授权)
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/serverId'
      responses:
        '200':
          $ref: '#/components/responses/CloudGameMonthCardShowRes'
  /cloud_game_duration/notify_charge_yuanbao:
    post:
      tags:
        - cloud_game_duration
      summary: 游戏通知充值元宝数量，服务负责折算充值比例
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/cloudGameUserType'
        - $ref: '#/components/parameters/cloudGameDurationChargeId'
        - $ref: '#/components/parameters/cloudGameDurationYuanbao'
        - $ref: '#/components/parameters/GameServerTimestamp'
      responses:
        '200':
          $ref: '#/components/responses/CloudGameNotifyYuanBaoChargeRes'
  /cloud_game_duration/notify_buy_month_card:
    post:
      tags:
        - cloud_game_duration
      summary: 游戏通知购买月卡信息
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/monthCardChannel'
        - $ref: '#/components/parameters/monthCardOrderId'
        - $ref: '#/components/parameters/monthCardBuyTime'
        - $ref: '#/components/parameters/monthCardDuration'
      responses:
        '200':
          $ref: '#/components/responses/cloudGameNotifyBuyMonthCardRes'
  /cloud_game_duration/receive_daily_award:
    post:
      tags:
        - cloud_game_duration
      summary: 领取每日登录奖励时长
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/cloudGameUserType'
        - $ref: '#/components/parameters/GameServerTimestamp'
      responses:
        '200':
          $ref: '#/components/responses/CloudGameDurationChangeRes'
  /cloud_game_duration/incr:
    post:
      tags:
        - cloud_game_duration
      summary: 新增云游戏时长
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/cloudGameUserType'
        - $ref: '#/components/parameters/cloudGameDurationType'
        - $ref: '#/components/parameters/cloudGameDuration'
        - $ref: '#/components/parameters/GameServerTimestamp'
      responses:
        '200':
          $ref: '#/components/responses/CloudGameDurationChangeRes'
  /cloud_game_duration/decr:
    post:
      tags:
        - cloud_game_duration
      summary: 扣除云游戏时长
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/cloudGameUserType'
        - $ref: '#/components/parameters/cloudGameCostType'
        - $ref: '#/components/parameters/cloudGameDuration'
        - $ref: '#/components/parameters/GameServerTimestamp'
      responses:
        '200':
          $ref: '#/components/responses/CloudGameDurationChangeRes'
  /admin/moment_pick/list:
    get:
      tags:
        - admin_moment_pick
      summary: 查看动态列表
      parameters:
        - $ref: '#/components/parameters/momentPickType'
        - $ref: '#/components/parameters/momentPickKw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/MomentPickListRes'
  /admin/moment_pick/update_status:
    post:
      tags:
        - admin_moment_pick
      summary: 批量更新精选状态
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MomentPickBatchOp'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /admin/moment_pick/toggle_top:
    post:
      tags:
        - admin_moment_pick
      summary: 移动该精选状态到最前
      parameters:
        - $ref: '#/components/parameters/momentId'
        - $ref: '#/components/parameters/switch'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /marriage/photo_wall/list:
    get:
      tags:
        - marriage_photo_wall
      summary: 查看情缘所有照片墙列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/marriageId'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallListRes'
  /marriage/photo_wall/photo/{id}/show:
    get:
      tags:
        - marriage_photo_wall
      summary: 查看照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallPhotoShowRes'
  /marriage/photo_wall/photo/{id}/move:
    post:
      tags:
        - marriage_photo_wall
      summary: 移动照片墙中图片的槽位
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoId'
        - $ref: '#/components/parameters/photoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/handpick_wall/up:
    post:
      tags:
        - marriage_photo_wall
      summary: 情缘当家从精选墙上架图片 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoIdInQuery'
        - $ref: '#/components/parameters/photoWallWallId'
        - $ref: '#/components/parameters/photoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/handpick_wall/down:
    post:
      tags:
        - marriage_photo_wall
      summary: 情缘当家从精选墙下架图片(CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallWallId'
        - $ref: '#/components/parameters/photoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/photo/{id}/del:
    post:
      tags:
        - marriage_photo_wall
      summary: 删除照片墙的这张图片 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/marriageId'
        - $ref: '#/components/parameters/isGuildLeader'
        - $ref: '#/components/parameters/photoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /marriage/photo_wall/photo/{id}/like:
    post:
      tags:
        - marriage_photo_wall
      summary: 点赞照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/photo/{id}/cancel_like:
    post:
      tags:
        - marriage_photo_wall
      summary: 取消点赞照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/comment/{id}/like:
    post:
      deprecated: true
      tags:
        - marriage_photo_wall
      summary: 点赞照片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/comment/{id}/cancel_like:
    post:
      deprecated: true
      tags:
        - marriage_photo_wall
      summary: 取消点赞照片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/photo/{id}/comment/list:
    get:
      tags:
        - marriage_photo_wall
      summary: 查看照片墙图片的评论列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallCommentListRes'
  /marriage/photo_wall/photo/{id}/comment/add:
    post:
      tags:
        - marriage_photo_wall
      summary: 添加照片墙图片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallPhotoId'
        - $ref: '#/components/parameters/commentText'
        - $ref: '#/components/parameters/replyId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/photo/{photo_id}/comment/del:
    post:
      tags:
        - marriage_photo_wall
      summary: 删除照片墙图片的评论 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/isGuildLeader'
        - $ref: '#/components/parameters/marriageId'
        - $ref: '#/components/parameters/photoWallPhotoIdFull'
        - $ref: '#/components/parameters/photoWallCommentIdInQuery'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /marriage/photo_wall/{id}/photo/add:
    post:
      tags:
        - marriage_photo_wall
      summary: 上传照片墙里的图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallId'
        - $ref: '#/components/parameters/gpwOverwrite'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhotoWallPhotoAdd'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/{id}/show:
    get:
      tags:
        - guild_photo_wall
      summary: 查看照片墙
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallId'
      responses:
        '200':
          $ref: '#/components/responses/PhotoWallShowRes'
  /guild/photo_wall/{id}/update:
    post:
      tags:
        - guild_photo_wall
      summary: 更新照片墙信息 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhotoWallAdd'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/add:
    post:
      tags:
        - guild_photo_wall
      summary: 开启新的照片墙 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildId'
        - $ref: '#/components/parameters/guildLevel'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhotoWallAdd'
      responses:
        '200':
          $ref: '#/components/responses/PhotoWallAddRes'
  /guild/photo_wall/notifications/list:
    get:
      tags:
        - guild_photo_wall_notification
      summary: 通知列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationStatus'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallNotificationListRes'
  /guild/photo_wall/notifications/read:
    post:
      tags:
        - guild_photo_wall_notification
      summary: 单个通知设为已读
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /guild/photo_wall/notifications/read_all:
    post:
      tags:
        - guild_photo_wall_notification
      summary: 通知列表全部设为已读
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /guild/photo_wall/list:
    get:
      tags:
        - guild_photo_wall
      summary: 查看帮会所有照片墙列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildId'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallListRes'
  /guild/photo_wall/photo/{id}/show:
    get:
      tags:
        - guild_photo_wall_photo
      summary: 查看照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallPhotoShowRes'
  /guild/photo_wall/photo/{id}/move:
    post:
      tags:
        - guild_photo_wall_photo
      summary: 移动照片墙中图片的槽位
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
        - $ref: '#/components/parameters/guildPhotoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/handpick_wall/up:
    post:
      tags:
        - guild_photo_wall_handpick
      summary: 帮会当家从精选墙上架图片 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoIdInQuery'
        - $ref: '#/components/parameters/guildPhotoWallWallId'
        - $ref: '#/components/parameters/guildPhotoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/handpick_wall/down:
    post:
      tags:
        - guild_photo_wall_handpick
      summary: 帮会当家从精选墙下架图片(CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallWallId'
        - $ref: '#/components/parameters/guildPhotoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/photo/{id}/del:
    post:
      tags:
        - guild_photo_wall_photo
      summary: 删除照片墙的这张图片 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/isGuildLeader'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /guild/photo_wall/photo/{id}/like:
    post:
      tags:
        - guild_photo_wall_photo
      summary: 点赞照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/photo/{id}/cancel_like:
    post:
      tags:
        - guild_photo_wall_photo
      summary: 取消点赞照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/comment/{id}/like:
    post:
      deprecated: true
      tags:
        - guild_photo_wall_comment
      summary: 点赞照片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/comment/{id}/cancel_like:
    post:
      deprecated: true
      tags:
        - guild_photo_wall_comment
      summary: 取消点赞照片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/photo/{id}/comment/list:
    get:
      tags:
        - guild_photo_wall_comment
      summary: 查看照片墙图片的评论列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallCommentListRes'
  /guild/photo_wall/photo/{id}/comment/add:
    post:
      tags:
        - guild_photo_wall_comment
      summary: 添加照片墙图片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallPhotoId'
        - $ref: '#/components/parameters/commentText'
        - $ref: '#/components/parameters/replyId'
        - $ref: '#/components/parameters/canComment'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /guild/photo_wall/photo/{photo_id}/comment/del:
    post:
      tags:
        - guild_photo_wall_comment
      summary: 删除照片墙图片的评论 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/isGuildLeader'
        - $ref: '#/components/parameters/photoWallPhotoIdFull'
        - $ref: '#/components/parameters/photoWallCommentIdInQuery'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /guild/photo_wall/{id}/photo/add:
    post:
      tags:
        - guild_photo_wall_photo
      summary: 上传照片墙里的图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildPhotoWallId'
        - $ref: '#/components/parameters/gpwOverwrite'
        - $ref: '#/components/parameters/canComment'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GuildPhotoWallPhotoAdd'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/{id}/show:
    get:
      tags:
        - marriage_photo_wall
      summary: 查看照片墙
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallId'
      responses:
        '200':
          $ref: '#/components/responses/PhotoWallShowRes'
  /marriage/photo_wall/{id}/update:
    post:
      tags:
        - marriage_photo_wall
      summary: 更新照片墙信息 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/photoWallId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhotoWallAdd'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /marriage/photo_wall/add:
    post:
      tags:
        - marriage_photo_wall
      summary: 开启新的照片墙 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/marriageId'
        - $ref: '#/components/parameters/marriageLevel'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhotoWallAdd'
      responses:
        '200':
          $ref: '#/components/responses/PhotoWallAddRes'
  /marriage/photo_wall/notifications/list:
    get:
      tags:
        - marriage_photo_wall
      summary: 通知列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationStatus'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallNotificationListRes'
  /marriage/photo_wall/notifications/read:
    post:
      tags:
        - marriage_photo_wall
      summary: 单个通知设为已读
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /marriage/photo_wall/notifications/read_all:
    post:
      tags:
        - marriage_photo_wall
      summary: 通知列表全部设为已读
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /multi_garden/photo_wall/list:
    get:
      tags:
        - multi_garden_photo_wall
      summary: 查看联居所有照片墙列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenId'
      responses:
        '200':
          $ref: '#/components/responses/MultiGardenPhotoWallListRes'
  /multi_garden/photo_wall/photo/{id}/show:
    get:
      tags:
        - multi_garden_photo_wall_photo
      summary: 查看照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/MultiGardenPhotoWallPhotoShowRes'
  /multi_garden/photo_wall/photo/{id}/move:
    post:
      tags:
        - multi_garden_photo_wall_photo
      summary: 移动照片墙中图片的槽位
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
        - $ref: '#/components/parameters/multiGardenPhotoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/handpick_wall/up:
    post:
      tags:
        - multi_garden_photo_wall_handpick
      summary: 联居当家从精选墙上架图片 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoIdInQuery'
        - $ref: '#/components/parameters/multiGardenPhotoWallWallId'
        - $ref: '#/components/parameters/multiGardenPhotoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/handpick_wall/down:
    post:
      tags:
        - multi_garden_photo_wall_handpick
      summary: 帮会当家从精选墙下架图片(CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallWallId'
        - $ref: '#/components/parameters/multiGardenPhotoWallSlot'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/photo/{id}/del:
    post:
      tags:
        - multi_garden_photo_wall_photo
      summary: 删除照片墙的这张图片 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/isMultiGardenLeader'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /multi_garden/photo_wall/photo/{id}/like:
    post:
      tags:
        - multi_garden_photo_wall_photo
      summary: 点赞照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/photo/{id}/cancel_like:
    post:
      tags:
        - multi_garden_photo_wall_photo
      summary: 取消点赞照片墙中具体图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/comment/{id}/like:
    post:
      deprecated: true
      tags:
        - multi_garden_photo_wall_comment
      summary: 点赞照片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/comment/{id}/cancel_like:
    post:
      deprecated: true
      tags:
        - multi_garden_photo_wall_comment
      summary: 取消点赞照片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallCommentId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/photo/{id}/comment/list:
    get:
      tags:
        - multi_garden_photo_wall_comment
      summary: 查看照片墙图片的评论列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GuildPhotoWallCommentListRes'
  /multi_garden/photo_wall/photo/{id}/comment/add:
    post:
      tags:
        - multi_garden_photo_wall_comment
      summary: 添加照片墙图片的评论
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoId'
        - $ref: '#/components/parameters/commentText'
        - $ref: '#/components/parameters/replyId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/photo/{photo_id}/comment/del:
    post:
      tags:
        - multi_garden_photo_wall_comment
      summary: 删除照片墙图片的评论 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/isMultiGardenLeader'
        - $ref: '#/components/parameters/multiGardenPhotoWallPhotoIdFull'
        - $ref: '#/components/parameters/multiGardenPhotoWallCommentIdInQuery'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /multi_garden/photo_wall/{id}/photo/add:
    post:
      tags:
        - multi_garden_photo_wall_photo
      summary: 上传照片墙里的图片
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallId'
        - $ref: '#/components/parameters/gpwOverwrite'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GuildPhotoWallPhotoAdd'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/{id}/show:
    get:
      tags:
        - multi_garden_photo_wall
      summary: 查看照片墙
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallId'
      responses:
        '200':
          $ref: '#/components/responses/MultiGardenPhotoWallShowRes'
  /multi_garden/photo_wall/{id}/update:
    post:
      tags:
        - multi_garden_photo_wall
      summary: 更新照片墙信息 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenPhotoWallId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PhotoWallAdd'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /multi_garden/photo_wall/add:
    post:
      tags:
        - multi_garden_photo_wall
      summary: 开启新的照片墙 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/multiGardenId'
        - $ref: '#/components/parameters/multiGardenLevel'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MultiGardenPhotoWallAdd'
      responses:
        '200':
          $ref: '#/components/responses/MultiGardenPhotoWallAddRes'
  /multi_garden/photo_wall/notifications/list:
    get:
      tags:
        - multi_garden_photo_wall_notification
      summary: 通知列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationStatus'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/MultiGardenPhotoWallNotificationListRes'
  /multi_garden/photo_wall/notifications/read:
    post:
      tags:
        - multi_garden_photo_wall_notification
      summary: 单个通知设为已读
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/notificationId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /multi_garden/photo_wall/notifications/read_all:
    post:
      tags:
        - multi_garden_photo_wall_notification
      summary: 通知列表全部设为已读
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /note_mail/{mail_box_type}/show:
    get:
      tags:
        - note_mail
      summary: 查看收件箱便利贴详情
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/mailBoxType'
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/noteMailId'
      responses:
        '200':
          $ref: '#/components/responses/NoteMailInboxShowRes'
  /note_mail/{mail_box_type}/del:
    post:
      tags:
        - note_mail
      summary: 删除便利贴
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/mailBoxType'
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/noteMailId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /note_mail/inbox/list:
    get:
      tags:
        - note_mail
      summary: 收件箱邮件列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/noteMailInboxListKw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/NoteMailInBoxListRes'
  /note_mail/outbox/list:
    get:
      tags:
        - note_mail
      summary: 发件箱邮件列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/noteMailOutboxListKw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/NoteMailInBoxListRes'
  /note_mail/{mail_box_type}/star:
    post:
      tags:
        - note_mail
      summary: 收藏便利贴邮件
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/mailBoxType'
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/noteMailId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /note_mail/{mail_box_type}/unstar:
    post:
      tags:
        - note_mail
      summary: 取消收藏便利贴邮件
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/mailBoxType'
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/noteMailId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /note_mail/send:
    post:
      tags:
        - note_mail
      summary: 发送便利贴邮件 (只支持游戏服务器通过ip白名单访问)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NoteMailSend'
      responses:
        '200':
          $ref: '#/components/responses/NoteMailSendRes'
  /note_mail/clean_all:
    post:
      tags:
        - note_mail
      summary: 清理玩家的便利贴信息，包括收和发, 比如藏宝阁交易时 (CallByGameServer)
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /gm/week_renqi/get:
    get:
      security:
        - gmAuth: []
      tags:
        - fcm
        - gm_week_renqi
      summary: 读取周人气
      operationId: getWeekRenQiGm
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/weekStr'
      responses:
        '200':
          $ref: '#/components/responses/GmWeekRenQiRes'
  /gm/gb_super_aas_limit/urs/{account}:
    get:
      security:
        - gmAuth: []
      tags:
        - gm_gb_super_aas_limit
        - fcm
      summary: 批量查询家长守护防沉迷规则(不使用gameId)
      parameters:
        - $ref: '#/components/parameters/accountInPath'
      responses:
        '200':
          description: OK
  /gm/gb_super_aas_limit/{gameid}/urs/{account}:
    post:
      security:
        - gmAuth: []
      tags:
        - gm_gb_super_aas_limit
        - fcm
      summary: 设置或者更新家长守护防沉迷规则
      description: |
        [计费对应上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#3.-%E6%B7%BB%E5%8A%A0(%E6%88%96%E6%9B%B4%E6%96%B0)%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
      parameters:
        - $ref: '#/components/parameters/gameidInPath'
        - $ref: '#/components/parameters/accountInPath'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GbSuperAssLimitFcmRuleReq'
      responses:
        '200':
          description: OK
    delete:
      security:
        - gmAuth: []
      tags:
        - gm_gb_super_aas_limit
      summary: 删除家长守护防沉迷规则
      parameters:
        - $ref: '#/components/parameters/gameidInPath'
        - $ref: '#/components/parameters/accountInPath'
      description: |
        [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#4-%E5%88%A0%E9%99%A4%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
      responses:
        '200':
          description: OK
    get:
      security:
        - gmAuth: []
      tags:
        - fcm
        - gm_gb_super_aas_limit
      summary: 查询家长守护防沉迷规则
      parameters:
        - $ref: '#/components/parameters/gameidInPath'
        - $ref: '#/components/parameters/accountInPath'
      description: |
        [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#5.-%E6%9F%A5%E8%AF%A2%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
      responses:
        '200':
          description: OK
  /gm/fcm/ty/add_kickoff_task:
    post:
      tags:
        - gm_fcm
      summary: 添加天谕踢人任务
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/kickDate'
      responses:
        '200':
          description: OK
  /gm/fcm/{gameid}/get_daily_online_time:
    get:
      tags:
        - gm_fcm
      summary: 查询当日在线时长
      parameters:
        - $ref: '#/components/parameters/gameidInPath'
        - $ref: '#/components/parameters/urs'
      responses:
        '200':
          description: OK
  /gm/fcm/{gameid}/set_daily_online_time:
    post:
      tags:
        - gm_fcm
      summary: 设置当日在线时长
      parameters:
        - $ref: '#/components/parameters/gameidInPath'
        - $ref: '#/components/parameters/urs'
        - name: duration
          in: query
          schema:
            type: integer
            description: 时长(秒)
            example: 600
      responses:
        '200':
          description: OK
  /gm/week_renqi/set:
    post:
      security:
        - gmAuth: []
      tags:
        - gm_week_renqi
      summary: 设置周人气
      operationId: setWeekRenqiGm
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/weekStr'
        - $ref: '#/components/parameters/weekRenqi'
      responses:
        '200':
          $ref: '#/components/responses/GmWeekRenQiRes'
  /gm/audit/update_status:
    get:
      security:
        - gmAuth: []
      tags:
        - gm_audit
      summary: 设置图片视频审核状态
      parameters:
        - $ref: '#/components/parameters/mediaType'
        - $ref: '#/components/parameters/mediaResourceId'
        - $ref: '#/components/parameters/auditStatus'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /gm/server_list/merge_server/list:
    get:
      security:
        - gmAuth: []
      tags:
        - gm_server_list
      summary: 查看设置的合服关系列表
      responses:
        '200':
          $ref: '#/components/responses/GmServerListMergeServerListRes'
  /gm/server_list/merge_server/add:
    post:
      security:
        - gmAuth: []
      tags:
        - gm_server_list
      summary: 添加合服关系
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GmServerListMergeServerItem'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /gm/server_list/merge_server/clean:
    post:
      security:
        - gmAuth: []
      tags:
        - gm_server_list
      summary: 清理所有设置的合服关系
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /server_annal/event/upvote:
    post:
      tags:
        - server_annal
      summary: 点赞编年史事件
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/eventId'
        - $ref: '#/components/parameters/undo'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /server_annal/event/downvote:
    post:
      tags:
        - server_annal
      summary: 点踩编年史事件
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/eventId'
        - $ref: '#/components/parameters/undo'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /server_annal/list:
    get:
      tags:
        - server_annal
      summary: 获取编年史事件列表
      security:
        - loginSkey: []
      operationId: GetServerAnnalEventList
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/EventCategory'
        - $ref: '#/components/parameters/RoleIdAndNameKw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GetServerAnnalEventListRes'
  /server_annal/score:
    get:
      summary: 获取个人累计积分数据
      tags:
        - server_annal
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ServerAnnalScoreRes'
  /server_annal/fengyun_players:
    get:
      summary: 风云人物
      tags:
        - server_annal
      operationId: getServerAnnalFengyunPlayers
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/size'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    properties:
                      list:
                        description: 排行的数组(前3)
                        type: array
                        items:
                          $ref: '#/components/schemas/ServerAnnalFengyunPlayer'
  /server_annal/sync:
    post:
      summary: 游戏服务器同步编年史事件 (IP白名单授权)
      tags:
        - server_annal
      operationId: SyncServerAnnal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncServerAnnalEvent'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /fcm/ban_account/show:
    get:
      tags:
        - fcm_ban_account
      summary: 查询账号封禁信息
      parameters:
        - $ref: '#/components/parameters/urs'
      responses:
        '200':
          $ref: '#/components/responses/BanAccountShowRes'
  /fcm/ban_account/add:
    post:
      tags:
        - fcm_ban_account
      summary: 添加账号封禁
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/banAccountTime'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /fcm/ban_account/remove:
    post:
      tags:
        - fcm_ban_account
      summary: 解除账号封禁
      parameters:
        - $ref: '#/components/parameters/urs'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /player_annal/list:
    get:
      tags:
        - player_annal
      summary: 获取玩家编年史事件列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GetPlayerAnnalEventListRes'
  /player_annal/sync:
    post:
      summary: 游戏服务器同步编年史事件 (CallByGameServer)
      tags:
        - player_annal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncPlayerAnnalEvent'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /fcm/interfaces/realname/sync_realname.do:
    get:
      tags:
        - fcm
      summary: 实名同步（游戏专用）
      description: 游戏服务器同步给梦岛，梦岛负责查询中宣部认证信息后，之后同步给urs [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=459#/)
      operationId: SyncRealName
      parameters:
        - $ref: '#/components/parameters/username'
        - $ref: '#/components/parameters/product'
        - $ref: '#/components/parameters/idnum'
        - $ref: '#/components/parameters/idtype'
        - $ref: '#/components/parameters/realname'
        - $ref: '#/components/parameters/userip'
        - $ref: '#/components/parameters/syncStatus'
        - $ref: '#/components/parameters/syncPi'
      responses:
        '200':
          description: 保持和原urs接口的一致性
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UrsApiRet'
  /fcm/interfaces/yd/web/queryRealnameStatus.do:
    get:
      tags:
        - fcm
      summary: 手机帐号查询实名接口
      operationId: QueryRealNameStatusByYd
      description: 接口参数格式保持和上游一致 [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=105#/)
      parameters:
        - $ref: '#/components/parameters/ydUsername'
        - $ref: '#/components/parameters/product'
        - $ref: '#/components/parameters/userip'
        - $ref: '#/components/parameters/needPi'
      responses:
        '200':
          description: 保持和原urs接口的一致性
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryRealNameStatus'
  /fcm/services/queryRealnameAndAntiIndulgenceServlet:
    get:
      tags:
        - fcm
      summary: 查询实名认证和防沉迷
      operationId: QueryRealNameAndAntiIndulgence
      description: 接口参数格式保持和上游一致 [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=170#/)
      parameters:
        - $ref: '#/components/parameters/username'
        - $ref: '#/components/parameters/product'
        - name: accept
          required: false
          in: query
          schema:
            type: string
            enum:
              - application/json
              - text/plain
            default: text/plain
      responses:
        '200':
          description: |
            #### 保持和原urs接口的一致性
            super_aas_limit_wrap会被 base64(JSON.stringify(super_aas_limit_wrap))拼接到返回中, 结构和含义参考手机账号接口
          content:
            text/plain:
              schema:
                type: string
                example: 201\nrealname_flag=1&realname_status=127&id=120104200301010398&reg_time=**********&update_time=**********&fakeAdult=false&isBan=false&banTime=*************&parentsAuth=true&parentsAuthRedirectUrl=https%3A%2F%2Fn.163.com%2F&ngc_result_wrap#risk_source=0&ngc_result_wrap#ngc_result#all_score=500&ngc_result_wrap#ngc_result#all_score_positive=0 &super_aas_limit_wrap=eyJyZXN0cmljdF9sb2dpbiI6ZmFsc2UsImNoZWNrX3N0YXR1cyI6LTEsInN1cGVyX2Fzc19saW1pdCI6bnVsbH0=
  /fcm/firefly/gm/query_gameid_by_urs:
    get:
      tags:
        - fcm
      summary: urs中实名无法区分来自哪个游戏，但是中宣实名上报时，需要区分游戏，并用到游戏对应的参数（中宣分配）， 因此，由计费搜集urs-gameid的映射关系，并提供给urs查询
      operationId: QueryGameIdByUrs
      parameters:
        - $ref: '#/components/parameters/username'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryGameIdByUrs'
  /fcm/behavior/login:
    get:
      tags:
        - fcm
      summary: 上报登录行为
      operationId: LoginBehavior
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/fcmGameId'
        - $ref: '#/components/parameters/timestamp'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiOk'
  /fcm/behavior/logout:
    get:
      tags:
        - fcm
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/fcmGameId'
        - $ref: '#/components/parameters/timestamp'
      summary: 上报登出行为
      operationId: LogoutBehavior
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiOk'
  /player/clean_history:
    post:
      tags:
        - player
      summary: 清理梦岛历史(删除状态， 留言板， 通知)
      description: 用于提供个买号玩家清理之前账号历史记录的接口
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /changesign:
    post:
      tags:
        - player
      summary: 编辑签名
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: signature
          in: query
          required: true
          description: 签名
          schema:
            type: string
            example: 签名
      responses:
        '200':
          description: OK
  /player/copy_moments:
    post:
      tags:
        - player
      summary: 复制旧号朋友圈动态
      description: |-
        换号时支持把旧号上的朋友圈状态复制到新号上
        全部由旧号发出的朋友圈状态，不包括状态下的转发、评论、点赞，不包括留言板，不包括左侧个人资料
        [ReadMine](http://cloud.pm.netease.com/v6/issues/60699)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - schema:
            type: number
            example: ***********
          in: query
          name: copyRoleId
          description: 旧号RoleId
          required: true
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          $ref: '#/components/responses/PlayerCopyMoments'
      operationId: copyMoments
    parameters: []
  /likemoment:
    post:
      tags:
        - like
      summary: 点赞心情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/likeAction'
        - $ref: '#/components/parameters/hotFactor'
      responses:
        '200':
          description: OK
  /guild_polemic/apply_add_token:
    get:
      tags:
        - polemic
        - server
      summary: 获取发布檄文的token(游戏服务器申请)
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildId'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(guildId + nonce + roleid +  time + auth_slat)
          schema:
            type: string
            example: 8947248ed8ee41a51cff29223f25a4c2
      responses:
        '200':
          description: OK
  /guild_polemic/add:
    post:
      tags:
        - polemic
      summary: 添加帮会檄文
      parameters:
        - $ref: '#/components/parameters/pubToken'
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/roleName'
        - name: serverId
          in: query
          description: 服务器ID
          schema:
            type: number
            example: 1
        - name: title
          in: query
          description: 檄文标题
          schema:
            type: string
            example: title
        - name: content
          in: query
          description: 檄文内容
          schema:
            type: string
            example: guild polemic content title
        - name: guildId
          in: query
          description: 发布者帮会id
          schema:
            type: number
            example: 1001
        - name: guildName
          in: query
          description: 发布者帮会名字
          schema:
            type: string
            example: guildName
        - name: toGuildId
          in: query
          description: 对手帮会id
          schema:
            type: number
            example: 1002
        - name: toGuildName
          in: query
          description: 对手帮会名字
          schema:
            type: string
            example: opponent_guildName
        - name: toServerName
          in: query
          description: 对手Serve名字
          schema:
            type: string
            example: opponent_serverName
        - name: visibility
          in: query
          description: 檄文可见 1 => 全部  2 => 帮会
          schema:
            type: number
            enum:
              - 1
              - 2
            example: 1
        - name: style
          in: query
          description: 檄文发布风格 1=>横排， 2=>竖排
          schema:
            type: number
            enum:
              - 1
              - 2
            example: 1
      responses:
        '200':
          description: ok
  /guild_polemic/like:
    post:
      tags:
        - polemic
      summary: 点赞檄文
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/polemic_id'
      responses:
        '200':
          description: ok
  /guild_polemic/cancel_like:
    post:
      tags:
        - polemic
      summary: 取消点赞檄文
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/polemic_id'
      responses:
        '200':
          description: ok
  /guild_polemic/get_detail:
    get:
      tags:
        - polemic
      summary: 获取檄文详情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/polemic_id'
      responses:
        '200':
          description: OK
  /guild_polemic/list:
    get:
      tags:
        - polemic
      summary: 获取檄文列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/guildId'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /guild_polemic/list_hot:
    get:
      tags:
        - polemic
      summary: 获取热门檄文列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: type
          in: query
          description: 筛选类型(本服热点， 全服热门)
          schema:
            type: string
            enum:
              - local
              - all
            example: local
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /guild_expression/add:
    post:
      tags:
        - expression
      summary: 添加帮会表情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/guildId'
        - $ref: '#/components/parameters/url'
        - name: style
          in: query
          description: 发布风格 1=>方形， 2=>圆形
          schema:
            type: number
            enum:
              - 1
              - 2
            example: 1
      responses:
        '200':
          description: ok
  /guild_expression/byGuildId:
    get:
      tags:
        - expression
      summary: 查看帮会表情
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/guildId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GuildExpressionRes'
  /club/sync_data/{name}:
    post:
      tags:
        - club
        - server
      summary: 同步俱乐部相关数据(游戏服务器调用)
      parameters:
        - name: name
          in: path
          required: true
          description: |
            | name           | description        |
            |----------------|--------------------|
            | clubList       | 俱乐部列表         |
            | clubDismiss    | 俱乐部解散         |
            | GPLSClubStat   | 公平联赛俱乐部数据 |
            | GPLSPlayerStat | 公平联赛玩家数据   |
            | YZClubStat     | 约战俱乐部数据     |
          schema:
            type: string
            enum:
              - clubList
              - clubDismiss
              - GPLSClubStat
              - GPLSPlayerStat
              - YZClubStat
            example: clubList
        - name: payload
          in: query
          description: 同步类型对应payload数据(json字符串)
          schema:
            type: string
            example: '{"clubId":"clubId"}'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat)
          schema:
            type: string
            example: f5f3fc3de42a3f45eed8575a7b8d328e
      responses:
        '200':
          description: OK
  /club/sync_data/YZClubStat:
    post:
      tags:
        - clubSync
      summary: 同步俱乐部约战统计数据
      description: |
        [比赛数据查询接口](/swagger/nsh/#/club/GetClubMatchStatistic)
      parameters:
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat)
          schema:
            type: string
            example: 215b3064b321e458235e7348040deeea
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                payload:
                  type: string
                  description: 约战数据(json字符串)
                  example: '{"clubId":1593955470969,"attend":1,"win":0,"resource":24050,"destroyBase":0,"destroyTower":0,"kill":0,"winTime":0,"loseTime":2400,"playerNum":0,"useCard":0,"shiQi":1,"die":0}'
      responses:
        '200':
          description: OK
  /club/get_extra_infos:
    get:
      tags:
        - club
      summary: 获取俱乐部额外信息 (俱乐部荣誉值， 成员列表荣誉值以及认证选手标记)
      parameters:
        - name: club_id
          in: query
          description: 俱乐部id
          schema:
            type: number
            example: 1001
        - name: role_ids
          in: query
          description: 成员玩家id列表(csv格式，逗号分开)
          schema:
            type: string
            example: 1002,1003
      responses:
        '200':
          description: OK
  /club/get_icons:
    get:
      tags:
        - club
      summary: 批量获取俱乐部图标
      parameters:
        - name: club_ids
          in: query
          description: Club Ids (csv syntax, separate by comma)
          schema:
            type: string
            example: 1001,1002
      responses:
        '200':
          description: OK
  /club/list:
    get:
      tags:
        - club
      summary: 获取俱乐部列表
      parameters:
        - name: apply_club_ids
          in: query
          description: 俱乐部列表，排序需要最前面
          schema:
            type: string
            example: 1001,1002
        - name: my_club_id
          in: query
          description: 所属俱乐部
          required: false
          schema:
            type: string
            example: '1002'
        - name: guild_id
          in: query
          description: 本帮帮会id
          required: false
          schema:
            type: string
            example: '1002'
        - $ref: '#/components/parameters/gForce'
        - name: server_id
          in: query
          description: 服务器id
          required: false
          schema:
            type: number
            example: 5080
        - name: sort_type
          in: query
          description: |
            | Sort| Value |
            |--|:---:|
            | 荣誉点倒序   | 1 |
            | 成员数量倒序 | 2 |
            | 帮会排名 | 3 |
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
            example: 1
        - name: kw
          in: query
          description: 俱乐部名字(部分匹配)
          schema:
            type: string
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /club/match_statistics:
    get:
      tags:
        - club
      summary: 俱乐部赛事统计
      operationId: GetClubMatchStatistic
      parameters:
        - $ref: '#/components/parameters/clubId'
      responses:
        '200':
          description: OK
  /club/player_match_statistics:
    get:
      tags:
        - club
      summary: 个人赛事统计
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /club/player/info:
    get:
      tags:
        - club
      summary: 查询玩家俱乐部相关信息
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
  /club/player/clean:
    post:
      tags:
        - club
        - server
      summary: 清理玩家俱乐部相关荣誉
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(nonce + roleid + time + auth_slat)
          schema:
            type: string
            example: 3f365e78a10e23efc1a82d175913731b
      responses:
        '200':
          description: OK
  /club/certified_players/list:
    get:
      tags:
        - club
      summary: 列出认证选手列表
      parameters:
        - name: join_status
          in: query
          description: player join club status
          schema:
            type: string
            enum:
              - all
              - join
              - free
            default: all
            example: all
        - name: role_type
          in: query
          description: 玩家认证角色类型   1=>所有 2=>操作手 3=>指挥官  4=>双重认证
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
              - 4
            default: 1
            example: 1
        - name: kw
          in: query
          description: search keyword
          schema:
            type: string
            example: ''
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /club/certified_players/detail:
    get:
      tags:
        - club
      summary: 获取认证选手详情
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiOk'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ClubCpDetail'
  /club/detail:
    get:
      tags:
        - club
      summary: 获取俱乐部详情
      parameters:
        - $ref: '#/components/parameters/clubId'
      responses:
        '200':
          description: OK
  /club/match/rank/list:
    get:
      tags:
        - clubMatchRank
      summary: 俱乐部比赛排行
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: matchType
          in: query
          description: |
            | name  | description |
            | ---   | ---         |
            | gpls  | 公平联赛    |
            | mxyqs | 明星邀请赛  |
            | nhzf  | 怒海争锋 |
          schema:
            type: string
            enum:
              - gpls
              - mxyqs
              - nhzf
            example: gpls
        - $ref: '#/components/parameters/whichTimes'
      responses:
        '200':
          description: OK
  /club/honors/location/swap:
    post:
      tags:
        - club
      summary: 俱乐部荣誉交换位置
      parameters:
        - $ref: '#/components/parameters/clubId'
        - $ref: '#/components/parameters/from'
        - $ref: '#/components/parameters/to'
      responses:
        '200':
          description: OK
  /club/honors/location/restore:
    post:
      tags:
        - club
      summary: 俱乐部荣誉恢复位置
      parameters:
        - $ref: '#/components/parameters/clubId'
      responses:
        '200':
          description: OK
  /open/competition/options:
    get:
      tags:
        - open
      summary: 获取赛事选项
      responses:
        '200':
          description: OK
          $ref: '#/components/responses/CompetitionOptionsRes'
  /open/competition/list:
    get:
      tags:
        - open
      summary: 获取赛事结果
      parameters:
        - $ref: '#/components/parameters/competitionId'
        - $ref: '#/components/parameters/serverType'
        - $ref: '#/components/parameters/whichTimes'
      responses:
        '200':
          description: OK
          $ref: '#/components/responses/WebCompetitionListRes'
  /club/web/auth/login:
    post:
      tags:
        - clubWeb
      summary: 俱乐部赛事中心登录
      parameters:
        - $ref: '#/components/parameters/urs'
      responses:
        '200':
          description: OK
  /club/web/roles/list:
    get:
      tags:
        - clubWeb
      summary: 列出角色列表
      parameters:
        - $ref: '#/components/parameters/urs'
        - name: neDunKey
          in: query
          description: 易盾key
          schema:
            type: string
            example: XlAvHf9ycOn1deQsnZEnrmvZWtJTu5ikEg922nL.9.EMhN7zd-y_k_MrRh7_.5mGjkBbkBbCMRv.Y-dTRVexatfr.yvIL-.AGoROhnPU_pdy6Sl-FnhOv281vU_UPiqU6pRlciH1RFjuKpnPCsEUCTJEcRJZwuwlGZSjOnw9br2RLON4pm_XfH1Kd0PRmqe.ySN8gce7QtCcvLoDzSUR9_6-w5MrAQkMl4hVBCR.iYEQHwQsYFzBRK_n6yPvepS9qNGUMdABaWnvwXPTmRV_tnkomnvZB6lTANprc1jiE_nPBo6u_e-1Kwv9m8l.Xge97F2l_Lmjzdo0o9XZo4NdW0d8.oJl_KuYOW02Gceu.ddUeg527lqkk.sQeP_A.AM-GqaZCqqBNnF-AQGliu2st_R9cj9PDQLcg.FvBRXSGArTpP_WmxZeoSlQvm9kY_IZqhl1bm.npbPcHbuFGMxt_DioTKJ.440p-_fEqw-VCfX07FqWEqe9dfoEHnp3
      responses:
        '200':
          description: OK
  /club/web/roles/bind:
    post:
      tags:
        - clubWeb
      summary: 切换urs账号绑定角色
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /club/web/loginInfo:
    get:
      tags:
        - clubWeb
      summary: 获取登录信息
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /club/web/commanders/apply/sms_code:
    post:
      tags:
        - clubWeb
      summary: 发送认证指挥的短信验证码
      parameters:
        - $ref: '#/components/parameters/phone'
        - name: neDunKey
          in: query
          description: 易盾key
          schema:
            type: string
      responses:
        '200':
          description: OK
  /club/web/commanders/apply:
    post:
      tags:
        - clubWeb
      summary: 申请认证指挥
      requestBody:
        description: 申请认证信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyCommander'
      responses:
        '200':
          description: OK
  /club/web/commanders/recommend:
    post:
      tags:
        - clubWeb
      summary: 推荐指挥人选
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
  /club/web/operators/apply:
    post:
      tags:
        - clubWeb
      summary: 申请认证操作手
      requestBody:
        description: 申请认证信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyOperator'
      responses:
        '200':
          description: OK
  /club/web/certified_players/list:
    get:
      tags:
        - clubWeb
      parameters:
        - name: role_type
          in: query
          description: 认证类型
          schema:
            type: string
            enum:
              - commander
              - operator
            default: commander
            example: commander
        - $ref: '#/components/parameters/kw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      summary: 认证玩家列表
      responses:
        '200':
          description: OK
  /club/web/certified_players/detail:
    get:
      tags:
        - clubWeb
      summary: 认证玩家详情
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /club/web/list:
    get:
      tags:
        - clubWeb
      parameters:
        - $ref: '#/components/parameters/kw'
        - $ref: '#/components/parameters/gForce'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      summary: 俱乐部列表
      responses:
        '200':
          description: OK
  /club/web/detail:
    get:
      tags:
        - clubWeb
      parameters:
        - $ref: '#/components/parameters/clubId'
      summary: 俱乐部详情页
      responses:
        '200':
          description: OK
  /club/web/match_statistics:
    get:
      tags:
        - clubWeb
      summary: 俱乐部赛事统计
      parameters:
        - $ref: '#/components/parameters/clubId'
      responses:
        '200':
          description: OK
  /club/web/player_match_statistics:
    get:
      tags:
        - clubWeb
      summary: 个人赛事统计
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /club/web/edit:
    post:
      tags:
        - clubWeb
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/clubId'
        - name: icon
          in: query
          description: 俱乐部图表
          schema:
            type: string
            example: club_icon.png
        - name: intro
          in: query
          description: 俱乐部简介
          schema:
            type: string
            example: a game club for loving game people
      summary: 编辑俱乐部信息
      responses:
        '200':
          description: OK
  /club/web/rank:
    get:
      tags:
        - clubWeb
      summary: 俱乐部排名列表
      parameters:
        - $ref: '#/components/parameters/size'
      responses:
        '200':
          description: OK
  /club/web/user/edit:
    post:
      tags:
        - clubWeb
      summary: 编辑玩家认证信息
      parameters:
        - $ref: '#/components/parameters/urs'
        - $ref: '#/components/parameters/gender'
        - $ref: '#/components/parameters/avatar'
        - $ref: '#/components/parameters/email'
        - $ref: '#/components/parameters/phone'
      responses:
        '200':
          description: OK
  /club/web/user/home:
    get:
      tags:
        - clubWeb
      summary: 玩家主页信息
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /club/web/certified_info/videos/add:
    post:
      tags:
        - clubWeb
      summary: 添加认证视频
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: applyId
          in: query
          description: 申请认证Id
          schema:
            type: number
            example: 1
        - name: url
          in: query
          description: 视频url
          schema:
            type: string
            example: http://hi-163-nsh.nosdn.127.net/dynamicVideo/2019/04/11/0F205F8211F0F1F1554947305.mp4
      responses:
        '200':
          description: OK
  /club/web/certified_info/videos/del:
    post:
      tags:
        - clubWeb
      summary: 删除认证视频
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: id
          in: query
          description: 素材ID
          schema:
            type: number
            example: 1
      responses:
        '200':
          description: OK
  /club/web/certified_info/videos/update:
    post:
      tags:
        - clubWeb
      summary: 更新认证视频
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: id
          in: query
          description: 素材ID
          schema:
            type: number
            example: 1
        - name: url
          in: query
          description: 视频url
          schema:
            type: string
            example: http://hi-163-nsh.nosdn.127.net/dynamicVideo/2019/04/11/0F205F8211F0F1F1554947305.mp4
      responses:
        '200':
          description: OK
  /club/web/competition/list:
    get:
      tags:
        - clubWeb
      summary: 获取赛事列表
      parameters:
        - name: type
          description: 比赛类型
          in: query
          required: true
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
        - name: subType
          in: query
          description: 诸神之战类型
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
        - name: group
          in: query
          description: 组别
          schema:
            type: number
            enum:
              - 1
              - 2
        - name: year
          description: 年份
          in: query
          schema:
            type: number
        - name: whichTimes
          description: 届数
          in: query
          schema:
            type: number
      responses:
        '200':
          description: OK
  /club/web/common/get_nos_token/{type}/{extName}:
    get:
      tags:
        - clubWeb
      summary: 获取nosToken上传凭证
      parameters:
        - name: type
          in: path
          required: true
          description: 上传对象子类型
          schema:
            type: string
            example: upload
        - name: extName
          in: path
          required: true
          description: 扩展名
          schema:
            type: string
            example: mp4
      responses:
        '200':
          description: OK
  /club/admin/loginInfo:
    get:
      tags:
        - clubAdmin
      summary: 获取登录信息
      description: 跳转地址 https://ssl.hi.163.com/file_mg/public/share/common_auth/corpauth/login
      responses:
        '200':
          description: OK
  /club/admin/logout:
    get:
      tags:
        - clubAdmin
      summary: 登出
      responses:
        '200':
          description: OK
  /club/admin/videos/list:
    get:
      tags:
        - clubAdmin
      summary: 列出视频列表
      parameters:
        - $ref: '#/components/parameters/auditStatus'
        - $ref: '#/components/parameters/kw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /club/admin/videos/audit:
    post:
      tags:
        - clubAdmin
      summary: 审核视频
      parameters:
        - $ref: '#/components/parameters/assertId'
        - $ref: '#/components/parameters/auditStatus'
      responses:
        '200':
          description: OK
  /club/admin/videos/audit_batch:
    post:
      tags:
        - clubAdmin
      summary: 批量审核视频
      parameters:
        - $ref: '#/components/parameters/assertIds'
        - $ref: '#/components/parameters/auditStatus'
        - $ref: '#/components/parameters/reason'
      responses:
        '200':
          description: OK
  /club/admin/images/list:
    get:
      tags:
        - clubAdmin
      summary: 列出图片列表
      parameters:
        - $ref: '#/components/parameters/auditStatus'
        - $ref: '#/components/parameters/kw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /club/admin/images/audit:
    post:
      tags:
        - clubAdmin
      summary: 审核图片
      parameters:
        - $ref: '#/components/parameters/assertId'
        - $ref: '#/components/parameters/auditStatus'
      responses:
        '200':
          description: OK
  /club/admin/images/audit_batch:
    post:
      tags:
        - clubAdmin
      summary: 批量审核图片
      parameters:
        - $ref: '#/components/parameters/assertIds'
        - $ref: '#/components/parameters/auditStatus'
        - $ref: '#/components/parameters/reason'
      responses:
        '200':
          description: OK
  /club/admin/certified_info/list:
    get:
      tags:
        - clubAdmin
      summary: 列出认证信息列表
      parameters:
        - $ref: '#/components/parameters/auditStatus'
        - name: applyType
          description: 申请类型 | 类型 |  值 | | --- |  -- | | 指挥官|  1 | | 高手 |  2 |
          in: query
          schema:
            type: number
            enum:
              - 1
              - 2
            default: 1
            example: 1
        - $ref: '#/components/parameters/reason'
        - $ref: '#/components/parameters/kw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /club/admin/certified_info/audit:
    post:
      tags:
        - clubAdmin
      summary: 审核认证信息
      parameters:
        - $ref: '#/components/parameters/certifiedInfoId'
        - $ref: '#/components/parameters/auditStatus'
        - $ref: '#/components/parameters/reason'
        - $ref: '#/components/parameters/reasonMsg'
      responses:
        '200':
          description: OK
  /club/admin/certified_info/audit_batch:
    post:
      tags:
        - clubAdmin
      summary: 批量审核认证信息
      parameters:
        - $ref: '#/components/parameters/certifiedInfoIds'
        - $ref: '#/components/parameters/auditStatus'
        - $ref: '#/components/parameters/reason'
        - $ref: '#/components/parameters/reasonMsg'
      responses:
        '200':
          description: OK
  /club/admin/player/tag/list:
    get:
      tags:
        - clubAdmin
      summary: 列出玩家标签列表
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/rares'
        - $ref: '#/components/parameters/kw'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /club/admin/player/tag/add_batch:
    post:
      tags:
        - clubAdmin
      summary: 批量添加标签
      parameters:
        - $ref: '#/components/parameters/roleIds'
        - $ref: '#/components/parameters/rare'
        - $ref: '#/components/parameters/tag'
      responses:
        '200':
          description: OK
  /club/admin/player/tag/update:
    post:
      tags:
        - clubAdmin
      summary: 修改玩家标签列表
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/rare'
        - $ref: '#/components/parameters/tags'
      responses:
        '200':
          description: OK
  /club/ue/stat:
    get:
      tags:
        - clubUE
      summary: 获取俱乐部统计信息
      parameters:
        - name: great_than
          in: query
          description: 成员数量大于的数值列表(CSV格式)
          schema:
            type: string
            example: 50,100,150
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(great_than + nonce + time + auth_slat)
          schema:
            type: string
            example: 34204cb340b5ea5fbe5dbbdd100739bc
      responses:
        '200':
          description: OK
  /club/competition/admin/loginInfo:
    get:
      tags:
        - competitionAdmin
      summary: 获取登录信息
      description: 跳转地址 https://ssl.hi.163.com/file_mg/public/share/common_auth/corpauth/login
      responses:
        '200':
          description: OK
  /club/competition/admin/logout:
    get:
      tags:
        - competitionAdmin
      summary: 登出
      responses:
        '200':
          description: OK
  /club/competition/admin/add:
    post:
      summary: 添加战绩
      tags:
        - competitionAdmin
      description: |
        ```
        赛事类型及其数据范围定义
        https://git-wz.nie.netease.com/hi-api/nsh_md_api/blob/develop/src/models/Competition.ts

        接口参数定义 -namespace CompetitionReq
        https://git-wz.nie.netease.com/hi-api/nsh_md_api/blob/develop/src/type/req.d.ts
        ```
      requestBody:
        content:
          application/json:
            schema:
              type: object
            examples:
              天下第一:
                value: |
                  {
                      "type": 1,
                      "year": 2020,
                      "whichTimes": 1,
                      "group": 1,
                      "info": [
                          {
                              "rank": 1,
                              "server": "服务器名称",
                              "teamName": "队伍名",
                              "roleList": [
                                  {
                                      "roleName": "角色名",
                                      "career": 1,
                                      "role": 1
                                  }
                              ]
                          }
                      ]
                  }
              剑试苍穹:
                value: |
                  {
                      "type": 2,
                      "year": 2020,
                      "whichTimes": 1,
                      "info": [
                          {
                              "rank": 1,
                              "server": "服务器名称",
                              "teamName": "队伍名",
                              "roleList": [
                                  {
                                      "roleName": "角色名",
                                      "career": 1,
                                      "role": 1
                                  }
                              ]
                          }
                      ]
                  }
              诸神之战:
                value: |
                  {
                    "type":3,
                    "year": 2020,
                    "whichTimes": 1,
                    "subType":2,
                    "group":3,
                    "info":[
                      {
                        "rank":1,
                        "server":"服务器名",
                        "gang":"帮会名",
                        "gangLeaderName":"帮主"
                      }
                    ]
                  }
      responses:
        '200':
          description: OK
  /club/competition/admin/edit:
    post:
      summary: 修改战绩
      tags:
        - competitionAdmin
      requestBody:
        content:
          application/json:
            schema:
              type: object
            examples:
              天下第一:
                value: |
                  {
                      "id":1,
                      "type": 1,
                      "year": 2020,
                      "whichTimes": 1,
                      "group": 1,
                      "info": [
                          {
                              "rank": 1,
                              "server": "服务器名称",
                              "teamName": "队伍名",
                              "roleList": [
                                  {
                                      "roleName": "角色名",
                                      "career": 1,
                                      "role": 1
                                  }
                              ]
                          }
                      ]
                  }
              剑试苍穹:
                value: |
                  {
                      "id":2,
                      "type": 2,
                      "year": 2020,
                      "whichTimes": 1,
                      "info": [
                          {
                              "rank": 1,
                              "server": "服务器名称",
                              "teamName": "队伍名",
                              "roleList": [
                                  {
                                      "roleName": "角色名",
                                      "career": 1,
                                      "role": 1
                                  }
                              ]
                          }
                      ]
                  }
              诸神之战:
                value: |
                  {
                    "id":3,
                    "type":3,
                    "year": 2020,
                    "whichTimes": 1,
                    "subType":2,
                    "group":3,
                    "info":[
                      {
                        "rank":1,
                        "server":"服务器名",
                        "gang":"帮会名",
                        "gangLeaderName":"帮主"
                      }
                    ]
                  }
      responses:
        '200':
          description: OK
  /club/competition/admin/del:
    post:
      summary: 删除战绩
      tags:
        - competitionAdmin
      parameters:
        - name: id
          description: 战绩id
          in: query
          required: true
          schema:
            type: number
      responses:
        '200':
          description: OK
  /club/competition/admin/list:
    get:
      summary: 战绩列表
      tags:
        - competitionAdmin
      parameters:
        - name: type
          description: 比赛类型
          in: query
          required: true
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
        - name: year
          description: 年份
          in: query
          schema:
            type: number
        - name: whichTimes
          in: query
          description: 届数
          schema:
            type: number
        - name: subType
          in: query
          description: 诸神之战类型
          schema:
            type: number
            enum:
              - 1
              - 2
        - name: group
          in: query
          description: 组别
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
              - 4
              - 5
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /getinforms:
    get:
      tags:
        - inform
      summary: 获取消息列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /informs/clean_all:
    post:
      tags:
        - inform
      summary: 清空所有消息
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /informs/red_dot:
    get:
      tags:
        - inform
      summary: 消息红点，是否有新消息(是否有好友新动态， 是否新便利贴消息etc)
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccRes'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/RedDotRes'
  /comment/list:
    get:
      tags:
        - comment
      summary: 评论列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - name: orderBy
          description: 排序方式(点赞数或者混合排序)
          in: query
          schema:
            type: string
            enum:
              - likes
              - mix
            example: likes
      responses:
        '200':
          description: OK
  /addcomment:
    post:
      tags:
        - comment
      summary: 添加评论或回复
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/text'
        - $ref: '#/components/parameters/replyid'
        - $ref: '#/components/parameters/hotFactor'
      responses:
        '200':
          description: OK
  /delcomment:
    post:
      tags:
        - comment
      summary: 删除评论
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/id_as_comment'
        - $ref: '#/components/parameters/hotFactor'
      responses:
        '200':
          description: OK
  /likecomment:
    post:
      tags:
        - like
      summary: 点赞评论
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/id_as_comment'
        - $ref: '#/components/parameters/likeAction'
      responses:
        '200':
          description: OK
  /gethotmoments:
    get:
      tags:
        - hotMoment
      summary: 获取本服热门心情列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/serverid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /getallhotmoments:
    get:
      tags:
        - hotMoment
      summary: 获取全服热门心情列表
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /getmoments:
    get:
      summary: 获取用户朋友圈或者指定用户的朋友圈
      tags:
        - moment
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /moment/picked_list:
    get:
      summary: 获取编辑精选的朋友圈列表
      tags:
        - moment
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /getmomentbyid:
    get:
      tags:
        - moment
      summary: 获取单条动态
      operationId: getMomentById
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
      responses:
        '200':
          description: OK
  /moment/forward:
    post:
      tags:
        - moment
      summary: 转发动态
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/moment_id'
        - $ref: '#/components/parameters/text'
        - $ref: '#/components/parameters/hotFactor'
      responses:
        '200':
          description: OK
  /getmessages:
    get:
      summary: 留言列表
      tags:
        - message
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /addmessage:
    post:
      summary: 添加留言
      tags:
        - message
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
        - $ref: '#/components/parameters/replyid'
        - $ref: '#/components/parameters/text'
      responses:
        '200':
          description: OK
  /delmessage:
    post:
      summary: 删除留言
      tags:
        - message
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/id_as_message'
      responses:
        '200':
          description: OK
  /getprofile:
    get:
      tags:
        - player
      security:
        - loginSkey: []
      summary: 获取玩家详情
      operationId: GetPlayerProfile
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          $ref: '#/components/responses/PlayerGetProfileRes'
  /players/job_avatars:
    get:
      tags:
        - player
      summary: 获取职业头像
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    required:
                      - jobs
                      - gender
                      - jobAvatar
                    properties:
                      jobs:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: number
                              example: 1
                            name:
                              type: string
                              example: 碎梦
                      gender:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: number
                              example: 0
                            name:
                              type: string
                              example: male
                      jobAvatar:
                        type: array
                        items:
                          type: object
                          properties:
                            jobId:
                              type: number
                              example: 1
                            gender:
                              type: number
                              example: 0
                            url:
                              type: string
                              example: http://hi-163-nsh.nosdn.127.net/asserts/job_avatar/suimeng_0.png
                    type: object
  /setprivacy:
    post:
      tags:
        - player
      summary: 更新玩家隐私设置
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: hideLocation
          in: query
          description: 隐藏位置
          schema:
            type: string
            enum:
              - 'false'
              - 'true'
            example: 'false'
        - name: hideVoiceGender
          in: query
          description: 隐藏语音性别
          schema:
            type: string
            enum:
              - 'false'
              - 'true'
            example: 'false'
        - name: limitComment
          in: query
          description: 限制评论，只允许好友评论
          schema:
            type: string
            enum:
              - 'false'
              - 'true'
            example: 'false'
        - name: muteNewMoment
          in: query
          description: 禁止新动态提醒
          schema:
            type: string
            enum:
              - 'false'
              - 'true'
            example: 'false'
        - name: limitCrossFriend
          in: query
          description: 限制跨服交友
          schema:
            type: string
            enum:
              - 'false'
              - 'true'
            example: 'false'
        - name: hideWishList
          in: query
          description: 隐藏心愿单
          schema:
            type: string
            enum:
              - 'false'
              - 'true'
            example: 'false'
      responses:
        '200':
          description: OK
  /player/avatar/update:
    post:
      tags:
        - player
      summary: 上传用户头像
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/url'
      responses:
        '200':
          description: OK
  /player/avatar/delete:
    post:
      tags:
        - player
      summary: 删除用户头像(恢复职业默认头像)
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /recent_visitors:
    get:
      tags:
        - player
      summary: 最近访客
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /server_competition/sync_result:
    post:
      tags:
        - server
      summary: 同步赛事结果数据(有队员)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                time:
                  type: number
                  example: 1716144000
                token:
                  type: string
                  description: token计算方式为 md5(time + auth_slat)
                  example: 37ad3c65836e4154f22f890b57fe82f2
                data:
                  type: object
                  properties:
                    serverType:
                      type: number
                      description: 服务器类型 1 正式服 2赛季服 3黄金服
                      example: 1
                    competitionId:
                      type: number
                      description: 赛事id 1天下第一 3皇城之巅 4剑试苍穹
                      example: 1
                    whichTimes:
                      type: number
                      description: 第几届
                      example: 1
                    result:
                      type: array
                      items:
                        type: object
                        properties:
                          rank:
                            type: number
                            description: 排名
                            example: 1
                          server:
                            type: number
                            description: 服务器id
                            example: 1
                          serverName:
                            type: string
                            description: 服务器名称
                            example: 烟雨江南
                          teamName:
                            type: string
                            description: 队伍名称
                            example: 队伍名称
                          roleList:
                            type: array
                            items:
                              type: object
                              properties:
                                roleId:
                                  type: number
                                  description: 角色id
                                  example: 1
                                account:
                                  type: string
                                  description: 账号(urs账号,可能用于大神跳转主页)
                                  example: 账号
                                roleName:
                                  type: string
                                  description: 角色名称
                                  example: 角色名称
                                career:
                                  type: number
                                  description: 职业
                                  example: 1
                                gender:
                                  type: number
                                  description: 性别
                                  example: 1
                                roleType:
                                  type: number
                                  description: 角色类型 1队长 2队员 3替补 4军师
                                  enum:
                                    - 1
                                    - 2
                                    - 3
                                    - 4
                                  example: 1
      responses:
        '200':
          description: OK
  /server_competition/sync_table:
    post:
      tags:
        - server
      summary: 同步赛事结果数据(表格,无队员)
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                time:
                  type: number
                  example: 1716144000
                token:
                  type: string
                  description: token计算方式为 md5(time + auth_slat)
                  example: 37ad3c65836e4154f22f890b57fe82f2
                data:
                  type: object
                  properties:
                    serverType:
                      type: number
                      description: 服务器类型 1 正式服 2赛季服 3黄金服
                      example: 1
                    competitionId:
                      type: number
                      description: 赛事id  2诸神之战 5跨服联赛
                      example: 1
                    whichTimes:
                      type: number
                      description: 第几届
                      example: 1
                    table:
                      type: array
                      items:
                        type: object
                        properties:
                          rank:
                            type: number
                            description: 排名
                            example: 1
                          server:
                            type: number
                            description: 服务器id
                            example: 1
                          serverName:
                            type: string
                            description: 服务器名称
                            example: 烟雨江南
                          guildName:
                            type: string
                            description: 帮会名称
                            example: 帮会名称
                          guildMaster:
                            type: string
                            description: 帮主名称
                            example: 帮主名称
                          account:
                            type: string
                            description: 账号(urs账号,可能用于大神跳转主页)
                            example: 账号
                          roleId:
                            type: number
                            description: 角色id
                            example: 1
      responses:
        '200':
          description: OK
  /server/transfer/add:
    post:
      tags:
        - server
        - transfer
      summary: 添加转服记录
      parameters:
        - name: oldId
          description: 转服前的ID
          in: query
          schema:
            type: number
            example: ***********
        - name: newId
          in: query
          description: 转服后的ID
          schema:
            type: number
            example: ***********
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(newId + nonce + oldId + time + auth_slat)
          schema:
            type: string
            example: 37ad3c65836e4154f22f890b57fe82f2
      responses:
        '200':
          description: OK
  /transfer/sync_data/{name}:
    post:
      tags:
        - transfer
      summary: 转服信息同步
      parameters:
        - name: name
          in: path
          required: true
          description: |
            | name           | description        |
            |----------------|--------------------|
            | signUpSync       | 报名信息         |
            | signUpCancel     | 取消报名         |
          schema:
            type: string
            enum:
              - signUpSync
              - signUpCancel
        - name: payload
          in: query
          description: 同步类型对应payload数据
          schema:
            type: string
            example: '{"roleId":1,"signUpServerId":1,"score":1}'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat)
          schema:
            type: string
            example: 1503dabf23ed1c85206c367b74cabc31
      responses:
        '200':
          description: OK
  /transfer/get_signup_num:
    get:
      tags:
        - transfer
      summary: 获取各个服务器报名数量
      parameters:
        - name: serverIds
          in: query
          description: 服务器列表,多个用英文逗号隔开
          schema:
            type: string
            example: 1,2
      responses:
        '200':
          description: OK
  /transfer/get_signup_list:
    get:
      tags:
        - transfer
      summary: 获取某个服务器报名列表
      parameters:
        - $ref: '#/components/parameters/roleId'
        - name: score
          in: query
          description: 积分
          required: true
          schema:
            type: number
        - $ref: '#/components/parameters/serverId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /server/moment/add:
    post:
      tags:
        - moment
        - server
      summary: 服务器添加动态
      description: ''
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/text'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(nonce + roleid + text + time + auth_slat)
          schema:
            type: string
            example: ebd8d71cbacf7e3fb16a2b3424ee2d5b
      responses:
        '200':
          description: OK
  /follow/getRecommendedList:
    get:
      tags:
        - follow
      summary: 获取推荐关注列表
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
  /follow/add:
    post:
      tags:
        - follow
      summary: 添加关注
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
  /follow/cancel:
    post:
      tags:
        - follow
      summary: 取消关注
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
  /follow/friends:
    post:
      tags:
        - follow
      summary: 一键同步跨服好友以及官方号
      description: ⚠️  该接口会删除不在好友列表里的关注
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: friend_ids
          description: 好友id(CSV格式)
          in: query
          schema:
            type: string
        - name: include_official
          in: query
          schema:
            type: string
            enum:
              - 'true'
              - 'false'
            example: 'true'
      responses:
        '200':
          description: OK
  /follow_list:
    post:
      tags:
        - follow
      summary: 关注列表
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /fan_list:
    post:
      tags:
        - follow
      summary: 粉丝列表
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          description: OK
  /addmoment:
    post:
      tags:
        - moment
      summary: 添加动态
      operationId: addMoment
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/text'
        - name: imgs
          in: query
          description: 图片url列表， csv格式，逗号分开
          schema:
            type: string
            example: http://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d
        - name: videos
          in: query
          description: 视频url列表， csv格式，逗号分开
          schema:
            type: string
        - name: videoImgs
          in: query
          description: 视频封面url列表， csv格式，逗号分开
          schema:
            type: string
        - name: topicId
          in: query
          description: 话题id
          schema:
            type: number
        - $ref: '#/components/parameters/context'
        - name: visualRange
          description: 可见范围 1-所有人可见 2-好友可见 3-自己可见
          in: query
          schema:
            type: number
            enum:
              - 1
              - 2
              - 3
        - $ref: '#/components/parameters/canCommentByAll'
        - $ref: '#/components/parameters/canForward'
        - $ref: '#/components/parameters/canCommentByStranger'
        - $ref: '#/components/parameters/hotFactor'
      responses:
        '200':
          description: OK
  /delmoment:
    description: ''
    post:
      tags:
        - moment
      summary: 删除动态
      operationId: delMoment
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/momentid'
        - $ref: '#/components/parameters/hotFactor'
      responses:
        '200':
          description: OK
  /topic/list:
    get:
      tags:
        - topic
      summary: 活动话题列表
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccRes'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Topic'
  /get_honors:
    get:
      tags:
        - honor
      summary: 获取荣誉墙荣誉
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    $ref: '#/components/schemas/GetHonorList'
  /get_honor_num:
    get:
      tags:
        - honor
      summary: 获取荣誉墙荣誉数量
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          description: OK
  /activity/friend_ids:
    get:
      summary: 查看玩家好友id列表
      tags:
        - activity
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/sameServer'
      responses:
        '200':
          $ref: '#/components/responses/ActivityFriendIdsRes'
  /activity/get_couple_info:
    get:
      summary: 获取玩家侠侣信息
      tags:
        - activity
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          $ref: '#/components/responses/GetCoupleInfoRes'
  /activity/add_moment:
    post:
      summary: 活动分享到梦岛
      description: |
        话题link特殊格式
        text1<link action={Color="ff8c00",Text="＃我要上花火节＃",Name="OnDoCircleOfFriendsTopicAction",Params={TopicId=39}}>text2
      tags:
        - activity
      requestBody:
        description: add moment body
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleId:
                  type: number
                  description: 玩家id
                  example: 100100001
                text:
                  type: string
                  description: 心情文本
                  example: test to add
                imgs:
                  type: array
                  description: 分享图片列表
                  items:
                    type: string
                    example: http://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d
                skipImgAudit:
                  description: 是否跳过图片审核
                  type: number
                  enum:
                    - 0
                    - 1
                videos:
                  type: array
                  description: 分享视频列表
                  items:
                    type: string
                    example: http://hi-163-qnm.nosdn.127.net/upload/201905/13/deaab8a0754e11e9bbd4a9db24757383.mp4
                topicId:
                  type: number
                  description: 话题id
                  example: 39
                skipVideoAudit:
                  description: 是否跳过视频审核
                  type: number
                  enum:
                    - 0
                    - 1
      responses:
        '200':
          description: OK
  /gm/ping:
    get:
      security:
        - gmAuth: []
      tags:
        - gm
      responses:
        '200':
          description: OK
  /gm/club/honor_excel/{action}:
    post:
      security:
        - gmAuth: []
      tags:
        - clubGM
      summary: 更新俱乐部和玩家荣誉
      parameters:
        - name: action
          description: 操作
          required: true
          in: path
          schema:
            type: string
            enum:
              - parse
              - calHonor
              - update
            example: parse
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                honorExcel:
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
  /gm/club/match_table/{action}:
    post:
      security:
        - gmAuth: []
      tags:
        - clubGM
      summary: 更新俱乐部比赛定义表
      parameters:
        - name: action
          description: 操作
          in: path
          required: true
          schema:
            type: string
            enum:
              - parse
              - update
            example: parse
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                matchExcel:
                  type: string
                  format: binary
      responses:
        '200':
          description: OK
  /gm/club/clean_honors:
    post:
      security:
        - gmAuth: []
      tags:
        - clubGM
      summary: 清理俱乐部荣誉
      responses:
        '200':
          description: OK
  /gm/identity/add_admin:
    post:
      security:
        - gmAuth: []
      tags:
        - gm
      summary: 添加标签认证后台管理员
      parameters:
        - name: urs
          description: 邮箱账号
          in: query
          schema:
            type: string
            example: <EMAIL>
      responses:
        '200':
          description: OK
  /gm/hot_moments/refresh:
    post:
      tags:
        - gm
      security:
        - gmAuth: []
      summary: 刷新朋友圈热门动态
      parameters:
        - name: serverId
          description: 服务器ID
          in: query
          schema:
            type: string
            example: 2
      responses:
        '200':
          description: OK
  /gm/copy_moments/clean:
    post:
      tags:
        - gm
      security:
        - gmAuth: []
      summary: 清除拷贝动态的记录
      parameters:
        - $ref: '#/components/parameters/roleid'
        - name: copyRoleId
          description: 旧号RoleId
          in: query
          schema:
            type: string
            example: ***********
      responses:
        '200':
          description: OK
  /gm/moment/get_hot:
    get:
      tags:
        - gm
      security:
        - gmAuth: []
      summary: 获取动态的热度
      parameters:
        - $ref: '#/components/parameters/momentid'
      responses:
        '200':
          description: OK
  /gm/player/set_couple_info:
    post:
      tags:
        - gm
      security:
        - gmAuth: []
      summary: 设置侠侣信息
      parameters:
        - $ref: '#/components/parameters/roleId'
        - $ref: '#/components/parameters/coupleId'
        - $ref: '#/components/parameters/coupleName'
      responses:
        '200':
          description: OK
  /gm/player/get_couple_info:
    get:
      tags:
        - gm
      summary: 获取玩家侠侣信息
      security:
        - gmAuth: []
      parameters:
        - $ref: '#/components/parameters/roleId'
      responses:
        '200':
          $ref: '#/components/responses/GetCoupleInfoRes'
  /gm/club/clean_by_server:
    get:
      tags:
        - gm_club
      summary: 根据服务器id批量清理俱乐部相关数据
      security:
        - gmAuth: []
      parameters:
        - $ref: '#/components/parameters/serverId'
      responses:
        '200':
          $ref: '#/components/responses/ApiOkRes'
  /wishlist/wishes/remove:
    post:
      tags:
        - wishlist
      summary: 删除一个心愿
      deprecated: true
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/wishId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /wishlist/wishes/remove_all:
    post:
      tags:
        - wishlist
      summary: 删除全部心愿
      deprecated: true
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /wishlist/add:
    post:
      tags:
        - wishlist
      summary: 添加商品到心愿单
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/commodityId'
        - $ref: '#/components/parameters/itemId'
        - $ref: '#/components/parameters/shopId'
        - $ref: '#/components/parameters/commodityIndex'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          description: OK
  /wishlist/add_by_fashion:
    post:
      tags:
        - wishlist
      summary: 添加时装到心愿单
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/fashionId'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          description: OK
  /wishlist/remove:
    post:
      tags:
        - wishlist
      summary: 从心愿单移除商品
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/commodityId'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          description: OK
  /wishlist/remove_by_fashion:
    post:
      tags:
        - wishlist
      summary: 从心愿单移除时装
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/fashionId'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          description: OK
  /wishlist/notify_buy_action:
    post:
      tags:
        - wishlist
      summary: 通知购买行为, 检查商品ID并处理心愿单
      parameters:
        - $ref: '#/components/parameters/fashionId'
        - name: buyerId
          in: query
          description: 购买者玩家Id
          schema:
            type: number
            example: 1
        - name: ownerId
          in: query
          description: 获得着玩家Id
          schema:
            type: number
            example: 1
        - name: buyTime
          in: query
          description: 购买时间戳(单位毫秒)
          schema:
            type: number
            example: 1599461623001
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          description: OK
  /wishlist/notify_fashion_get_action:
    post:
      tags:
        - wishlist
      summary: 通知获取时装事件，检查时装id并处理心愿单
      parameters:
        - $ref: '#/components/parameters/fashionId'
        - name: buyerId
          in: query
          description: 购买者玩家Id
          schema:
            type: number
            example: 1
        - name: ownerId
          in: query
          description: 获得着玩家Id
          schema:
            type: number
            example: 1
        - name: buyTime
          in: query
          description: 购买时间戳(单位毫秒)
          schema:
            type: number
            example: 1599461623001
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - $ref: '#/components/parameters/authToken'
      responses:
        '200':
          description: OK
  /wishlist/show:
    get:
      tags:
        - wishlist
      summary: 查看玩家心愿单
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/targetid'
      responses:
        '200':
          $ref: '#/components/responses/ShowWishList'
  /wishlist/full_filled:
    get:
      tags:
        - wishlist_full_fill
      summary: 列出已经实现的心愿
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/FullFillWishList'
  /wishlist/full_filled/remove:
    post:
      tags:
        - wishlist_full_fill
      summary: 删除一条心愿实现记录
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
        - $ref: '#/components/parameters/wishId'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /wishlist/full_filled/remove_all:
    post:
      tags:
        - wishlist_full_fill
      summary: 删除全部心愿实现记录
      security:
        - loginSkey: []
      parameters:
        - $ref: '#/components/parameters/roleid'
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /expansion_trial_scene/set_reward:
    post:
      tags:
        - expansion_trial_scene
      summary: 服务器设置奖励
      security:
        - loginSkey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - urs
                - reward
              properties:
                urs:
                  type: string
                  description: 通行证账号，用英文逗号隔开，固定前面是邮箱后面是手机号
                  example: <EMAIL>,18888888888
                reward:
                  type: string
                  description: 奖励内容
                  example: '[0,0,0]'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - status
                    properties:
                      status:
                        type: number
                        description: 状态 -1失败 1新增成功 2覆盖
                        example: 1
  /expansion_trial_scene/get_reward:
    post:
      tags:
        - expansion_trial_scene
      summary: 服务器领取奖励
      security:
        - loginSkey: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - urs
              properties:
                urs:
                  type: string
                  description: 通行证账号，用英文逗号隔开，固定前面是邮箱后面是手机号
                  example: <EMAIL>,18888888888
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - status
                    properties:
                      status:
                        type: number
                        description: 状态 1成功 -1已领过 -2奖励不存在
                        example: 1
                      reward:
                        type: string
                        description: 奖励内容
                        example: '[0,0,0]'
  /expansion_trial_scene/query_reward:
    get:
      tags:
        - expansion_trial_scene
      summary: 客户端查询奖励
      security:
        - loginSkey: []
      parameters:
        - name: urs
          in: query
          required: true
          description: 通行证账号，用英文逗号隔开，固定前面是邮箱后面是手机号
          schema:
            type: string
            example: <EMAIL>,18888888888
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                required:
                  - code
                  - data
                properties:
                  code:
                    type: number
                    example: 0
                  data:
                    type: object
                    required:
                      - status
                    properties:
                      status:
                        type: number
                        description: 状态 0未发 1已发 -2不存在
                        example: 1
                      reward:
                        type: string
                        description: 奖励内容
                        example: '[0,0,0]'
  /activity/take_photo/add_photo:
    post:
      tags:
        - activity_take_photo
      summary: 上传照片
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - locationId
                - imgUrl
              properties:
                roleId:
                  type: number
                  description: 玩家id
                  example: ***********
                locationId:
                  type: number
                  description: 位置ID
                  example: 1
                imgUrl:
                  type: string
                  description: 图片地址
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /activity/take_photo/get_my_photos:
    get:
      tags:
        - activity_take_photo
      summary: 查询自己的照片
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - name: locationId
          in: query
          required: false
          schema:
            type: number
            example: 1
      responses:
        '200':
          $ref: '#/components/responses/ActivityTakePhotoGetMyPhotosRes'
  /activity/take_photo/select_photo:
    post:
      tags:
        - activity_take_photo
      summary: 选中照片
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - locationId
                - imgId
              properties:
                roleId:
                  type: number
                  description: 玩家id
                  example: ***********
                locationId:
                  type: number
                  description: 位置ID
                  example: 1
                imgId:
                  type: number
                  description: 图片ID
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /activity/take_photo/lock_photo:
    post:
      tags:
        - activity_take_photo
      summary: 锁定照片
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - locationId
                - imgId
              properties:
                roleId:
                  type: number
                  description: 玩家id
                  example: ***********
                locationId:
                  type: number
                  description: 位置ID
                  example: 1
                imgId:
                  type: number
                  description: 图片ID
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /activity/take_photo/unlock_photo:
    post:
      tags:
        - activity_take_photo
      summary: 解锁照片
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - locationId
                - imgId
              properties:
                roleId:
                  type: number
                  description: 玩家id
                  example: ***********
                locationId:
                  type: number
                  description: 位置ID
                  example: 1
                imgId:
                  type: number
                  description: 图片ID
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /activity/take_photo/get_location_selected_photo:
    get:
      tags:
        - activity_take_photo
      summary: 查玩家某地点选中的照片
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - $ref: '#/components/parameters/targetId'
        - name: locationId
          in: query
          required: true
          schema:
            type: number
            example: 1
      responses:
        '200':
          $ref: '#/components/responses/ActivityTakePhotoGetLocationSelectedPhotoRes'
  /garden/sync_data/{name}:
    post:
      tags:
        - garden
      summary: 同步庄园数据
      parameters:
        - name: name
          in: path
          required: true
          description: |
            | name           | description        |
            |----------------|--------------------|
            | info       | 庄园信息         |
          schema:
            type: string
            enum:
              - info
            example: info
        - name: payload
          in: query
          description: 同步类型对应payload数据(json字符串)
          schema:
            type: string
            example: '{"GardenOwnerId":12312,"GardenGrade":0,"Score":0,"Popularity":0,"GardenId":"gardenId-test","FengShui":0,"ShiYong":0,"MeiGuan":0}'
        - $ref: '#/components/parameters/time'
        - $ref: '#/components/parameters/nonce'
        - name: token
          in: query
          description: token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat)
          schema:
            type: string
            example: f5f3fc3de42a3f45eed8575a7b8d328e
      responses:
        '200':
          description: OK
  /garden/detail:
    get:
      tags:
        - garden
      summary: 庄园详情
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - $ref: '#/components/parameters/ownerId'
      responses:
        '200':
          $ref: '#/components/responses/GardenDetailRes'
  /garden/rank:
    get:
      tags:
        - garden
      summary: 庄园排行
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GardenRankRes'
  /garden/evaluation_add:
    post:
      tags:
        - garden
      summary: 庄园评价
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - ownerId
                - score
                - text
              properties:
                roleId:
                  type: number
                  description: 角色id
                ownerId:
                  type: number
                  description: 庄园主角色id
                score:
                  type: number
                  description: 评分
                text:
                  type: string
                  description: 内容
                imgList:
                  type: array
                  description: 图片
                  items:
                    type: string
      responses:
        '200':
          $ref: '#/components/responses/idRes'
  /garden/evaluation_del:
    post:
      tags:
        - garden
      summary: 庄园删除评价
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - evaluationId
              properties:
                roleId:
                  type: number
                  description: 角色id
                evaluationId:
                  type: number
                  description: 评价id
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /garden/evaluation_like:
    post:
      tags:
        - garden
      summary: 评价点赞
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - evaluationId
              properties:
                roleId:
                  type: number
                  description: 角色id
                evaluationId:
                  type: number
                  description: 评价id
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /garden/evaluation_cancel_like:
    post:
      tags:
        - garden
      summary: 评价取消点赞
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - evaluationId
              properties:
                roleId:
                  type: number
                  description: 角色id
                evaluationId:
                  type: number
                  description: 评价id
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /garden/evaluation_list:
    get:
      tags:
        - garden
      summary: 庄园评价列表
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - $ref: '#/components/parameters/ownerId'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GardenEvalutionListRes'
  /garden/evaluation/comment:
    post:
      tags:
        - garden
      summary: 评价评论
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - evaluationId
                - text
              properties:
                roleId:
                  type: number
                  description: 角色id
                evaluationId:
                  type: number
                  description: 评价id
                replyCommentId:
                  type: number
                  description: 回复的评论id，回复评论时传递
                text:
                  type: string
                  description: 评论内容
      responses:
        '200':
          $ref: '#/components/responses/idRes'
  /garden/evaluation/comment_del:
    post:
      tags:
        - garden
      summary: 评价评论删除
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - commentId
              properties:
                roleId:
                  type: number
                  description: 角色id
                commentId:
                  type: number
                  description: 评论id
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /garden/evaluation/comment_list:
    get:
      tags:
        - garden
      summary: 评论列表-一级评论
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - name: evaluationId
          in: query
          required: true
          description: 评价id
          schema:
            type: number
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GardenEvaluationCommentListRes'
  /garden/evaluation/comment_sub_list:
    get:
      tags:
        - garden
      summary: 评论列表-二级评论
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - name: commentId
          in: query
          required: true
          description: 一级评论id
          schema:
            type: number
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GardenEvaluationCommentListRes'
  /garden/evaluation/comment_like:
    post:
      tags:
        - garden
      summary: 评论点赞
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - commentId
              properties:
                roleId:
                  type: number
                  description: 角色id
                commentId:
                  type: number
                  description: 评论id
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /garden/evaluation/comment_cancel_like:
    post:
      tags:
        - garden
      summary: 评论取消点赞
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - roleId
                - commentId
              properties:
                roleId:
                  type: number
                  description: 角色id
                commentId:
                  type: number
                  description: 评论id
      responses:
        '200':
          $ref: '#/components/responses/ApiActionRes'
  /garden/inform/list:
    get:
      tags:
        - garden
      summary: 庄园消息列表
      description: |
        ```
        返回字段中type为消息的类型
        1 - 新的打卡
        2 - 打卡点赞
        3 - 打卡评论
        4 - 评论点赞
        5 - 评论回复

        返回字段中status为读取状态
        0 - 未读
        1 - 已读

        查看该接口后,玩家的所有消息的读取状态都会变为已读
        ```
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          $ref: '#/components/responses/GardenInformListRes'
  /garden/inform/unread:
    get:
      tags:
        - garden
      summary: 庄园消息红点
      parameters:
        - $ref: '#/components/parameters/roleIdIn'
      responses:
        '200':
          $ref: '#/components/responses/GardenInformUnreadRes'
components:
  parameters:
    forceId:
      name: forceId
      in: query
      description: 势力id
      required: true
      schema:
        type: number
        example: 1001
    forceEventId:
      name: id
      in: query
      description: 势力大事的id, 添加后生成
      required: true
      schema:
        type: number
        example: 1
    forceEventContent:
      name: content
      in: query
      description: 史官评注文本
      required: true
      schema:
        type: string
        maxLength: 22
        minLength: 1
        example: 史官对大事件的评注
    forceEventRemarkRoleId:
      name: roleId
      in: query
      description: 评论的史官角色id
      required: true
      schema:
        type: number
        example: 1001
    forceEventRemarkRoleName:
      name: roleName
      in: query
      description: 评论的史官名字
      required: true
      schema:
        type: string
        maxLength: 22
        minLength: 1
        example: 评论的史官名字
    guildPhotoWallType:
      name: type
      in: query
      required: true
      description: 照片墙类型 0 => 普通 1 => 精选
      schema:
        type: number
        enum:
          - 0
          - 1
        example: 0
    notificationStatus:
      name: status
      in: query
      required: false
      description: 通知状态过滤 0=>未读 1=>已读
      schema:
        type: number
        enum:
          - 0
          - 1
        example: 0
    replyId:
      name: replyId
      in: query
      required: false
      description: 回复评论的玩家id
      schema:
        type: number
        example: ***********
    commentText:
      required: true
      name: text
      in: query
      description: 评论你想说的话
      schema:
        type: string
        example: 评论文本
    gpwOverwrite:
      required: false
      name: overwrite
      in: query
      description: 覆盖模式(目标槽位有图片时直接替换)
      schema:
        type: boolean
        example: true
        default: true
    guildPhotoWallId:
      required: true
      name: id
      in: path
      description: 照片墙id
      schema:
        type: number
        example: 11
    photoWallId:
      required: true
      name: id
      in: path
      description: 照片墙id
      schema:
        type: number
        example: 11
    isGuildLeader:
      required: true
      name: isLeader
      in: query
      description: 是否帮会当家
      schema:
        type: boolean
        example: false
    guildPhotoWallCommentId:
      required: true
      name: id
      in: path
      description: 图片评论id
      schema:
        type: number
        example: 11
    photoWallCommentId:
      required: true
      name: id
      in: path
      description: 图片评论id
      schema:
        type: number
        example: 11
    photoWallCommentIdInQuery:
      required: true
      name: id
      in: query
      description: 图片评论id
      schema:
        type: number
        example: 11
    guildPhotoWallSlot:
      required: true
      name: slot
      in: query
      description: 图片槽位id
      schema:
        type: number
        example: 12
    photoWallSlot:
      required: true
      name: slot
      in: query
      description: 图片槽位id
      schema:
        type: number
        example: 12
    guildPhotoWallWallId:
      required: true
      name: wallId
      in: query
      description: 精选墙id
      schema:
        type: number
        example: 10
    photoWallWallId:
      required: true
      name: wallId
      in: query
      description: 精选墙id
      schema:
        type: number
        example: 10
    guildPhotoWallPhotoId:
      required: true
      name: id
      in: path
      description: 图片id
      schema:
        type: number
        example: 10
    photoWallPhotoId:
      required: true
      name: id
      in: path
      description: 图片id
      schema:
        type: number
        example: 10
    photoWallPhotoIdFull:
      required: true
      name: photo_id
      in: path
      description: 图片id
      schema:
        type: number
        example: 10
    guildPhotoWallPhotoIdInQuery:
      required: true
      name: photoId
      in: query
      description: 图片id
      schema:
        type: number
        example: 10
    photoWallPhotoIdInQuery:
      required: true
      name: photoId
      in: query
      description: 图片id
      schema:
        type: number
        example: 10
    multiGardenPhotoWallId:
      required: true
      name: id
      in: path
      description: 照片墙id
      schema:
        type: number
        example: 11
    multiGardenPhotoWallCommentId:
      required: true
      name: id
      in: path
      description: 图片评论id
      schema:
        type: number
        example: 11
    multiGardenPhotoWallCommentIdInQuery:
      required: true
      name: id
      in: query
      description: 图片评论id
      schema:
        type: number
        example: 11
    multiGardenPhotoWallSlot:
      required: true
      name: slot
      in: query
      description: 图片槽位id
      schema:
        type: number
        example: 12
    multiGardenPhotoWallWallId:
      required: true
      name: wallId
      in: query
      description: 精选墙id
      schema:
        type: number
        example: 10
    multiGardenPhotoWallPhotoId:
      name: id
      in: path
      description: 图片id
      required: true
      schema:
        type: number
        example: 10
    multiGardenPhotoWallPhotoIdFull:
      name: photo_id
      in: path
      description: 图片id
      required: true
      schema:
        type: number
        example: 10
    multiGardenPhotoWallPhotoIdInQuery:
      required: true
      name: photoId
      in: query
      description: 图片id
      schema:
        type: number
        example: 10
    isMultiGardenLeader:
      required: true
      name: isLeader
      in: query
      description: 是否联居当家
      schema:
        type: boolean
        example: false
    eventId:
      required: true
      name: id
      in: query
      description: 编年史事件id
      schema:
        type: number
        example: 1002
    undo:
      required: false
      name: undo
      in: query
      description: 撤销该行为
      schema:
        type: boolean
        example: false
    product:
      required: true
      name: product
      in: query
      description: 调用产品标识，该产品标识是接口配置允许调用的产品标识。（如：xyq、xy2、ff、xyw等等。）
      schema:
        type: string
        example: qn
    syncStatus:
      required: false
      name: status
      in: query
      description: 状态（1：待认证，13：认证通过；5：认证失败。 不传默认为1待认证） **游戏无需处理, 梦岛负责同步给urs**
      schema:
        type: number
        enum:
          - 1
          - 13
          - 5
        example: 1
    syncPi:
      required: false
      name: pi
      in: query
      description: 中宣部认证返回的pi值 **游戏无需处理, 梦岛负责同步给urs**
      schema:
        type: string
        example: test-pi
    fcmEmail:
      required: true
      name: email
      in: query
      description: 实名认证登记时填写的Email地址，符合邮件地址格式。
      schema:
        type: string
        example: <EMAIL>
    idnum:
      required: true
      name: idnum
      in: query
      description: 需要进行实名认证的证件号码，对于身份证号码，长度15字符或18字符，（对于18位的身份证前17位均为数字，只有最后一位可以为字母，必须符合身份证的校验规则）。
      schema:
        type: string
        example: 120104200301010400
    idtype:
      required: true
      name: idtype
      in: query
      description: 证件类型（0：身份证，1：军官证，2：护照，3：其他）
      schema:
        enum:
          - 0
          - 1
          - 2
          - 3
        type: number
        example: 0
    realname:
      required: true
      name: realname
      in: query
      description: 用户真实姓名，长度4到12位（2到6个汉字），需要进行UTF-8 encode。
      schema:
        type: string
        example: 李华
    userip:
      required: false
      name: userip
      in: query
      description: 该参数表示用户的真实IP（如果真实IP获取不到，则记录下调用服务器IP，该参数尽量传用户的真实IP）
      schema:
        type: string
        example: *************
    needPi:
      required: false
      name: needPi
      in: query
      description: |
        是否需要返回pi（1需要返回）**游戏不需要关心，梦岛会处理这个参数**
      schema:
        type: number
        example: 1
    ydUsername:
      required: true
      name: username
      in: query
      description: 手机帐号主帐号（yd.xxxxxxx）
      schema:
        type: string
        example: <EMAIL>
    username:
      required: true
      name: username
      in: query
      description: 用户名，可以是目前通行证支持的所有帐号，非163帐号带上域信息（如***********, ************等等），163帐号可以不带域信息。 格式要求：帐号不能为空，必须是字符、下划线、减号、@或点组成，长度小于40个字符。
      schema:
        type: string
        example: <EMAIL>
    GameServerTimestamp:
      required: false
      name: ts
      in: query
      description: 游戏服务器时间戳(单位s), 建议游戏添加，不传使用梦岛的服务器时间
      schema:
        type: number
        example: **********
    timestamp:
      required: false
      name: ts
      in: query
      description: 行为上报时间戳(单位s), 建议游戏添加，不传使用梦岛的服务器时间
      schema:
        type: number
        example: **********
    commodityId:
      name: commodityId
      in: query
      description: 商品 ID
      schema:
        type: number
        example: 1
    itemId:
      name: itemId
      in: query
      description: 道具 ID
      schema:
        type: number
        example: 1
    shopId:
      name: shopId
      in: query
      description: 商店 ID
      schema:
        type: number
        example: 1
    banAccountTime:
      name: banTime
      in: query
      required: false
      description: 封禁的截止时间戳(单位ms), 注意不是封禁的时长，是截止时间
      schema:
        type: number
        example: *************
    commodityIndex:
      name: commodityIndex
      in: query
      description: 商品 Index
      schema:
        type: number
        example: 1
    fashionId:
      name: fashionId
      in: query
      description: 时装 ID
      schema:
        type: number
        example: 1
    kickDate:
      name: kickDate
      in: query
      description: kick date
      required: true
      schema:
        type: string
        example: '2024-01-04 15:05:05'
    urs:
      name: urs
      in: query
      required: true
      description: 通行证账号登录
      schema:
        type: string
        example: <EMAIL>
    fcmGameId:
      name: gameid
      in: query
      required: true
      description: 倩女端游 d10; 逆水寒端游 d30; 天谕端游 d21; 超激斗梦境 d41
      schema:
        type: string
        example: d30
    cloudGameDurationTodayStr:
      name: ds
      in: query
      required: true
      description: 指定日期 (yyyy-MM-dd)
      schema:
        type: string
        example: '2022-01-05'
    cloudGameDurationChargeId:
      name: orderId
      in: query
      required: true
      description: 充值订单id, 游戏需要保证唯一性来保障接口幂等
      schema:
        type: string
        example: '2022-01-05'
    cloudGameDurationYuanbao:
      name: num
      in: query
      required: true
      description: 充值元宝数量
      schema:
        type: number
        example: 100
    cloudGameDurationType:
      name: type
      in: query
      required: true
      description: 时长类型
      schema:
        type: string
        enum:
          - daily
          - permanent
        example: daily
    cloudGameCostType:
      name: type
      in: query
      required: true
      description: 扣除类型 1 => 只允许扣除当日时长; 2 => 优先扣除当日时长，余额从永久时长扣除
      schema:
        type: number
        enum:
          - 1
          - 2
        example: 1
    cloudGameUserType:
      name: userType
      in: query
      required: false
      description: 用户类型
      schema:
        type: string
        enum:
          - normal
          - cloud
        example: cloud
        default: normal
    cloudGameDuration:
      name: duration
      in: query
      required: true
      description: 变化游戏时间(点数)
      schema:
        type: number
        example: 2
    fromServerId:
      name: from
      in: query
      required: true
      description: 被合服的服务器id
      schema:
        type: number
        example: 127
    toServerId:
      name: to
      in: query
      required: true
      description: 合服目的地服务器id
      schema:
        type: number
        example: 123
    weekStr:
      name: ds
      in: query
      required: true
      description: 具体哪一周(用周一的日期表示)
      schema:
        description: 具体哪一周(用周一的日期表示)
        type: string
        example: '2021-05-31'
    weekRenqi:
      name: renqi
      in: query
      required: true
      description: 周人气值
      schema:
        type: number
        example: 100
    noteMailId:
      name: id
      in: query
      description: 邮件id
      required: true
      schema:
        type: number
        example: 1
    mailBoxType:
      name: mail_box_type
      in: path
      description: 邮箱类型
      required: true
      schema:
        $ref: '#/components/schemas/NoteMailBoxType'
    noteMailInboxListKw:
      name: kw
      in: query
      description: 搜索关键字(支持发件人名字)
      required: false
      schema:
        type: string
        example: 发件人名字
    noteMailOutboxListKw:
      name: kw
      in: query
      description: 搜索关键字(支持收件人名字)
      required: false
      schema:
        type: string
        example: 收件人名字
    wishId:
      name: wishId
      in: query
      required: true
      description: 心愿id
      schema:
        type: number
        example: 10
    memeId:
      name: id
      in: query
      required: true
      description: 表情包id
      schema:
        type: number
        example: 10
    roleid:
      name: roleid
      in: query
      description: 角色id
      schema:
        type: number
        example: ***********
      required: true
    canComment:
      name: canComment
      in: query
      description: 是否可以评论
      required: false
      schema:
        type: number
        enum:
          - 0
          - 1
        default: 1
        example: 1
    canCommentByAll:
      name: canCommentByAll
      in: query
      description: 是否可以评论
      required: false
      schema:
        type: number
        enum:
          - 0
          - 1
        default: 1
        example: 1
    canForward:
      name: canForward
      in: query
      description: 是否可以转发
      required: false
      schema:
        type: number
        enum:
          - 0
          - 1
        default: 1
        example: 1
    canCommentByStranger:
      name: canCommentByStranger
      in: query
      description: 是否可以被陌生人评论
      required: false
      schema:
        type: number
        enum:
          - 0
          - 1
        default: 1
        example: 1
    damageStatShareId:
      name: shareId
      in: query
      description: 伤害数据上传后生成的分享id
      required: true
      schema:
        type: string
        example: 1TYRZY6oFUI8syatJ877E
    skillComboId:
      name: id
      in: query
      description: 技能组合id
      required: true
      schema:
        type: number
        example: 1
    skillComboCategory:
      name: category
      in: query
      required: false
      description: |
        | value |    desc    |
        |-------|:----------:|
        |1      |论武套路     |
        |2      |帮战套路     |
        |3      |副本套路     |
      schema:
        $ref: '#/components/schemas/SkillComboCategory'
    skillComboJobId:
      name: jobId
      in: query
      required: false
      description: 套路职业
      schema:
        type: number
        example: 1
    gamePlayId:
      name: gamePlayId
      in: query
      description: 游戏副本id
      required: true
      schema:
        type: number
        example: 51001777
    bossId:
      name: bossId
      in: query
      required: true
      description: 副本里的bossId
      schema:
        type: number
        example: 100
    sameServer:
      name: sameServer
      in: query
      description: 是否同一个服
      schema:
        type: boolean
        example: true
      required: false
    notificationId:
      name: notificationId
      in: query
      required: true
      description: 通知 id
      schema:
        type: number
        example: 3
    roleId:
      name: roleId
      in: query
      description: 角色id
      schema:
        type: number
        example: ***********
    roleIdIn:
      name: roleId
      in: query
      required: true
      description: 角色id
      schema:
        type: number
        example: ***********
    timeStampOpt:
      name: ts
      in: query
      required: false
      description: 游戏当前时间时间戳
      schema:
        type: number
        example: 1695720311
    roleIdRqd:
      name: roleId
      in: query
      required: true
      description: 角色id
      schema:
        type: number
        example: ***********
    itemTemplateId:
      name: itemTemplateId
      in: query
      required: true
      description: 物品模板id
      schema:
        type: number
        example: 10
    bustPhotoIndex:
      name: index
      in: query
      required: true
      description: 半身像位置索引(1-20， 1个玩家最多20张位置)
      schema:
        type: number
        minimum: 1
        maximum: 20
        example: 10
    bustPhotoUrl:
      name: url
      in: query
      required: true
      description: 半身像图片链接
      schema:
        type: string
        example: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
    commonPhotoUrl:
      name: url
      in: query
      required: true
      description: 像图片链接
      schema:
        type: string
        example: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
    roleType:
      name: roleType
      in: query
      required: true
      description: 0=>普通身份 1=>史官身份
      schema:
        type: number
        enum:
          - 0
          - 1
        example: 1
    ownerId:
      name: ownerId
      in: query
      required: true
      description: 庄园主角色id
      schema:
        type: number
        example: ***********
    targetId:
      name: targetId
      in: query
      required: true
      description: 目标玩家id
      schema:
        type: number
        example: ***********
    targetIds:
      name: targetIds
      in: query
      required: true
      description: 目标玩家id列表, csv格式
      schema:
        type: string
        example: ***********,***********
    marriageOldId:
      name: oldId
      required: true
      in: query
      description: 情缘id
      schema:
        type: number
        example: 12
    marriageNewId:
      name: newId
      required: true
      in: query
      description: 情缘id
      schema:
        type: number
        example: 34
    marriageInfoId:
      name: marriageInfoId
      required: true
      in: query
      description: 情缘id
      schema:
        type: number
        example: 1234
    coupleId:
      name: coupleId
      in: query
      description: 侠侣id
      schema:
        type: number
        example: ***********
    coupleName:
      name: coupleName
      in: query
      description: 侠侣名字
      schema:
        type: string
        example: 过儿
    momentPickKw:
      name: kw
      in: query
      required: false
      description: 搜索关键字(玩家id或者有何玩家名)
      schema:
        type: string
        example: '***********'
    momentPickType:
      name: pickType
      in: query
      description: '-1 => 未精选 0 => 所有  1 => 精选'
      schema:
        type: number
        enum:
          - -1
          - 0
          - 1
        example: 0
    roleIds:
      name: roleIds
      in: query
      description: 角色id列表(csv格式)
      schema:
        type: string
        example: ***********,***********
    roleName:
      name: roleName
      in: query
      description: 角色名字
      schema:
        type: string
        example: roleName
    signUpServerId:
      name: signUpServerId
      in: query
      description: 报名转服的目标服务器id
      schema:
        type: number
        example: 1
    transferMark:
      name: transferMark
      in: query
      description: 转服的唯一标记,可不传,之后多次转服需求用,最长20
      schema:
        type: string
        example: 2020-08-10_1
    tag:
      name: tag
      in: query
      description: 标签
      schema:
        type: string
        example: ssr
    tags:
      name: tags
      in: query
      description: 标签列表
      schema:
        type: string
        example: ssr1,ssr2
    rares:
      name: rares
      in: query
      description: 标签稀有度(CSV_ARRAY)
      schema:
        type: string
        example: SSR,SR,F
    rare:
      name: rare
      in: query
      description: 标签稀有度
      schema:
        type: string
        example: SSR
        enum:
          - SSR
          - SR
          - F
    targetid:
      name: targetid
      in: query
      description: 目标玩家id
      schema:
        type: number
        example: ***********
    guildLevel:
      name: guildLevel
      in: query
      required: true
      description: 帮会等级
      schema:
        type: number
        example: 5
    marriageLevel:
      name: marriageLevel
      in: query
      required: true
      description: 情缘等级
      schema:
        type: number
        example: 5
    multiGardenLevel:
      name: multiGardenLevel
      in: query
      required: true
      description: 联居等级
      schema:
        type: number
        example: 5
    marriageId:
      name: marriageId
      in: query
      description: 情缘id
      required: true
      schema:
        type: number
        example: 1002
    guildId:
      name: guildId
      in: query
      description: 帮会id
      required: true
      schema:
        type: number
        example: 1002
    multiGardenId:
      name: multiGardenId
      in: query
      description: 联居id
      required: true
      schema:
        type: string
        example: 060000000001162400020000C6EF0A5D
    EventCategory:
      name: category
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/ServerAnnalCategory'
    RoleIdAndNameKw:
      name: kw
      in: query
      required: false
      description: 请输入玩家名称或id
      schema:
        type: string
        example: ''
    minId:
      name: minId
      in: query
      description: 最小id
      schema:
        type: number
        description: 返回比该id大的数据, 不包含该id
        example: 1
    maxId:
      name: maxId
      in: query
      description: 最大id
      schema:
        type: number
        description: 返回比该id小的数据, 不包含该id
        example: 1100
    monthCardChannel:
      name: channel
      in: query
      description: 月卡购买渠道
      schema:
        type: string
        enum:
          - game
          - cloud_game
        example: game
        default: game
    monthCardOrderId:
      name: orderId
      in: query
      description: 月卡购买订单id
      schema:
        type: string
        example: 20220105UNIQ_ORDER_ID
    monthCardBuyTime:
      name: buyTime
      in: query
      description: 月卡购买时间戳(单位s)
      schema:
        type: number
        example: 1662249600
    monthCardDuration:
      name: duration
      in: query
      description: 月卡时长(单位s)
      schema:
        type: number
        example: 3600
    mergeFrom:
      name: from
      in: query
      description: 被合服的服务器Id
      schema:
        type: number
        example: 1
    mergeTo:
      name: to
      in: query
      description: 合服到的服务器Id
      schema:
        type: number
        example: 1
    serverId:
      name: serverId
      in: query
      description: 服务器Id
      schema:
        type: number
        example: 1
    replyid:
      name: replyid
      in: query
      required: false
      description: 回复的玩家id
      schema:
        type: number
        example: ***********
    time:
      name: time
      in: query
      description: 时间戳单位(ms)
      schema:
        type: number
        example: 1568792675339
    nonce:
      name: nonce
      in: query
      description: 随机字符串(6位)
      schema:
        minLength: 6
        maxLength: 6
        type: string
        example: awsdef
    authToken:
      name: token
      in: query
      description: token计算方式为 md5(ASCII升序参数的值 + salt)
      schema:
        type: string
        example: 8947248ed8ee41a51cff29223f25a4c2
    skey:
      name: skey
      in: query
      description: 登录后返回的校验凭证
      schema:
        type: string
        example: justASkeyToken
    text:
      name: text
      in: query
      description: 文本
      schema:
        type: string
        example: example text
    hotFactor:
      name: hotFactor
      description: 热度系数,默认为1,数值范围[0,2]
      required: false
      in: query
      schema:
        type: number
        example: 0.9
    page:
      name: page
      in: query
      description: 页码
      schema:
        type: number
        default: 1
        example: 1
    sunWeekStr:
      name: week
      in: query
      required: false
      description: 以周日开始的周标记
      schema:
        $ref: '#/components/schemas/SunWeekStr'
    size:
      name: size
      in: query
      description: 数量
      schema:
        type: number
        maximum: 10
        example: 3
    pageSize:
      name: pageSize
      in: query
      description: 每页大小
      schema:
        type: number
        maximum: 20
        default: 10
        example: 10
    momentid:
      name: id
      required: true
      in: query
      description: 心情id
      schema:
        type: number
        example: 886790
    moment_id:
      name: moment_id
      required: true
      in: query
      description: 心情id
      schema:
        type: number
        example: 886790
    momentId:
      name: momentId
      required: true
      in: query
      description: 心情id
      schema:
        type: number
        example: 886790
    switch:
      name: switch
      required: true
      in: query
      description: 开关
      schema:
        type: string
        enum:
          - 'on'
          - 'off'
        example: 'on'
    serverid:
      required: true
      name: serverid
      in: query
      description: 服务器id
      schema:
        type: number
        example: 1
    id_as_comment:
      name: id
      required: true
      in: query
      description: 评论id
      schema:
        type: number
        example: 33640
    id_as_message:
      name: id
      required: true
      in: query
      description: 留言id
      schema:
        type: number
        example: 7128
    likeAction:
      name: action
      in: query
      schema:
        type: string
        enum:
          - do
          - undo
        default: do
    context:
      name: context
      in: query
      schema:
        type: string
        example: ''
        maxLength: 1024
    url:
      name: url
      in: query
      required: true
      schema:
        type: string
        example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
    memeClientId:
      name: clientId
      required: true
      in: query
      description: 客户端的clientId
      schema:
        type: number
        example: 10
    polemic_id:
      name: id
      in: query
      description: 檄文id
      schema:
        type: number
        example: 1
    clubId:
      name: club_id
      in: query
      description: club id
      schema:
        type: number
        example: 1001
    from:
      name: from
      in: query
      description: from location
      schema:
        type: number
        example: 0
    to:
      name: to
      in: query
      description: to location
      schema:
        type: number
        example: 1
    gForce:
      name: gForce
      in: query
      description: 势力
      required: false
      schema:
        type: number
        example: 1
    kw:
      name: kw
      in: query
      description: 搜索关键字
      schema:
        type: string
        example: ''
    honorId:
      name: honor_id
      in: query
      description: honor id
      schema:
        type: number
        example: 1001
    gender:
      name: gender
      in: query
      schema:
        type: number
        example: 1
    email:
      name: email
      in: query
      description: 邮箱地址
      schema:
        type: string
        example: <EMAIL>
    avatar:
      name: avatar
      in: query
      description: 认证头像
      schema:
        type: string
        example: beauty.png
    phone:
      name: phone
      in: query
      description: 手机号
      schema:
        type: number
        example: ***********
    offset:
      name: offset
      required: false
      in: query
      description: 导出时的偏移量,请从上一个导出的表格的sheetName复制出来
      schema:
        type: string
        example: 100-50-30
    mediaType:
      name: mediaType
      in: query
      description: 审核资源媒体类型
      schema:
        type: string
        enum:
          - guild_photo_wall_photo
          - multi_garden_photo_wall_photo
        example: guild_photo_wall_photo
    accountInPath:
      name: account
      in: path
      description: 账号
      required: true
      schema:
        type: string
        example: <EMAIL>
    gameidInPath:
      name: gameid
      in: path
      description: gameid
      required: true
      schema:
        type: string
        example: d30
    mediaResourceId:
      name: resourceId
      in: query
      description: 审核资源id(填对应资源类型的id, 比如帮会照片墙图片，这里就填图片id)
      schema:
        type: number
        example: 1
    auditStatus:
      name: auditStatus
      in: query
      description: 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
      schema:
        type: number
        example: 1
        enum:
          - 0
          - 1
          - -1
    reason:
      name: reason
      in: query
      description: 理由 1=>敏感用词 2=>虚假信息， 3 => 身份信息格式错误， 4=> 其他
      schema:
        type: number
        example: 1
    reasonMsg:
      name: reasonMsg
      in: query
      description: 自定义的理由文本
      schema:
        type: string
        example: 不符合参赛标准
    assertId:
      name: id
      in: query
      description: 素材ID
      schema:
        type: number
        example: 1
    assertIds:
      name: ids
      in: query
      description: 素材ID列表
      schema:
        type: array
        items:
          type: number
          example: 1
    certifiedInfoId:
      name: id
      in: query
      description: 素材ID
      schema:
        type: number
        example: 1
    certifiedInfoIds:
      name: ids
      in: query
      description: 认证信息ID列表
      schema:
        type: array
        items:
          type: number
          example: 1
    pubToken:
      name: pubToken
      in: query
      description: 允许发布檄文的凭证
      schema:
        type: string
        example: JustASecureRandomString
    whichTimes:
      name: whichTimes
      in: query
      description: 届数
      schema:
        type: number
        default: 1
    serverType:
      name: serverType
      in: query
      description: 服务器类型 1=> 正式服  2=>赛季服 3=>黄金服
      schema:
        type: number
        example: 1
        enum:
          - 1
          - 2
          - 3
    competitionId:
      name: competitionId
      in: query
      description: 赛事id 1=>天下第一 2=>诸神之战 3=>皇城之巅 4=>剑试苍穹 5=>跨服联赛
      schema:
        type: number
        example: 1
        enum:
          - 1
          - 2
          - 3
          - 4
          - 5
    roleid_in_query:
      name: roleid
      in: query
      required: true
      description: 角色id, 指向当前登录roleid，和skey需要匹配
      example: ***********
      schema:
        type: number
        example: ***********
    common_page:
      name: page
      in: query
      required: true
      description: 页码
      schema:
        type: number
        example: 1
        minimum: 1
        default: 1
    common_page_size:
      name: pageSize
      in: query
      description: 每页大小
      required: true
      schema:
        type: number
        maximum: 20
        default: 10
        example: 10
    astrology_topic_ids:
      name: topicIds
      in: query
      required: false
      description: 星巫话题id列表，以逗号分隔的数字列表, 如果为空，则不进行过滤
      example: 1,2,3
      schema:
        type: string
        example: 1,2,3
    bazaar_post_id:
      name: postId
      in: query
      required: true
      description: 问惑id
      example: 1
      schema:
        type: number
        example: 1
    timestamp_in_query:
      name: timestamp
      in: query
      required: true
      description: 游戏时间戳，单位毫秒
      example: 1747689600000
      schema:
        type: number
        format: int64
    commonMessageModuleId:
      name: moduleId
      in: query
      description: |
        | 模块id|模块名字 |
        | --- | ----|
        | 10001 | 吃饭奇遇|
      required: true
      schema:
        $ref: '#/components/schemas/commonMessageModuleId'
  schemas:
    CompetitionOptions:
      type: object
      properties:
        serverType:
          type: number
          example: 1
          enum:
            - 1
            - 2
            - 3
        serverTypeName:
          type: string
          example: 正式服
        competitionIds:
          type: array
          items:
            type: object
            properties:
              competitionId:
                type: number
                example: 1
              competitionName:
                type: string
                example: 天下第一
              whichTimes:
                type: array
                items:
                  properties:
                    whichTimes:
                      type: number
                      description: 届数
                      example: 1
                    whichTimesName:
                      type: string
                      example: 第一届
    WebCompetitionList:
      type: object
      properties:
        competitionId:
          type: number
          example: 1
        serverType:
          type: number
          example: 1
        whichTimes:
          type: number
          example: 1
        result:
          type: array
          description: 赛事结果,有队员的
          items:
            type: object
            properties:
              rank:
                type: number
                description: 排名
                example: 1
              server:
                type: number
                description: 服务器id
                example: 1
              serverName:
                type: string
                description: 服务器名称
                example: 烟雨江南
              teamName:
                type: string
                description: 队伍名称
                example: 队伍名称
              roleList:
                type: array
                items:
                  type: object
                  properties:
                    roleName:
                      type: string
                      description: 角色名称
                      example: 角色名称
                    career:
                      type: number
                      description: 职业
                      example: 1
                    gender:
                      type: number
                      description: 性别
                      example: 1
                    roleType:
                      type: number
                      description: 角色类型 1队长 2队员 3替补 4军师
                      enum:
                        - 1
                        - 2
                        - 3
                        - 4
                      example: 1
                    avatarUrl:
                      type: string
                      description: 头像
                      example: https://hi-163-nsh.nos-jd.163yun.com/club/4297f44b13955235245b2497399d7a93/202007/14/39c8d8a0c5c311ea98ab7b68c63692c8.png
        table:
          type: array
          description: 赛事表格,有队员的,表格形式的
          items:
            type: object
            properties:
              rank:
                type: number
                description: 排名
                example: 1
              serverName:
                type: string
                description: 服务器名称
                example: 烟雨江南
              guildName:
                type: string
                description: 帮会名称
                example: 帮会名称
              guildMaster:
                type: string
                description: 帮主名称
                example: 帮主名称
    SunWeekStr:
      description: 具体哪一周(用周日作为日期开始)
      type: string
      example: '2021-05-31'
    PlayerGetProfile:
      type: object
      required:
        - roleId
        - roleName
        - isFollowing
        - title
        - titleId
        - level
        - jobId
        - gender
        - subGender
        - voiceGender
        - frameId
        - gang
        - horoscopeTag
        - photo
        - photoAudit
        - photoAuditMsg
        - gift
        - flower
        - location
        - renqi
        - momentImageLimit
        - signature
        - yinyuan
        - momentCount
        - fansNum
        - privacy
        - bRenQiFull
        - honorNum
        - hasUnReceiveRenQiReward
        - messageBoard
        - bOfficialAccount
        - enableNotConcernOfficial
        - isPublic
        - lianghaoId
        - lianghaoOutTimeStamp
        - identityList
        - currentIdentity
        - starPlayerType
        - starPlayerExpTime
      properties:
        roleId:
          type: number
          example: ***********
        roleName:
          type: string
          example: 我最帅比
        isFollowing:
          description: 是否关注
          type: boolean
          example: false
        level:
          type: number
          description: 玩家等级
          example: 109
        title:
          description: 称号
          type: string
          example: ''
        titleId:
          description: 称号id
          type: number
          example: ********
        jobId:
          type: number
          description: 玩家职业id
          example: 4
        gender:
          type: number
          description: 玩家性别
          example: 0
        subGender:
          type: number
          description: 玩家子性别
          example: 0
        voiceGender:
          type: string
          description: 玩家语音识别性别
          example: '0'
        frameId:
          type: number
          example: 44710001
        gang:
          type: string
          example: ''
        horoscopeTag:
          type: number
          example: 0
        photo:
          type: string
          description: 头像
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2021/03/03/0F1F9217F0F1F1614743838.png?imageView&crop=233_48_531_531
        photoAudit:
          description: 头像审核状态
          type: number
          example: 1
        photoAuditMsg:
          type: number
          example: 0
        gift:
          type: number
          example: 20
        flower:
          type: number
          example: 0
        location:
          type: string
          example: 测试12获取地理位置
        renqi:
          type: number
          description: 本周周人气
          example: 0
        momentImageLimit:
          description: 玩家发送动态附带的图片数量限制
          type: number
          example: 4
        signature:
          type: string
          description: 玩家签名
          example: 一二三四五六七八九十一二三四五六七二三四五六七八九十
        yinyuan:
          type: string
          description: 姻缘信息
          example: ''
        momentCount:
          type: number
          example: 297
        fansNum:
          description: 粉丝数
          type: number
          example: 21
        privacy:
          $ref: '#/components/schemas/PrivacyOptions'
        bRenQiFull:
          type: number
          example: 0
        honorNum:
          type: number
          example: 5
        hasUnReceiveRenQiReward:
          type: boolean
          example: false
        messageBoard:
          type: object
          required:
            - signUpReachLimit
            - signUpForTargetToday
          properties:
            signUpReachLimit:
              type: boolean
              example: false
            signUpForTargetToday:
              type: boolean
              example: false
        bOfficialAccount:
          type: boolean
          description: 是否官方账号
          example: false
        enableNotConcernOfficial:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: true
        lianghaoId:
          type: number
          example: 0
        lianghaoOutTimeStamp:
          type: number
          example: 0
        identityList:
          type: array
          example: []
        currentIdentity:
          type: object
          example: {}
        starPlayerType:
          type: integer
          description: 玩家明星认证过期时间
          example: *************
        starPlayerExpTime:
          type: integer
          description: 玩家明星认证类型
          example: 10
    PrivacyOptions:
      type: object
      description: 玩家隐私设置
      required:
        - hideLocation
        - hideVoiceGender
        - limitComment
        - muteNewMoment
        - limitCrossFriend
        - hideWishList
      properties:
        hideLocation:
          type: boolean
          description: 隐藏lbs位置
          example: false
        hideVoiceGender:
          type: boolean
          description: 隐藏语音性别
          example: false
        limitComment:
          type: boolean
          description: 限制非好友评论
          example: false
        muteNewMoment:
          type: boolean
          description: 禁止新动态提醒
          example: false
        limitCrossFriend:
          type: boolean
          description: 限制跨服交友
          example: false
        hideWishList:
          type: boolean
          description: 隐藏心愿单
          example: true
    MayBeForceEventArgs:
      type: object
      description: 事件参数字典, 用来实例化事件文本显示模板, 内容根据实际自定义
      additionalProperties: true
      properties:
        forceId:
          type: number
          description: 势力id
          example: 1001
    MayBeServerAnnalEventArgs:
      type: object
      description: 事件参数字典, 用来实例化事件文本显示模板
      properties:
        playerId:
          description: 和本事件关联的玩家id
          type: number
          example: 1001
        playerIdList:
          description: 和本事件关联的玩家id列表
          type: array
          items:
            type: number
            example: 1001
        playerName:
          description: 和本事件关联的玩家名字
          type: string
          example: 绝代双骄
        playerNameList:
          description: 和本事件关联的玩家名字列表
          type: array
          items:
            type: string
            example: 绝代双骄
        playerScore:
          description: 玩家战力
          type: number
          example: 6488
        playerScoreList:
          description: 玩家战力列表
          type: array
          items:
            type: number
            example: 6488
        guildId:
          description: 帮会id
          type: number
          example: 3001
        guildName:
          description: 帮会名字
          type: string
          example: 斧头帮
        targetGuildId:
          description: 敌对/联盟帮会id
          type: number
          example: 3002
        targetGuildName:
          description: 敌对/联盟帮会名字
          type: string
          example: 青龙帮
        first:
          description: 标记是否首次事件 需要高亮和置顶
          type: number
          enum:
            - 0
            - 1
        condIndex:
          description: 事件积分映射位置(用于处理相同事件不同参数具有不等积分的情况)
          type: number
          example: 3
    MayBePlayerAnnalEventArgs:
      type: object
      description: 事件参数字典, 用来实例化事件文本显示模板
      properties:
        playerId:
          description: 和本事件关联的玩家id
          type: number
          example: 1001
        playerIdList:
          description: 和本事件关联的玩家id列表
          type: array
          items:
            type: number
            example: 1001
        playerName:
          description: 和本事件关联的玩家名字
          type: string
          example: 绝代双骄
        playerNameList:
          description: 和本事件关联的玩家名字列表
          type: array
          items:
            type: string
            example: 绝代双骄
        guildId:
          description: 帮会id
          type: number
          example: 3001
        guildName:
          description: 帮会名字
          type: string
          example: 斧头帮
        targetGuildId:
          description: 敌对/联盟帮会id
          type: number
          example: 3002
        targetGuildName:
          description: 敌对/联盟帮会名字
          type: string
          example: 青龙帮
        first:
          description: 标记是否首次事件 需要高亮和置顶
          type: number
          enum:
            - 0
            - 1
        condIndex:
          description: 事件积分映射位置(用于处理相同事件不同参数具有不等积分的情况)
          type: number
          example: 3
    BaseServerAnnalEvent:
      type: object
      description: 编年史数据定义
      properties:
        serverId:
          type: number
          description: 服务器Id
          example: 12
        eventType:
          type: number
          description: 事件分类下的具体类型id, 用来映射策划表的事件文本
          example: 10
        eventTime:
          type: number
          description: 事件发生的事件(单位ms)
          example: 1620628236384
        eventArgs:
          $ref: '#/components/schemas/MayBeServerAnnalEventArgs'
      required:
        - serverId
        - category
        - eventType
        - eventTime
        - eventArgs
        - upvote
    BasePlayerAnnalEvent:
      type: object
      description: 编年史数据定义
      required:
        - roleId
        - serverId
        - eventType
        - eventTime
        - eventArgs
      properties:
        roleId:
          description: 和本事件关联的玩家id
          type: number
          example: 1001
        serverId:
          type: number
          description: 服务器Id
          example: 12
        eventType:
          type: number
          description: 事件分类下的具体类型id, 用来映射策划表的事件文本
          example: 10
        eventTime:
          type: number
          description: 事件发生的事件(单位ms)
          example: 1620628236384
        eventArgs:
          $ref: '#/components/schemas/MayBePlayerAnnalEventArgs'
    EventHighlight:
      type: number
      description: 事件高亮标记(控制是否进总览, 1 => 普通事件 2 => 高光事件)
      enum:
        - 1
        - 2
      example: 1
    ServerAnnalEvent:
      allOf:
        - type: object
          description: 编年史数据定义
          required:
            - id
            - category
            - upvote
            - isUpVote
          properties:
            id:
              type: number
              example: 1
            category:
              $ref: '#/components/schemas/ServerAnnalCategory'
            upvote:
              type: number
              description: 点赞数
              example: 10
            isUpVote:
              type: boolean
              description: 是否点赞
              example: true
        - $ref: '#/components/schemas/BaseServerAnnalEvent'
    PicSendPicItem:
      type: object
      properties:
        url:
          type: string
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
        pic_id:
          type: number
          description: string
          example: times_square_photo:1
        role_id:
          type: number
          description: 角色id
          example: 1001
        media:
          type: number
          description: 资源类型 0=>图片 1=> 视频
          example: 0
        note:
          type: string
          description: 备注
          example: ''
    PicReturnPicItem:
      type: object
      properties:
        url:
          type: string
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
        status:
          type: number
          enum:
            - -1
            - 0
            - 1
          description: 状态 0 => 审核中 1 => 通过 -1 => 拒绝
          example: PASS
        pic_id:
          type: number
          description: string
          example: times_square_photo:1
        role_id:
          type: number
          description: 角色id
          example: 1001
        media:
          type: number
          description: 资源类型 0=>图片 1=> 视频
          example: 0
        note:
          type: string
          description: 备注
          example: ''
    AuditSendPic:
      type: object
      required:
        - pic_list
      properties:
        product:
          type: string
          description: 产品
          example: nsh
        pic_list:
          type: array
          items:
            $ref: '#/components/schemas/PicSendPicItem'
          example:
            - url: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
              role_id: ***********
              pic_id: times_square_photo:1
              media: 0
              note: ''
        callback_url:
          type: string
          description: 审核回调url
          example: http://your-app.com/audit/return_pic
    AuditReturnPic:
      type: object
      required:
        - pic_list
      properties:
        product:
          type: string
          description: 产品
          example: nsh
        pic_list:
          type: array
          items:
            $ref: '#/components/schemas/PicReturnPicItem'
          example:
            - url: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
              role_id: ***********
              pic_id: times_square_photo:1
              media: 0
              status: 1
              note: ''
    ActivityTakePhotoMyListItem:
      type: object
      properties:
        locationId:
          type: number
          description: 位置ID
        imgId:
          type: number
          description: 图片ID
        imgUrl:
          type: string
        isLocked:
          type: boolean
          description: 是否被锁定
        isSelected:
          type: boolean
          description: 是否被选中
    RoleInfo:
      type: object
      required:
        - roleId
        - roleName
        - jobId
        - gender
        - level
      properties:
        roleId:
          type: number
        roleName:
          description: 角色名字
          type: string
          example: zhangsan
        server:
          description: 服务器id
          type: number
          example: 12
        jobId:
          description: 职业id
          type: number
          example: 100100001
        gender:
          description: 性别
          type: number
          example: 1
        level:
          description: 等级
          type: number
          example: 20
    GardenBasicInfo:
      allOf:
        - $ref: '#/components/schemas/RoleInfo'
        - type: object
          required:
            - gardenGrade
            - score
            - evaluationCount
            - popularity
          properties:
            gardenGrade:
              type: number
              description: 庄园等级
            score:
              type: number
              description: 评分
            evaluationCount:
              type: number
              description: 打卡人数
            popularity:
              type: number
              description: 人气值
            gardenId:
              type: string
              description: 庄园id
            fengShui:
              type: number
            shiYong:
              type: number
            meiGuan:
              type: number
            photo:
              type: string
    idData:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          example: 1
    TimesSquarePhotoAddRet:
      type: object
      properties:
        auditStatus:
          type: number
          description: 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
          example: 1
        createTime:
          type: number
          description: 创建时间
          example: 1620628236384
    TimesSquarePhotoListRet:
      type: object
      properties:
        list:
          type: array
          items:
            allOf:
              - $ref: '#/components/schemas/TimesSquarePhoto'
              - $ref: '#/components/schemas/TimesSquarePhotoAddRet'
        count:
          type: number
          example: 1
    MemeAddRet:
      type: object
      properties:
        id:
          type: number
          description: 表情包id
          example: 10
        auditStatus:
          type: number
          description: 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
          example: 1
        createTime:
          type: number
          description: 创建时间
          example: 1620628236384
    GardenEvalutionListItem:
      allOf:
        - type: object
          properties:
            id:
              type: number
            text:
              type: string
            score:
              type: number
            imgList:
              type: array
              items:
                type: string
            likeCount:
              type: number
              description: 点赞数
            commentCount:
              type: number
              description: 评论数
            createTime:
              type: number
              description: 创建时间，单位：ms
            isLiked:
              type: boolean
              description: 是否点赞
        - $ref: '#/components/schemas/RoleInfo'
    GardenEvalutionCommentListItem:
      allOf:
        - $ref: '#/components/schemas/RoleInfo'
        - type: object
          required:
            - id
            - text
            - likeCount
            - replyCount
            - createTime
            - isLiked
          properties:
            id:
              type: number
            text:
              type: string
            likeCount:
              type: number
              description: 点赞数
            replyCount:
              type: number
              description: 回复评论数
            createTime:
              type: number
              description: 创建时间，单位：ms
            isLiked:
              type: boolean
              description: 是否点赞
            replyRoleInfo:
              description: 被回复的角色信息
              allOf:
                - $ref: '#/components/schemas/RoleInfo'
    GardenInformListItem:
      allOf:
        - $ref: '#/components/schemas/RoleInfo'
        - type: object
          required:
            - id
            - text
          properties:
            id:
              type: number
            text:
              type: string
            type:
              type: number
              description: 消息类型
            status:
              type: number
              description: 已读状态 0=>未读 1=>已读
              enum:
                - 0
                - 1
    PlayerAnnalEvent:
      allOf:
        - type: object
          description: 编年史数据定义
          required:
            - id
          properties:
            id:
              type: number
              example: 1
        - $ref: '#/components/schemas/BasePlayerAnnalEvent'
    QueryRealNameStatus:
      type: object
      required:
        - result
        - msg
        - realnameVerify
        - realnameSet
        - isAdult
        - pi
      properties:
        result:
          type: string
          example: '201'
        msg:
          type: string
          example: ''
        realnameVerify:
          type: number
          example: 1
        realnameSet:
          type: boolean
          example: true
        isAdult:
          type: boolean
          example: true
        pi:
          type: string
          example: pi_test
        fake_adult:
          type: boolean
          example: true
        isBanned:
          type: boolean
          description: 是否被封禁
          example: true
        banTime:
          description: 封禁截止时间(单位ms)
          type: number
          example: *************
        parentsAuth:
          description: 未满14岁是否通过家长授权允许
          type: boolean
          example: true
        parentsAuthRedirectUrl:
          description: 未满14岁家长认证表单页面
          type: string
          example: https://mpay-personal-privacy-protection.g.mkey.163.com/parents_auth/authorize?ticket=XxXaAonXPnZLEqVbZSGFqfDFzvLaStzP&redirect_uri=https%3A%2F%2Fn.163.com%2F
        super_aas_limit_wrap:
          $ref: '#/components/schemas/SuperAssLimitWrap'
        ngc_result_wrap:
          description: 信用分结果
          type: object
          properties:
            check_res:
              description: 用户是否正常，正常/风险
              type: string
              example: 正常
            risk_source:
              description: '-1代表接口调用异常，0代表结果正常，1代表用户在批注库内就结束判断了，2代表用户为urs批注用户就判断结束了，3代表用户没有安全手机'
              type: number
              example: 0
            ngc_result:
              description: NGC信用分相关
              type: object
              properties:
                credit_score:
                  description: 游戏账号分
                  type: number
                  example: 500
                all_score_positive:
                  description: 账号正向分
                  type: number
                  example: 0
                largest_reduced_item:
                  description: 最大扣分项目
                  type: string
                largest_reduced_score:
                  description: 最大扣分，若为满分500，则为None
                  type: number
                ngc_score:
                  description: 信用分
                  type: number
                  example: 500
    SuperAssLimitWrap:
      type: object
      description: 家长守护平台规则获取以及是否触发限制
      required:
        - restrict_login
        - check_status
        - daily_play_time
        - super_aas_limit
      properties:
        restrict_login:
          type: boolean
          description: 是否由于家长守护规则限制登录, 判断是否被限制用该字段即可
          example: false
        check_status:
          type: number
          description: |
            校验规则具体状态码
            -1: 访问上游接口失败，此时不限制登录
            0: 上游接口正常, 未触发限制规则
            1: 触发宵禁规则被限制
            2: 触发工作日时长上限被限制
            3: 触发节假日时长上限被限制
          example: 0
        daily_play_time:
          type: number
          description: 当日统计游玩时长(秒)
          example: 600
        super_aas_limit:
          type: object
          description: 当访问上游接口失败时，该字段为null
          required:
            - create_order_limit
            - month_sum_limit
            - online_time_limit
            - holiday_online_time_limit
            - curfew_start_time
            - curfew_end_time
            - expired_time
            - aas_msg
            - source
          properties:
            create_order_limit:
              description: 单次消费限额，单位元；限额范围：[0, 999999999]  不传对成年账号默认不限消费额度，对未成年账号默认采用游戏防沉迷标准限额
              type: number
              example: 45
            month_sum_limit:
              description: 月消费限额，单位元；限额范围：[0, 999999999] * 不传对成年账号默认不限消费额度，对未成年账号默认采用游戏防沉迷标准限额
              type: number
              example: 390
            online_time_limit:
              description: 工作日可在线时长，单位精确到秒，0表示工作日禁止登录 不传默认使用中宣部限制，未成年账号和未实名账号不允许传除0以外的其他值
              type: number
              format: float
              example: 3600
            holiday_online_time_limit:
              description: 节假日可在线时长，单位精确到秒，0表示节假日禁止登录 不传默认使用中宣部限制，未成年账号和未实名账号不允许传除0以外的其他值
              type: number
              example: 1200
            curfew_start_time:
              description: 宵禁开启时间，格式为"hh:mm:ss"；如19:00:00，表示从19:00:00到当天23:59:59为宵禁时间 不传默认不限制宵禁
              type: string
              format: time
              example: '19:00:00'
            curfew_end_time:
              description: 宵禁结束时间，格式为"hh:mm:ss"；如06:00:00，表示从00:00:00到当天06:00:00为宵禁时间 不传默认不限制宵禁
              type: string
              format: time
              example: '06:00:00'
            expired_time:
              type: string
              format: date
              example: '2021-05-04'
              description: 设置规则过期时间，格式为"YYYY-mm-dd"；如2021-05-05，表示过期时间为2021-05-05 00:00:00；不传则默认不过期
            aas_msg:
              type: string
              maxLength: 130
              description: 被限制时的提示语，提示语应提及所有被限制的参数项，提示信息长度不超过130个中文字符
              example: 当前账号触发防沉迷限制：单笔订单限额为10元，工作日登录时长为3600秒，宵禁时间为每日19:00:00至次日06:00:00。
            source:
              type: integer
              description: 家长守护平台：不传/传0 运营限制：传1
              example: 1
    GuildExpressionRes:
      required:
        - id
        - serverId
        - guildId
        - roleId
        - url
        - style
        - auditStatus
        - status
        - createTime
      properties:
        id:
          type: number
          example: 1
        serverId:
          type: number
          example: 1
        guildId:
          type: number
          example: 1
        roleId:
          type: number
          example: 61468700232
        url:
          type: string
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
        style:
          type: number
          example: 1
          description: 0表示圆形 1表示正方形
        auditStatus:
          type: number
          example: 1
          description: 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
        status:
          type: number
          example: 1
          description: 0未使用 1使用中
        createTime:
          type: number
          example: 1617334320059
    Topic:
      type: object
      properties:
        ID:
          type: number
        Name:
          type: string
        Banner:
          type: string
        Desc:
          type: string
        CreateTime:
          type: number
    SuccRes:
      type: object
      properties:
        code:
          type: number
        data:
          type: object
    RedDotRes:
      type: object
      required:
        - dynaNum
        - msgNum
        - noteMailNum
        - guildPhotoWallNum
        - newPlayerAnnal
      properties:
        dynaNum:
          type: number
          description: 新动态数量
          example: 1
        msgNum:
          type: number
          description: 新消息数量
          example: 2
        noteMailNum:
          type: number
          example: 2
          description: 便签消息数量
        guildPhotoWallNum:
          type: number
          enum:
            - 0
            - 1
          example: 1
          description: 帮会照片墙是否有红点标记  0=>无红点 1 => 有红点
        multiGardenPhotoWallNum:
          type: number
          example: 1
          enum:
            - 0
            - 1
          description: 联居照片墙是否有红点标记  0=>无红点 1 => 有红点
        newPlayerAnnal:
          type: boolean
          example: true
          description: 个人大事记有否有红点
    ServerAnnalCategory:
      type: number
      description: |
        | value |    desc    |
        |-------|:----------:|
        | 0     |  未归类事件  |
        | 1     |  高光事件  |
        | 2     | 个人类世界 |
        | 3     | 帮会类事件 |
      enum:
        - 0
        - 1
        - 2
        - 3
      example: 1
    SkillComboCategory:
      type: number
      description: |
        | value |    desc    |
        |-------|:----------:|
        |1      |论武套路     |
        |2      |帮战套路     |
        |3      |副本套路     |
      enum:
        - 1
        - 2
        - 3
      example: 1
    ClubPlayerTag:
      type: object
      properties:
        rare:
          type: string
          description: |
            | Rare | index | Color|
            |--|:---:|:---:|
            | SSR| 3   | 黄 |
            | SR | 2   | 蓝 |
            | F  | 4  | 棕 |
            | N  | 1  | 红 |
          enum:
            - SSR
            - SR
            - F
            - 'N'
          example: 'N'
        name:
          type: string
          description: 标签文字
          example: 官方认证选手
    ApplyPersonInfo:
      type: object
      properties:
        urs:
          type: string
          description: urs
          example: <EMAIL>
        roleid:
          type: number
          example: ***********
        nickname:
          type: string
          example: nickname
        expertClazz:
          type: number
          example: 1
        avatar:
          type: string
          example: avatar.png
        realName:
          type: string
          example: realName
        idCard:
          type: string
          example: '33050122121024'
        gender:
          type: number
          example: 1
        email:
          type: string
          example: <EMAIL>
        phone:
          type: number
          example: 13751239012
        smsCode:
          type: string
          description: 短信验证码
          example: '0513'
    ApplyOperator:
      allOf:
        - $ref: '#/components/schemas/ApplyPersonInfo'
        - type: object
          properties:
            images:
              type: array
              items:
                type: string
                example: photo.png
            videos:
              type: array
              items:
                type: string
                example: video.mp4
    MarriageInfo:
      type: object
      required:
        - marriageInfoId
        - updateTime
      properties:
        marriageInfoId:
          type: number
          example: 1234567890
        marriageInfo:
          type: string
          example: marrageInfo
        marriageMemory:
          type: string
          example: marrageInfo
        updateTime:
          description: 时间戳(ms)
          type: number
          example: 1661918550231
    ApplyCommander:
      allOf:
        - $ref: '#/components/schemas/ApplyPersonInfo'
        - type: object
          properties:
            videos:
              type: array
              items:
                type: string
                example: video.mp4
    QueryRealNameAndAntiIndulgence:
      type: object
      properties:
        code:
          type: number
          example: 0
          description: urs接口返回码
        data:
          type: object
          properties:
            realname_flag:
              type: number
              description: |
                标记用户是否填写证件号码、真实姓名和联系电话；位顺序右起，从0开始：
                - 0 填写证件号码标记：1为填写，0为未填写；
                - 1 填写真实姓名标记：1为填写，0为未填写；
                - 2 填写联系电话标记：1为填写，0为未填写；
              enum:
                - 0
                - 1
                - 2
              example: 1
            realname_status:
              type: number
              example: 127
              description: |
                按位表示，每一位的含义如下(右起)
                - 0 是否登记了实名制认证信息，如果为0则不返回id和reg_time
                - 1 是否未成年
                - 2 是否已经过公安机关验证
                - 3 已经过公安机关验证的，是否通过了验证
                - 4 公安认证不通过的原因，1: 号码、姓名不匹配 0: 号码不存在
                    - 5 1: 用户提交了证件复印件 0：没有提交
                    - 6 1: 复印件审核通过 0: 没有通过
                    - 7 1: 已经审核完成 0: 还未审核
            id:
              type: string
              description: 身份证号码，用于游戏中控制使用同一个身份证的不同帐号的游戏时间
              example: '120104200301010398'
            reg_time:
              type: number
              description: 登记实名制信息的时间，精确到秒的长整型时间戳
              example: **********
            update_time:
              type: number
              description: 修改实名登记状态时间，精确到秒的长整型时间戳（经过公安机关验证才返回此字段）
              example: **********
    QueryGameIdByUrs:
      type: object
      required:
        - code
        - subcode
        - status
        - data
      properties:
        code:
          type: number
          example: 200
        subcode:
          type: number
          example: 0
        status:
          type: string
          example: ok
        data:
          type: string
          example: HV9vzAnmtRPOstA//Pop0gq19Od1LLNODfrnkJM+ZYllbqHEnIwmefzVj6t1Alr593fBC/jCLmLqrjB0YkHlg6JyesY9oZtLZnkB6WQ6H9bDr8wGi/NQaoar6oi7m7Gbz028Gln7bdb3YfCGpchVGQ==
    ApiAction:
      type: object
      required:
        - isOk
      properties:
        isOk:
          type: boolean
          description: 操作是否正常
          example: true
      additionalProperties: true
      example:
        isOk: true
    BanAccountShow:
      type: object
      properties:
        urs:
          type: string
          description: 返回值,0正常, 其他皆为异常
          example: <EMAIL>
        isBanned:
          type: boolean
          description: 是否被封禁
          example: true
        banTime:
          description: 封禁截止时间(单位ms)
          type: number
          example: *************
      required:
        - urs
        - isBanned
        - banTime
    ForceEventAddRet:
      type: object
      properties:
        id:
          type: number
          example: 3
        createTime:
          description: 大事件保存时间
          type: number
          example: *************
    ApiOk:
      type: object
      properties:
        code:
          type: number
          description: 返回值,0正常, 其他皆为异常
        data:
          type: object
          description: 具体返回的数据对象
          additionalProperties: true
          example: {}
      required:
        - code
        - data
    UrsApiRet:
      type: object
      properties:
        retCode:
          type: number
          description: 返回码
          example: 200
        retDesc:
          type: string
          description: 返回说明
          example: Ok and more information returned.
        result:
          type: object
          description: 具体返回的数据对象
          example: null
    ClubCpDetail:
      type: object
      properties:
        roleId:
          type: number
          description: 角色Id
        avatar:
          type: string
          description: 头像
        honor:
          type: number
          description: 荣誉点
        nickname:
          type: string
          description: 昵称
        rank:
          type: number
          description: 名次
        expertClazz:
          type: number
          description: 擅长职业
        tags:
          type: array
          description: 标签
          items:
            $ref: '#/components/schemas/ClubPlayerTag'
        clubHonors:
          type: array
          description: 俱乐部相关个人荣誉
          items:
            type: string
            example: 第一届公平联赛冠军指挥
        isCommander:
          type: boolean
          description: 是否认证指挥
          example: false
        isOperator:
          type: boolean
          description: 是否认证高手
          example: false
    ShowWishListItem:
      description: ''
      type: object
      properties:
        id:
          type: number
        roleId:
          type: number
        type:
          type: string
          minLength: 1
        payload:
          type: object
          properties:
            commodityId:
              type: number
            commodityIndex:
              type: number
            itemId:
              type: number
            shopId:
              type: number
          required:
            - commodityId
            - commodityIndex
            - itemId
            - shopId
        createTime:
          type: number
      required:
        - id
        - roleId
        - type
        - payload
        - createTime
    CopyMomentsRes:
      title: CopyMomentsRes
      type: object
      description: ''
      x-examples: {}
      properties:
        count:
          type: integer
          example: 10
          description: 复制的动态数量
      required:
        - count
    NoteMailBoxType:
      description: 邮箱类型
      type: string
      enum:
        - inbox
        - outbox
      example: inbox
    NoteMailSendAppend:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          description: 邮件id
          example: 1
    NoteMailInboxExtra:
      type: object
      required:
        - id
        - star
        - starTime
        - isRead
        - senderName
        - receiverRoleName
      properties:
        id:
          type: number
          description: 邮件id
          example: 1
        senderName:
          type: string
          description: 发件人角色名
          example: 法外狂徒张三
        receiverRoleName:
          type: string
          description: 收件人角色名
          example: 王小美
        star:
          type: boolean
          description: 是否收藏
          example: true
        starTime:
          type: number
          description: 收藏时间(ms)
          example: 1625812450785
        sendTime:
          type: number
          description: 发送时间(ms)
          example: 1625812450785
        isRead:
          type: boolean
          description: 是否已读
          example: true
    NoteMailSend:
      type: object
      required:
        - sender
        - receiver
        - receiverName
        - content
        - signature
        - sendTime
        - style
      properties:
        sender:
          type: number
          description: 写信人Id
          example: 1001
        receiver:
          type: number
          description: 收信人Id
          example: 1002
        receiverName:
          type: string
          description: 收件人昵称
          example: 收件人昵称
        content:
          type: string
          description: 文本
          maxLength: 100
          example: 便签内容
        signature:
          type: string
          description: 邮箱落款签名
          maxLength: 7
          example: 邮箱落款签名
        sendTime:
          type: number
          description: 发送时间（单位ms)
          example: 1625807170708
        style:
          type: number
          description: 便签样式
          example: 1
    ServerAnnalFengyunPlayer:
      type: object
      required:
        - id
        - roleId
        - roleName
        - serverId
        - rank
        - score
        - guild
      properties:
        id:
          type: number
          example: 1
        rank:
          type: number
          description: 排名
          example: 1
        roleId:
          type: number
          description: 玩家id
          example: 38104100237
        roleName:
          type: string
          description: 玩家名字
          example: 一叶知秋
        serverId:
          type: number
          example: 1001
          description: 服务器id
        guild:
          $ref: '#/components/schemas/GuildInfo'
        score:
          type: number
          example: 100
    GuildInfo:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: number
          description: 帮会id
          example: 1
        name:
          type: string
          description: 帮会名字
          example: 四海为家
    SyncServerAnnalEvent:
      title: SyncServerAnnalEvent
      description: 游戏服务器同步的编年史事件
      allOf:
        - type: object
          description: 游戏服务器推送的编年史事件
          properties:
            uuid:
              type: string
              description: 建议生成一个uuid追踪作为每次的事件唯一标记
              example: 14f8071127d644d3a8eceae7c638fe94
          required:
            - id
            - uuid
        - $ref: '#/components/schemas/BaseServerAnnalEvent'
    SyncPlayerAnnalEvent:
      description: 游戏服务器同步的个人编年史事件
      allOf:
        - type: object
          description: 游戏服务器推送的个人编年史事件
          properties:
            uuid:
              type: string
              description: 建议生成一个uuid追踪作为每次的事件唯一标记
              example: 14f8071127d644d3a8eceae7c638fe94
          required:
            - id
            - uuid
        - $ref: '#/components/schemas/BasePlayerAnnalEvent'
    ServerAnnalScore:
      type: object
      required:
        - score
      properties:
        score:
          type: number
          description: 积分
          example: 80
    WeekRenQi:
      type: object
      required:
        - renqi
      properties:
        renqi:
          type: number
          description: 人气值
          example: 10
    GbSuperAssLimitFcmRuleReq:
      type: object
      required:
        - effective_time
        - aas_msg
        - source
      properties:
        create_order_limit:
          type: number
          format: float
          minimum: 0
          maximum: 999999999
          description: 单次消费限额，单位元；限额范围：[0, 999999999]
        month_sum_limit:
          type: number
          format: float
          minimum: 0
          maximum: 999999999
          description: 月消费限额，单位元；限额范围：[0, 999999999]
        currency:
          type: string
          enum:
            - CNY
          description: 币种，暂时只支持CNY，不传默认使用CNY
        online_time_limit:
          type: number
          format: float
          description: 工作日可在线时长，单位精确到秒
        holiday_online_time_limit:
          type: number
          format: float
          description: 节假日可在线时长，单位精确到秒
        curfew_start_time:
          type: string
          format: time
          description: 宵禁开启时间，格式为"hh:mm:ss"；如19:00:00，表示从19:00:00到当天23:59:59为宵禁时间
        curfew_end_time:
          type: string
          format: time
          description: 宵禁结束时间，格式为"hh:mm:ss"；如06:00:00，表示从00:00:00到当天06:00:00为宵禁时间
        effective_time:
          type: string
          format: date
          description: 设置规则生效时间，格式为"YYYY-mm-dd"；如2021-04-04，表示生效时间为2021-04-04 00:00:00
        expired_time:
          type: string
          format: date
          description: 设置规则过期时间，格式为"YYYY-mm-dd"；如2021-05-05，表示过期时间为2021-05-05 00:00:00；不传则默认不过期
        aas_msg:
          type: string
          maxLength: 130
          description: 被限制时的提示语，提示语应提及所有被限制的参数项，提示信息长度不超过130个中文字符
        source:
          type: integer
          enum:
            - 0
            - 1
          description: 家长守护平台：不传/传0 运营限制：传1
    GmServerListMergeServerItem:
      type: object
      required:
        - from_list
        - to
      properties:
        to:
          description: 最终合服id
          type: number
          example: 123
        from_list:
          description: 被合服的服务器id列表
          type: array
          items:
            type: number
            example: 127
    PaginationMeta:
      type: object
      properties:
        curPage:
          type: number
          example: 1
        totalPage:
          type: number
          example: 2
        totalCount:
          type: number
          example: 20
      required:
        - curPage
        - totalPage
        - totalCount
    NoteMailInboxShow:
      allOf:
        - $ref: '#/components/schemas/NoteMailInboxExtra'
        - $ref: '#/components/schemas/NoteMailSend'
    NoteMailInBoxList:
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/NoteMailInboxShow'
        meta:
          $ref: '#/components/schemas/PaginationMeta'
    GuildPhotoWallNotificationType:
      type: number
      description: |
        | id | desc                                 |
        | -- | --                                   |
        | 1  | 我发布的照片被点赞                   |
        | 2  | 我发布的照片被评论                   |
        | 3  | 别人发布照片时，把我设置为“标记用户” |
        | 4  | 我的评论被回复                       |
        | 5  | 我的照片被管理员删除                 |
        | 6  | 我的照片上了“精选墙”。
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
      example: 1
    GuildPhotoWallCommentItem:
      type: object
      required:
        - id
        - photoId
        - roleId
        - roleName
        - isLiked
        - likeCount
        - text
        - createTime
      properties:
        id:
          description: 评论id
          type: number
          example: 1
        photoId:
          description: 评论的照片id
          type: number
          example: 2
        roleId:
          description: 评论人的角色id
          type: number
          example: 1004
        roleName:
          description: 角色名
          type: string
          example: 李四
        text:
          description: 通知文本 评论和回复类通知会附带评论文本
          type: string
          example: 照片真好看
        replyId:
          description: 回复的人的角色id
          type: number
          example: 1004
        replyName:
          description: 回复的人角色名
          type: string
          example: 李四
        likeCount:
          description: 点赞数
          type: number
          example: 10
        isLiked:
          description: 是否点赞过
          type: boolean
          example: false
        createTime:
          description: 评论时间
          type: number
          example: 1630033216732
        canComment:
          description: 是否可以评论
          type: number
          example: 1
    ShowRoleInfo:
      type: object
      required:
        - roleName
        - server
        - jobId
        - gender
        - level
        - frameId
      properties:
        roleName:
          description: 角色名字
          type: string
          example: zhangsan
        server:
          description: 服务器id
          type: number
          example: 12
        jobId:
          description: 职业id
          type: number
          example: 100100001
        gender:
          description: 性别
          type: number
          example: 1
        level:
          description: 等级
          type: number
          example: 20
        frameId:
          description: 头像框
          type: number
          example: 10
    GuildPhotoWallNotificationItem:
      allOf:
        - type: object
          required:
            - id
            - roleId
            - roleName
            - targetId
            - type
            - status
            - createTime
          properties:
            id:
              description: 通知id
              type: number
              example: 1
            roleId:
              description: 通知发起人, 比如点赞就是点赞的人,评论就是评论的人(无发起人此时为0, 比如入选精选墙)
              type: number
              example: 1004
            targetId:
              description: 接受通知的人
              type: number
              example: 100100001
            type:
              $ref: '#/components/schemas/GuildPhotoWallNotificationType'
            text:
              description: 通知文本 评论和回复类通知会附带评论文本
              type: string
              example: 照片真好看
            photoId:
              description: 和照片相关的通知输出照片id
              type: number
              example: 1
            photoUrl:
              description: 和照片相关的通知输出照片url(用于通知处预览照片)
              type: string
              example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2021/08/26/0F1F9217F0F1F1629944290.jpg
            wallId:
              description: 和照片墙相关的通知会输出照片墙id
              type: number
              example: 10
            status:
              description: 通知状态 0=>未读, 1=>已读
              type: number
              example: 1
            createTime:
              description: 通知产生的时间
              type: number
              example: 1630033216732
        - $ref: '#/components/schemas/ShowRoleInfo'
    GuildPhotoWallNotificationList:
      type: object
      required:
        - list
        - meta
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/GuildPhotoWallNotificationItem'
        meta:
          $ref: '#/components/schemas/PaginationMeta'
    GuildPhotoWallCommentList:
      type: object
      required:
        - list
        - meta
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/GuildPhotoWallCommentItem'
        meta:
          $ref: '#/components/schemas/PaginationMeta'
    MultiGardenPhotoWallNotificationType:
      type: number
      description: |
        | id | desc                                 |
        | -- | --                                   |
        | 1  | 我发布的照片被点赞                   |
        | 2  | 我发布的照片被评论                   |
        | 3  | 别人发布照片时，把我设置为“标记用户” |
        | 4  | 我的评论被回复                       |
        | 5  | 我的照片被管理员删除                 |
        | 6  | 我的照片上了“精选墙”。
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
      example: 1
    MultiGardenPhotoWallNotificationItem:
      allOf:
        - type: object
          required:
            - id
            - roleId
            - roleName
            - targetId
            - type
            - status
            - createTime
          properties:
            id:
              description: 通知id
              type: number
              example: 1
            roleId:
              description: 通知发起人, 比如点赞就是点赞的人,评论就是评论的人(无发起人此时为0, 比如入选精选墙)
              type: number
              example: 1004
            targetId:
              description: 接受通知的人
              type: number
              example: 100100001
            type:
              $ref: '#/components/schemas/MultiGardenPhotoWallNotificationType'
            text:
              description: 通知文本 评论和回复类通知会附带评论文本
              type: string
              example: 照片真好看
            photoId:
              description: 和照片相关的通知输出照片id
              type: number
              example: 1
            photoUrl:
              description: 和照片相关的通知输出照片url(用于通知处预览照片)
              type: string
              example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2021/08/26/0F1F9217F0F1F1629944290.jpg
            wallId:
              description: 和照片墙相关的通知会输出照片墙id
              type: number
              example: 10
            status:
              description: 通知状态 0=>未读, 1=>已读
              type: number
              example: 1
            createTime:
              description: 通知产生的时间
              type: number
              example: 1630033216732
        - $ref: '#/components/schemas/ShowRoleInfo'
    MultiGardenPhotoWallNotificationList:
      type: object
      required:
        - list
        - meta
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/MultiGardenPhotoWallNotificationItem'
        meta:
          $ref: '#/components/schemas/PaginationMeta'
    MultiGardenPhotoWallCommentList:
      type: object
      required:
        - list
        - meta
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/GuildPhotoWallCommentItem'
        meta:
          $ref: '#/components/schemas/PaginationMeta'
    MomentPickBatchOp:
      type: object
      description: 批量操作精选状态
      required:
        - ids
        - isPick
      properties:
        ids:
          description: 动态ids
          type: array
          items:
            type: number
            example: 17
        isPick:
          type: boolean
          example: true
    PhotoWallPhotoAdd:
      type: object
      description: 上传图片相关信息
      required:
        - url
        - slot
      properties:
        text:
          description: 作者的一句话描述
          type: string
          example: 作者发照片想说的话， 作者发照片想说的话
        url:
          description: 图片url
          type: string
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
        slot:
          description: 图片在墙的槽位
          type: number
          example: 2
        atRoleIds:
          description: 文本中at的玩家id列表
          type: array
          items:
            type: number
            description: 玩家id
            example: 100100001
    GuildPhotoWallPhotoAdd:
      type: object
      description: 上传图片相关信息
      required:
        - url
        - slot
      properties:
        text:
          description: 作者的一句话描述
          type: string
          example: 作者发照片想说的话， 作者发照片想说的话
        url:
          description: 图片url
          type: string
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
        slot:
          description: 图片在墙的槽位
          type: number
          example: 2
        atRoleIds:
          description: 文本中at的玩家id列表
          type: array
          items:
            type: number
            description: 玩家id
            example: 100100001
    PhotoWallAdd:
      type: object
      properties:
        name:
          description: 每一面照片墙有自己的名字，管理员可进行修改。
          type: string
          example: 帮会团战回忆
        templateId:
          description: 模板id
          type: number
          example: 1002
        frameId:
          type: number
          description: 照片墙相框id
          example: 3
        type:
          description: 照片墙类型 0 => 普通 1 => 精选
          type: number
          enum:
            - 0
            - 1
          example: 0
        idx:
          description: 照片墙下标
          type: number
          example: 1
    PhotoWallShow:
      allOf:
        - type: object
          required:
            - id
            - photos
          properties:
            id:
              type: number
              example: 1
            photos:
              type: array
              items:
                $ref: '#/components/schemas/GuildPhotoWallPhotoListItem'
            guildName:
              type: string
              example: guildName
        - $ref: '#/components/schemas/PhotoWallAdd'
    GuildPhotoWallShow:
      allOf:
        - type: object
          required:
            - id
            - guildName
            - photos
          properties:
            id:
              type: number
              example: 1
            photos:
              type: array
              items:
                $ref: '#/components/schemas/GuildPhotoWallPhotoListItem'
            guildName:
              type: string
              example: 帮会名字
        - $ref: '#/components/schemas/PhotoWallAdd'
    GuildPhotoWallBaseItem:
      type: object
      required:
        - id
        - type
        - createTime
      properties:
        id:
          type: number
          example: 1
        type:
          type: number
          description: 0 普通照片墙 1 => 精选照片墙
          example: 0
        createTime:
          description: 照片墙开启时间
          type: number
          example: *************
    GuildPhotoWallList:
      type: array
      items:
        allOf:
          - type: object
            required:
              - id
            properties:
              id:
                type: number
                example: 1
          - $ref: '#/components/schemas/GuildPhotoWallBaseItem'
          - $ref: '#/components/schemas/PhotoWallAdd'
    GuildPhotoWallPhoto:
      allOf:
        - $ref: '#/components/schemas/GuildPhotoWallPhotoListItem'
        - $ref: '#/components/schemas/GuildPhotoWallPhotoActionData'
        - properties:
            canComment:
              description: 是否可以评论
              type: number
              example: 1
    MultiGardenPhotoWallPhotoAdd:
      type: object
      description: 上传图片相关信息
      required:
        - url
        - slot
      properties:
        text:
          description: 作者的一句话描述
          type: string
          example: 作者发照片想说的话， 作者发照片想说的话
        url:
          description: 图片url
          type: string
          example: http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
        slot:
          description: 图片在墙的槽位
          type: number
          example: 2
        atRoleIds:
          description: 文本中at的玩家id列表
          type: array
          items:
            type: number
            description: 玩家id
            example: 100100001
    MultiGardenPhotoWallAdd:
      type: object
      properties:
        name:
          description: 每一面照片墙有自己的名字，管理员可进行修改。
          type: string
          example: 联居回忆
        templateId:
          description: 模板id
          type: number
          example: 1002
        frameId:
          type: number
          description: 照片墙相框id
          example: 3
        type:
          description: 照片墙类型 0 => 普通 1 => 精选
          type: number
          enum:
            - 0
            - 1
          example: 0
        idx:
          description: 照片墙下标
          type: number
          example: 1
    MultiGardenPhotoWallShow:
      allOf:
        - type: object
          required:
            - id
            - multiGardenName
            - photos
          properties:
            id:
              type: number
              example: 1
            photos:
              type: array
              items:
                $ref: '#/components/schemas/MultiGardenPhotoWallPhotoListItem'
            multiGardenName:
              type: string
              example: 联居名字
        - $ref: '#/components/schemas/MultiGardenPhotoWallAdd'
    MultiGardenPhotoWallBaseItem:
      type: object
      required:
        - id
        - type
        - createTime
      properties:
        id:
          type: number
          example: 1
        type:
          type: number
          description: 0 普通照片墙 1 => 精选照片墙
          example: 0
        createTime:
          description: 照片墙开启时间
          type: number
          example: *************
    MultiGardenPhotoWallList:
      type: array
      items:
        allOf:
          - type: object
            required:
              - id
            properties:
              id:
                type: number
                example: 1
          - $ref: '#/components/schemas/MultiGardenPhotoWallBaseItem'
          - $ref: '#/components/schemas/MultiGardenPhotoWallAdd'
    MultiGardenPhotoWallPhoto:
      allOf:
        - $ref: '#/components/schemas/MultiGardenPhotoWallPhotoListItem'
        - $ref: '#/components/schemas/MultiGardenPhotoWallPhotoActionData'
    MomentPickItem:
      type: object
      required:
        - id
        - roleId
        - roleName
        - text
        - imgList
        - likeCount
        - commentCount
        - isPicked
        - pickTime
      properties:
        id:
          type: number
          description: 动态id
          example: 1
        roleId:
          type: number
          example: 2339902297
        roleName:
          type: string
          example: 稀饭
        text:
          type: string
          example: <link button=寻找每日小锦鲤,PSHotTalk,105>
        imgList:
          type: array
          items:
            type: string
            example: http://hi-163-qnm.nosdn.127.net/moment/202109/24/3918c1601d2211ec849e339407ca61bf
        likeCount:
          type: number
          example: 0
        commentCount:
          type: number
          example: 0
        isPicked:
          description: 是否入选
          type: boolean
          example: false
        pickTime:
          description: 入选时间
          type: number
          example: *************
    MomentPickList:
      type: array
      items:
        $ref: '#/components/schemas/MomentPickItem'
    EAuditStatus:
      type: number
      description: |
        | id | desc     |
        | -- | ----     |
        | -1 | 审核拒绝 |
        | 0  | 审核中   |
        | 1  | 审核通过 |
      enum:
        - -1
        - 0
        - 1
      example: 1
    GuildPhotoWallPhotoListItem:
      allOf:
        - type: object
          required:
            - id
            - photoId
            - roleId
            - roleName
            - auditStatus
            - isLiked
            - createTime
            - wallId
            - guildId
            - isPicked
          properties:
            id:
              description: id
              type: number
              example: 21
            photoId:
              description: 照片id
              type: number
              example: 21
            wallId:
              description: 墙id
              type: number
              example: 11
            guildId:
              description: 照片墙的帮会id
              type: number
              example: 11
            roleId:
              description: 上传图片的角色id
              type: number
              example: 100100001
            roleName:
              description: 作者昵称
              type: string
              example: 闫瑜
            isLiked:
              description: 是否点赞过
              type: boolean
              example: false
            auditStatus:
              $ref: '#/components/schemas/EAuditStatus'
            createTime:
              description: 图片发布时间
              type: number
              example: *************
            isPicked:
              description: 是否入选了精选墙
              type: boolean
              example: false
            canComment:
              type: number
              description: 是否可以评论
              example: 1
        - $ref: '#/components/schemas/GuildPhotoWallPhotoAdd'
    GuildPhotoWallPhotoActionData:
      type: object
      required:
        - likeCount
        - commentCount
      properties:
        likeCount:
          description: 点赞数
          type: number
          example: 8
        commentCount:
          description: 评论数
          type: number
          example: 10
    MultiGardenPhotoWallPhotoListItem:
      allOf:
        - type: object
          required:
            - id
            - photoId
            - roleId
            - roleName
            - auditStatus
            - isLiked
            - createTime
            - wallId
            - multiGardenId
            - isPicked
          properties:
            id:
              description: id
              type: number
              example: 21
            photoId:
              description: 照片id
              type: number
              example: 21
            wallId:
              description: 墙id
              type: number
              example: 11
            multiGardenId:
              description: 照片墙的联居id
              type: string
              example: 060000000001162400020000C6EF0A5D
            roleId:
              description: 上传图片的角色id
              type: number
              example: 100100001
            roleName:
              description: 作者昵称
              type: string
              example: 闫瑜
            isLiked:
              description: 是否点赞过
              type: boolean
              example: false
            auditStatus:
              $ref: '#/components/schemas/EAuditStatus'
            createTime:
              description: 图片发布时间
              type: number
              example: *************
            isPicked:
              description: 是否入选了精选墙
              type: boolean
              example: false
        - $ref: '#/components/schemas/GuildPhotoWallPhotoAdd'
    MultiGardenPhotoWallPhotoActionData:
      type: object
      required:
        - likeCount
        - commentCount
      properties:
        likeCount:
          description: 点赞数
          type: number
          example: 8
        commentCount:
          description: 评论数
          type: number
          example: 10
    GetCoupleInfoData:
      type: object
      required:
        - has
      properties:
        has:
          description: 存在侠侣关系
          type: boolean
          example: true
        coupleId:
          description: 侠侣id
          type: number
          example: 100245
        coupleName:
          description: 侠侣名字
          type: string
          example: 过儿
    CloudGameDurationShowData:
      allOf:
        - $ref: '#/components/schemas/CloudGameDurationData'
        - type: object
          properties:
            yuanbao:
              $ref: '#/components/schemas/CloudGameMonthYuanBao'
        - type: object
          properties:
            monthCard:
              $ref: '#/components/schemas/CloudGameMonthCardData'
    CloudGameDurationChangeData:
      allOf:
        - $ref: '#/components/schemas/CloudGameDurationData'
        - $ref: '#/components/schemas/CloudGameDurationDeltaData'
    CloudGameNotifyYuanbaoChangeData:
      allOf:
        - $ref: '#/components/schemas/CloudGameDurationChangeData'
        - type: object
          properties:
            yuanbao:
              $ref: '#/components/schemas/CloudGameMonthYuanBao'
    CloudGameDurationDeltaData:
      type: object
      required:
        - isChange
        - dailyDurationDelta
        - permanentDurationDelta
      properties:
        isChange:
          description: 是否新增/扣除成功
          type: boolean
          example: true
        dailyDurationDelta:
          description: 当日时长变化量(单位s)
          type: number
          example: 60
        permanentDurationDelta:
          description: 永久时长变化量(单位s)
          type: number
          example: -60
    CloudGameMonthCardData:
      type: object
      required:
        - status
        - expireAt
      properties:
        status:
          description: 月卡状态 (0=>从未开通, 1 => 开通正常, 2 => 开通已过期)
          type: number
          enum:
            - 0
            - 1
            - 2
          example: 0
        expireAt:
          description: 月卡过期时间
          type: number
          example: 1662249600
    CloudGameDurationData:
      type: object
      required:
        - urs
        - dailyDuration
        - permanentDuration
      properties:
        urs:
          description: 通行证账号
          type: string
          example: <EMAIL>
        ds:
          description: 今日字符串
          type: string
          example: '2022-01-05'
        dailyDuration:
          description: 剩余当日时长(单位s)
          type: number
          example: 300
        permanentDuration:
          description: 剩余永久时长(单位s)
          type: number
          example: 600
    CloudGameMonthYuanBao:
      description: 本月元宝充值以及奖励时长
      type: object
      required:
        - month
        - chargeNum
        - awardDuration
      properties:
        month:
          description: 月度标记
          type: string
          example: 2022-01
        chargeNum:
          description: 充值元宝数量
          type: number
          example: 30
        awardDuration:
          description: 奖励永久时长(单位s)
          type: number
          example: 300
    GetHonorList:
      type: object
      properties:
        code:
          type: integer
        data:
          type: object
          properties:
            list:
              type: array
              items:
                type: object
                properties:
                  ID:
                    type: integer
                    description: 策划配置的荣誉id, 如 10000060, 可以通过这个id在策划表里所有该赛事信息
                  HonorId:
                    type: integer
                    description: 玩家绑定荣誉后生成的id, 存在梦岛后db里的id
                  WhichTimes:
                    type: integer
                    description: 第几届
                  ShowWhichTimes:
                    type: string
                    description: 显示届数的名字(是用策划表名字)
                  UseName2:
                    type: integer
                    description: 是否使用设置的届数名字(0和1, 1的时候需要使用ShowWhichTimes字段)
                  HonorType:
                    type: integer
                    example: 32
                    description: 荣誉名次类型 (是指策划HonorPlayers.xlsx 这个配表的名次那一列的值)
                  Date:
                    type: integer
                    description: 比赛时间戳(ms)
                  CompetitionId:
                    description: 赛事类型id, 在 Mengdao.xlsm策划表的 CompetitionType的这个表格
                    type: integer
                  GroupName:
                    type: string
                    description: 组别(比如风云可待组, 有些比赛类型是比赛)
                required:
                  - ID
                  - HonorId
                  - WhichTimes
                  - ShowWhichTimes
                  - UseName2
                  - HonorType
                  - Date
                  - CompetitionId
                  - GroupName
          required:
            - list
          additionalProperties: false
      additionalProperties: false
      required:
        - code
        - data
    SkillComboStat:
      type: object
      required:
        - likeCnt
        - hot
      properties:
        likeCnt:
          type: number
          description: 点赞数
        hot:
          type: number
          description: 热度
    SkillComboUserAction:
      type: object
      required:
        - isLiked
        - isCollected
      properties:
        isLiked:
          type: boolean
          description: 是否点赞
          example: false
        isCollected:
          type: boolean
          description: 是否收藏
          example: false
    SkillComboPlayerExtend:
      allOf:
        - $ref: '#/components/schemas/AppearancePaint'
        - type: object
          required:
            - roleName
            - jobId
            - gender
            - subGender
          properties:
            roleName:
              type: string
              description: 角色名字
              example: roleName
            jobId:
              type: number
              description: 角色职业
              example: 1
            gender:
              type: number
              description: 角色性别
              example: 0
            subGender:
              type: number
              description: 角色性别
              example: 0
    GardenPhotoUnitAddBody:
      type: object
      required:
        - url
        - itemTemplateId
      properties:
        url:
          type: string
          description: 图片地址
          example: https://hi-163-nsh.nos-jd.163yun.com/nsh/skill_combo/skill_combo_1.meta
        itemTemplateId:
          type: number
          description: 物品模板id
          example: 10
    AppearancePaint:
      type: object
      properties:
        headPaintId:
          type: number
          example: 44731002
        bodyPaintId:
          type: number
          example: 44732002
    SkillComboRecord:
      type: object
      required:
        - roleId
        - url
        - name
        - tagIds
        - desc
        - category
        - jobId
        - region
      properties:
        roleId:
          type: number
          description: 上传玩家id
          example: ***********
        url:
          type: string
          description: 技能组合数据nos地址
          example: https://hi-163-nsh.nos-jd.163yun.com/nsh/skill_combo/skill_combo_1.meta
        name:
          type: string
          description: 套路名字
          example: 套路名字7个字
          minLength: 1
          maxLength: 7
        tagIds:
          type: array
          description: 策划配表的标签id列表
          items:
            type: number
          example:
            - 1
            - 2
        desc:
          type: string
          description: 技能描述
          example: 对具体技能组合的特点介绍和描述
          minLength: 1
          maxLength: 50
        category:
          $ref: '#/components/schemas/SkillComboCategory'
          type: number
          description: |
            |val|desc|
            |--|--|
            |1|论武套路|
            |2|帮战套路|
            |3|副本套路|
          enum:
            - 1
            - 2
            - 3
          example: 1
        jobId:
          type: number
          description: 职业id
          example: 10
        region:
          type: number
          description: 技能推荐要按照服务器分组分开, 同属一个推荐区域使用同一个region
          example: 1
    SkillComboAddRet:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          description: 技能推荐组合id
          example: 1
    SkillComboListRet:
      type: object
      required:
        - list
        - count
      properties:
        list:
          type: array
          description: 技能推荐组合列表
          items:
            $ref: '#/components/schemas/SkillComboGetRet'
        count:
          type: number
          description: 技能组合数量
          example: 100
    SkillComboGetRet:
      allOf:
        - $ref: '#/components/schemas/SkillComboAddRet'
        - $ref: '#/components/schemas/SkillComboRecord'
        - $ref: '#/components/schemas/SkillComboPlayerExtend'
        - $ref: '#/components/schemas/SkillComboStat'
        - $ref: '#/components/schemas/SkillComboUserAction'
    DamageStatRecord:
      type: object
      required:
        - playerId
        - gamePlayId
        - bossId
        - recordStr
        - recordTime
        - hurtPerSec
        - curePerSec
      properties:
        playerId:
          type: number
          description: 玩家id
          example: ***********
        gamePlayId:
          type: number
          description: 游戏副本id
          example: 51001777
        bossId:
          type: number
          description: 因为这边需求是副本里的每个boss单独统计，所以这里的参数应该要加一个bossId
          example: 100
        recordTime:
          type: number
          description: 记录时间(ms)
          example: 1620628236384
        recordStr:
          type: string
          example: ZW5jb2RlR2FtZURhdGFTdHI=
          description: 游戏序列化的二进制数据(base64编码)
        hurtPerSec:
          type: number
          example: 1234
          description: 每秒伤害
        curePerSec:
          type: number
          example: 1234
          description: 每秒治疗
    ForceEventAdd:
      type: object
      required:
        - uuid
        - serverId
        - forceId
        - eventType
        - eventTime
      properties:
        uuid:
          type: string
          description: 需要生成一个uuid追踪作为每次的事件唯一标记
          example: 14f8071127d644d3a8eceae7c638fe94
        serverId:
          type: number
          description: 服务器id
          example: 12
        forceId:
          type: number
          description: 势力id
          example: 13
        eventType:
          type: number
          description: 事件分类下的具体类型id, 用来映射策划表的事件文本
          example: 10
        eventTime:
          type: number
          description: 事件发生的事件(单位ms)
          example: 1620628236384
        eventArgs:
          $ref: '#/components/schemas/MayBeForceEventArgs'
    BustPhotoAddRet:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          description: 记录id
          example: 1
    GardenPhotoUnitAddRet:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          description: 记录id
          example: 1
    DamageStatAddRet:
      type: object
      required:
        - id
      properties:
        id:
          type: number
          description: 数据id
          example: 1
        shareId:
          type: string
          description: 分享的时候我要给别人拼一个能唯一定位到某条记录的方式
          example: 1TYRZY6oFUI8syatJ877E
    DamageStatItem:
      allOf:
        - $ref: '#/components/schemas/DamageStatAddRet'
        - $ref: '#/components/schemas/DamageStatRecord'
    GardenPhotoUnitItem:
      allOf:
        - $ref: '#/components/schemas/GardenPhotoUnitAddRet'
        - type: object
          properties:
            roleId:
              type: number
              example: ***********
        - $ref: '#/components/schemas/GardenPhotoUnitAddBody'
    BustPhotoItem:
      allOf:
        - $ref: '#/components/schemas/BustPhotoAddRet'
        - type: object
          required:
            - roleId
            - url
            - index
          properties:
            roleId:
              description: 玩家角色id
              type: number
              example: ***********
            index:
              description: 半身像位置索引(1-20， 1个玩家最多20张位置)
              type: number
              minimum: 1
              maximum: 20
              example: 10
            url:
              description: 半身像图片链接
              type: string
              example: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
    AppearancePaintGetRet:
      allOf:
        - $ref: '#/components/schemas/AppearancePaint'
        - type: object
          properties:
            roleId:
              type: number
              description: 角色id
    ForceEventRemark:
      type: object
      required:
        - remark
        - remarkTime
        - remarkRoleId
        - remarkRoleName
      properties:
        remark:
          type: string
          description: 史官评注文本
          example: 史官评注文本
        remarkTime:
          type: number
          description: 史官评注时间
          example: *************
        remarkRoleId:
          type: number
          description: 评论的史官角色id
          example: 1001
        remarkRoleName:
          description: 评论的史官名字
          type: string
          example: 评论的史官名字
    ForceEventtat:
      type: object
      required:
        - likeCount
      properties:
        likeCount:
          type: number
          description: 点赞数
          example: 1
    ForceEventAction:
      type: object
      required:
        - isLiked
      properties:
        isLiked:
          type: boolean
          description: 是否点赞
          example: false
    TimesSquarePhoto:
      type: object
      required:
        - url
        - index
      properties:
        url:
          type: string
          description: 图片链接
          example: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
        index:
          type: number
          description: 图片位置索引
          example: 1
    MemeAdd:
      type: object
      properties:
        roleid:
          type: number
          example: ***********
        url:
          type: string
          example: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
    planet:
      type: string
      enum:
        - sun
        - moon
        - mercury
        - venus
        - mars
        - jupiter
        - saturn
        - uranus
        - neptune
        - pluto
        - southNode
        - northNode
      description: |-
        行星ID（英文枚举值）。ID与行星的对应关系如下：
        | ID | 行星 |
        |----|------|
        | sun | 太阳 |
        | moon | 月亮 |
        | mercury | 水星 |
        | venus | 金星 |
        | mars | 火星 |
        | jupiter | 木星 |
        | saturn | 土星 |
        | uranus | 天王星 |
        | neptune | 海王星 |
        | pluto | 冥王星 |
        | southNode | 南郊点 |
        | northNode | 北郊点 |
    constellation:
      type: string
      enum:
        - aries
        - taurus
        - gemini
        - cancer
        - leo
        - virgo
        - libra
        - scorpio
        - sagittarius
        - capricorn
        - aquarius
        - pisces
      description: |-
        星座ID（英文枚举值）。ID与星座的对应关系如下：
        | ID | 星座 |
        |----|------|
        | aries | 白羊座 |
        | taurus | 金牛座 |
        | gemini | 双子座 |
        | cancer | 巨蟹座 |
        | leo | 狮子座 |
        | virgo | 处女座 |
        | libra | 天秤座 |
        | scorpio | 天蝎座 |
        | sagittarius | 射手座 |
        | capricorn | 摩羯座 |
        | aquarius | 水瓶座 |
        | pisces | 双鱼座 |
    house:
      type: integer
      minimum: 1
      maximum: 12
      description: |-
        宫位ID。ID与宫位的对应关系如下：
        | ID | 宫位 |
        |----|------|
        | 1  | 第一宫命宫 |
        | 2  | 第二宫财运宫 |
        | 3  | 第三宫兄弟宫 |
        | 4  | 第四宫家庭宫 |
        | 5  | 第五宫子女宫 |
        | 6  | 第六宫健康宫 |
        | 7  | 第七宫夫妻宫 |
        | 8  | 第八宫疾厄宫 |
        | 9  | 第九宫迁移宫 |
        | 10 | 第十宫事业宫 |
        | 11 | 第十一宫人际宫 |
        | 12 | 第十二宫精神宫 |
    bazaar_dice:
      type: object
      required:
        - planet
        - constellation
        - house
      properties:
        planet:
          $ref: '#/components/schemas/planet'
        constellation:
          $ref: '#/components/schemas/constellation'
        house:
          $ref: '#/components/schemas/house'
    bazaar_post_add:
      type: object
      required:
        - question
        - dice
        - isSyncMoment
        - topicId
        - topicText
      properties:
        topicId:
          type: integer
          description: 话题ID
          example: 1
          format: int64
        topicText:
          type: string
          description: 话题文本
          example: 爱情运势
        question:
          type: string
          description: 用户选择或输入的占卜问题。
          example: '#今日困惑 考研初试分数怎么样?'
        dice:
          $ref: '#/components/schemas/bazaar_dice'
        isSyncMoment:
          type: boolean
          description: 是否同步到朋友圈
          example: true
    bazaar_post_add_resp:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: 问惑ID
          example: 1234567890
          format: int64
    basic_role_info:
      type: object
      required:
        - roleId
        - roleName
        - jobId
        - gender
        - subGender
        - headPaintId
        - bodyPaintId
      properties:
        roleId:
          type: integer
          description: 角色ID
          example: 1234567890
          format: int64
        roleName:
          type: string
          description: 角色名称
          example: 星巫
        jobId:
          type: integer
          description: 职业ID
          example: 1
          format: int64
        gender:
          type: integer
          description: 性别
          example: 1
        subGender:
          type: integer
          description: 子性别
          example: 1
        headPaintId:
          type: number
          description: 头像ID
          example: 44731002
        bodyPaintId:
          type: number
          description: 立绘ID
          example: 44732002
    bazaar_core_user_info:
      type: object
      required:
        - roleId
        - gender
        - astroLevel
        - astroType
      properties:
        roleId:
          type: integer
          description: 角色ID
          example: 1234567890
          format: int64
        gender:
          type: integer
          description: 性别
          example: 1
          format: int32
        astroLevel:
          type: integer
          description: 星巫等级
          example: 1
          format: int32
        astroType:
          type: integer
          description: 星巫类型
          example: 1
          format: int32
    bazaar_post_show_item:
      type: object
      required:
        - id
        - question
        - dice
        - roleId
        - roleInfo
        - userInfo
        - topicId
        - commentCount
        - canComment
        - createTime
      properties:
        id:
          type: integer
          description: 问惑ID
          example: 1
          format: int64
        roleId:
          type: integer
          description: 角色ID
          example: 1
          format: int64
        topicId:
          type: integer
          description: 话题ID
          example: 1
          format: int64
        roleInfo:
          $ref: '#/components/schemas/basic_role_info'
        userInfo:
          $ref: '#/components/schemas/bazaar_core_user_info'
        question:
          type: string
          description: 用户选择或输入的占卜问题。
          example: '#今日困惑 考研初试分数怎么样?'
        dice:
          $ref: '#/components/schemas/bazaar_dice'
        commentCount:
          type: integer
          description: 评论数
          example: 1
          format: int64
        canComment:
          type: boolean
          description: 是否可以评论, 当用户已经解析过该帖子并完成评论后时，该字段为false
          example: true
        createTime:
          type: integer
          description: 创建时间
          example: 1747294491610
          format: int64
    bazaar_post_list_resp:
      type: object
      required:
        - list
        - total
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/bazaar_post_show_item'
        total:
          type: number
          description: 总数量
          example: 100
          format: integer
    bazaar_post_interpret_resp:
      type: object
      required:
        - title
        - content
      properties:
        title:
          type: string
          description: 占卜標題
          example: 占星骰子
        content:
          type: array
          items:
            type: object
            required:
              - title
              - content
            properties:
              title:
                type: string
                description: 內容標題
                example: 骰子解讀
              content:
                type: string
                description: 內容詳情
                example: 骰子1：行星骰，冥王星。冥王星象征深度蜕变、隐秘力量与重生，暗示关系中存在潜在的控制欲或重大转折。\n骰子2：星座骰，双子。双子座代表信息流动、双重性与灵活应变，关系可能呈现多变特质，涉及大量言语交流或心智博弈。\n骰子3：宫位骰，11宫。11宫关联社群网络、共同理想，说明双方关系可能根植于社交圈层或共享的未来愿景。
      example:
        title: 占星骰子
        content:
          - title: 骰子解读
            content: 骰子1：行星骰，冥王星。冥王星象征深度蜕变、隐秘力量与重生，暗示关系中存在潜在的控制欲或重大转折。\n骰子2：星座骰，双子。双子座代表信息流动、双重性与灵活应变，关系可能呈现多变特质，涉及大量言语交流或心智博弈。\n骰子3：宫位骰，11宫。11宫关联社群网络、共同理想，说明双方关系可能根植于社交圈层或共享的未来愿景。
          - title: 总体解读
            content: 这段关系存在隐秘的双向影响，冥王星在11宫暗示双方可能通过社交活动建立深层羁绊，但双子座的变动性使信任基础易受流言干扰。表面看似轻松的友谊联结，实则暗含信息不对等或理念分歧的风险。
          - title: 建议
            content: 主动澄清关键信息，避免群体舆论影响判断。用双子座的幽默感化解紧张，同时建立冥王星式的深度信任契约。关注共同目标而非琐碎争议。
    bazaar_comment_add:
      type: object
      required:
        - postId
        - text
      properties:
        postId:
          type: integer
          description: 问惑ID
          example: 1
          format: int64
        text:
          type: string
          description: 作为评论的解析内容
          example: 解析内容
    bazaar_comment_add_resp:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          description: 问惑评论ID
          example: 1234567890
          format: int64
    bazaar_rating_add:
      type: object
      description: 解析评价
      required:
        - commentId
        - star
        - text
      properties:
        commentId:
          type: integer
          format: int32
          description: 打分的评论ID
          example: 1
        star:
          type: integer
          format: int32
          description: 用户评分，1 到 5 之间的整数
          minimum: 1
          maximum: 5
          example: 4
        text:
          type: string
          description: ''
          minLength: 0
          maxLength: 30
          example: 谢谢您的解析，非常准确！
    bazaar_rating:
      allOf:
        - $ref: '#/components/schemas/bazaar_rating_add'
        - type: object
          properties:
            createTime:
              type: integer
              format: int64
              description: 评价时间
              example: 1747294491610
    bazaar_comment_show_item:
      type: object
      required:
        - id
        - postId
        - roleId
        - roleInfo
        - userInfo
        - text
        - createTime
      properties:
        id:
          type: integer
          description: 评论ID
          example: 1
          format: int64
        roleId:
          type: integer
          description: 角色ID
          example: 1
          format: int64
        roleInfo:
          $ref: '#/components/schemas/basic_role_info'
        userInfo:
          $ref: '#/components/schemas/bazaar_core_user_info'
        postId:
          type: integer
          description: 问惑ID
          example: 1
          format: int64
        text:
          type: string
          description: 评论内容
          example: 评论内容
        createTime:
          type: integer
          description: 创建时间
          example: 1747294491610
          format: int64
        rating:
          $ref: '#/components/schemas/bazaar_rating'
    bazaar_comment_list_resp:
      type: object
      required:
        - list
        - total
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/bazaar_comment_show_item'
        total:
          type: number
          description: 总数量
          example: 100
          format: integer
    bazaar_rating_add_resp:
      type: object
      required:
        - id
        - createTime
      properties:
        id:
          type: integer
          description: 评价id
          example: 1234567890
          format: int64
        createTime:
          type: integer
          description: 评价时间
          example: 1747294491610
          format: int64
    rating_add:
      type: object
      description: 用户评价
      required:
        - star
        - text
        - fromRoleId
        - toRoleId
      properties:
        fromRoleId:
          type: integer
          description: 评价来源角色id
          example: 1234567890
          format: int64
        toRoleId:
          type: integer
          description: 被评价的角色id
          example: 1234567890
          format: int64
        star:
          type: integer
          format: int32
          description: 用户评分，1 到 5 之间的整数
          minimum: 1
          maximum: 5
          example: 4
        text:
          type: string
          description: ''
          minLength: 0
          maxLength: 30
          example: 谢谢您的解析，非常准确！
    bazaar_topic_show_item:
      type: object
      properties:
        id:
          type: integer
          description: 话题ID
          example: 1234567890
          format: int64
        text:
          type: string
          description: 话题文本
          example: 占卜
        hot:
          type: integer
          description: 热度
          example: 100
          format: int64
      required:
        - id
        - text
    bazaar_hot_topic_resp:
      type: object
      required:
        - list
        - lastRefreshTime
      properties:
        list:
          type: array
          description: 热门话题列表
          items:
            $ref: '#/components/schemas/bazaar_topic_show_item'
          required:
            - id
            - text
            - hot
        lastRefreshTime:
          type: integer
          description: 上一次刷新时间
          example: 1716028800000
          format: int64
    bazaar_group_divination_report_resp:
      type: object
      required:
        - reportDate
        - mostFocusedTopic
        - averageFortuneScore
        - mostCommentedQuestion
        - generatedAt
      properties:
        reportDate:
          type: string
          description: 报告对应的日期
          example: '2023-05-24'
          format: date-time
        mostFocusedTopic:
          type: object
          required:
            - id
            - text
          properties:
            id:
              type: integer
              description: 话题ID
              example: 1234567890
            text:
              type: string
              description: 话题文本
              example: 爱情运势
        averageFortuneScore:
          type: number
          description: 平均运势分数
          example: 75.5
        mostCommentedQuestion:
          type: string
          description: 最多回答的问惑
          example: 怎么才能让我的财运变好？
        generatedAt:
          type: string
          description: 报告生成时间
          example: '2023-05-24T20:00:00Z'
          format: date-time
    location:
      type: object
      required:
        - province
        - city
        - district
      properties:
        province:
          type: string
          description: 出生地-省份
          example: 浙江省
        city:
          type: string
          description: 出生地-城市
          example: 杭州市
        district:
          type: string
          description: 出生地-区
          example: 滨江区
    bazaar_user_register_req:
      type: object
      required:
        - gender
        - birthTime
        - birthPlace
        - currentPlace
        - astroLevel
        - astroType
      properties:
        gender:
          type: number
          description: 玩家性别，e.g. 0男  1女
          enum:
            - 0
            - 1
          example: 0
        birthTime:
          type: string
          description: 出生日期，e.g. 2025-01-01
          example: 2025-01-01 00:00
          format: date-time
        astroLevel:
          type: integer
          description: 星巫等级
          example: 1
          format: int32
        astroType:
          type: integer
          description: 星巫类型(游戏配表决定)
          example: 1
          format: int32
        birthPlace:
          $ref: '#/components/schemas/location'
        currentPlace:
          $ref: '#/components/schemas/location'
    bazaar_user_register_res:
      type: object
      required:
        - roleId
        - createTime
      properties:
        roleId:
          type: integer
          description: 角色ID
          example: 1234567890
          format: int64
        createTime:
          type: number
          description: 创建时间
          example: 1547516362459
          format: int64
    bazaar_user_info:
      type: object
      required:
        - gender
        - birthTime
        - birthPlace
        - currentPlace
        - astroLevel
        - astroType
      properties:
        gender:
          type: number
          description: 玩家性别，e.g. 0男  1女
          enum:
            - 0
            - 1
          example: 0
        birthTime:
          type: string
          description: 出生日期，e.g. 2025-01-01
          example: 2025-01-01 00:00
          format: date-time
        birthPlace:
          $ref: '#/components/schemas/location'
        currentPlace:
          $ref: '#/components/schemas/location'
        astroLevel:
          type: integer
          description: 星巫等级
          example: 1
          format: int32
        astroType:
          type: integer
          description: 星巫类型(游戏配表决定)
          example: 1
          format: int32
    bazaar_user_profile:
      type: object
      required:
        - roleId
        - roleInfo
        - averageRating
        - totalReviews
      properties:
        roleId:
          type: integer
          description: 角色ID
          example: 1234567890
          format: int64
        roleInfo:
          $ref: '#/components/schemas/basic_role_info'
        userInfo:
          $ref: '#/components/schemas/bazaar_user_info'
        averageRating:
          type: number
          description: 平均评分
          example: 4.5
          format: double
        totalReviews:
          type: integer
          description: 解惑被评分总次数
          example: 100
          format: int64
    bazaar_user_profile_for_server:
      type: object
      required:
        - roleId
        - averageRating
        - totalReviews
      properties:
        roleId:
          type: integer
          description: 角色ID
          example: 1234567890
          format: int64
        averageRating:
          type: number
          description: 平均评分
          example: 4.5
          format: double
        totalReviews:
          type: integer
          description: 解惑被评分总次数
          example: 100
          format: int64
    bazaar_user_update_req:
      type: object
      properties:
        gender:
          type: number
          description: 玩家性别，e.g. 0男  1女
          enum:
            - 0
            - 1
          example: 0
        birthTime:
          type: string
          description: 出生日期，e.g. 2025-01-01
          example: 2025-01-01 00:00
          format: date-time
        astroLevel:
          type: integer
          description: 星巫等级
          example: 1
          format: int32
        astroType:
          type: integer
          description: 星巫类型(游戏配表决定)
          example: 1
          format: int32
        birthPlace:
          $ref: '#/components/schemas/location'
        currentPlace:
          $ref: '#/components/schemas/location'
    base_resp:
      type: object
      additionalProperties: true
    bazaar_rating_show_item:
      allOf:
        - $ref: '#/components/schemas/bazaar_rating'
        - type: object
          properties:
            id:
              type: integer
              description: 评分ID
              example: 1234567890
              format: int64
            roleId:
              type: integer
              description: 角色ID
              example: 1234567890
              format: int64
            roleInfo:
              $ref: '#/components/schemas/basic_role_info'
            userInfo:
              $ref: '#/components/schemas/bazaar_core_user_info'
            commentId:
              type: integer
              description: 解惑的评论ID
              example: 1234567890
              format: int64
            postId:
              type: integer
              description: 问题ID
              example: 1234567890
              format: int64
    bazaar_user_rating_list:
      type: object
      required:
        - list
        - total
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/bazaar_rating_show_item'
        total:
          type: number
          description: 总数量
          example: 100
          format: integer
    horoscope_planetary_aspects_resp:
      type: object
      required:
        - title
        - content
      properties:
        title:
          type: string
          description: 标题
          example: 今日星象
        content:
          type: array
          description: 内容
          example:
            - 00:15 木星进入巨蟹座3宫
            - 形成持续至6月4日的有利相位，带来认知升级，水星逆行促使你重新梳理知识体系，上午9 - 11点适合系统性知识整理；行动赋能，下午15 - 17点适合签署协议或启动新项目；情感共振，强化家庭与情感联结，晚间21 - 23点适合进行家庭对话。
            - 12:30 太阳与海王星形成135度相位
            - 巨蟹座迎来本命月关键转折日，情绪沉淀后迎来行动突破，在生活各方面完成蜕变循环。
          items:
            type: string
            description: 行星相位
            example: 00:15 木星进入巨蟹座3宫
            format: string
    horoscope_daily_forecast_resp:
      type: object
      required:
        - fortune
        - time
        - user_constellation
        - in_water_reversal
        - day_for_water_reversal_remain
        - day_for_next_water_reversal
        - title
        - content
        - score
        - lucky_color
      properties:
        fortune:
          type: string
          description: 透传，玩家选择的测运类型
          example: 事业与学业
          format: string
        time:
          type: string
          description: 玩家选择的时间区间。如果是本周，返回 2025-05-11 ~ 2025-05-17
          example: 2025-05-11 ~ 2025-05-17
          format: string
        user_constellation:
          type: string
          description: 玩家的星座
          example: 射手座
          format: string
        in_water_reversal:
          type: boolean
          description: 是否在水逆期
          example: true
          format: boolean
        day_for_water_reversal_remain:
          type: integer
          description: 在水逆期时，剩余天数
          example: 0
          format: int64
        day_for_next_water_reversal:
          type: integer
          description: 不在水逆期，玩家的星座距离下一次水逆的时间
          example: 21
          format: int64
        title:
          type: string
          description: 标题
          example: 事业与学业运势解读
          format: string
        content:
          type: array
          description: 内容
          items:
            type: object
            required:
              - title
              - content
            properties:
              title:
                type: string
                description: 标题
                example: 2025年5月30日双子座事业趋势分析
              content:
                type: string
                description: 内容
                example: 6月1日火星进入天秤座六合水星，利于谈判、合作项目推进，适合建立新职场关系。6月4日月亮过境第10宫合木星，事业运上升，有望获得上级认可或新机会降临。6月8日金星对冲天王星，财务决策需谨慎，避免冒险投资影响事业发展。
        score:
          type: integer
          description: 整体打分
          example: 85
          format: int64
        lucky_color:
          type: string
          description: 今日幸运色
          example: 深蓝色
          format: string
        lucky_color_code:
          type: string
          description: 今日幸运色具体色值
          example: '#FF7D00'
          format: string
    comment_rank_resp:
      type: object
      required:
        - list
        - updateTime
      properties:
        list:
          type: array
          description: 排行榜列表
          items:
            type: object
            required:
              - roleId
              - commentCount
              - rank
              - jobId
              - gender
              - subGender
              - headPaintId
              - bodyPaintId
            properties:
              roleId:
                type: integer
                description: 角色ID
                example: 123456
              roleName:
                type: string
                description: 角色名称
                example: 星巫大师
              commentCount:
                type: integer
                description: 解惑次数
                example: 100
              lastCommentTime:
                type: integer
                description: 最后一次解惑时间戳
                example: 1640995200000
              rank:
                type: integer
                description: 排名
                example: 1
              jobId:
                type: integer
                description: 职业ID
                example: 1
              gender:
                type: integer
                description: 性别
                example: 1
              subGender:
                type: integer
                description: 子性别
                example: 0
              headPaintId:
                type: integer
                description: 头部涂装ID
                example: 0
              bodyPaintId:
                type: integer
                description: 身体涂装ID
                example: 0
        updateTime:
          type: integer
          description: 排行榜更新时间戳
          example: 1640995200000
        weekStart:
          type: string
          description: 周开始时间戳
          example: '2025-07-14 00:00:00'
        weekEnd:
          type: string
          description: 周结束时间戳
          example: '2025-07-20 23:59:59'
    commonMessageModuleId:
      type: integer
      description: |
        | 模块id|模块名字 |
        | --- | ----|
        | 10001 | 吃饭奇遇|
      enum:
        - 10001
    cloudGameNotifyBuyMonthCardData:
      type: object
      required:
        - urs
        - serverId
        - expireAt
      properties:
        urs:
          description: 通行证账号
          type: string
          example: <EMAIL>
        status:
          description: 月卡状态 (0=>从未开通, 1 => 开通正常, 2 => 开通已过期)
          type: number
          enum:
            - 0
            - 1
            - 2
          example: 0
        serverId:
          type: number
          example: 200
        expireAt:
          description: 月卡资格过期时间戳(单位s)
          type: number
          example: 1662249600
    cloudGameNotifyBuyMonthCardRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/cloudGameNotifyBuyMonthCardData'
  responses:
    CompetitionOptionsRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/CompetitionOptions'
    WebCompetitionListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/WebCompetitionList'
    GetCoupleInfoRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GetCoupleInfoData'
    ActivityFriendIdsRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                properties:
                  list:
                    type: array
                    items:
                      description: 好友id
                      type: number
                      example: 10013
    CloudGameMonthCardShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CloudGameMonthCardData'
    CloudGameDurationShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CloudGameDurationShowData'
    CloudGameNotifyBuyMonthCard:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/cloudGameNotifyBuyMonthCardRes'
    CloudGameNotifyYuanBaoChargeRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CloudGameNotifyYuanbaoChangeData'
    CloudGameDurationChangeRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CloudGameDurationChangeData'
    MomentPickListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - meta
                properties:
                  list:
                    $ref: '#/components/schemas/MomentPickList'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
    GuildPhotoWallListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - photoCnt
                properties:
                  list:
                    $ref: '#/components/schemas/GuildPhotoWallList'
                  photoCnt:
                    description: 帮会下图片总数量(aka 普通照片墙图片总数之和)
                    type: number
                    example: 10
    GuildPhotoWallPhotoShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GuildPhotoWallPhoto'
    PhotoWallShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/PhotoWallShow'
    MultiGardenPhotoWallListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - photoCnt
                properties:
                  list:
                    $ref: '#/components/schemas/MultiGardenPhotoWallList'
                  photoCnt:
                    description: 帮会下图片总数量(aka 普通照片墙图片总数之和)
                    type: number
                    example: 10
    MultiGardenPhotoWallPhotoShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MultiGardenPhotoWallPhoto'
    MultiGardenPhotoWallShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MultiGardenPhotoWallShow'
    NoteMailSendRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/NoteMailSendAppend'
    NoteMailInboxShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/NoteMailInboxShow'
    NoteMailInBoxListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/NoteMailInBoxList'
    GuildPhotoWallNotificationListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GuildPhotoWallNotificationList'
    GuildPhotoWallCommentListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GuildPhotoWallCommentList'
    PhotoWallAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - id
                properties:
                  id:
                    type: number
                    description: 新增墙的id
                    example: 1
    MultiGardenPhotoWallNotificationListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MultiGardenPhotoWallNotificationList'
    MultiGardenPhotoWallCommentListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MultiGardenPhotoWallCommentList'
    MultiGardenPhotoWallAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - id
                properties:
                  id:
                    type: number
                    description: 新增墙的id
                    example: 1
    ApiOkRes:
      description: OK
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiOk'
    ForceEventRemarkRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ForceEventRemark'
    AppearancePaintGetRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/AppearancePaintGetRet'
    SkillComboAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/SkillComboAddRet'
    SkillComboListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/SkillComboListRet'
    SkillComboGetRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/SkillComboGetRet'
    DamageStatAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/DamageStatAddRet'
    DamageStatShareInfoRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/DamageStatItem'
    DamageStatListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/DamageStatItem'
    BustPhotoAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/BustPhotoAddRet'
    GardenPhotoUnitAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GardenPhotoUnitAddRet'
    BustPhotoGetRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/BustPhotoItem'
    GardenPhotoUnitGetRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/GardenPhotoUnitItem'
    GardenPhotoUnitListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/GardenPhotoUnitItem'
    BustPhotoListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/BustPhotoItem'
    ForceEventAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ForceEventAddRet'
    ForceEventListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - count
                properties:
                  list:
                    type: array
                    items:
                      allOf:
                        - $ref: '#/components/schemas/ForceEventAdd'
                        - $ref: '#/components/schemas/ForceEventAddRet'
                        - $ref: '#/components/schemas/ForceEventRemark'
                        - $ref: '#/components/schemas/ForceEventtat'
                        - $ref: '#/components/schemas/ForceEventAction'
                  count:
                    type: number
                    description: 总数, 周六23点59拿到道具，应该是本周六-下周五的结果的数量总和
                    example: 50
    BanAccountShowRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/BanAccountShow'
    ApiActionRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ApiAction'
    GmWeekRenQiRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/WeekRenQi'
    GmServerListMergeServerListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: array
                items:
                  $ref: '#/components/schemas/GmServerListMergeServerItem'
    PlayerGetProfileRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/PlayerGetProfile'
    ServerAnnalScoreRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/ServerAnnalScore'
    GetServerAnnalEventListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/ServerAnnalEvent'
    GetPlayerAnnalEventListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                type: object
                required:
                  - list
                  - count
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/PlayerAnnalEvent'
                  count:
                    type: number
                    example: 3
    PlayerCopyMoments:
      description: Example response
      content:
        application/json:
          schema:
            description: 复制朋友圈动态返回
            type: object
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/CopyMomentsRes'
            required:
              - code
              - data
          examples:
            example-1:
              value:
                code: 0
                data:
                  count: 10
    ShowWishList:
      description: Example response
      content:
        application/json:
          schema:
            description: ''
            type: object
            properties:
              code:
                type: number
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/ShowWishListItem'
            required:
              - code
              - data
          examples:
            example-1:
              value:
                code: 0
                data:
                  list:
                    - id: 0
                      roleId: 0
                      type: string
                      payload:
                        commodityId: 0
                        commodityIndex: 0
                        itemId: 0
                        shopId: 0
                      createTime: 0
    FullFillWishList:
      description: Example response
      content:
        application/json:
          schema:
            description: ''
            type: object
            properties:
              code:
                type: number
              data:
                type: object
                properties:
                  list:
                    type: array
                    uniqueItems: true
                    minItems: 1
                    items:
                      required:
                        - id
                        - type
                        - adderId
                        - adderName
                        - addTime
                        - buyerId
                        - buyerName
                        - fullFillTime
                      properties:
                        id:
                          type: number
                        type:
                          type: string
                          minLength: 1
                        adderId:
                          type: number
                        adderName:
                          type: string
                          minLength: 1
                        addTime:
                          type: number
                        buyerId:
                          type: number
                        buyerName:
                          type: string
                          minLength: 1
                        fullFillTime:
                          type: number
                        payload:
                          type: object
                          properties:
                            commodityId:
                              type: number
                            itemId:
                              type: number
                            shopId:
                              type: number
                            commodityIndex:
                              type: number
                          required:
                            - commodityId
                            - itemId
                            - shopId
                            - commodityIndex
                  meta:
                    type: object
                    properties:
                      curPage:
                        type: number
                      totalPage:
                        type: number
                      totalCount:
                        type: number
                    required:
                      - curPage
                      - totalPage
                      - totalCount
                required:
                  - list
                  - meta
            required:
              - code
              - data
          examples:
            example-1:
              value:
                code: 0
                data:
                  list:
                    - id: 0
                      type: string
                      adderId: 0
                      adderName: string
                      addTime: 0
                      buyerId: 0
                      buyerName: string
                      fullFillTime: 0
                      payload:
                        commodityId: 0
                        itemId: 0
                        shopId: 0
                        commodityIndex: 0
                  meta:
                    curPage: 0
                    totalPage: 0
                    totalCount: 0
    ActivityTakePhotoGetMyPhotosRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: array
                description: 数组类型，数组顺序对应地点顺序，即：下标=地点ID-1
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/ActivityTakePhotoMyListItem'
    ActivityTakePhotoGetLocationSelectedPhotoRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                required:
                  - imgUrl
                properties:
                  imgUrl:
                    type: string
    GardenDetailRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                $ref: '#/components/schemas/GardenBasicInfo'
    GardenRankRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                properties:
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/GardenBasicInfo'
    GardenEvalutionListRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                required:
                  - count
                  - list
                properties:
                  count:
                    type: number
                    description: 总数
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/GardenEvalutionListItem'
    GardenEvaluationCommentListRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                required:
                  - count
                  - list
                properties:
                  count:
                    type: number
                    description: 总数
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/GardenEvalutionCommentListItem'
    GardenInformListRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                required:
                  - count
                  - list
                properties:
                  count:
                    type: number
                    description: 总数
                  list:
                    type: array
                    items:
                      $ref: '#/components/schemas/GardenInformListItem'
    GardenInformUnreadRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                required:
                  - count
                properties:
                  count:
                    type: number
                    description: 未读消息数
    idRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/idData'
    TimesSquarePhotoAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/TimesSquarePhotoAddRet'
    MemeAddRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/MemeAddRet'
    TimesSquarePhotoListRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/TimesSquarePhotoListRet'
    MemeListRes:
      description: ok
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
              data:
                type: object
                required:
                  - list
                properties:
                  list:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: number
                          description: 表情包id
                          example: 342
                        url:
                          type: string
                          description: 表情包url
                          example: https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
                        clientId:
                          type: number
                          description: 客户端id
                          example: 10
                        auditStatus:
                          $ref: '#/components/schemas/EAuditStatus'
                        createTime:
                          type: number
                          description: 创建时间
                          example: 1620628236384
    cloudGameNotifyBuyMonthCardRes:
      description: OK
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                type: number
                example: 0
              data:
                $ref: '#/components/schemas/cloudGameNotifyBuyMonthCardData'
  securitySchemes:
    loginSkey:
      description: skey登录校验
      type: apiKey
      in: query
      name: skey
    gmAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
