# AstrologyComponent 接口业务逻辑与SQL映射文档

## 数据库定义
```sql
-- nodejs_yxb.nsh_astrology_bazaar_comment definition

CREATE TABLE `nsh_astrology_bazaar_comment` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PostId` bigint(20) NOT NULL COMMENT '帖子ID',
  `PostRoleId` bigint(20) NOT NULL COMMENT '帖子角色ID',
  `RoleId` bigint(20) NOT NULL COMMENT '评论的角色ID',
  `Text` text NOT NULL COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0: 正常, -1: 删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_uniq_post_role` (`PostId`,`RoleId`),
  KEY `idx_role_id` (`RoleId`),
  KEY `idx_post_status_id` (`PostId`,`Status`,`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫-问惑-评论';


-- nodejs_yxb.nsh_astrology_bazaar_post definition

CREATE TABLE `nsh_astrology_bazaar_post` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `TopicId` int(11) NOT NULL COMMENT '话题ID',
  `Question` text NOT NULL COMMENT '问题',
  `DicePlanet` varchar(32) NOT NULL COMMENT '行星',
  `DiceConstellation` varchar(32) NOT NULL COMMENT '星座',
  `DiceHouse` tinyint(4) NOT NULL COMMENT '星座宫位',
  `CommentCount` int(11) NOT NULL DEFAULT '0' COMMENT '评论数',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0: 正常, -1: 删除',
  PRIMARY KEY (`ID`),
  KEY `idx_status_comment_count` (`Status`,`CommentCount`),
  KEY `idx_role_status_id` (`RoleId`,`Status`,`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫-问惑-帖子';


-- nodejs_yxb.nsh_astrology_bazaar_rating definition

CREATE TABLE `nsh_astrology_bazaar_rating` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PostId` bigint(20) NOT NULL COMMENT '问题ID',
  `CommentId` bigint(20) NOT NULL COMMENT '评论ID',
  `ToRoleId` bigint(20) NOT NULL COMMENT '被评分的角色ID',
  `FromRoleId` bigint(20) NOT NULL COMMENT '评分的角色ID',
  `Star` tinyint(4) NOT NULL COMMENT '解惑评分',
  `Text` text NOT NULL COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0: 正常, -1: 删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_comment_id` (`CommentId`),
  KEY `idx_post_id` (`PostId`),
  KEY `idx_to_role_status` (`ToRoleId`,`Status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫-问惑-评价';


-- nodejs_yxb.nsh_astrology_post_daily_hot definition

CREATE TABLE `nsh_astrology_post_daily_hot` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
  `PostId` bigint(20) NOT NULL COMMENT '帖子ID',
  `Hot` int(11) NOT NULL COMMENT '热度',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_post_id_ds` (`PostId`,`DS`),
  KEY `idx_ds_hot_id` (`DS`,`Hot`,`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星座帖子每日热门表';


-- nodejs_yxb.nsh_astrology_post_topic definition

CREATE TABLE `nsh_astrology_post_topic` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `Text` varchar(255) NOT NULL COMMENT '话题文本',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫话题表';


-- nodejs_yxb.nsh_astrology_post_topic_daily_hot definition

CREATE TABLE `nsh_astrology_post_topic_daily_hot` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
  `TopicId` bigint(20) NOT NULL COMMENT '话题ID',
  `Hot` int(11) NOT NULL COMMENT '热度',
  `CreateTime` bigint(20) NOT NULL COMMENT '建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_topic_id_ds` (`TopicId`,`DS`),
  KEY `idx_ds_hot_update` (`DS`,`Hot`,`UpdateTime`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星座帖子每日热门表';


-- nodejs_yxb.nsh_astrology_user definition

CREATE TABLE `nsh_astrology_user` (
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `Gender` tinyint(1) NOT NULL COMMENT '性别 0 男 1 女',
  `BirthTime` bigint(20) NOT NULL COMMENT '出生时间戳',
  `BirthPlace` varchar(255) NOT NULL COMMENT '出生地',
  `CurrentPlace` varchar(255) NOT NULL COMMENT '当前地',
  `AstroLevel` int(10) NOT NULL DEFAULT '0' COMMENT '星巫等级',
  `AstroType` int(10) NOT NULL DEFAULT '0' COMMENT '星巫类型',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间戳',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间戳',
  `RatingSum` bigint(20) NOT NULL DEFAULT '0' COMMENT '解惑评分总和',
  `RatingCount` int(10) NOT NULL DEFAULT '0' COMMENT '解惑评分次数',
  `CommentCount` int(10) NOT NULL DEFAULT '0' COMMENT '解惑总次数',
  `LastCommentTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '最后一次解惑时间戳，用于相同解惑次数时的排序',
  PRIMARY KEY (`RoleId`),
  KEY `idx_comment_count_time` (`CommentCount`,`LastCommentTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='星巫用户资料';


-- nodejs_yxb.nsh_astrology_user_daily_forecast definition

CREATE TABLE `nsh_astrology_user_daily_forecast` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
  `RoleId` bigint(20) NOT NULL COMMENT '用户ID',
  `FortuneScore` int(11) NOT NULL COMMENT '运势分数',
  `CreateTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uk_role_id_ds` (`RoleId`,`DS`),
  KEY `idx_ds_fortune` (`DS`,`FortuneScore`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='用户每日运势表';

```


## 1. POST /astrology/bazaar/post/add - 添加占卜帖子

### 业务逻辑
1. 验证问题文本长度和敏感词检查
2. 创建占卜帖子记录
3. 保存话题信息
4. 增加话题每日热度
5. 可选同步到动态

### SQL执行序列
```sql
-- 1. 插入占卜帖子
INSERT INTO nsh_astrology_bazaar_post (
    RoleId, TopicId, Question, DicePlanet, DiceConstellation, DiceHouse,
    CommentCount, CreateTime, UpdateTime, Status
) VALUES (?, ?, ?, ?, ?, ?, 0, ?, ?, ?)

-- 2. 保存话题信息
INSERT INTO nsh_astrology_post_topic (ID, Text, CreateTime, UpdateTime)
VALUES (?, ?, ?, ?)
ON DUPLICATE KEY UPDATE UpdateTime = ?, Text = ?

-- 3. 增加话题每日热度
INSERT INTO nsh_astrology_post_topic_daily_hot (DS, TopicId, Hot, CreateTime, UpdateTime)
VALUES (?, ?, 1, ?, ?)
ON DUPLICATE KEY UPDATE Hot = Hot + 1, UpdateTime = ?
```

### 索引使用分析
- **SQL 1**: INSERT操作，使用PRIMARY KEY (`ID`)自增
- **SQL 2**: INSERT ON DUPLICATE KEY，使用PRIMARY KEY (`ID`)检查重复
- **SQL 3**: INSERT ON DUPLICATE KEY，使用UNIQUE KEY `idx_topic_id_ds` (`TopicId`,`DS`)检查重复

---

## 2. GET /astrology/bazaar/post/list - 获取公开占卜帖子列表

### 业务逻辑
1. 根据话题ID筛选帖子（可选）
2. 按评论数降序排列
3. 分页查询帖子列表
4. 获取发帖用户的角色信息和星巫信息
5. 过滤当前用户已评论的帖子
6. 组装展示数据

### SQL执行序列
```sql
-- 1. 查询公开帖子列表
SELECT * FROM nsh_astrology_bazaar_post
WHERE Status = ? [AND TopicId IN (?)]
ORDER BY CommentCount DESC
LIMIT ? OFFSET ?

-- 2. 统计总数
SELECT COUNT(*) FROM nsh_astrology_bazaar_post
WHERE Status = ? [AND TopicId IN (?)]

-- 3. 获取发帖用户角色信息
SELECT RoleId, RoleName, Gender, SubGender, JobId, HeadPaintId, BodyPaintId
FROM nsh_roleinfo
WHERE RoleId IN (?)
LIMIT ?

-- 4. 获取发帖用户星巫信息
SELECT RoleId, Gender, AstroLevel, AstroType
FROM nsh_astrology_user
WHERE RoleId IN (?)
LIMIT ?

-- 5. 过滤当前用户已评论的帖子
SELECT PostId FROM nsh_astrology_bazaar_comment
WHERE RoleId = ? AND PostId IN (?)
LIMIT ?
```

### 索引使用分析
- **SQL 1**: 使用KEY `idx_status_comment_count` (`Status`,`CommentCount`)进行WHERE过滤和ORDER BY排序
- **SQL 2**: 使用KEY `idx_status_comment_count` (`Status`,`CommentCount`)进行WHERE过滤的COUNT统计
- **SQL 3**: 使用PRIMARY KEY (`RoleId`)进行IN查询（nsh_roleinfo表）
- **SQL 4**: 使用PRIMARY KEY (`RoleId`)进行IN查询（nsh_astrology_user表）
- **SQL 5**: 使用KEY `idx_role_id` (`RoleId`)进行WHERE过滤，PostId IN条件需要额外过滤

---

## 3. GET /astrology/bazaar/post/self_list - 获取用户自己的占卜帖子列表

### 业务逻辑
1. 查询指定用户的帖子
2. 可按话题ID筛选
3. 按ID降序排列（最新优先）
4. 分页查询

### SQL执行序列
```sql
-- 1. 查询用户自己的帖子
SELECT * FROM nsh_astrology_bazaar_post
WHERE RoleId = ? AND Status = ? [AND TopicId IN (?)]
ORDER BY ID DESC
LIMIT ? OFFSET ?

-- 2. 统计用户帖子总数
SELECT COUNT(*) FROM nsh_astrology_bazaar_post
WHERE RoleId = ? AND Status = ? [AND TopicId IN (?)]

-- 3-5. 获取用户信息（同接口2）
```

### 索引使用分析
- **SQL 1**: 使用KEY `idx_role_status_id` (`RoleId`,`Status`,`ID`)进行WHERE过滤和ORDER BY排序
- **SQL 2**: 使用KEY `idx_role_status_id` (`RoleId`,`Status`,`ID`)进行WHERE过滤的COUNT统计
- **SQL 3-5**: 同接口2的索引使用分析

---

## 4. GET /astrology/bazaar/post/interpret - 获取占卜结果解读

### 业务逻辑
1. 根据帖子ID获取占卜信息
2. 调用外部AI服务解读占卜结果

### SQL执行序列
```sql
-- 1. 获取占卜帖子详情
SELECT * FROM nsh_astrology_bazaar_post
WHERE ID = ? AND Status = ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`ID`)进行精确查找，Status条件需要额外过滤

---

## 5. POST /astrology/bazaar/comment/add - 添加评论

### 业务逻辑
1. 验证评论文本长度和敏感词
2. 验证帖子存在性
3. 检查用户是否已评论过该帖子
4. 添加评论记录
5. 增加帖子评论数
6. 增加帖子每日热度
7. 增加用户解惑次数统计

### SQL执行序列
```sql
-- 1. 验证帖子存在
SELECT * FROM nsh_astrology_bazaar_post
WHERE ID = ? AND Status = ?

-- 2. 检查用户是否已评论
SELECT ID FROM nsh_astrology_bazaar_comment
WHERE RoleId = ? AND PostId = ? AND Status = ?

-- 3. 插入评论
INSERT INTO nsh_astrology_bazaar_comment (
    PostId, PostRoleId, RoleId, Text, CreateTime, UpdateTime, Status
) VALUES (?, ?, ?, ?, ?, ?, ?)

-- 4. 增加帖子评论数
UPDATE nsh_astrology_bazaar_post
SET CommentCount = CommentCount + 1
WHERE ID = ? AND Status = ?

-- 5. 增加帖子每日热度
INSERT INTO nsh_astrology_post_daily_hot (DS, PostId, Hot, CreateTime, UpdateTime)
VALUES (?, ?, 1, ?, ?)
ON DUPLICATE KEY UPDATE Hot = Hot + 1, UpdateTime = ?

-- 6. 增加用户解惑次数
UPDATE nsh_astrology_user
SET CommentCount = COALESCE(CommentCount, 0) + 1,
    LastCommentTime = ?, UpdateTime = ?
WHERE RoleId = ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`ID`)进行精确查找，Status条件需要额外过滤
- **SQL 2**: 使用UNIQUE KEY `idx_uniq_post_role` (`PostId`,`RoleId`)进行重复检查，Status条件需要额外过滤
- **SQL 3**: INSERT操作，使用PRIMARY KEY (`ID`)自增，同时会更新UNIQUE KEY `idx_uniq_post_role`
- **SQL 4**: 使用PRIMARY KEY (`ID`)进行精确更新，Status条件需要额外过滤
- **SQL 5**: INSERT ON DUPLICATE KEY，使用UNIQUE KEY `idx_post_id_ds` (`PostId`,`DS`)检查重复
- **SQL 6**: 使用PRIMARY KEY (`RoleId`)进行精确更新

---

## 6. GET /astrology/bazaar/comment/list - 获取评论列表

### 业务逻辑
1. 验证帖子存在性
2. 分页查询评论列表
3. 获取评论用户的角色信息和星巫信息
4. 获取评论的评价信息
5. 统计评论总数

### SQL执行序列
```sql
-- 1. 验证帖子存在
SELECT * FROM nsh_astrology_bazaar_post
WHERE ID = ? AND Status = ?

-- 2. 查询评论列表
SELECT * FROM nsh_astrology_bazaar_comment
WHERE PostId = ? AND Status = ?
ORDER BY ID DESC
LIMIT ? OFFSET ?

-- 3. 统计评论总数
SELECT COUNT(*) FROM nsh_astrology_bazaar_comment
WHERE PostId = ? AND Status = ?

-- 4. 获取评论用户角色信息
SELECT RoleId, RoleName, Gender, SubGender, JobId, HeadPaintId, BodyPaintId
FROM nsh_roleinfo
WHERE RoleId IN (?)
LIMIT ?

-- 5. 获取评论用户星巫信息
SELECT RoleId, Gender, AstroLevel, AstroType
FROM nsh_astrology_user
WHERE RoleId IN (?)
LIMIT ?

-- 6. 获取评论的评价信息
SELECT * FROM nsh_astrology_bazaar_rating
WHERE CommentId IN (?) AND Status = ?
ORDER BY ID DESC
LIMIT ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`ID`)进行精确查找，Status条件需要额外过滤
- **SQL 2**: 使用KEY `idx_post_status_id` (`PostId`,`Status`,`ID`)进行WHERE过滤和ORDER BY排序
- **SQL 3**: 使用KEY `idx_post_status_id` (`PostId`,`Status`,`ID`)进行WHERE过滤的COUNT统计
- **SQL 4**: 使用PRIMARY KEY (`RoleId`)进行IN查询（nsh_roleinfo表）
- **SQL 5**: 使用PRIMARY KEY (`RoleId`)进行IN查询（nsh_astrology_user表）
- **SQL 6**: 使用KEY `idx_comment_id` (`CommentId`)进行IN查询，Status条件需要额外过滤

---

## 7. GET /astrology/bazaar/hot_topic - 获取热门话题

### 业务逻辑
1. 根据当前时间计算日期字符串
2. 获取当日话题热度排行
3. 获取话题文本内容
4. 组装展示数据

### SQL执行序列
```sql
-- 1. 获取当日热门话题
SELECT TopicId, Hot FROM nsh_astrology_post_topic_daily_hot
WHERE DS = ?
ORDER BY Hot DESC, UpdateTime DESC
LIMIT ?

-- 2. 获取话题文本
SELECT ID, Text FROM nsh_astrology_post_topic
WHERE ID IN (?)
LIMIT ?
```

### 索引使用分析
- **SQL 1**: 使用KEY `idx_ds_hot_update` (`DS`,`Hot`,`UpdateTime`)进行WHERE过滤和ORDER BY排序
- **SQL 2**: 使用PRIMARY KEY (`ID`)进行IN查询

---

## 8. GET /astrology/bazaar/group_divination_report - 获取群体占卜报告

### 业务逻辑
1. 计算报告日期（前一天）
2. 获取最受关注的话题
3. 计算平均运势分数
4. 获取最多评论的问题

### SQL执行序列
```sql
-- 1. 获取最受关注话题（复用接口7的SQL）
SELECT TopicId, Hot FROM nsh_astrology_post_topic_daily_hot
WHERE DS = ?
ORDER BY Hot DESC, UpdateTime DESC
LIMIT 1

-- 2. 计算平均运势分数
SELECT AVG(FortuneScore) as avgScore
FROM nsh_astrology_user_daily_forecast
WHERE DS = ?

-- 3. 获取最多评论的帖子
SELECT PostId FROM nsh_astrology_post_daily_hot
WHERE DS = ?
ORDER BY Hot DESC, ID ASC
LIMIT 1

-- 4. 获取帖子问题内容
SELECT Question FROM nsh_astrology_bazaar_post
WHERE ID = ? AND Status = ?
```

### 索引使用分析
- **SQL 1**: 使用KEY `idx_ds_hot_update` (`DS`,`Hot`,`UpdateTime`)进行WHERE过滤和ORDER BY排序
- **SQL 2**: 使用KEY `idx_ds_fortune` (`DS`,`FortuneScore`)进行WHERE过滤和AVG聚合
- **SQL 3**: 使用KEY `idx_ds_hot_id` (`DS`,`Hot`,`ID`)进行WHERE过滤和ORDER BY排序
- **SQL 4**: 使用PRIMARY KEY (`ID`)进行精确查找，Status条件需要额外过滤

---

## 9. POST /astrology/bazaar/user/register - 用户注册

### 业务逻辑
1. 验证出生时间格式
2. 验证用户未注册
3. 创建或更新用户记录

### SQL执行序列
```sql
-- 1. 验证用户是否已注册
SELECT RoleId FROM nsh_astrology_user
WHERE RoleId = ?

-- 2. 注册用户信息
INSERT INTO nsh_astrology_user (
    RoleId, Gender, BirthTime, BirthPlace, CurrentPlace,
    AstroLevel, AstroType, CreateTime, UpdateTime
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    Gender = ?, BirthTime = ?, BirthPlace = ?, CurrentPlace = ?,
    AstroLevel = ?, AstroType = ?, UpdateTime = ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`RoleId`)进行精确查找
- **SQL 2**: INSERT ON DUPLICATE KEY，使用PRIMARY KEY (`RoleId`)检查重复

---

## 10. GET /astrology/bazaar/user/profile - 获取用户资料

### 业务逻辑
1. 获取用户角色信息
2. 获取用户星巫资料
3. 计算平均评分（需满足最小评价人数）
4. 组装展示数据

### SQL执行序列
```sql
-- 1. 获取角色信息
SELECT RoleId, RoleName, Gender, SubGender, JobId, HeadPaintId, BodyPaintId
FROM nsh_roleinfo
WHERE RoleId = ?

-- 2. 获取星巫资料
SELECT * FROM nsh_astrology_user
WHERE RoleId = ?

-- 3. 检查评价人数是否满足最小要求
SELECT COUNT(*) >= ? AS isGteMinNum
FROM (
    SELECT DISTINCT FromRoleId
    FROM nsh_astrology_bazaar_rating
    WHERE ToRoleId = ? AND Status = 0
    LIMIT ?
) AS distinct_roles
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`RoleId`)进行精确查找（nsh_roleinfo表）
- **SQL 2**: 使用PRIMARY KEY (`RoleId`)进行精确查找
- **SQL 3**: 使用KEY `idx_to_role_status` (`ToRoleId`,`Status`)进行WHERE过滤，需要DISTINCT去重

---

## 11. GET /astrology/server/bazaar/user/profile - 服务端获取用户资料

### 业务逻辑
与接口10相同，但返回数据格式略有不同（服务端专用）

### SQL执行序列
```sql
-- 使用与接口10相同的SQL查询
```

### 索引使用分析
- 同接口10的索引使用分析

---

## 12. POST /astrology/bazaar/user/profile/update - 更新用户资料

### 业务逻辑
1. 验证出生时间格式（如有）
2. 检查用户是否存在
3. 更新或创建用户资料

### SQL执行序列
```sql
-- 1. 检查用户是否存在
SELECT RoleId FROM nsh_astrology_user
WHERE RoleId = ?

-- 2a. 更新现有用户
UPDATE nsh_astrology_user
SET [动态字段] = [动态值], UpdateTime = ?
WHERE RoleId = ?

-- 2b. 创建新用户（如不存在）
INSERT INTO nsh_astrology_user (
    RoleId, [动态字段], CreateTime, UpdateTime
) VALUES (?, [动态值], ?, ?)
ON DUPLICATE KEY UPDATE
    [动态字段] = [动态值], UpdateTime = ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`RoleId`)进行精确查找
- **SQL 2a**: 使用PRIMARY KEY (`RoleId`)进行精确更新
- **SQL 2b**: INSERT ON DUPLICATE KEY，使用PRIMARY KEY (`RoleId`)检查重复

---

## 13. POST /astrology/bazaar/rating/add - 添加评价

### 业务逻辑
1. 验证评价文本长度和敏感词（可选）
2. 验证评论存在性
3. 验证评价者是帖子发布者
4. 检查是否已评价过
5. 添加评价记录
6. 更新被评价者的评分统计

### SQL执行序列
```sql
-- 1. 验证评论存在
SELECT * FROM nsh_astrology_bazaar_comment
WHERE ID = ? AND Status = ?

-- 2. 检查是否已评价
SELECT ID FROM nsh_astrology_bazaar_rating
WHERE FromRoleId = ? AND CommentId = ? AND Status = ?

-- 3. 插入评价记录
INSERT INTO nsh_astrology_bazaar_rating (
    CommentId, ToRoleId, PostId, FromRoleId, Star, Text,
    CreateTime, UpdateTime, Status
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)

-- 4. 更新用户评分统计
INSERT INTO nsh_astrology_user (
    RoleId, Gender, BirthTime, BirthPlace, CurrentPlace,
    RatingSum, RatingCount, CreateTime, UpdateTime
) VALUES (?, 0, 0, '', '', ?, 1, ?, ?)
ON DUPLICATE KEY UPDATE
    RatingSum = RatingSum + ?, RatingCount = RatingCount + 1, UpdateTime = ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`ID`)进行精确查找，Status条件需要额外过滤
- **SQL 2**: 使用KEY `idx_comment_id` (`CommentId`)进行精确查找
- **SQL 3**: INSERT操作，使用PRIMARY KEY (`ID`)自增，同时会更新UNIQUE KEY `idx_comment_id`
- **SQL 4**: INSERT ON DUPLICATE KEY，使用PRIMARY KEY (`RoleId`)检查重复

---

## 14. POST /astrology/rating/add - 添加评价(通用)

### 业务逻辑
与接口13类似，但不关联具体评论，用于通用评价场景

### SQL执行序列
```sql
-- 1. 插入通用评价记录（CommentId和PostId为0）
INSERT INTO nsh_astrology_bazaar_rating (
    CommentId, ToRoleId, PostId, FromRoleId, Star, Text,
    CreateTime, UpdateTime, Status
) VALUES (0, ?, 0, ?, ?, ?, ?, ?, ?)

-- 2. 更新用户评分统计（同接口13）
```

### 索引使用分析
- **SQL 1**: INSERT操作，使用PRIMARY KEY (`ID`)自增
- **SQL 2**: 同接口13的SQL 4索引使用分析

---

## 15. GET /astrology/bazaar/user/rating/receive_list - 获取接收的评价列表

### 业务逻辑
1. 分页查询用户接收的评价
2. 获取评价者的角色信息和星巫信息
3. 统计总评价数

### SQL执行序列
```sql
-- 1. 查询接收的评价列表
SELECT * FROM nsh_astrology_bazaar_rating
WHERE ToRoleId = ? AND Status = ?
ORDER BY ID DESC
LIMIT ? OFFSET ?

-- 2. 统计总评价数
SELECT COUNT(*) FROM nsh_astrology_bazaar_rating
WHERE ToRoleId = ? AND Status = ?

-- 3. 获取评价者角色信息
SELECT RoleId, RoleName, Gender, SubGender, JobId, HeadPaintId, BodyPaintId
FROM nsh_roleinfo
WHERE RoleId IN (?)
LIMIT ?

-- 4. 获取评价者星巫信息
SELECT RoleId, Gender, AstroLevel, AstroType
FROM nsh_astrology_user
WHERE RoleId IN (?)
LIMIT ?
```

### 索引使用分析
- **SQL 1**: 使用KEY `idx_to_role_status` (`ToRoleId`,`Status`)进行WHERE过滤，ID DESC排序需要额外排序
- **SQL 2**: 使用KEY `idx_to_role_status` (`ToRoleId`,`Status`)进行WHERE过滤的COUNT统计
- **SQL 3**: 使用PRIMARY KEY (`RoleId`)进行IN查询（nsh_roleinfo表）
- **SQL 4**: 使用PRIMARY KEY (`RoleId`)进行IN查询（nsh_astrology_user表）

---

## 16. GET /astrology/horoscope/planetary_aspects - 获取行星相位

### 业务逻辑
调用外部占星服务获取行星相位信息

### SQL执行序列
```sql
-- 无本地数据库查询，纯外部服务调用
```

### 索引使用分析
- 无数据库查询

---

## 17. GET /astrology/horoscope/daily_forecast - 获取每日运势

### 业务逻辑
1. 获取用户星巫资料
2. 调用外部服务获取运势预测
3. 保存每日运势分数（仅当日运势）

### SQL执行序列
```sql
-- 1. 获取用户资料
SELECT * FROM nsh_astrology_user
WHERE RoleId = ?

-- 2. 保存每日运势分数（仅today类型）
INSERT INTO nsh_astrology_user_daily_forecast (
    RoleId, DS, FortuneScore, CreateTime, UpdateTime
) VALUES (?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
    RoleId = ?, DS = ?, FortuneScore = ?, UpdateTime = ?
```

### 索引使用分析
- **SQL 1**: 使用PRIMARY KEY (`RoleId`)进行精确查找
- **SQL 2**: INSERT ON DUPLICATE KEY，使用UNIQUE KEY `uk_role_id_ds` (`RoleId`,`DS`)检查重复

---

## 18. POST /astrology/dice_result/interpret - 骰子结果解读

### 业务逻辑
调用外部AI服务解读骰子占卜结果

### SQL执行序列
```sql
-- 无本地数据库查询，纯外部服务调用
```

### 索引使用分析
- 无数据库查询

---

## 19. GET /astrology/comment/rank - 获取解惑次数排行榜

### 业务逻辑
1. 尝试从缓存获取排行榜
2. 如缓存不存在，使用分布式锁刷新排行榜
3. 执行复杂JOIN查询获取用户解惑统计
4. 计算排名并缓存结果

### SQL执行序列
```sql
-- 1. 复杂JOIN查询获取排行榜数据
SELECT
    nsh_astrology_user.RoleId,
    nsh_astrology_user.CommentCount,
    nsh_astrology_user.LastCommentTime,
    nsh_roleinfo.RoleName,
    nsh_roleinfo.JobId,
    nsh_roleinfo.Gender,
    nsh_roleinfo.SubGender,
    nsh_roleinfo.HeadPaintId,
    nsh_roleinfo.BodyPaintId
FROM nsh_astrology_user
INNER JOIN nsh_roleinfo ON nsh_astrology_user.RoleId = nsh_roleinfo.RoleId
WHERE nsh_astrology_user.CommentCount > 0
ORDER BY nsh_astrology_user.CommentCount DESC, nsh_astrology_user.LastCommentTime DESC
LIMIT ?
```
---