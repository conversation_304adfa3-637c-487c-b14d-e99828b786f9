```mermaid
erDiagram
    nsh_astrology_bazaar_post {
        BIGINT ID PK "帖子ID"
        BIGINT RoleId FK "角色ID"
        INT TopicId FK "话题ID"
        TEXT Question "问题"
        VARCHAR DicePlanet "行星"
        VARCHAR DiceConstellation "星座"
        TINYINT DiceHouse "星座宫位"
        INT CommentCount "评论数"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
        INT Status "0: 正常, -1: 删除"
    }

    nsh_astrology_bazaar_comment {
        BIGINT ID PK "评论ID"
        BIGINT PostId FK "帖子ID"
        BIGINT PostRoleId "帖子角色ID"
        BIGINT RoleId FK "评论的角色ID"
        TEXT Text "评论内容"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
        INT Status "0: 正常, -1: 删除"
    }

    nsh_astrology_bazaar_rating {
        BIGINT ID PK "评价ID"
        BIGINT PostId FK "问题ID"
        BIGINT CommentId FK "评论ID"
        BIGINT ToRoleId "评论角色ID"
        BIGINT FromRoleId FK "评分的角色ID"
        TINYINT Star "解惑评分"
        TEXT Text "评论内容"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
        INT Status "0: 正常, -1: 删除"
    }

    nsh_astrology_post_topic {
        BIGINT ID PK "主键ID"
        VARCHAR Text "话题文本"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
    }

    nsh_astrology_post_topic_daily_hot {
        BIGINT ID PK "主键ID"
        VARCHAR DS "日期, yyyyMMdd"
        BIGINT TopicId FK "话题ID"
        INT Hot "热度"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
    }

    nsh_astrology_user {
        BIGINT RoleId PK "角色ID"
        TINYINT Gender "性别 0 男 1 女"
        BIGINT BirthTime "出生时间戳"
        VARCHAR BirthPlace "出生地"
        VARCHAR CurrentPlace "当前地"
        INT AstroLevel "星巫等级"
        INT AstroType "星巫类型"
        BIGINT CreateTime "创建时间戳"
        BIGINT UpdateTime "更新时间戳"
        BIGINT RatingSum "解惑评分总和"
        INT RatingCount "解惑评分次数"
    }

    nsh_astrology_user_daily_forecast {
        BIGINT ID PK "主键ID"
        VARCHAR DS "日期, yyyyMMdd"
        BIGINT RoleId FK "用户ID"
        INT FortuneScore "运势分数"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
    }

    nsh_astrology_post_daily_hot {
        BIGINT ID PK "主键ID"
        VARCHAR DS "日期, yyyyMMdd"
        BIGINT PostId FK "帖子ID"
        INT Hot "热度"
        BIGINT CreateTime "创建时间"
        BIGINT UpdateTime "更新时间"
    }

    nsh_astrology_bazaar_post ||--o{ nsh_astrology_bazaar_comment : "has"
    nsh_astrology_bazaar_post ||--o{ nsh_astrology_bazaar_rating : "has"
    nsh_astrology_bazaar_comment ||--o{ nsh_astrology_bazaar_rating : "has"
    nsh_astrology_user ||--o{ nsh_astrology_bazaar_post : "posts"
    nsh_astrology_user ||--o{ nsh_astrology_bazaar_comment : "comments"
    nsh_astrology_user ||--o{ nsh_astrology_bazaar_rating : "rates"
    nsh_astrology_user ||--o{ nsh_astrology_user_daily_forecast : "has"
    nsh_astrology_post_topic ||--o{ nsh_astrology_bazaar_post : "belongs to"
    nsh_astrology_post_topic ||--o{ nsh_astrology_post_topic_daily_hot : "has"
    nsh_astrology_bazaar_post ||--o{ nsh_astrology_post_daily_hot : "has"
```
