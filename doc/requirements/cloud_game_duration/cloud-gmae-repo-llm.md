This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where content has been compressed (code blocks are separated by ⋮---- delimiter).

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
logConsumer/
  addYuanbaoLog.ts
  logConsumer.ts
  type.ts
  util.ts
constant.ts
gm.md
gm.ts
index.ts
operation.ts
service.ts
type.ts
```

# Files

## File: logConsumer/addYuanbaoLog.ts
```typescript
import { clazzLogger } from "../../../logger";
import { cloudGameDurationNotifyChargeYuanbao } from "../operation";
import { CloudGameDurationRes } from "../type";
import { AddYuanbaoLog, GameLogStruct, ParseLogRet } from "./type";
⋮----
// 2024-10-21 15:51:02 gas[36576]:[Development Build]INFO|PLAYER|[404135]***********,,<EMAIL>,,1000,,***********z1e0f3,,false
// e.PlayerId, e.AccountName, e.Yuanbao, e.Sn, e.bCloud	玩家ID，玩家URS，元宝充值数量，订单号，是否云游戏
export function parseAddYuanbaoLog(gs: GameLogStruct): ParseLogRet<AddYuanbaoLog>
⋮----
export function isAddYuanbaoLog(log: GameLogStruct): boolean
⋮----
export async function onCloudGameAddYuanbaoLog(gs: GameLogStruct): Promise<CloudGameDurationRes.NotifyChargeYuanbao>
```

## File: logConsumer/logConsumer.ts
```typescript
import { Kafka, KafkaConfig, KafkaMessage, Consumer, logLevel, EachBatchPayload } from "kafkajs";
import { getLogger } from "../../../common/logger";
import { parseGameRawLog, toBunyanLogLevel } from "./util";
import { clazzLogger } from "../../../logger";
⋮----
import { cloudGameDurationCfg } from "../../../common/config";
import { GameLogStruct, LogEventId } from "./type";
import { onCloudGameAddYuanbaoLog } from "./addYuanbaoLog";
⋮----
const bunyanLoggerCreator = (level: logLevel) =>
⋮----
function createKafkaClient()
⋮----
function getKafkaClient()
⋮----
export function getConsumer()
⋮----
function createConsumer()
⋮----
export async function listenKafkaTopic(consumer: Consumer)
⋮----
export async function processKafkaMessage(consumer: Consumer, topic: string, partition: number, message: KafkaMessage)
⋮----
async function persistLogMessage(consumer: Consumer, logItem: GameLogStruct)
⋮----
export async function transformKafkaMessage(consumer: Consumer, message: string)
```

## File: logConsumer/type.ts
```typescript
export interface GameLogStruct {
  eventTime: Date;
  eventId: number;
  payload: string;
  args: string[];
}
⋮----
export const enum LogEventId {
  /** 404135	gas;	CloudGameAddYuanbao	云游戏增加元宝				e.PlayerId, e.AccountName, e.Yuanbao, e.Sn, e.bCloud	玩家ID，玩家URS，元宝充值数量，订单号，是否云游戏 */
  CloudGameAddYuanbao = 404135,
}
⋮----
/** 404135	gas;	CloudGameAddYuanbao	云游戏增加元宝				e.PlayerId, e.AccountName, e.Yuanbao, e.Sn, e.bCloud	玩家ID，玩家URS，元宝充值数量，订单号，是否云游戏 */
⋮----
export interface AddYuanbaoLog {
  playerId: number;
  accountName: string;
  yuanbao: number;
  sn: string;
  bCloud: boolean;
}
⋮----
export interface ParseLogRet<T> {
  isOk: boolean;
  data: T;
}
```

## File: logConsumer/util.ts
```typescript
import { logLevel } from "kafkajs";
import { GameLogStruct } from "./type";
import { clazzLogger } from "../../../logger";
⋮----
export function toBunyanLogLevel(level: logLevel)
⋮----
export function parseGameRawLog(log: string): GameLogStruct
```

## File: constant.ts
```typescript
/** 每个账号初始拥有12点【永久云游戏时长】*/
⋮----
/**  这个日期之后第一次登录会送永久时长 */
⋮----
/** 每充值10元宝，获得1点永久时长(点数) */
⋮----
/** 每月通过此方式获得的时长最多为1080小时（64800分钟） */
⋮----
/* 每月账号累计充值达到以下金额时，额外赠送【永久时长】 */
⋮----
/** 没有在“指定日期的初始当日时长”表内配置的日期，初始当日云游戏时间默认为此值（单位：点数） */
⋮----
export const OperationLockTime = 5000 // 5s
```

## File: gm.md
```markdown
# GM操作记录

### 补发元宝奖励时长记录
| urs                   | num  | orderId        | userType | ts         |
|-----------------------|------|----------------|----------|------------|
| <EMAIL> | 3000 | 40588400305z5e | normal   | 1647266382 |
```

## File: gm.ts
```typescript
import { callOperation } from '../../common/util'
import { clazzLogger } from '../../logger'
import { cloudGameDurationNotifyChargeYuanbao } from './operation'
```

## File: index.ts
```typescript
import { Path } from "../../types/type";
import {
  cloudGameDurationShow,
  cloudGameDurationMonthCardShow,
  cloudGameDurationNotifyBuyMonthCard,
  cloudGameDurationReceiveDailyAward,
  cloudGameDurationIncr,
  cloudGameDurationDecr,
  cloudGameDurationNotifyChargeYuanbaoWrap,
} from "./operation";
import { ReqSchemas } from "./type";
```

## File: operation.ts
```typescript
import { nanoid } from "nanoid";
import { RedisLock } from "../../common/cacheUtil";
import { cloudGameDurationCfg } from "../../common/config.all";
import { cacheKeyGen } from "../../common/util";
import { BussError, CloudGameDurationErrors } from "../../errorCodes";
import { clazzLogger } from "../../logger";
import {
  CloudGameDurationAccountModel,
  CloudGameDurationAccountRecord,
  CloudGameYuanbaoChangeLogModel,
  CloudGameYuanbaoChangeLogRecord,
} from "../../models";
import { MonthCardAccountModel, MonthCardAccountRecord } from "../../models/monthCardAccount";
import {
  MonthCardChangeLogModel,
  MonthCardChangeLogRecord,
  MonthCardChangeReason,
} from "../../models/monthCardChangeLog";
import { MonthCardChargeModel, MonthCardChargeRecord } from "../../models/monthCardCharge";
import { CLOUD_DAILY_DURATION_PER_DAY, FIRST_LOGIN_AFTER_LAUNCH_DURATION } from "./constant";
import {
  checkDailyLoginAwardByDs,
  decrUserDurationOnlyDaily,
  decrUserDurationPreferDaily,
  EChangeReasonType,
  EDescType,
  getDayStr,
  getMonthStr,
  getMonthYuanbao,
  incrUserDailyDuration,
  receiveDailyAwardWhenAccountNotExist,
  incrUserPermanentDuration,
  InsertRecord,
  receiveDailyAwardWhenAccountExist,
  saveDailyLoginAwardChangeLog,
  saveMonthYuanbaoInfo,
  freeModifyLock,
  EUserType,
  initAccount,
} from "./service";
import { CloudGameDurationReq, CloudGameDurationRes } from "./type";
⋮----
async function checkUrsAccountInitAndAutoExpire(
  urs: string,
  curDate: Date,
  userType: EUserType
): Promise<CloudGameDurationAccountRecord>
⋮----
async function getAccountAndAutoExpire(
  urs: string,
  ds: string,
  userType: EUserType
): Promise<CloudGameDurationAccountRecord>
⋮----
/** 展示当前urs账号云游戏时长信息 */
export async function cloudGameDurationShow(params: CloudGameDurationReq.Show): Promise<CloudGameDurationRes.Show>
⋮----
/** 一个空实现，只是为了方便游戏侧过渡 */
export async function cloudGameDurationNotifyChargeYuanbaoEmpty(
  params: CloudGameDurationReq.NotifyChargeYuanbao
): Promise<CloudGameDurationRes.NotifyChargeYuanbao>
⋮----
/** http接口新增一个包装层， 可以通过当日志消费逻辑开启后, 开关开启是否启用真实逻辑 */
export async function cloudGameDurationNotifyChargeYuanbaoWrap(
  params: CloudGameDurationReq.NotifyChargeYuanbao
): Promise<CloudGameDurationRes.NotifyChargeYuanbao>
⋮----
/** 游戏通知充值元宝数量，服务负责折算充值比例 */
export async function cloudGameDurationNotifyChargeYuanbao(
  params: CloudGameDurationReq.NotifyChargeYuanbao
): Promise<CloudGameDurationRes.NotifyChargeYuanbao>
⋮----
// first add charge log
⋮----
// incr current month yuanbao chargeNum
⋮----
/** 领取每日登录奖励时长 */
export async function cloudGameDurationReceiveDailyAward(
  params: CloudGameDurationReq.ReceiveDailyAward
): Promise<CloudGameDurationRes.ReceiveDailyAward>
⋮----
/**
 * 游戏程序利用这个每日登录领取奖励来对普通用户初始化, 目前需要适配这周形式
 */
export async function autoInitNormalUserAccountWhenDailyLogin(
  params: CloudGameDurationReq.ReceiveDailyAward
): Promise<CloudGameDurationRes.ReceiveDailyAward>
⋮----
/** 新增云游戏时长 */
export async function cloudGameDurationIncr(params: CloudGameDurationReq.Incr): Promise<CloudGameDurationRes.Incr>
⋮----
// should not go here
⋮----
/** 扣除云游戏时长 */
export async function cloudGameDurationDecr(params: CloudGameDurationReq.Decr): Promise<CloudGameDurationRes.Decr>
⋮----
// should not go here
⋮----
/** 游戏通知购买月卡信息 */
export async function cloudGameDurationNotifyBuyMonthCard(
  params: CloudGameDurationReq.NotifyBuyMonthCard
): Promise<CloudGameDurationRes.NotifyBuyMonthCard>
⋮----
// 目前月卡还未过期的情况, 直接延长过期时间
⋮----
// 月卡已经过期， 重新计算过期时间
⋮----
/** 展示当前urs账号服务器下月卡信息 (ip白名单授权) */
export async function cloudGameDurationMonthCardShow(
  params: CloudGameDurationReq.MonthCardShow
): Promise<CloudGameDurationRes.MonthCardShow>
```

## File: service.ts
```typescript
import { OperationInterval } from "../../common/cacheUtil";
import { isAfter } from "../../common/dateUtil";
import { cacheKeyGen, formatDate } from "../../common/util";
import { BussError, CloudGameDurationErrors } from "../../errorCodes";
import { errorHandler } from "../../helper";
import { clazzLogger } from "../../logger";
import {
  CloudGameDailyDurationChangeLogModel,
  CloudGameDailyDurationChangeLogRecord,
  CloudGameDailyLoginModel,
  CloudGameDailyLoginRecord,
  CloudGameDurationAccountModel,
  CloudGameDurationAccountRecord,
  CloudGameDurationChangeLogModel,
  CloudGameDurationChangeLogRecord,
  CloudGameYuanbaoMonthModel,
  CloudGameYuanbaoMonthRecord,
} from "../../models";
import { UpdateResult } from "../../types/type";
import {
  CHARGE_DURATION_PER_YUANBAO,
  CHARGE_YUANBAO_MAX_DURATION_PER_MONTH,
  CLOUD_DAILY_DURATION_DAY_LIST,
  CLOUD_DAILY_DURATION_PER_DAY,
  FIRST_LOGIN_AFTER_LAUNCH_DURATION,
  KEEP_CHARGE_AWARD_TIME,
  ONLINE_LAUNCH_BEGIN_TIME,
  OperationLockTime,
} from "./constant";
import { CloudGameDurationRes } from "./type";
import { server } from "../../common/config";
⋮----
export type EUserType = "normal" | "cloud";
⋮----
export const enum EChangeReasonType {
  BY_API = 1,
  FIRST_LOGIN_AFTER_LAUNCH = 2,

  DAILY_LOGIN = 3,

  BY_CHARGE = 4,
}
⋮----
export function getDayStr(date: Date): string
⋮----
export function getMonthStr(date: Date): string
⋮----
function getDailyDurationByDateStr(ds: string): number
⋮----
export const enum EChangLogType {
  INCR = 1,
  DECR = 2,
}
⋮----
export const enum EChangLogReason {
  Normal = 0,
  DailyLogin = 1,
}
⋮----
export type InsertRecord<T> = Omit<T, "id">;
⋮----
export async function saveDailyLoginAwardChangeLog(urs: string, ds: string): Promise<number>
⋮----
export async function checkDailyLoginAwardByDs(urs: string, ds: string): Promise<boolean>
⋮----
export async function markDailyLoginAwardToday(
  urs: string,
  ds: string,
): Promise<InsertRecord<CloudGameDailyLoginRecord>>
⋮----
export type MonthYuanBaoInfo = CloudGameDurationRes.Show["yuanbao"];
⋮----
function getAwardDurationByYuanbaoNum(chargeNum: number): number
⋮----
export async function getMonthYuanbao(urs: string, month: string): Promise<MonthYuanBaoInfo>
⋮----
export async function receiveDailyAwardWhenAccountExist(
  account: CloudGameDurationAccountRecord,
  curDate: Date,
  incrDuration: number,
  userType: EUserType,
)
⋮----
// 非云用户无非处理当日奖励
⋮----
export async function incrUserDailyDuration(
  account: CloudGameDurationAccountRecord,
  curDate: Date,
  incrDuration: number,
)
⋮----
export async function receiveDailyAwardWhenAccountNotExist(urs: string, curDate: Date, dailyDuration: number)
⋮----
async function addFirstLoginAfterLaunchDurationLog(urs: string, now: number)
⋮----
export async function initAccount(
  urs: string,
  accountInitSeconds: number,
  dailyDuration: number,
  ds: string,
  now: number,
): Promise<CloudGameDurationAccountRecord>
⋮----
export async function deleteAccount(urs: string)
⋮----
export async function incrUserPermanentDuration(
  account: CloudGameDurationAccountRecord,
  curDate: Date,
  incrDuration: number,
  reason: EChangeReasonType,
)
⋮----
export async function addPermanentDurationChangeLog(prop: InsertRecord<CloudGameDurationChangeLogRecord>)
⋮----
export async function addDailyDurationChangeLog(prop: InsertRecord<CloudGameDailyDurationChangeLogRecord>)
⋮----
export const enum EDescType {
  ONLY_DAILY = 1,
  PREFER_DAILY = 2,
}
⋮----
export async function decrUserDurationOnlyDaily(
  account: CloudGameDurationAccountRecord,
  curDate: Date,
  duration: number,
  reason: EChangeReasonType,
): Promise<CloudGameDurationRes.Decr>
⋮----
function createUnChangeDurationRes(account: CloudGameDurationAccountRecord): CloudGameDurationRes.Decr
⋮----
export async function decrUserDurationPreferDaily(
  account: CloudGameDurationAccountRecord,
  curDate: Date,
  duration: number,
): Promise<CloudGameDurationRes.Decr>
⋮----
// 当日时长余额不足, 需要扣除当日加上永久
⋮----
/** 本次充值元宝应该对应的赠送时长 */
export async function saveMonthYuanbaoInfo(urs: string, month: string, chargeNum: number): Promise<number>
⋮----
export async function dailyDurationExpire(date: Date): Promise<number>
⋮----
export async function checkModifyFreq(req, res, next)
⋮----
// 所有get无副作用，不需要lock
⋮----
export async function getModifyLock(urs: string): Promise<boolean>
⋮----
export async function freeModifyLock(urs: string): Promise<void>
```

## File: type.ts
```typescript
import { operations } from "../../types/swagger";
⋮----
export type Show = operations["cloudGameDurationShow"]["parameters"]["query"];
export type MonthCardShow = operations["cloudGameDurationMonthCardShow"]["parameters"]["query"];
export type NotifyChargeYuanbao = operations["cloudGameDurationNotifyChargeYuanbao"]["parameters"]["query"];
export type NotifyBuyMonthCard = operations["cloudGameDurationNotifyBuyMonthCard"]["parameters"]["query"];
export type ReceiveDailyAward = operations["cloudGameDurationReceiveDailyAward"]["parameters"]["query"];
export type Incr = operations["cloudGameDurationIncr"]["parameters"]["query"];
export type Decr = operations["cloudGameDurationDecr"]["parameters"]["query"];
⋮----
export type Show = operations["cloudGameDurationShow"]["responses"]["200"]["content"]["application/json"]["data"];
export type MonthCardShow =
    operations["cloudGameDurationMonthCardShow"]["responses"]["200"]["content"]["application/json"]["data"];
export type NotifyChargeYuanbao =
    operations["cloudGameDurationNotifyChargeYuanbao"]["responses"]["200"]["content"]["application/json"]["data"];
export type NotifyBuyMonthCard =
    operations["cloudGameDurationNotifyBuyMonthCard"]["responses"]["200"]["content"]["application/json"]["data"];
export type ReceiveDailyAward =
    operations["cloudGameDurationReceiveDailyAward"]["responses"]["200"]["content"]["application/json"]["data"];
export type Incr = operations["cloudGameDurationIncr"]["responses"]["200"]["content"]["application/json"]["data"];
export type Decr = operations["cloudGameDurationDecr"]["responses"]["200"]["content"]["application/json"]["data"];
```
