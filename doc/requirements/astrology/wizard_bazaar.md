# 需求文档: 巫师集市 (Wizard's Bazaar)

## 1. 功能概述 (Feature Overview)
巫师集市是一个类似社交朋友圈的频道，核心功能围绕玩家的每日运势占卜、占星骰子互动以及社区交流。玩家可以通过占卜、评论、评价等行为获得奖励。

## 2. 核心功能模块 (Core Feature Modules)

### 2.1 每日运势自动发布 (Automatic Daily Fortune Posting)
- **触发条件:** 玩家完成每日运势占卜。
- **动作:** 系统自动生成每日运势动态，并发布到巫师集市。
- **动态内容格式:** `#每日运势 + 问题文本 + 骰子参数 + (骰子图片)`
    - `问题文本`: 玩家占卜时选择或输入的问题。
    - `骰子参数`: 由占星骰子摇出的具体参数。
    - `(骰子图片)`: 可选，展示摇出的骰子图案。

### 2.2 占星骰子 (Astrology Dice)
- **玩家操作:**
    1. 玩家选中一个占卜问题。
    2. 点击【问惑】按钮。
- **系统响应:**
    1. 界面下方的3个占星骰子进行随机滚动，并自动停止。
    2. 系统记录摇出的骰子参数。
- **骰子类型 (3个):** 每个骰子对应不同的符文图案和参数。
    - **行星 (Planet):** 太阳，月亮，水星，金星，火星，木星，土星，天王星，海王星，冥王星，南郊点，北郊点
    - **宫位 (House):** 第一宫命宫, 第二宫财运宫, 第三宫兄弟宫, 第四宫家庭宫, 第五宫子女宫, 第六宫健康宫, 第七宫夫妻宫, 第八宫疾厄宫, 第九宫迁移宫, 第十宫事业宫, 第十一宫人际宫, 第十二宫精神宫
    - **星座 (Constellation):** 白羊座，金牛座，双子座，巨蟹座，狮子座，处女座，天秤座，天蝎座，射手座，摩羯座，水瓶座，双鱼座
    - 摇出的骰子参数需要记录下来，并发送给 **有灵AI-DS版** 进行解析。

### 2.3 集市广场 (Bazaar Square)
#### 2.3.1 动态展示 (Post Display)
- 显示玩家发布的动态，包含动态文案（如每日运势）和骰子信息。

#### 2.3.2 评论与AI解析 (Comments & AI Analysis)
- **玩家操作:**
    1. 针对某条动态，点击【解析】按钮。
- **系统响应:**
    1. 调用 **有灵AI-DS版**，根据动态的骰子参数和问题（如果适用）自动生成对应的AI解析文本。
    2. AI生成的解析文本自动填充到评论框中。
    3. 玩家可以对评论框内的AI解析文本进行编辑修改。
    4. 玩家点击【确认】按钮发送评论。
- **奖励:** 成功发送评论后，玩家可以获得【灵力点】奖励。
- **限制:** 有单日评论奖励获取上限。

#### 2.3.3 评价解析 (Rating Analyses)
- **适用场景:** 玩家可以对自己动态下方，由其他玩家（或自己通过AI）提供的解析评论进行评价。
- **界面:**
    - 玩家自己的动态发布可在【我的】界面中查看。
    - 在自己动态下，对进行了解析的玩家评论旁，会显示一个【评价】按钮。
- **玩家操作:** 点击【评价】按钮对该条解析评论进行评价。
- **奖励:** 评价他人解析可获得【灵力点】奖励。
- **限制:** 有单日评价奖励获取上限（原文为“对巫师评分奖励获取上限”，此处理解为对解析评论的评价）。

### 2.4 今日热点 (Today's Hot Topics)
#### 2.4.1 热点话题刷新
- **频率:** 每日每2小时刷新一次热点话题。
- **内容格式:** `今日热门关注{话题文案xxx}` (例如：今日热门关注爱情运势)

#### 2.4.2 群体占卜报告
- **生成时间:** 每日20:00。
- **显示位置:** 在“今日热点”区域的上方，以浮窗形式显示。
- **内容格式:** `今天巫师们对{话题选中最多的xxx}比较关注、整体运势为{今日进行占卜玩家的平均分数}，其中部分巫师对{话题评论最多的xxxx}比较在意……`
    - `{话题选中最多的xxx}`: 当日玩家占卜时选择次数最多的话题。
    - `{今日进行占卜玩家的平均分数}`: 当日所有进行占卜的玩家运势的平均分（需定义分数来源）。
    - `{话题评论最多的xxxx}`: 当日评论互动最多的话题。

### 2.5 专项答疑 (Specialized Q&A)
- **功能:** 允许玩家之间进行一对一的占星咨询。
- **流程:**
    1. **发起方 (A):** 在游戏场景中找到目标玩家 (B)，点击【邀请占星】。
    2. 系统为玩家A打开【占星骰子】界面，界面会明确提示是为玩家B进行占卜提问。
    3. **发起方 (A):** 选择问题，点击【问惑】（完成骰子投掷，记录参数）。
    4. **接收方 (B):** 聊天框会收到一条来自玩家A的消息，包含占卜请求（问题+骰子参数）。
    5. **接收方 (B):** 点击消息中的【解答】按钮。
    6. 系统连接 **有灵AI-DS版**，将玩家A的骰子参数发送给AI进行解析，并将AI生成的解析结果直接显示在玩家A和B的对话框中（作为玩家B的回复）。
    7. **发起方 (A):** 在对话结束后，可以对玩家B（借助AI）提供的这段解析进行评价。
    8. **数据记录:** 玩家A对玩家B的评价，将收录在玩家B的巫师个人信息里。

## 3. 奖励系统 (Reward System)
- **通用货币/积分:** 【灵力点】
- **获取途径:**
    - 在集市广场对他人的动态进行评论解析。
    - 在集市广场对自己动态下的解析评论进行评价。
- **限制:**
    - **单日留言评论奖励获取上限:** 玩家每日通过评论获取的【灵力点】有上限。
    - **单日对巫师评分/评价奖励获取上限:** 玩家每日通过评价解析/评价巫师获取的【灵力点】有上限。

## 4. AI 集成点 (AI Integration Points)
- **指定AI服务:** 有灵AI-DS版
- **集成场景:**
    1.  **占星骰子结果解析:** 玩家使用占星骰子后，其参数发送给AI，用于生成每日运势内容或专项答疑的初步解析。
    2.  **集市广场评论AI辅助:** 玩家点击【解析】评论动态时，调用AI根据动态的骰子信息生成解析文本。
    3.  **专项答疑AI辅助解答:** 接收占星邀请的玩家点击【解答】时，调用AI根据发起方的骰子参数生成解析文本，作为回复内容。

## 5. 待明确/补充 (To Be Clarified/Supplemented)
- 每日运势占卜的具体流程和问题来源。
- 【灵力点】的具体数值、用途及消耗场景。
- “评价”的具体形式（如星级、标签、文字评价等）。
- “今日进行占卜玩家的平均分数”中，“分数”是如何产生的？
- 巫师个人信息中具体包含哪些内容，以及评价如何展示。
- 星座列表是否需要补全至12个标准星座。
