# 占星 API 接口文档

## 接口概述
- **接口名称**: skill/horoscope
- **功能描述**: 占星相关功能
- **测试环境**: POST https://d30-skill-trunk.apps-sl.danlu.netease.com/api/skill/horoscope

## 接口参数说明

### 通用参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| command_type | int | 是 | 指令类别：1-星盘测算，2-星象提醒，3-占星骰子 |
| timestamp | int | 是 | 游戏服务器时间戳，毫秒级 |

### 用户信息参数 (user_list)
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_gender | str | 是 | 玩家性别，如：男/女 |
| birthdate_year | int | 是 | 出生日期-年，如：2025 |
| birthdate_month | int | 是 | 出生日期-月，如：4 |
| birthdate_day | int | 是 | 出生日期-日，如：21 |
| birthdate_hour | int | 是 | 出生日期-时，如：9 |
| birthdate_minute | int | 是 | 出生日期-分，如：30 |
| birth_province | str | 是 | 出生地-省份，如：浙江省 |
| birth_city | str | 是 | 出生地-城市，如：杭州市 |
| birth_district | str | 是 | 出生地-区，如：滨江区 |
| now_province | str | 是 | 现居地-省份 |
| now_city | str | 是 | 现居地-城市 |
| now_district | str | 是 | 现居地-区 |

### 其他参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| time_interval | int | 是 | 时间区间：1-今日，2-本周 |
| fortune | str | 是 | 测运类型：基本运势/财运/事业与学业/情感 |
| fortune_query | str | 是 | 占卜问题 |
| dice_result | list | 是 | 骰子结果，如：["水星", "白羊座", "11宫"] |

## 接口类型说明

### 1. 星盘测算 (command_type=1)

#### 请求示例
```json
{
    "command_type": 1,
    "timestamp": 1748599964977,
    "user_list": [
        {
            "user_gender": "男",
            "birthdate_year": 2025,
            "birthdate_month": 4,
            "birthdate_day": 21,
            "birthdate_hour": 9,
            "birthdate_minute": 30,
            "birth_province": "浙江省",
            "birth_city": "杭州市",
            "birth_district": "滨江区",
            "now_province": "浙江省",
            "now_city": "杭州市",
            "now_district": "滨江区"
        }
    ],
    "time_interval": 1,
    "fortune": "事业与学业"
}
```

#### 响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "fortune": "事业与学业",
        "time": "2025-05-12",
        "user_constellation": "射手座",
        "in_water_reversal": false,
        "day_for_water_reversal_remain": 0,
        "day_for_next_water_reversal": 21,
        "title": "事业与学业运势解读",
        "content": [
            {
                "title": "2023年5月12日射手座事业趋势分析",
                "content": "5月16日水星合木星于金牛座，思维缜密，适合制定长期计划或财务决策。5月20日火星六合天王星，突发项目机遇，需快速行动但忌冲动。5月25日太阳进入双子座对冲土星，团队协作压力增大，需平衡效率与沟通。"
            },
            {
                "title": "2023年5月12日射手座学业趋势分析",
                "content": "5月15日水星合木星于金牛座，逻辑思维强化，考试/论文效率提升。5月20日火星六合土星，学习耐力增强，但需避免过度消耗精力。5月25日太阳进入双子座对冲本命海王星，警惕粗心失误，复习需加强细节核查。"
            }
        ],
        "score": 85,
        "lucky_color": "深蓝色"
    }
}
```

### 2. 星象提醒 (command_type=2)

#### 请求示例
```json
{
    "command_type": 2,
    "timestamp": 1748599964977
}
```

#### 响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "title": "今日星象",
        "content": [
            "00:15 木星进入巨蟹座3宫",
            "形成持续至6月4日的有利相位，带来认知升级，水星逆行促使你重新梳理知识体系，上午9 - 11点适合系统性知识整理；行动赋能，下午15 - 17点适合签署协议或启动新项目；情感共振，强化家庭与情感联结，晚间21 - 23点适合进行家庭对话。",
            "12:30 太阳与海王星形成135度相位",
            "巨蟹座迎来本命月关键转折日，情绪沉淀后迎来行动突破，在生活各方面完成蜕变循环。",
            "17:30 冥王星逆行",
            "冥王星在水瓶座3°逆行。冥王星在水瓶座的议程是将权力（冥王星）从"权威/精英"（摩羯座）手中转移，并将其民主化（水瓶座）。但这个过渡阶段，火炬的传递并非没有挑战。冥王星的逆行是一个暂停和反思的机会，让我们做出必要的调整，以与自去年11月冥王星进入该星座以来出现的趋势保持一致。"
        ]
    }
}
```

### 3. 占星骰子 (command_type=3)

#### 请求示例
```json
{
    "command_type": 3,
    "timestamp": 1748599964977,
    "fortune_query": "今天打本掉出的装备如何",
    "dice_result": ["水星", "白羊座", "11宫"]
}
```

#### 响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "title": "占星骰子",
        "content": [
            {
                "title": "骰子解读",
                "content": "骰子1：行星骰，冥王星。冥王星象征深度蜕变、隐秘力量与重生，暗示关系中存在潜在的控制欲或重大转折。\n骰子2：星座骰，双子。双子座代表信息流动、双重性与灵活应变，关系可能呈现多变特质，涉及大量言语交流或心智博弈。\n骰子3：宫位骰，11宫。11宫关联社群网络、共同理想，说明双方关系可能根植于社交圈层或共享的未来愿景。"
            },
            {
                "title": "总体解读",
                "content": "这段关系存在隐秘的双向影响，冥王星在11宫暗示双方可能通过社交活动建立深层羁绊，但双子座的变动性使信任基础易受流言干扰。表面看似轻松的友谊联结，实则暗含信息不对等或理念分歧的风险。"
            },
            {
                "title": "建议",
                "content": "主动澄清关键信息，避免群体舆论影响判断。用双子座的幽默感化解紧张，同时建立冥王星式的深度信任契约。关注共同目标而非琐碎争议。"
            }
        ]
    }
}
```

## 可引用字段
- time_query (str): 和 time_interval 对应的日期/日期段，如：2025-04-20 或 2025-04-20 ~ 2025-04-26
- date_today (str): 基于 timestamp 自动生成今日日期，如：2025-04-20
- date_week (str): 基于 timestamp 自动生成本周时间段，如：2025-04-20 ~ 2025-04-26

## 日志查询
- 实时日志: l36_npc_im$log*
- 查询条件: "deploy_name: d30_skill-测试服" && "info_1"
