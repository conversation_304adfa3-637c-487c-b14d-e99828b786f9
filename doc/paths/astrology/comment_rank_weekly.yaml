get:
  summary: 星巫-解惑次数周排行榜 (服务端调用)
  description: 获取解惑次数周排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
  tags:
    - Astrology
  security:
    - ipWhitelist: []
  operationId: commentRankWeekly
  responses:
    '200':
      description: 成功获取排行榜
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
                example: 0
              data:
                $ref: "../../components/schemas/astrology/comment_rank_resp.yaml"
