
get:
  summary: 星巫-热门话题
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarHotTopic
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_hot_topic_resp.yaml"