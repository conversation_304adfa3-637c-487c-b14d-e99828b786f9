post:
  summary: 星巫-用户资料-更新 (服务端调用)
  description: 更新星巫用户资料，更新字段可选，不传对应字段不更新
  tags:
    - Astrology
  security: []
  operationId: bazaarUserProfileUpdate
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../components/schemas/astrology/bazaar_user_update_req.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/common/base_resp.yaml"
