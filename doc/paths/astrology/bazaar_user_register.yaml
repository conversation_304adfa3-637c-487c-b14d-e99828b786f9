post:
  summary: 星巫-用户-注册 (服务端调用)
  tags:
    - Astrology
  security: []
  operationId: bazaarUserRegister
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../components/schemas/astrology/bazaar_user_register_req.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_user_register_res.yaml"
