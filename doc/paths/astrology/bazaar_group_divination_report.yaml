get:
  summary: 星巫-群体占卜报告
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarGroupDivinationReport
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
    - $ref: "../../components/parameters/timestamp_in_query.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_group_divination_report_resp.yaml"