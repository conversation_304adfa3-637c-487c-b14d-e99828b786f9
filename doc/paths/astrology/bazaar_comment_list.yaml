get:
  summary: 星巫-问疑解答-列表
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarPostCommentList
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
    - $ref: "../../components/parameters/bazaar_post_id.yaml"
    - $ref: "../../components/parameters/common_page.yaml"
    - $ref: "../../components/parameters/common_page_size.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_comment_list_resp.yaml"