get:
  summary: 星巫-问惑-AI占星骰子
  description: |
    发起解读，把对应问题和占卜结果发送AI, 并返回解读结果
    [伏羲提供的上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794)
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarPostInterpret
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
    - $ref: "../../components/parameters/bazaar_post_id.yaml"
  responses:
    "200":
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_post_interpret_resp.yaml"
