post:
  summary: 星巫-问惑-发布
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarPostAdd
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../components/schemas/astrology/bazaar_post_add.yaml'
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_post_add_resp.yaml"