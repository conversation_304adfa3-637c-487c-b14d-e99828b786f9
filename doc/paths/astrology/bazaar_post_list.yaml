get:
  summary: 星巫-问惑-公共列表
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarPostList
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
    - $ref: "../../components/parameters/common_page.yaml"
    - $ref: "../../components/parameters/common_page_size.yaml"
    - $ref: "../../components/parameters/astrology/astrology_topic_ids.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_post_list_resp.yaml"
