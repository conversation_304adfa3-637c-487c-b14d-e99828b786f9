get:
  summary: 星巫-星盘-AI今日运势
  description: 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: horoscopeDailyForecast
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
    - $ref: "../../components/parameters/timestamp_in_query.yaml"
    - name: fortune
      in: query
      required: true
      description: 测运类型，包括：basic:基本运势、wealth:财运、career:事业与学业、love:情感
      schema:
        type: string
        enum: [basic, wealth, career, love]
      example: career
    - name: timeInterval
      in: query
      required: true
      description: 时间区间。today:今日，week:本周（周日为起始）
      schema:
        type: string
        enum: [today, week]
      example: today
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/horoscope_daily_forecast_resp.yaml"