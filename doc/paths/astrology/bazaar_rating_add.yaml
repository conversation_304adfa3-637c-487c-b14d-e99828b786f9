post:
  summary: 星巫-解惑评分-添加
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: bazaarRatingAdd
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../components/schemas/astrology/bazaar_rating_add.yaml'
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_rating_add_resp.yaml"