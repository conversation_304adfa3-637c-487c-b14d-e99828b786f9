post:
  summary: 星巫-评分-添加 (服务端调用)
  description: 直接给用户评分, 不走巫师集会逻辑
  tags:
    - Astrology
  security: []
  operationId: ratingAdd
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: '../../components/schemas/astrology/rating_add.yaml'
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_rating_add_resp.yaml"