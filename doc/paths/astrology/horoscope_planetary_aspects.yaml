get:
  summary: 星巫-星盘-AI今日星象
  description: 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: horoscopePlanetaryAspects
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
    - $ref: "../../components/parameters/timestamp_in_query.yaml"
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/horoscope_planetary_aspects_resp.yaml"