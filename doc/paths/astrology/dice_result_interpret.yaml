post:
  summary: 星巫-占星骰子-AI解读
  description: 不走巫师集会逻辑, 根据问题和骰子结果，直接调用伏羲AI接口
  tags:
    - Astrology
  security:
    - loginSkey: []
  operationId: diceResultInterpret
  parameters:
    - $ref: "../../components/parameters/roleid_in_query.yaml"
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          required:
            - question
            - dice
          properties:
            question:
              type: string
            dice:
              $ref: '../../components/schemas/astrology/bazaar_dice.yaml'
  responses:
    '200':
      description: Http Status Code 200时的业务响应
      content:
        application/json:
          schema:
            type: object
            required:
              - code
              - data
            properties:
              code:
                description: 业务响应Code
                type: integer
              data:
                $ref: "../../components/schemas/astrology/bazaar_post_interpret_resp.yaml"