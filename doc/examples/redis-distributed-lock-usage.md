# Redis分布式锁使用指南

## 概述

`RedisDistributedLock` 是一个通用的Redis分布式锁工具类，提供了安全、可靠的分布式锁实现。

## 特性

- ✅ **原子性操作**: 使用SET NX EX命令原子性获取锁
- ✅ **安全释放**: 使用Lua脚本确保只有锁持有者才能释放锁
- ✅ **防死锁**: 支持锁超时自动释放
- ✅ **重试机制**: 支持获取锁失败时的重试
- ✅ **便捷API**: 提供withLock方法自动处理锁的生命周期

## 基本使用

### 1. 手动管理锁的生命周期

```typescript
import { RedisDistributedLock } from "../common/redisDistributedLock";

async function criticalSection() {
    const lock = new RedisDistributedLock("my_business_lock");
    
    // 获取锁
    const result = await lock.acquire({ 
        timeoutSeconds: 30,  // 锁30秒后自动释放
        retryCount: 3,       // 失败后重试3次
        retryIntervalMs: 100 // 重试间隔100ms
    });
    
    if (!result.acquired) {
        throw new Error(`Failed to acquire lock: ${result.error}`);
    }
    
    try {
        // 执行需要加锁的业务逻辑
        await doSomeCriticalWork();
    } finally {
        // 释放锁
        await lock.release(result.lockValue!);
    }
}
```

### 2. 使用withLock自动管理（推荐）

```typescript
import { RedisDistributedLock } from "../common/redisDistributedLock";

async function criticalSection() {
    const lock = new RedisDistributedLock("my_business_lock");
    
    // 自动处理锁的获取和释放
    return await lock.withLock(async () => {
        // 执行需要加锁的业务逻辑
        return await doSomeCriticalWork();
    }, {
        timeoutSeconds: 30,
        retryCount: 3
    });
}
```

## 实际应用场景

### 1. 防止重复处理

```typescript
// 防止同一订单被重复处理
async function processOrder(orderId: number) {
    const lock = new RedisDistributedLock(`order_process_${orderId}`);
    
    return await lock.withLock(async () => {
        // 检查订单状态
        const order = await getOrder(orderId);
        if (order.status !== 'pending') {
            return; // 订单已处理
        }
        
        // 处理订单
        await processOrderLogic(order);
        await updateOrderStatus(orderId, 'processed');
    }, { timeoutSeconds: 60 });
}
```

### 2. 缓存刷新防并发

```typescript
// 防止缓存刷新时的并发问题
async function getDataWithCache(key: string) {
    // 先尝试从缓存获取
    let data = await getFromCache(key);
    if (data) {
        return data;
    }
    
    // 缓存未命中，使用分布式锁防止并发刷新
    const lock = new RedisDistributedLock(`cache_refresh_${key}`);
    
    const result = await lock.acquire({ timeoutSeconds: 10 });
    if (!result.acquired) {
        // 获取锁失败，等待一下再从缓存获取
        await sleep(100);
        return await getFromCache(key) || await getFromDatabase(key);
    }
    
    try {
        // 再次检查缓存（可能其他进程已经刷新了）
        data = await getFromCache(key);
        if (data) {
            return data;
        }
        
        // 从数据库获取并缓存
        data = await getFromDatabase(key);
        await setCache(key, data, 300); // 缓存5分钟
        return data;
    } finally {
        await lock.release(result.lockValue!);
    }
}
```

### 3. 定时任务防重复执行

```typescript
// 防止定时任务在多个实例上重复执行
async function scheduledTask() {
    const lock = new RedisDistributedLock("scheduled_task_daily_report");
    
    const result = await lock.acquire({ 
        timeoutSeconds: 3600, // 任务最多执行1小时
        retryCount: 0         // 不重试，避免重复执行
    });
    
    if (!result.acquired) {
        console.log("Task is already running on another instance");
        return;
    }
    
    try {
        await generateDailyReport();
    } finally {
        await lock.release(result.lockValue!);
    }
}
```

## 配置选项

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `timeoutSeconds` | number | 30 | 锁的超时时间（秒） |
| `retryCount` | number | 1 | 获取锁失败时的重试次数 |
| `retryIntervalMs` | number | 100 | 重试间隔（毫秒） |

## 最佳实践

1. **选择合适的锁键名**: 使用有意义且唯一的键名，避免不同业务的锁冲突
2. **设置合理的超时时间**: 根据业务逻辑的执行时间设置锁超时，既要避免死锁，又要防止锁过早释放
3. **优先使用withLock**: 自动处理锁的生命周期，减少忘记释放锁的风险
4. **处理获取锁失败的情况**: 根据业务需求决定是重试、等待还是直接返回错误
5. **避免长时间持锁**: 尽量缩短临界区的执行时间，提高系统并发性能

## 注意事项

- 分布式锁依赖Redis的可用性，Redis故障会影响锁的功能
- 锁的精度受Redis时钟和网络延迟影响
- 在高并发场景下，建议配合业务层面的幂等性设计
- 锁的超时时间应该大于业务逻辑的最大执行时间
