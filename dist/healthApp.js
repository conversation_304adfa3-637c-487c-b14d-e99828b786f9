"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthAppListen = healthAppListen;
const restify = require("restify");
const server = restify.createServer({
    name: "l36",
    version: "1.0.0",
});
server.get("/health", (req, res) => {
    res.send("ok");
});
function healthAppListen(port) {
    server.listen(port, () => {
        console.log(`server is running on port ${port}`);
    });
}
//# sourceMappingURL=healthApp.js.map