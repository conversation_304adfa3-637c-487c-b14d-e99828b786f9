"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    ActivityFriendIds: {
        roleid: { type: Number },
        sameServer: { type: Boolean, required: false, default: true },
    },
    ActivityAddMoment: {
        roleId: { type: Number, required: false },
        text: { type: String, required: false },
        imgs: { type: Array, required: false, items: { type: String } },
        skipImgAudit: { type: Number, required: false, values: [0, 1] },
        videos: { type: Array, required: false, items: { type: String } },
        topicId: { type: Number, required: false },
        skipVideoAudit: { type: Number, required: false, values: [0, 1] },
    },
};
//# sourceMappingURL=type.js.map