"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
const ActivityController = require("../../controllers/activity");
const authCheck_1 = require("../../middlewares/authCheck");
exports.paths = [
    {
        method: "get",
        url: "/activity/friend_ids",
        paramsSchema: type_1.ReqSchemas.ActivityFriendIds,
        operation: operation_1.activityFriendIds,
    },
    {
        method: "post",
        url: "/activity/add_moment",
        handler: ActivityController.addMoment
    },
    {
        method: "get",
        url: "/activity/get_couple_info",
        handlers: [authCheck_1.authCheckByToken, ActivityController.getCoupleInfoHandler],
    },
];
exports.ActivityComponent = {
    paths: exports.paths,
    prefix: "/activity/",
};
//# sourceMappingURL=index.js.map