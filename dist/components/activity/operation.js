"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activityFriendIds = activityFriendIds;
const cacheService_1 = require("../../common/cacheService");
const util_1 = require("../../common/util");
const PyqProfile_1 = require("../../models/PyqProfile");
const serverList_1 = require("../../services/serverList");
async function getFriendIds(roleId) {
    const profile = await PyqProfile_1.ProfileModel.findOne({ RoleId: roleId }, ['FriendList']) || { FriendList: '' };
    const friendIds = (0, util_1.csvStrToIntArray)(profile.FriendList);
    return friendIds;
}
const getFriendIdsFast = (0, cacheService_1.smartMemorize)(getFriendIds, {
    keyGen: function (roleId) {
        return (0, util_1.cacheKeyGen)("friend_ids_for", { roleId });
    },
    expireSeconds: 5
});
function getServerIdByRoleId(roleId) {
    if (roleId > 0) {
        return roleId % 1000;
    }
    else {
        return 0;
    }
}
/** 查看玩家好友列表 */
async function activityFriendIds(params) {
    const { roleid } = params;
    let friendIds = await getFriendIdsFast(roleid);
    if (params.sameServer) {
        const curServerId = getServerIdByRoleId(roleid);
        const mergeServerIdList = await (0, serverList_1.getMergedServerIds)(curServerId);
        const mergeServerIdSet = new Set(mergeServerIdList);
        friendIds = friendIds.filter(friendId => {
            const friendServerId = getServerIdByRoleId(friendId);
            return mergeServerIdSet.has(friendServerId);
        });
    }
    const data = {
        list: friendIds
    };
    return data;
}
//# sourceMappingURL=operation.js.map