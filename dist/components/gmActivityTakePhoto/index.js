"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GmActivityTakePhotoComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/gm/activity/take_photo/set_role_abnormal",
        paramsSchema: type_1.ReqSchemas.GmActivityTakePhotoSetRoleAbnormal,
        operation: operation_1.gmActivityTakePhotoSetRoleAbnormal,
    },
    {
        method: "post",
        url: "/gm/activity/take_photo/cancel_role_abnormal",
        paramsSchema: type_1.ReqSchemas.GmActivityTakePhotoCancelRoleAbnormal,
        operation: operation_1.gmActivityTakePhotoCancelRoleAbnormal,
    },
    {
        method: "post",
        url: "/gm/activity/take_photo/get_role_selected_photo",
        paramsSchema: type_1.ReqSchemas.GmActivityTakePhotoGetRoleSelectedPhoto,
        operation: operation_1.gmActivityTakePhotoGetRoleSelectedPhoto,
    },
];
exports.GmActivityTakePhotoComponent = {
    paths: exports.paths,
    prefix: "/gm/activity/take_photo/",
};
//# sourceMappingURL=index.js.map