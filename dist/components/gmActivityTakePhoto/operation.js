"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmActivityTakePhotoGetRoleSelectedPhoto = exports.gmActivityTakePhotoCancelRoleAbnormal = exports.gmActivityTakePhotoSetRoleAbnormal = void 0;
const UserPermission_1 = require("../../models/UserPermission");
const ActivityTakePhoto_1 = require("../../models/ActivityTakePhoto");
const util_1 = require("../../common/util");
/** 拍照活动-设置用户异常状态 */
function gmActivityTakePhotoSetRoleAbnormal(params) {
    return __awaiter(this, void 0, void 0, function* () {
        yield UserPermission_1.banPermission(params.roleId, UserPermission_1.Permission.ActivityTakePhotoAbnormal);
        return null;
    });
}
exports.gmActivityTakePhotoSetRoleAbnormal = gmActivityTakePhotoSetRoleAbnormal;
/** 拍照活动-取消用户异常状态 */
function gmActivityTakePhotoCancelRoleAbnormal(params) {
    return __awaiter(this, void 0, void 0, function* () {
        yield UserPermission_1.unBanPermission(params.roleId, UserPermission_1.Permission.ActivityTakePhotoAbnormal);
        return null;
    });
}
exports.gmActivityTakePhotoCancelRoleAbnormal = gmActivityTakePhotoCancelRoleAbnormal;
/** 拍照活动-获取玩家位置选中照片 */
function gmActivityTakePhotoGetRoleSelectedPhoto(params) {
    return __awaiter(this, void 0, void 0, function* () {
        const { roleIds, locationId } = params;
        const roleIdArr = roleIds.split(',').map(Number).filter(num => !isNaN(num) && num !== 0);
        const raws = yield ActivityTakePhoto_1.ActivityTakePhotoModel.executeByQuery(ActivityTakePhoto_1.ActivityTakePhotoModel.scope()
            .whereIn('RoleId', roleIdArr)
            .where('LocationId', locationId));
        const rawMap = util_1.keyToRecordMap(raws, 'RoleId');
        const list = roleIdArr.map(roleId => {
            const raw = rawMap.get(roleId);
            const imgList = util_1.getJsonInfo(raw === null || raw === void 0 ? void 0 : raw.ImgList, []);
            let url = "";
            for (let v of imgList) {
                if (v.isSelected) {
                    url = v.imgUrl;
                    break;
                }
            }
            return {
                roleId: roleId,
                locationId,
                url
            };
        });
        return { list };
    });
}
exports.gmActivityTakePhotoGetRoleSelectedPhoto = gmActivityTakePhotoGetRoleSelectedPhoto;
//# sourceMappingURL=operation.js.map