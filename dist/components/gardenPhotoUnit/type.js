"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    GardenPhotoUnitAdd: {
        type: "object",
        properties: { roleid: { type: "number" }, url: { type: "string" }, itemTemplateId: { type: "number" } },
        required: ["roleid", "url", "itemTemplateId"],
    },
    GardenPhotoUnitGet: {
        type: "object",
        properties: { roleid: { type: "number" }, targetId: { type: "number" }, itemTemplateId: { type: "number" } },
        required: ["roleid", "targetId", "itemTemplateId"],
    },
    GardenPhotoUnitList: {
        type: "object",
        properties: { roleid: { type: "number" }, targetId: { type: "number" } },
        required: ["roleid", "targetId"],
    },
};
//# sourceMappingURL=type.js.map