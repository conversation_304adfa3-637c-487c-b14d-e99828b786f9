"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GardenPhotoUnitComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/garden_photo_unit/add",
        paramsSchema: type_1.ReqSchemas.GardenPhotoUnitAdd,
        operation: operation_1.gardenPhotoUnitAdd,
    },
    {
        method: "get",
        url: "/garden_photo_unit/get",
        paramsSchema: type_1.ReqSchemas.GardenPhotoUnitGet,
        operation: operation_1.gardenPhotoUnitGet,
    },
    {
        method: "get",
        url: "/garden_photo_unit/list",
        paramsSchema: type_1.ReqSchemas.GardenPhotoUnitList,
        operation: operation_1.gardenPhotoUnitList,
    },
];
exports.GardenPhotoUnitComponent = {
    paths: exports.paths,
    prefix: "/garden_photo_unit/",
};
//# sourceMappingURL=index.js.map