"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gardenPhotoUnitAdd = gardenPhotoUnitAdd;
exports.gardenPhotoUnitGet = gardenPhotoUnitGet;
exports.gardenPhotoUnitList = gardenPhotoUnitList;
const config_1 = require("../../common/config");
const errorCodes_1 = require("../../errorCodes");
const model_1 = require("./model");
/** 庄园特点玩法图片上传 */
async function gardenPhotoUnitAdd(params) {
    const condition = { roleId: params.roleid, itemTemplateId: params.itemTemplateId };
    const r = await model_1.GardenPhotoUnitModel.findOne(condition, ["id"]);
    if (r && r.id) {
        await model_1.GardenPhotoUnitModel.updateByCondition({ id: r.id }, {
            url: params.url,
            itemTemplateId: params.itemTemplateId,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        });
        return { id: r.id };
    }
    else {
        const props = {
            roleId: params.roleid,
            url: params.url,
            itemTemplateId: params.itemTemplateId,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        };
        const id = await model_1.GardenPhotoUnitModel.insert(props);
        return { id: id };
    }
}
/** 庄园特点玩法图片获取 */
async function gardenPhotoUnitGet(params) {
    const viewerId = params.targetId || params.roleid;
    const r = await model_1.GardenPhotoUnitModel.findOne({ roleId: viewerId, itemTemplateId: params.itemTemplateId, status: 0 /* Statues.Normal */ }, ["id", "roleId", "itemTemplateId", "url", "createTime"]);
    if (r && r.id) {
        return r;
    }
    else {
        throw errorCodes_1.GardenPhotoUnitErrors.PhotoNotFound;
    }
}
/** 庄园特点玩法图片列表 */
async function gardenPhotoUnitList(params) {
    const viewerId = params.targetId || params.roleid;
    const list = await model_1.GardenPhotoUnitModel.powerQuery({
        initQuery: model_1.GardenPhotoUnitModel.normalScope(),
        where: { roleId: viewerId },
        orderBy: [["itemTemplateId"], ["asc"]],
        pagination: { page: 1, pageSize: config_1.gardenPhotoUnitCfg.maxSizePerRoleId },
        select: ["id", "roleId", "url", "itemTemplateId", "createTime"],
    });
    return { list };
}
//# sourceMappingURL=operation.js.map