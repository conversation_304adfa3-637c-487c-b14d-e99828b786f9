"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GardenPhotoUnitModel = exports.GardenPhotoUnitCols = void 0;
const BaseModel2_1 = require("../../models/BaseModel2");
exports.GardenPhotoUnitCols = ["id", "roleId", "itemTemplateId", "url", "createTime"];
class GardenPhotoUnitModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_garden_photo_unit");
    }
}
exports.GardenPhotoUnitModel = new GardenPhotoUnitModelClass();
//# sourceMappingURL=model.js.map