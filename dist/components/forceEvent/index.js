"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forceEventComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/force_event/add",
        paramsSchema: type_1.ReqSchemas.forceEventAdd,
        before: [gameIpLimit_1.gameIpLimit],
        operation: operation_1.forceEventAdd,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/force_event/remark",
        paramsSchema: type_1.ReqSchemas.forceEventRemark,
        before: [gameIpLimit_1.gameIpLimit],
        operation: operation_1.forceEventRemark,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/force_event/list",
        paramsSchema: type_1.ReqSchemas.forceEventList,
        operation: operation_1.forceEventList,
    },
    {
        method: "post",
        url: "/force_event/del",
        paramsSchema: type_1.ReqSchemas.forceEventDel,
        before: [gameIpLimit_1.gameIpLimit],
        operation: operation_1.forceEventDel,
        option: { skipSkey: true },
    },
    {
        method: "post",
        url: "/force_event/like",
        paramsSchema: type_1.ReqSchemas.forceEventLike,
        operation: operation_1.forceEventLike,
    },
    {
        method: "post",
        url: "/force_event/cancel_like",
        paramsSchema: type_1.ReqSchemas.forceEventCancelLike,
        operation: operation_1.forceEventCancelLike,
    },
];
exports.forceEventComponent = {
    paths: exports.paths,
    prefix: "/force_event/",
};
//# sourceMappingURL=index.js.map