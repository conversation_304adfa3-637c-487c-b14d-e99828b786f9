"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forceEventAdd = forceEventAdd;
exports.forceEventRemark = forceEventRemark;
exports.forceEventList = forceEventList;
exports.forceEventDel = forceEventDel;
exports.forceEventLike = forceEventLike;
exports.forceEventCancelLike = forceEventCancelLike;
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const audit_1 = require("../neDun/audit");
const forceEvent_1 = require("./models/forceEvent");
const forceEventLike_1 = require("./models/forceEventLike");
const _ = require("lodash");
const logger = (0, logger_1.clazzLogger)("forceEvent/operation");
/** 新增势力大事 (游戏服务器调用) */
async function forceEventAdd(params) {
    const createTime = Date.now();
    const prop = {
        uuid: params.uuid,
        forceId: params.forceId,
        serverId: params.serverId,
        eventType: params.eventType,
        eventArgs: JSON.stringify(params.eventArgs),
        remark: "",
        remarkTime: 0,
        remarkRoleId: 0,
        remarkRoleName: "",
        eventTime: params.eventTime,
        createTime,
        status: 0 /* Statues.Normal */,
        likeCount: 0,
    };
    try {
        const id = await forceEvent_1.ForceEventModel.insert(prop);
        const data = {
            id: id,
            createTime,
        };
        return data;
    }
    catch (e) {
        if (e && e.code === "ER_DUP_ENTRY") {
            throw errorCodes_1.ForceEventErrors.ForceEventDuplicate;
        }
        else {
            throw e;
        }
    }
}
/** 史官评论势力大事 (游戏服务器调用) */
async function forceEventRemark(params) {
    await (0, audit_1.textCheckFilter)({
        content: params.content,
        title: params.roleName,
        userId: params.roleId,
        dataId: `${forceEvent_1.ForceEventModel.tableName}:${params.id}`,
    });
    const ret = await forceEvent_1.ForceEventModel.updateRemark(params.id, params.content, {
        roleId: params.roleId,
        roleName: params.roleName,
    });
    const isOk = ret.affectedRows > 0;
    const data = { isOk };
    return data;
}
/** 势力大事列表 */
async function forceEventList(params) {
    let query = forceEvent_1.ForceEventModel.forceScope(params.serverId, params.forceId);
    /** 普通用户只显示有史官评论的 */
    if (params.roleType === 0 /* ERoleType.NormalUser */) {
        query = query.where("remarkTime", ">", 0);
    }
    else {
        // 如果是史官， 只显示一个周期内的数据
        const timestampMs = params.ts ? params.ts * 1000 : Date.now();
        query = query.whereBetween("eventTime", forceEvent_1.ForceEventModel.weekRangeForEventTime(timestampMs));
    }
    const countQuery = query.clone();
    const rows = await forceEvent_1.ForceEventModel.powerQuery({
        initQuery: query,
        select: forceEvent_1.ForceEventCols,
        pagination: {
            page: params.page,
            pageSize: params.pageSize,
        },
        orderBy: [["id"], ["desc"]],
    });
    const count = await forceEvent_1.ForceEventModel.countByQuery(countQuery);
    const eventIds = rows.map((r) => r.id);
    const likeEventIds = await forceEventLike_1.ForceEventLikeModel.filterByLiked(eventIds, params.roleId);
    const list = rows.map((r) => {
        const item = {
            id: r.id,
            uuid: r.uuid,
            serverId: r.serverId,
            forceId: r.forceId,
            eventType: r.eventType,
            eventTime: r.eventTime,
            eventArgs: (0, util_1.getJsonInfo)(r.eventArgs, {}),
            remark: r.remark,
            remarkTime: r.remarkTime,
            remarkRoleId: r.remarkRoleId,
            remarkRoleName: r.remarkRoleName,
            likeCount: r.likeCount,
            isLiked: likeEventIds.includes(r.id),
            createTime: r.createTime,
        };
        return item;
    });
    const data = {
        list,
        count,
    };
    return data;
}
/** 删除势力大事 (游戏服务器调用) */
async function forceEventDel(params) {
    logger.info({ params }, "ForceEventDel");
    const ret = await forceEvent_1.ForceEventModel.softDeleteByCondition({ id: params.forceId });
    const isOk = ret.affectedRows > 0;
    const data = {
        isOk,
    };
    return data;
}
/** 点赞势力大事 */
async function forceEventLike(params) {
    const roleId = params.roleId;
    const eventId = params.id;
    const event = await forceEvent_1.ForceEventModel.checkExist(eventId);
    const like = await forceEventLike_1.ForceEventLikeModel.findOne({ roleId, eventId }, ["id", "roleId", "status"]);
    if (!_.isEmpty(like) && like.status === 0 /* Statues.Normal */) {
        throw errorCodes_1.ForceEventErrors.ForceEventNotExist;
    }
    let id = 0;
    if (!_.isEmpty(like)) {
        id = like.id;
        await forceEventLike_1.ForceEventLikeModel.updateById(like.id, { status: 0 /* Statues.Normal */, createTime: Date.now() });
    }
    else {
        const props = {
            roleId,
            eventId,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        };
        id = await forceEventLike_1.ForceEventLikeModel.insert(props);
    }
    // 事件点赞计数
    forceEvent_1.ForceEventModel.onEventLike(event);
    const data = { isOk: id > 0 };
    return data;
}
/** 取消点赞势力大事 */
async function forceEventCancelLike(params) {
    const roleId = params.roleId;
    const eventId = params.id;
    const event = await forceEvent_1.ForceEventModel.checkExist(eventId);
    const like = await forceEventLike_1.ForceEventLikeModel.findOne({ roleId, eventId }, ["id", "roleId", "status"]);
    if (_.isEmpty(like) || like.status === -1 /* Statues.Deleted */) {
        throw errorCodes_1.ForceEventErrors.ForceEventNotLiked;
    }
    else {
        await forceEventLike_1.ForceEventLikeModel.softDeleteById(like.id);
    }
    // 事件取消点赞计数
    forceEvent_1.ForceEventModel.onEventCancelLike(event);
    const data = {
        isOk: true,
    };
    return data;
}
//# sourceMappingURL=operation.js.map