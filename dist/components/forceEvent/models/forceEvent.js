"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ForceEventModel = exports.ForceEventCols = void 0;
const moment = require("moment");
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
const baseModel_1 = require("../../../models/baseModel");
const logger = (0, logger_1.clazzLogger)("models/forceEvent");
exports.ForceEventCols = [
    "id",
    "uuid",
    "forceId",
    "serverId",
    "eventType",
    "eventArgs",
    "remark",
    "remarkTime",
    "remarkRoleId",
    "remarkRoleName",
    "eventTime",
    "createTime",
    "likeCount",
    "status",
];
class ForceEventModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("nsh_force_event");
    }
    updateRemark(id, remark, author) {
        const remarkTime = Date.now();
        return this.updateByCondition({ id }, { remark, remarkTime, remarkRoleId: author.roleId, remarkRoleName: author.roleName });
    }
    async checkExist(id) {
        const r = await this.findOne({ id, status: 0 /* Statues.Normal */ });
        if (r && r.id) {
            return r;
        }
        else {
            throw errorCodes_1.ForceEventErrors.ForceEventNotExist;
        }
    }
    forceScope(serverId, forceId) {
        return this.normalScope().where({ serverId, forceId });
    }
    /**
     *
     * @param curDate
     * 应该是上周六 00:00 -周五23:59的结果, eventTime游戏用秒作为时间戳
     * @returns
     */
    weekRangeForEventTime(timestamp) {
        const subDays = moment(timestamp).isoWeekday() === 7 ? 0 : 7;
        const start = moment(timestamp).startOf("day").hour(0).subtract(subDays, "days").isoWeekday("saturday").unix();
        const end = moment(start * 1000)
            .add(7, "days")
            .subtract(1, "milliseconds")
            .unix();
        return [start, end];
    }
    async onEventLike(event) {
        try {
            await this.increment({ id: event.id }, "likeCount", 1);
        }
        catch (err) {
            logger.error({ err, event }, "incrEventLikeFailed");
        }
    }
    async onEventCancelLike(event) {
        try {
            await this.decrByCondition({ id: event.id }, "likeCount", 1);
        }
        catch (err) {
            logger.error({ err, event }, "decrEventLikeFailed");
        }
    }
}
exports.ForceEventModel = new ForceEventModelClass();
//# sourceMappingURL=forceEvent.js.map