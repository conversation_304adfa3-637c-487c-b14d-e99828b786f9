"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ForceEventLikeModel = exports.ForceEventLikeCols = void 0;
const baseModel_1 = require("../../../models/baseModel");
exports.ForceEventLikeCols = ['id', 'roleId', 'eventId', 'createTime', 'status'];
class ForceEventLikeModelClass extends baseModel_1.BaseModelClass {
    constructor() {
        super("nsh_force_event_like");
    }
    async filterByLiked(eventIds, curRoleId) {
        const rows = await this.powerQuery({
            initQuery: this.normalScope(),
            where: {
                eventId: eventIds,
                roleId: curRoleId,
            },
            select: ["eventId"],
            pagination: { page: 1, pageSize: eventIds.length },
        });
        return rows.map((r) => r.eventId);
    }
}
exports.ForceEventLikeModel = new ForceEventLikeModelClass();
//# sourceMappingURL=forceEventLike.js.map