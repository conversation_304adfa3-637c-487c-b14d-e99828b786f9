"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.textCheckFilter = void 0;
const util_1 = require("../../common/util");
const neDun_1 = require("../neDun/neDun");
const constants_1 = require("../../common/constants");
const errorCodes_1 = require("../../errorCodes");
async function textCheckFilter(params) {
    const dataId = params.dataId || (0, util_1.hexMd5)(params.content);
    const ret = await (0, neDun_1.textCheckSimple)({
        account: "" + params.userId,
        dataId,
        content: params.content,
        title: params.title,
    });
    if (ret === constants_1.AuditStatues.Reject) {
        throw errorCodes_1.errorCode.ContainSensitive;
    }
    else {
        return dataId;
    }
}
exports.textCheckFilter = textCheckFilter;
//# sourceMappingURL=audit.js.map