"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    GardenDetail: {
        roleId: { type: Number },
        ownerId: { type: Number },
    },
    GardenRank: {
        roleId: { type: Number },
        page: { type: Number, default: 1, min: 1 },
        pageSize: { type: Number, max: 20, default: 10, min: 1 },
    },
    GardenEvaluationAdd: {
        roleId: { type: Number },
        ownerId: { type: Number },
        score: { type: Number, min: 3, max: 5 },
        text: { type: String, maxlen: 1000 },
        imgList: { type: Array, required: false, items: { type: String }, maxSize: 4 },
    },
    GardenEvaluationDel: {
        roleId: { type: Number },
        evaluationId: { type: Number },
    },
    GardenEvaluationLike: {
        roleId: { type: Number },
        evaluationId: { type: Number },
    },
    GardenEvaluationCancelLike: {
        roleId: { type: Number },
        evaluationId: { type: Number },
    },
    GardenEvaluationList: {
        roleId: { type: Number },
        ownerId: { type: Number },
        orderby: { type: String, required: false, default: "hot", values: ["hot"] },
        page: { type: Number, default: 1, min: 1 },
        pageSize: { type: Number, max: 20, default: 10, min: 1 },
    },
    GardenEvaluationComment: {
        roleId: { type: Number },
        evaluationId: { type: Number },
        replyCommentId: { type: Number, required: false },
        text: { type: String, maxlen: 1000 },
    },
    GardenEvaluationCommentDel: {
        roleId: { type: Number },
        commentId: { type: Number },
    },
    GardenEvaluationCommentList: {
        roleId: { type: Number },
        evaluationId: { type: Number },
        page: { type: Number, default: 1, min: 1 },
        pageSize: { type: Number, max: 20, default: 10, min: 1 },
    },
    GardenEvaluationCommentSubList: {
        roleId: { type: Number },
        commentId: { type: Number },
        page: { type: Number, default: 1, min: 1 },
        pageSize: { type: Number, max: 20, default: 10, min: 1 },
    },
    GardenEvaluationCommentLike: {
        roleId: { type: Number },
        commentId: { type: Number },
    },
    GardenEvaluationCommentCancelLike: {
        roleId: { type: Number },
        commentId: { type: Number },
    },
    GardenInformList: {
        roleId: { type: Number },
        page: { type: Number, default: 1, min: 1 },
        pageSize: { type: Number, max: 20, default: 10, min: 1 },
    },
    GardenInformUnread: {
        roleId: { type: Number },
    },
};
//# sourceMappingURL=type.js.map