"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GardenComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/garden/detail",
        paramsSchema: type_1.ReqSchemas.GardenDetail,
        operation: operation_1.gardenDetail,
    },
    {
        method: "get",
        url: "/garden/rank",
        paramsSchema: type_1.ReqSchemas.GardenRank,
        operation: operation_1.gardenRank,
    },
    {
        method: "post",
        url: "/garden/evaluation_add",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationAdd,
        operation: operation_1.gardenEvaluationAdd,
    },
    {
        method: "post",
        url: "/garden/evaluation_del",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationDel,
        operation: operation_1.gardenEvaluationDel,
    },
    {
        method: "post",
        url: "/garden/evaluation_like",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationLike,
        operation: operation_1.gardenEvaluationLike,
    },
    {
        method: "post",
        url: "/garden/evaluation_cancel_like",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationCancelLike,
        operation: operation_1.gardenEvaluationCancelLike,
    },
    {
        method: "get",
        url: "/garden/evaluation_list",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationList,
        operation: operation_1.gardenEvaluationList,
    },
    {
        method: "post",
        url: "/garden/evaluation/comment",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationComment,
        operation: operation_1.gardenEvaluationComment,
    },
    {
        method: "post",
        url: "/garden/evaluation/comment_del",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationCommentDel,
        operation: operation_1.gardenEvaluationCommentDel,
    },
    {
        method: "get",
        url: "/garden/evaluation/comment_list",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationCommentList,
        operation: operation_1.gardenEvaluationCommentList,
    },
    {
        method: "get",
        url: "/garden/evaluation/comment_sub_list",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationCommentSubList,
        operation: operation_1.gardenEvaluationCommentSubList,
    },
    {
        method: "post",
        url: "/garden/evaluation/comment_like",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationCommentLike,
        operation: operation_1.gardenEvaluationCommentLike,
    },
    {
        method: "post",
        url: "/garden/evaluation/comment_cancel_like",
        paramsSchema: type_1.ReqSchemas.GardenEvaluationCommentCancelLike,
        operation: operation_1.gardenEvaluationCommentCancelLike,
    },
    {
        method: "get",
        url: "/garden/inform/list",
        paramsSchema: type_1.ReqSchemas.GardenInformList,
        operation: operation_1.gardenInformList,
    },
    {
        method: "get",
        url: "/garden/inform/unread",
        paramsSchema: type_1.ReqSchemas.GardenInformUnread,
        operation: operation_1.gardenInformUnread,
    },
];
exports.GardenComponent = {
    paths: exports.paths,
    prefix: "/garden/",
};
//# sourceMappingURL=index.js.map