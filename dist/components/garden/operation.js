"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gardenDetail = gardenDetail;
exports.gardenRank = gardenRank;
exports.gardenEvaluationAdd = gardenEvaluationAdd;
exports.gardenEvaluationDel = gardenEvaluationDel;
exports.gardenEvaluationLike = gardenEvaluationLike;
exports.gardenEvaluationCancelLike = gardenEvaluationCancelLike;
exports.gardenEvaluationList = gardenEvaluationList;
exports.gardenEvaluationComment = gardenEvaluationComment;
exports.gardenEvaluationCommentDel = gardenEvaluationCommentDel;
exports.gardenEvaluationCommentList = gardenEvaluationCommentList;
exports.gardenEvaluationCommentSubList = gardenEvaluationCommentSubList;
exports.gardenEvaluationCommentLike = gardenEvaluationCommentLike;
exports.gardenEvaluationCommentCancelLike = gardenEvaluationCommentCancelLike;
exports.gardenInformList = gardenInformList;
exports.gardenInformUnread = gardenInformUnread;
const Garden_1 = require("../../models/Garden");
const GardenEvaluation_1 = require("../../models/GardenEvaluation");
const RoleInfos_1 = require("../../models/RoleInfos");
const GardenService = require("../../services/garden");
const GardenInformService = require("../../services/gardenInform");
const GardenEvaluationLike_1 = require("../../models/GardenEvaluationLike");
const errorCodes_1 = require("../../errorCodes");
const GardenEvaluationComment_1 = require("../../models/GardenEvaluationComment");
const GardenEvaluationCommentLike_1 = require("../../models/GardenEvaluationCommentLike");
const cache_1 = require("../../services/cache");
const GardenInform_1 = require("../../models/GardenInform");
const util_1 = require("../../common/util");
const yunyinLog_1 = require("../../services/yunyinLog");
const UserPermission_1 = require("../../models/UserPermission");
/** 庄园详情 */
async function gardenDetail(params) {
    const { roleId, ownerId } = params;
    const [roleInfo, gardenInfo] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(ownerId),
        GardenService.ensureGardenInit(ownerId)
    ]);
    const data = {
        roleId: ownerId,
        roleName: roleInfo.RoleName,
        server: roleInfo.ServerId,
        jobId: roleInfo.JobId,
        gender: roleInfo.Gender,
        level: roleInfo.Level,
        gardenGrade: gardenInfo.GardenGrade,
        score: gardenInfo.Score,
        evaluationCount: gardenInfo.EvaluationCount,
        popularity: gardenInfo.Popularity,
        gardenId: gardenInfo.GardenId,
        fengShui: gardenInfo.FengShui,
        shiYong: gardenInfo.ShiYong,
        meiGuan: gardenInfo.MeiGuan,
    };
    return data;
}
/** 庄园排行 */
async function gardenRank(params) {
    const { roleId, page, pageSize } = params;
    const list = await GardenService.getGardenRank(roleId, { page, pageSize });
    return { list };
}
/** 庄园评价 */
async function gardenEvaluationAdd(params) {
    const { roleId, ownerId, score, text, imgList } = params;
    const roleInfo = await RoleInfos_1.RoleInfoModel.checkOne(roleId);
    await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.GardenEvaluationAdd, "禁止添加庄园评价");
    if (roleId === ownerId)
        throw errorCodes_1.GardenErrors.CannotEvaluationSelf;
    await GardenService.ensureGardenInit(ownerId);
    const id = await GardenEvaluation_1.GardenEvaluationModel.insert({
        RoleId: roleId,
        OwnerId: ownerId,
        Score: score,
        Text: text,
        ImgList: imgList ? JSON.stringify(imgList) : null,
        CreateTime: Date.now()
    });
    await Garden_1.GardenModel.incrementProperties(ownerId, {
        EvaluationCount: 1
    });
    await GardenInformService.addGardenEvaluationInform(roleId, ownerId, id, GardenInform_1.GardenInformType.EvaluationAdd, text, imgList);
    (0, yunyinLog_1.addGardenEvaluationAddLog)(roleInfo, { evaluationId: id, text, ownerId, score, imgList });
    return { id };
}
/** 庄园删除评价 */
async function gardenEvaluationDel(params) {
    const { roleId, evaluationId } = params;
    const [roleInfo, record] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluation_1.GardenEvaluationModel.checkOne(evaluationId, roleId)
    ]);
    await Promise.all([
        GardenEvaluation_1.GardenEvaluationModel.updateById(evaluationId, { Status: -1 /* Statues.Deleted */ }),
        Garden_1.GardenModel.incrementProperties(record.OwnerId, { EvaluationCount: -1 }),
        GardenInformService.deleteGardenInformByObjectId(evaluationId, GardenInform_1.GardenInformType.EvaluationAdd)
    ]);
    (0, yunyinLog_1.addGardenEvaluationDelLog)(roleInfo, { evaluationId, text: record.Text, ownerId: record.OwnerId, score: record.Score, imgList: (0, util_1.getJsonInfo)(record.imgList, []) });
    return { isOk: true };
}
/** 评价点赞 */
async function gardenEvaluationLike(params) {
    const { roleId, evaluationId } = params;
    const [roleInfo, record, likeRecord] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluation_1.GardenEvaluationModel.checkOne(evaluationId),
        GardenEvaluationLike_1.GardenEvaluationLikeModel.findOne({ RoleId: roleId, EvaluationId: evaluationId })
    ]);
    if (likeRecord && likeRecord.Status === 0 /* Statues.Normal */)
        throw errorCodes_1.GardenErrors.AlreadyLiked;
    const now = Date.now();
    let id;
    if (likeRecord) {
        id = likeRecord.ID;
        await GardenEvaluationLike_1.GardenEvaluationLikeModel.updateById(id, { Status: 0 /* Statues.Normal */, UpdateTime: now });
    }
    else {
        id = await GardenEvaluationLike_1.GardenEvaluationLikeModel.insert({
            RoleId: roleId,
            EvaluationId: evaluationId,
            CreateTime: now,
            UpdateTime: now
        });
    }
    await Promise.all([
        GardenEvaluation_1.GardenEvaluationModel.incrementProperty(evaluationId, 'LikeCount'),
        GardenInformService.addGardenLikeInform(roleId, record.RoleId, id, GardenInform_1.GardenInformType.EvaluationLike)
    ]);
    (0, yunyinLog_1.addGardenEvaluationLikeLog)(roleInfo, { evaluationId, text: record.Text, score: record.Score, imgList: (0, util_1.getJsonInfo)(record.imgList, []) });
    const data = {
        isOk: true,
    };
    return data;
}
/** 评价取消点赞 */
async function gardenEvaluationCancelLike(params) {
    const { roleId, evaluationId } = params;
    const [roleInfo, record, likeRecord] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluation_1.GardenEvaluationModel.checkOne(evaluationId),
        GardenEvaluationLike_1.GardenEvaluationLikeModel.findOne({ RoleId: roleId, EvaluationId: evaluationId })
    ]);
    if (!likeRecord || likeRecord.Status === -1 /* Statues.Deleted */)
        throw errorCodes_1.GardenErrors.NotLiked;
    await Promise.all([
        GardenEvaluationLike_1.GardenEvaluationLikeModel.updateById(likeRecord.ID, { Status: -1 /* Statues.Deleted */, UpdateTime: Date.now() }),
        GardenEvaluation_1.GardenEvaluationModel.incrementProperty(evaluationId, 'LikeCount', -1),
        GardenInformService.deleteGardenInformByObjectId(likeRecord.ID, GardenInform_1.GardenInformType.EvaluationLike)
    ]);
    (0, yunyinLog_1.addGardenEvaluationCancelLikeLog)(roleInfo, { evaluationId, text: record.Text, score: record.Score, imgList: (0, util_1.getJsonInfo)(record.imgList, []) });
    const data = {
        isOk: true,
    };
    return data;
}
/** 庄园评价列表 */
async function gardenEvaluationList(params) {
    const { roleId, ownerId, page, pageSize } = params;
    const gardenInfo = await GardenService.ensureGardenInit(ownerId);
    const list = await GardenService.getEvaluationList(roleId, ownerId, { page, pageSize });
    return { count: gardenInfo.EvaluationCount, list: (0, util_1.keysToCamelCase)(list) };
}
/** 评价评论 */
async function gardenEvaluationComment(params) {
    const { roleId, evaluationId, replyCommentId, text } = params;
    const [roleInfo, record] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluation_1.GardenEvaluationModel.checkOne(evaluationId)
    ]);
    await (0, UserPermission_1.checkPermission)(roleId, UserPermission_1.Permission.GardenEvaluationComment, "禁止添加评论");
    let targetId;
    let replyComment;
    let originCommentId = -1;
    if (replyCommentId) {
        replyComment = await GardenEvaluationComment_1.GardenEvaluationCommentModel.checkOne(replyCommentId);
        originCommentId = replyComment.OriginCommentId !== -1 ? replyComment.OriginCommentId : replyComment.ID;
        targetId = replyComment.RoleId;
    }
    else {
        targetId = record.RoleId;
    }
    const id = await GardenEvaluationComment_1.GardenEvaluationCommentModel.insert({
        RoleId: roleId,
        Text: text,
        EvaluationId: evaluationId,
        ReplyRoleId: replyComment?.RoleId || null,
        ReplyCommentId: replyCommentId || null,
        OriginCommentId: originCommentId,
        CreateTime: Date.now()
    });
    if (replyComment) {
        await GardenEvaluationComment_1.GardenEvaluationCommentModel.incrementProperty(replyComment.OriginCommentId !== -1 ? replyComment.OriginCommentId : replyComment.ID, 'ReplyCount');
    }
    else {
        await GardenEvaluation_1.GardenEvaluationModel.incrementProperty(evaluationId, 'CommentCount');
    }
    await GardenInformService.addGardenCommentInform(roleId, targetId, id, replyComment ? GardenInform_1.GardenInformType.EvaluationCommentReply : GardenInform_1.GardenInformType.EvaluationComment);
    (0, yunyinLog_1.addGardenEvaluationCommentLog)(roleInfo, { evaluationId, commentId: id, text: record.Text, replyCommentId });
    return { id };
}
/** 评价评论删除 */
async function gardenEvaluationCommentDel(params) {
    const { roleId, commentId } = params;
    const [roleInfo, commentRecord] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluationComment_1.GardenEvaluationCommentModel.checkOne(commentId, roleId)
    ]);
    await Promise.all([
        GardenEvaluationComment_1.GardenEvaluationCommentModel.updateById(commentId, { Status: -1 /* Statues.Deleted */ }),
        commentRecord.OriginCommentId !== -1 ?
            GardenEvaluationComment_1.GardenEvaluationCommentModel.incrementProperty(commentRecord.OriginCommentId, 'ReplyCount', -1)
            : GardenEvaluation_1.GardenEvaluationModel.incrementProperty(commentRecord.EvaluationId, 'CommentCount', -1),
        GardenInformService.deleteGardenInformByObjectId(commentId, commentRecord.ReplyCommentId ? GardenInform_1.GardenInformType.EvaluationCommentReply : GardenInform_1.GardenInformType.EvaluationComment)
    ]);
    (0, yunyinLog_1.addGardenEvaluationCommentDelLog)(roleInfo, { evaluationId: commentRecord.EvaluationId, commentId, text: commentRecord.Text, replyCommentId: commentRecord.ReplyCommentId });
    return { isOk: true };
}
/** 评论列表-一级评论 */
async function gardenEvaluationCommentList(params) {
    const { roleId, evaluationId, page, pageSize } = params;
    const record = await GardenEvaluation_1.GardenEvaluationModel.checkOne(evaluationId);
    const list = await GardenService.getEvaluationCommentList(roleId, evaluationId, { page, pageSize });
    return { count: record.CommentCount, list: (0, util_1.keysToCamelCase)(list) };
}
/** 评论列表-二级评论 */
async function gardenEvaluationCommentSubList(params) {
    const { roleId, commentId, page, pageSize } = params;
    const record = await GardenEvaluationComment_1.GardenEvaluationCommentModel.checkOne(commentId);
    const list = await GardenService.getEvaluationSubCommentList(roleId, commentId, { page, pageSize });
    return { count: record.ReplyCount, list: (0, util_1.keysToCamelCase)(list) };
}
/** 评论点赞 */
async function gardenEvaluationCommentLike(params) {
    const { roleId, commentId } = params;
    const [roleInfo, record, likeRecord] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluationComment_1.GardenEvaluationCommentModel.checkOne(commentId),
        GardenEvaluationCommentLike_1.GardenEvaluationCommentLikeModel.findOne({ RoleId: roleId, CommentId: commentId })
    ]);
    if (likeRecord && likeRecord.Status === 0 /* Statues.Normal */)
        throw errorCodes_1.GardenErrors.AlreadyLiked;
    const now = Date.now();
    let id;
    if (likeRecord) {
        id = likeRecord.ID;
        await GardenEvaluationCommentLike_1.GardenEvaluationCommentLikeModel.updateById(id, { Status: 0 /* Statues.Normal */, UpdateTime: now });
    }
    else {
        id = await GardenEvaluationCommentLike_1.GardenEvaluationCommentLikeModel.insert({
            RoleId: roleId,
            CommentId: commentId,
            CreateTime: now,
            UpdateTime: now
        });
    }
    await GardenEvaluationComment_1.GardenEvaluationCommentModel.incrementProperty(commentId, 'LikeCount');
    await GardenInformService.addGardenLikeInform(roleId, record.RoleId, id, GardenInform_1.GardenInformType.EvaluationCommentLike);
    (0, yunyinLog_1.addGardenEvaluationCommentLikeLog)(roleInfo, { evaluationId: record.EvaluationId, commentId, text: record.Text, replyCommentId: record.ReplyCommentId });
    const data = {
        isOk: true,
    };
    return data;
}
/** 评论取消点赞 */
async function gardenEvaluationCommentCancelLike(params) {
    const { roleId, commentId } = params;
    const [roleInfo, record, likeRecord] = await Promise.all([
        RoleInfos_1.RoleInfoModel.checkOne(roleId),
        GardenEvaluationComment_1.GardenEvaluationCommentModel.checkOne(commentId),
        GardenEvaluationCommentLike_1.GardenEvaluationCommentLikeModel.findOne({ RoleId: roleId, CommentId: commentId })
    ]);
    if (!likeRecord || likeRecord.Status === -1 /* Statues.Deleted */)
        throw errorCodes_1.GardenErrors.NotLiked;
    await Promise.all([
        GardenEvaluationCommentLike_1.GardenEvaluationCommentLikeModel.updateById(likeRecord.ID, { Status: -1 /* Statues.Deleted */, UpdateTime: Date.now() }),
        GardenEvaluationComment_1.GardenEvaluationCommentModel.incrementProperty(commentId, 'LikeCount', -1),
        GardenInformService.deleteGardenInformByObjectId(likeRecord.ID, GardenInform_1.GardenInformType.EvaluationCommentLike)
    ]);
    (0, yunyinLog_1.addGardenEvaluationCommentCancelLikeLog)(roleInfo, { evaluationId: record.EvaluationId, commentId, text: record.Text, replyCommentId: record.ReplyCommentId });
    const data = {
        isOk: true,
    };
    return data;
}
/**
 * ```
 * 返回字段中type为消息的类型
 * 1 - 新的打卡
 * 2 - 打卡点赞
 * 3 - 打卡评论
 * 4 - 评论点赞
 * 5 - 评论回复
 *
 * 返回字段中status为读取状态
 * 0 - 未读
 * 1 - 已读
 *
 * 查看该接口后,玩家的所有消息的读取状态都会变为已读
 * ```
 */
async function gardenInformList(params) {
    const { roleId, page, pageSize } = params;
    const [count, list] = await Promise.all([
        cache_1.gardenInformCounter.get(roleId),
        GardenInformService.getGardenInformList(roleId, { page, pageSize })
    ]);
    await Promise.all([
        cache_1.gardenInformUnreadCounter.del(roleId),
        GardenInformService.readGardenInformByRoleId(roleId)
    ]);
    return { count, list: (0, util_1.keysToCamelCase)(list) };
}
/** 庄园消息红点 */
async function gardenInformUnread(params) {
    const { roleId } = params;
    const count = await cache_1.gardenInformUnreadCounter.get(roleId);
    return { count };
}
//# sourceMappingURL=operation.js.map