"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forceEventComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/force_event/add",
        paramsSchema: type_1.ReqSchemas.forceEventAdd,
        operation: operation_1.forceEventAdd,
    },
    {
        method: "post",
        url: "/force_event/remark",
        paramsSchema: type_1.ReqSchemas.forceEventRemark,
        operation: operation_1.forceEventRemark,
    },
    {
        method: "get",
        url: "/force_event/list",
        paramsSchema: type_1.ReqSchemas.forceEventList,
        operation: operation_1.forceEventList,
    },
    {
        method: "post",
        url: "/force_event/del",
        paramsSchema: type_1.ReqSchemas.forceEventDel,
        operation: operation_1.forceEventDel,
    },
    {
        method: "post",
        url: "/force_event/like",
        paramsSchema: type_1.ReqSchemas.forceEventLike,
        operation: operation_1.forceEventLike,
    },
    {
        method: "post",
        url: "/force_event/cancel_like",
        paramsSchema: type_1.ReqSchemas.forceEventCancelLike,
        operation: operation_1.forceEventCancelLike,
    },
];
exports.forceEventComponent = {
    paths: exports.paths,
    prefix: "/force_event/",
};
//# sourceMappingURL=index.js.map