"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    forceEventAdd: {
        type: "object",
        properties: {
            uuid: { type: "string" },
            serverId: { type: "number" },
            forceId: { type: "number" },
            eventType: { type: "number" },
            eventTime: { type: "number" },
            eventArgs: { type: "object", properties: { forceId: { type: "number" } } },
        },
        required: ["uuid", "serverId", "forceId", "eventType", "eventTime"],
    },
    forceEventRemark: {
        type: "object",
        properties: { roleId: { type: "number" }, id: { type: "number" }, content: { type: "string" } },
        required: ["roleId", "id", "content"],
    },
    forceEventList: {
        type: "object",
        properties: {
            roleId: { type: "number" },
            forceId: { type: "number" },
            page: { type: "number", default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10 },
        },
        required: ["roleId", "forceId"],
    },
    forceEventDel: {
        type: "object",
        properties: { roleId: { type: "number" }, forceId: { type: "number" } },
        required: ["roleId", "forceId"],
    },
    forceEventLike: {
        type: "object",
        properties: { roleId: { type: "number" }, id: { type: "number" } },
        required: ["roleId", "id"],
    },
    forceEventCancelLike: {
        type: "object",
        properties: { roleId: { type: "number" }, id: { type: "number" } },
        required: ["roleId", "id"],
    },
};
//# sourceMappingURL=type.js.map