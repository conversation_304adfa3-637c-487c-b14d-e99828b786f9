"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forceEventCancelLike = exports.forceEventLike = exports.forceEventDel = exports.forceEventList = exports.forceEventRemark = exports.forceEventAdd = void 0;
/** 新增势力大事 (游戏服务器调用) */
async function forceEventAdd(params) {
    const data = {
        id: 3,
        createTime: 1628340362093,
    };
    return data;
}
exports.forceEventAdd = forceEventAdd;
/** 史官评论势力大事 (游戏服务器调用) */
async function forceEventRemark(params) {
    const data = {
        isOk: true,
    };
    return data;
}
exports.forceEventRemark = forceEventRemark;
/** 势力大事列表 */
async function forceEventList(params) {
    const data = {
        list: [
            {
                uuid: "14f8071127d644d3a8eceae7c638fe94",
                serverId: 12,
                forceId: 13,
                eventType: 10,
                eventTime: 1620628236384,
                eventArgs: {
                    forceId: 1001,
                },
                remark: "史官评注文本",
                remarkTime: 1628340362093,
                likeCount: 1,
                isLiked: false,
            },
        ],
    };
    return data;
}
exports.forceEventList = forceEventList;
/** 删除势力大事 (游戏服务器调用) */
async function forceEventDel(params) {
    const data = {
        isOk: true,
    };
    return data;
}
exports.forceEventDel = forceEventDel;
/** 点赞势力大事 */
async function forceEventLike(params) {
    const data = {
        isOk: true,
    };
    return data;
}
exports.forceEventLike = forceEventLike;
/** 取消点赞势力大事 */
async function forceEventCancelLike(params) {
    const data = {
        isOk: true,
    };
    return data;
}
exports.forceEventCancelLike = forceEventCancelLike;
//# sourceMappingURL=operation.js.map