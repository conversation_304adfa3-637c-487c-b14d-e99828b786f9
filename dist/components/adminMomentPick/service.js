"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllPickInfosFast = void 0;
exports.getAllPickInfos = getAllPickInfos;
exports.getIsPickerFetcher = getIsPickerFetcher;
exports.onDeleteMomentForMomentPick = onDeleteMomentForMomentPick;
const models_1 = require("../../models");
const _ = require("lodash");
const util_1 = require("../../common/util");
const cacheService_1 = require("../../common/cacheService");
const logger_1 = require("../../logger");
const logger = (0, logger_1.clazzLogger)('adminMomentPick/service');
async function getAllPickInfos() {
    const rows = await models_1.MomentPickModel.find({}, { cols: ['momentId', 'pickTime', 'topTime'] });
    const orderRows = _.orderBy(rows, ['topTime', 'pickTime'], ['desc', 'desc']);
    return orderRows;
}
exports.getAllPickInfosFast = (0, cacheService_1.smartMemorize)(getAllPickInfos, {
    keyGen: () => {
        return 'moment_pick_infos';
    },
    expireSeconds: 30
});
// use cache for pick infos use in moment list
async function getIsPickerFetcher(refresh = false) {
    const pickInfos = await (refresh ? getAllPickInfos() : (0, exports.getAllPickInfosFast)());
    const pickIds = pickInfos.map(r => r.momentId);
    const fetcher = (momentId) => {
        return (0, util_1.contains)(pickIds, momentId);
    };
    return fetcher;
}
async function onDeleteMomentForMomentPick(payload) {
    const { momentId } = payload;
    const ret = await models_1.MomentPickModel.deleteByCondition({ momentId });
    if (ret.affectedRows > 0) {
        logger.info({ momentId, ret }, 'onDeleteMomentForMomentPickSuccuss');
    }
}
//# sourceMappingURL=service.js.map