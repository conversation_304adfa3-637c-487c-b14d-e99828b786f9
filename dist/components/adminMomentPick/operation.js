"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminMomentPickList = adminMomentPickList;
exports.adminMomentPickBatch = adminMomentPickBatch;
exports.adminMomentPickRemoveBatch = adminMomentPickRemoveBatch;
exports.adminMomentPickUpdateStatus = adminMomentPickUpdateStatus;
exports.adminMomentPickToggleTop = adminMomentPickToggleTop;
const _ = require("lodash");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const PyqMoments_1 = require("../../models/PyqMoments");
const RoleInfos_1 = require("../../models/RoleInfos");
const moment_1 = require("../../services/moment");
const roleInfo_1 = require("../../services/roleInfo");
const constants_1 = require("./constants");
const service_1 = require("./service");
const logger = (0, logger_1.clazzLogger)('adminMomentPick/operation');
async function getCurPickSize() {
    const cnt = await models_1.MomentPickModel.count({});
    return cnt;
}
/** 查看动态列表 */
async function adminMomentPickList(params) {
    const { pickType, page, pageSize } = params;
    // 只有在精选或者选择roleId的情况下， 计算总页数，防止db卡死
    let useRealCount = false;
    let totalCount = 0;
    const pickInfos = await (0, service_1.getAllPickInfos)();
    const pickMomentIds = pickInfos.map(r => r.momentId);
    const pickInfoHash = (0, util_1.keyToRecordMap)(pickInfos, 'momentId');
    let query = (0, moment_1.publicNormalMomentScope)().orderBy('ID', 'desc');
    if (pickType === 1 /* EPickType.Pick */) {
        query = query.whereIn('ID', pickMomentIds);
        useRealCount = true;
    }
    else if (pickType === -1 /* EPickType.UnPick */) {
        query = query.whereNotIn('ID', pickMomentIds);
    }
    else {
        // pick all, not need to filter
    }
    if (params.kw) {
        const isNum = (0, util_1.isNumeric)(params.kw);
        if (isNum) {
            const roleId = parseInt(params.kw);
            query = query.where('RoleId', roleId);
            useRealCount = true;
        }
        else {
            const roleName = params.kw;
            const rows = await RoleInfos_1.RoleInfoModel.find({ RoleName: roleName });
            const roleIds = rows.map(r => r.RoleId);
            if (roleIds && roleIds.length > 0) {
                query = query.whereIn('RoleId', roleIds);
                useRealCount = true;
            }
        }
    }
    const countQuery = _.cloneDeep(query);
    if (useRealCount) {
        totalCount = await models_1.MomentModel.countByQuery(countQuery);
    }
    else {
        totalCount = constants_1.PoolMomentMaxSize;
    }
    let moments = [];
    if (pickType === 1 /* EPickType.Pick */) {
        moments = await models_1.MomentModel.smartQuery(query, models_1.MomentCols);
        moments = _.compact(pickMomentIds.map(id => moments.find(r => r.ID === id)));
        moments = (0, util_1.getPageDataByList)(moments, { page: params.page, pageSize: params.pageSize });
    }
    else {
        moments = await models_1.MomentModel.powerQuery({ select: models_1.MomentCols, initQuery: query, pagination: { page, pageSize } });
    }
    const totalPage = Math.ceil(totalCount / pageSize);
    const meta = {
        curPage: page,
        totalPage,
        totalCount,
    };
    const queryRoleIds = moments.map(r => r.RoleId);
    const roleNameFetcher = await (0, roleInfo_1.getRoleNameFetcher)(queryRoleIds);
    let list = moments.map(r => {
        const imgList = (0, util_1.csvStrToArray)(r.ImgList);
        const momentId = r.ID;
        let hotState = PyqMoments_1.Moment.formatHotState(r.HotState);
        const isPicked = (0, util_1.contains)(pickMomentIds, momentId);
        const pickInfo = pickInfoHash.get(momentId) || { topTime: 0, pickTime: 0 };
        const commentCount = hotState.comment + hotState.reply;
        const item = {
            id: r.ID,
            roleId: r.RoleId,
            roleName: roleNameFetcher(r.RoleId),
            text: r.Text,
            imgList,
            likeCount: hotState.like,
            commentCount,
            isPicked: isPicked,
            createTime: r.CreateTime,
            pickTime: pickInfo.pickTime,
            isTop: pickInfo.topTime > 0,
            topTime: pickInfo.topTime,
        };
        return item;
    });
    if (pickType === 1 /* EPickType.Pick */) {
        list = _.orderBy(list, ['topTime', 'createTime'], ['desc', 'desc']);
    }
    const data = { list, meta };
    return data;
}
/** 入选动态精选 */
async function adminMomentPickBatch(ids) {
    const pickCnt = await getCurPickSize();
    const toAddSize = ids.length;
    if (pickCnt + toAddSize >= constants_1.MaxPickSize) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MomentPickErrors.PickExceedMaxSize);
    }
    const pickRows = await models_1.MomentPickModel.findOne({ momentId: ids });
    if (!_.isEmpty(pickRows)) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MomentPickErrors.BatchPickContainPicked);
    }
    const rows = await models_1.MomentModel.find({ ID: ids, Status: 0 /* Statues.Normal */ }, { cols: ['ID', 'CreateTime'] });
    if (_.isEmpty(rows)) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MomentPickErrors.BatchPickMomentNotFound);
    }
    const momentMap = (0, util_1.keyToRecordMap)(rows, 'ID');
    const props = rows.map(r => {
        const m = momentMap.get(r.ID) || { CreateTime: 0 };
        return { momentId: r.ID, pickTime: m.CreateTime, topTime: 0 };
    });
    await models_1.MomentPickModel.insertBatch(props);
    return true;
}
/** 移出动态精选 */
async function adminMomentPickRemoveBatch(ids) {
    const rows = await models_1.MomentModel.find({ ID: ids }, { cols: ['ID'] });
    if (rows && rows.length > 0) {
        const ret = await models_1.MomentPickModel.delete({ momentId: ids });
        const isOk = ret.affectedRows > 0;
        return isOk;
    }
    else {
        return (0, errorCodes_1.BussError)(errorCodes_1.MomentPickErrors.BatchPickMomentNotFound);
    }
}
/** 批量更新精选状态 */
async function adminMomentPickUpdateStatus(params) {
    const { ids } = params;
    let isOk = false;
    logger.debug(params, 'adminMomentPickUpdateStatus');
    if (params.isPick) {
        isOk = await adminMomentPickBatch(ids);
    }
    else {
        isOk = await adminMomentPickRemoveBatch(ids);
    }
    const data = { isOk };
    return data;
}
/** 移动该精选状态到最前 */
async function adminMomentPickToggleTop(params) {
    const momentId = params.momentId;
    const topTime = params.switch === "on" ? Date.now() : 0;
    const m = await models_1.MomentPickModel.findOne({ momentId });
    if (!m) {
        return (0, errorCodes_1.BussError)(errorCodes_1.MomentPickErrors.MomentNotPicked);
    }
    const ret = await models_1.MomentPickModel.updateByCondition({ momentId }, { topTime });
    const isOk = ret.affectedRows > 0;
    const data = { isOk };
    return data;
}
//# sourceMappingURL=operation.js.map