"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminMomentPickComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/admin/moment_pick/list",
        paramsSchema: type_1.ReqSchemas.AdminMomentPickList,
        operation: operation_1.adminMomentPickList,
    },
    {
        method: "post",
        url: "/admin/moment_pick/update_status",
        paramsSchema: type_1.ReqSchemas.AdminMomentPickUpdateStatus,
        operation: operation_1.adminMomentPickUpdateStatus,
    },
    {
        method: "post",
        url: "/admin/moment_pick/toggle_top",
        paramsSchema: type_1.ReqSchemas.AdminMomentPickToggleTop,
        operation: operation_1.adminMomentPickToggleTop,
    },
];
exports.AdminMomentPickComponent = {
    paths: exports.paths,
    prefix: "/admin/moment_pick/",
};
//# sourceMappingURL=index.js.map