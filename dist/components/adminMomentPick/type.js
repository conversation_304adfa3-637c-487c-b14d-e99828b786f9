"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    AdminMomentPickList: {
        pickType: { type: Number, values: [-1, 0, 1] },
        kw: { type: String, required: false },
        page: { type: Number, default: 1 },
        pageSize: { type: Number, max: 20, default: 10 },
    },
    AdminMomentPickUpdateStatus: {
        ids: { type: Array, items: { type: Number } },
        isPick: { type: Boolean },
    },
    AdminMomentPickToggleTop: {
        momentId: { type: Number },
    },
};
//# sourceMappingURL=type.js.map