"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.damageStatAdd = damageStatAdd;
exports.damageStatList = damageStatList;
exports.damageStatShareInfo = damageStatShareInfo;
const nanoid_1 = require("nanoid");
const cacheUtil_1 = require("../../common/cacheUtil");
const config_1 = require("../../common/config");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const model_1 = require("./model");
const logger = (0, logger_1.clazzLogger)("damageStat/operation");
/** 新增伤害统计数据 */
async function damageStatAdd(params) {
    const shareId = (0, nanoid_1.nanoid)();
    if (!(0, util_1.isBase64Str)(params.recordStr)) {
        throw errorCodes_1.DamageStatErrors.RecordStrIsNotBase64;
    }
    const addProp = {
        roleId: params.playerId,
        gamePlayId: params.gamePlayId,
        bossId: params.bossId,
        shareId,
        record: Buffer.from(params.recordStr, "base64"),
        recordTime: params.recordTime,
        hurtPerSec: params.hurtPerSec,
        curePerSec: params.curePerSec,
        status: 0 /* Statues.Normal */,
    };
    const condition = { roleId: params.roleid, bossId: params.bossId, gamePlayId: params.gamePlayId };
    const cnt = await model_1.DamageRecordModel.countByGamePlayBoss(condition);
    let id = 0;
    if (cnt >= config_1.damageStatCfg.keepRecentSize) {
        const lockKey = (0, util_1.cacheKeyGen)("damage_record_replace_oldest", condition);
        const ownerId = (0, nanoid_1.nanoid)();
        const hasLock = await cacheUtil_1.RedisLock.optimistic(lockKey, ownerId, 12000, 200, 50);
        if (hasLock) {
            const statInfo = await model_1.DamageStatModel.findOne(condition);
            const toBeReplace = await model_1.DamageRecordModel.getReplaceOne(condition, statInfo);
            if (toBeReplace && toBeReplace.id) {
                id = toBeReplace.id;
                await model_1.DamageRecordModel.updateById(toBeReplace.id, addProp);
            }
            else {
                id = await model_1.DamageRecordModel.insert(addProp);
            }
        }
        else {
            logger.warn({ lockKey, addProp }, "DamageRecordReplaceOldestLockError");
        }
    }
    else {
        id = await model_1.DamageRecordModel.insert(addProp);
    }
    model_1.DamageStatModel.onNewRecord({ id, ...addProp });
    const data = {
        id,
        shareId,
    };
    return data;
}
/**
 * 1. 列表保留最新20条
 * 2. 以及计算历史最高秒伤和最高治疗
 */
async function damageStatList(params) {
    const rows = await model_1.DamageRecordModel.powerQuery({
        initQuery: model_1.DamageRecordModel.normalScope(),
        where: { roleId: params.roleid, bossId: params.bossId, gamePlayId: params.gamePlayId },
        select: model_1.DamageRecordCols,
        orderBy: [
            ["recordTime", "updatedAt"],
            ["desc", "desc"],
        ],
        pagination: { page: 1, pageSize: config_1.damageStatCfg.keepRecentSize },
    });
    const list = rows.map((r) => model_1.DamageRecordModel.formatRecord(r));
    const data = { list };
    return data;
}
/** 通过分享id获取某一个伤害统计数据 */
async function damageStatShareInfo(params) {
    const r = await model_1.DamageRecordModel.findOne({ shareId: params.shareId, status: 0 /* Statues.Normal */ });
    if (r && r.id) {
        const data = model_1.DamageRecordModel.formatRecord(r);
        return data;
    }
    else {
        throw errorCodes_1.DamageStatErrors.ShareInfoNotFound;
    }
}
//# sourceMappingURL=operation.js.map