"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DamageStatComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/damage_stat/add",
        paramsSchema: type_1.ReqSchemas.DamageStatAdd,
        operation: operation_1.damageStatAdd,
        before: gameIpLimit_1.gameIpLimit,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/damage_stat/list",
        paramsSchema: type_1.ReqSchemas.DamageStatList,
        operation: operation_1.damageStatList,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/damage_stat/share_info",
        paramsSchema: type_1.ReqSchemas.DamageStatShareInfo,
        operation: operation_1.damageStatShareInfo,
        option: { skipSkey: true },
    },
];
exports.DamageStatComponent = {
    paths: exports.paths,
    prefix: "/damage_stat/",
};
//# sourceMappingURL=index.js.map