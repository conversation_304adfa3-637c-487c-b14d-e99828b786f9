"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DamageStatModel = exports.DamageStatCols = exports.DamageRecordModel = exports.DamageRecordCols = void 0;
const nanoid_1 = require("nanoid");
const cacheUtil_1 = require("../../common/cacheUtil");
const util_1 = require("../../common/util");
const logger_1 = require("../../logger");
const BaseModel2_1 = require("../../models/BaseModel2");
const logger = (0, logger_1.clazzLogger)("damageStat/model");
exports.DamageRecordCols = [
    "id",
    "roleId",
    "gamePlayId",
    "bossId",
    "recordTime",
    "record",
    "hurtPerSec",
    "curePerSec",
    "shareId",
    "status",
    "createdAt",
    "updatedAt",
];
class DamageRecordModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_damage_record");
    }
    formatRecord(r) {
        const recordStr = Buffer.isBuffer(r.record) ? r.record.toString("base64") : "";
        return {
            id: r.id,
            shareId: r.shareId,
            playerId: r.roleId,
            gamePlayId: r.gamePlayId,
            bossId: r.bossId,
            recordTime: r.recordTime,
            recordStr: recordStr,
            hurtPerSec: r.hurtPerSec,
            curePerSec: r.curePerSec,
        };
    }
    async countByGamePlayBoss(params) {
        const cnt = await this.count(params);
        return cnt;
    }
    /** 超出上限自动删除最早的记录，伤害/治疗最高的最高 */
    async getReplaceOne(params, r) {
        let query = this.scope();
        if (r && r.maxHurtRecordId) {
            query = query.whereNot("id", r.maxHurtRecordId);
        }
        if (r && r.maxCureRecordId) {
            query = query.whereNot("id", r.maxCureRecordId);
        }
        const rows = await this.powerQuery({
            initQuery: query,
            where: params,
            select: exports.DamageRecordCols,
            orderBy: [
                ["recordTime", "updatedAt"],
                ["asc", "asc"],
            ],
            pagination: { page: 1, pageSize: 1 },
        });
        if (rows.length > 0) {
            return rows[0];
        }
        else {
            return null;
        }
    }
}
exports.DamageRecordModel = new DamageRecordModelClass();
exports.DamageStatCols = [
    "id",
    "roleId",
    "gamePlayId",
    "bossId",
    "maxHurtPerSec",
    "maxHurtRecordId",
    "maxCurePerSec",
    "maxCureRecordId",
    "updatedAt",
];
class DamageStatModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_damage_stat");
    }
    async getStatInfo(params) {
        const r = await this.findOne(params);
        if (r && r.id) {
            return { maxHurtPerSec: r.maxHurtPerSec, maxCurePerSec: r.maxCurePerSec };
        }
        else {
            return { maxHurtPerSec: 0, maxCurePerSec: 0 };
        }
    }
    async onNewRecord(newOne) {
        const ownerId = (0, nanoid_1.nanoid)();
        try {
            const condition = {
                roleId: newOne.roleId,
                gamePlayId: newOne.gamePlayId,
                bossId: newOne.bossId,
            };
            const lockKey = (0, util_1.cacheKeyGen)("damage_stat_on_new_record", condition);
            const hasLock = await cacheUtil_1.RedisLock.optimistic(lockKey, ownerId, 5000, 40, 100);
            if (hasLock) {
                const current = await this.findOne(condition);
                if (current && current.id) {
                    let query = this.scope().where("id", current.id);
                    const upProp = {};
                    let hasUpdate = false;
                    if (newOne.hurtPerSec >= current.maxHurtPerSec) {
                        hasUpdate = true;
                        upProp.maxHurtPerSec = newOne.hurtPerSec;
                        upProp.maxHurtRecordId = newOne.id;
                    }
                    if (newOne.curePerSec >= current.maxCurePerSec) {
                        hasUpdate = true;
                        upProp.maxCurePerSec = newOne.curePerSec;
                        upProp.maxCureRecordId = newOne.id;
                    }
                    if (hasUpdate) {
                        query = query.update(upProp);
                        const r = await this.executeByQuery(query);
                        await cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
                        return r;
                    }
                    else {
                        await cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
                    }
                }
                else {
                    const props = {
                        ...condition,
                        maxHurtPerSec: newOne.hurtPerSec,
                        maxHurtRecordId: newOne.id,
                        maxCurePerSec: newOne.curePerSec,
                        maxCureRecordId: newOne.id,
                    };
                    const r = await this.insert(props);
                    await cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
                    return r;
                }
            }
            else {
                logger.error({ lockKey, damageRecord: newOne }, "DamageStatOnNewRecordLockError");
            }
        }
        catch (err) {
            logger.error({ err, damageRecord: newOne }, "DamageStatOnNewRecordError");
        }
    }
}
exports.DamageStatModel = new DamageStatModelClass();
//# sourceMappingURL=model.js.map