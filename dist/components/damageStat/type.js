"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    DamageStatAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            playerId: { type: "number" },
            gamePlayId: { type: "number" },
            bossId: { type: "number" },
            recordTime: { type: "number" },
            recordStr: { type: "string" },
            hurtPerSec: { type: "number" },
            curePerSec: { type: "number" },
        },
        required: ["roleid", "playerId", "gamePlayId", "bossId", "recordTime", "recordStr", "hurtPerSec", "curePerSec"],
    },
    DamageStatList: {
        type: "object",
        properties: { roleid: { type: "number" }, gamePlayId: { type: "number" }, bossId: { type: "number" } },
        required: ["roleid", "gamePlayId", "bossId"],
    },
    DamageStatShareInfo: {
        type: "object",
        properties: { roleid: { type: "number" }, shareId: { type: "string" } },
        required: ["roleid", "shareId"],
    },
};
//# sourceMappingURL=type.js.map