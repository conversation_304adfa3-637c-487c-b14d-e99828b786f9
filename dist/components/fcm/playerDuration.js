"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayDurationModel = exports.DurationType = void 0;
const BaseModelClass_1 = require("../../models/BaseModelClass");
var DurationType;
(function (DurationType) {
    DurationType[DurationType["IN2OUT"] = 0] = "IN2OUT";
    DurationType[DurationType["IN2IN"] = 1] = "IN2IN";
})(DurationType || (exports.DurationType = DurationType = {}));
class PlayDurationModelClass extends BaseModelClass_1.BaseModelClass {
    constructor() {
        super("nsh_play_duration");
    }
}
exports.PlayDurationModel = new PlayDurationModelClass();
//# sourceMappingURL=playerDuration.js.map