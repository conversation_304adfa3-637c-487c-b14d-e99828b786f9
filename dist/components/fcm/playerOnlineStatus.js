"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerOnlineStatus = exports.OnlineStatus = void 0;
const moment = require("moment");
const redis_1 = require("../../common/redis");
const logger_1 = require("../../logger");
const playerDuration_1 = require("./playerDuration");
const config_1 = require("../../common/config");
const util_1 = require("../../common/util");
var OnlineStatus;
(function (OnlineStatus) {
    /** 获取状态不存在的时候 */
    OnlineStatus["INIT"] = "init";
    /** 在线 */
    OnlineStatus["ONLINE"] = "on";
    /** 离线 */
    OnlineStatus["OFFLINE"] = "off";
})(OnlineStatus || (exports.OnlineStatus = OnlineStatus = {}));
class PlayerOnlineStatus {
    constructor(gameId, username) {
        this.gameId = gameId;
        this.username = username;
        this.logger = logger_1.fcmLogger.child({ clazz: "fcm/playerOnlineStatus", gameId, username });
    }
    getKey() {
        return `fcm:${this.gameId}:${this.username}:online_status`;
    }
    getDailyOnlineTimeKey(ts) {
        const ds = (0, util_1.formatDate)(ts, "yyyyMMdd");
        return `fcm:${this.gameId}:${this.username}:${ds}:daily_online_time`;
    }
    async getDailyOnlineTime(now) {
        const key = this.getDailyOnlineTimeKey(now);
        const val = await (0, redis_1.getRedis)().getAsync(key);
        if (val) {
            return parseInt(val, 10);
        }
        else {
            return 0;
        }
    }
    async incrDailyOnlineTime(pdm) {
        const { loginTime, logoutTime } = pdm;
        const incr = PlayerOnlineStatus.calCurDailyPlayDuration(new Date(loginTime), new Date(logoutTime));
        if (incr > 0) {
            const key = this.getDailyOnlineTimeKey(logoutTime);
            this.logger.info({ pdm, incr, key }, "IncrDailyOnlineTime");
            await (0, redis_1.getRedis)().incrbyAsync(key, incr);
            await (0, redis_1.getRedis)().expireAsync(key, config_1.ONE_DAY_SECONDS * config_1.parentEscortCfg.dailyPlayDurationKeepDays);
        }
        else {
            this.logger.error({ pdm, incr }, "incrDailyOnlineTimeFail");
        }
    }
    async getState() {
        const ret = (await (0, redis_1.getRedis)().hgetallAsync(this.getKey()));
        if (ret) {
            const loginTime = parseInt(ret.loginTime, 10);
            return { status: ret.status, loginTime };
        }
        return {
            status: OnlineStatus.INIT,
            loginTime: 0,
        };
    }
    async savePlayDuration(roleId, loginTime, logoutTime, type) {
        const duration = logoutTime.getTime() - loginTime.getTime();
        if (loginTime && duration < config_1.parentEscortCfg.minimalPlayDuration) {
            this.logger.info({
                roleId,
                loginTime: (0, util_1.formatDate)(loginTime),
                type,
                logoutTime: (0, util_1.formatDate)(logoutTime),
                duration,
                minDuration: config_1.parentEscortCfg.minimalPlayDuration,
            }, "PlayDurationDiscard");
            return false;
        }
        const ds = (0, util_1.formatDate)(logoutTime, "yyyyMMdd");
        const pdm = {
            ds,
            loginTime: loginTime.getTime(),
            logoutTime: logoutTime.getTime(),
            gameId: this.gameId,
            username: this.username,
            type,
        };
        await this.incrDailyOnlineTime(pdm);
        if (config_1.parentEscortCfg.playDurationLogToDb) {
            await playerDuration_1.PlayDurationModel.insert(pdm);
            return true;
        }
        else {
            return true;
        }
    }
    async login(roleId, curLoginTime) {
        const state = await this.getState();
        if (state.status === OnlineStatus.INIT || state.status === OnlineStatus.OFFLINE) {
            await (0, redis_1.getRedis)().hsetAsync(this.getKey(), "status", OnlineStatus.ONLINE, "loginTime", "" + curLoginTime.getTime());
        }
        else {
            const lastLoginTime = new Date(state.loginTime);
            /** 登录状态下在登陆， 把此次登录时间作为登出时间结算一次时长
             login -> login,  如果事件间隔大于一定间隔，我们认为这种情况中间丢失了一次logout, 并且结算一次时长
            */
            const saveRet = await this.savePlayDuration(roleId, lastLoginTime, curLoginTime, playerDuration_1.DurationType.IN2IN);
            if (saveRet) {
                //如果完成结算，需要重新设置登录时间
                await (0, redis_1.getRedis)().hsetAsync(this.getKey(), "status", OnlineStatus.ONLINE, "loginTime", "" + curLoginTime.getTime());
            }
        }
        await (0, redis_1.getRedis)().expireAsync(this.getKey(), config_1.parentEscortCfg.onlineStatusExpireDays * config_1.ONE_DAY_SECONDS);
    }
    async logout(roleId, logoutTime) {
        const state = await this.getState();
        if (state.status === OnlineStatus.ONLINE) {
            const loginTime = new Date(state.loginTime);
            // 保存登录间隔，记录消耗时长
            await this.savePlayDuration(roleId, loginTime, logoutTime, playerDuration_1.DurationType.IN2OUT);
        }
        else {
            this.logger.warn({ now: logoutTime, state }, "PlayerLogoutNotAfterLogin");
        }
        await (0, redis_1.getRedis)().delAsync(this.getKey());
    }
    static calCurDailyPlayDuration(loginTime, logoutTime) {
        const todayZero = moment(logoutTime).startOf("day");
        const todayLoginTime = moment.max(todayZero, moment(loginTime));
        return moment(logoutTime).diff(todayLoginTime, "seconds");
    }
    /** 获取今日当前在线游玩时长,
     * 该函数只计算本次游玩在线时长中的当日部分, 会剔除跨日时长以及今日累计时间
     *  等于今日多段累计
     * */
    async getCurDailyPlayDuration(today) {
        const state = await this.getState();
        const dailyAccDuration = await this.getDailyOnlineTime(today.getTime());
        let sessionDuration = 0;
        if (state.status === OnlineStatus.ONLINE) {
            sessionDuration = PlayerOnlineStatus.calCurDailyPlayDuration(new Date(state.loginTime), today);
        }
        return Math.min(dailyAccDuration + sessionDuration, config_1.ONE_DAY_SECONDS);
    }
    static create(gameId, username) {
        return new PlayerOnlineStatus(gameId, username);
    }
}
exports.PlayerOnlineStatus = PlayerOnlineStatus;
//# sourceMappingURL=playerOnlineStatus.js.map