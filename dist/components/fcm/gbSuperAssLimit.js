"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GbSuperAssLimitApi = exports.ResponseCode = void 0;
const crypto = require("crypto");
const config_1 = require("../../common/config");
const request_1 = require("../../common/request");
const logger_1 = require("../../logger");
const logger = logger_1.fcmLogger.child({ clazz: "fcm/gbSuperAssLimit" });
var ResponseCode;
(function (ResponseCode) {
    ResponseCode[ResponseCode["Success"] = 1] = "Success";
    ResponseCode[ResponseCode["InvalidParam"] = -1] = "InvalidParam";
    ResponseCode[ResponseCode["GameNotSupport"] = -2] = "GameNotSupport";
    ResponseCode[ResponseCode["AccountNotExist"] = -3] = "AccountNotExist";
    ResponseCode[ResponseCode["InvalidAccount"] = -4] = "InvalidAccount";
    ResponseCode[ResponseCode["AccountAbnormal"] = -5] = "AccountAbnormal";
    ResponseCode[ResponseCode["RecordNotExist"] = -6] = "RecordNotExist";
    ResponseCode[ResponseCode["DatabaseError"] = -7] = "DatabaseError";
    ResponseCode[ResponseCode["IPNotAllowed"] = -8] = "IPNotAllowed";
    ResponseCode[ResponseCode["InternalError"] = -9] = "InternalError";
    ResponseCode[ResponseCode["ExternalError"] = -10] = "ExternalError";
    ResponseCode[ResponseCode["SignError"] = -11] = "SignError";
})(ResponseCode || (exports.ResponseCode = ResponseCode = {}));
const ResponseMessage = {
    [ResponseCode.Success]: "成功",
    [ResponseCode.InvalidParam]: "参数报错",
    [ResponseCode.GameNotSupport]: "游戏不支持",
    [ResponseCode.AccountNotExist]: "账号不存在",
    [ResponseCode.InvalidAccount]: "无效账号(如非官网账号、未实名账号)",
    [ResponseCode.AccountAbnormal]: "该账号处于异常状态，暂不提供调整防沉迷规则",
    [ResponseCode.RecordNotExist]: "记录不存在",
    [ResponseCode.DatabaseError]: "数据库错误",
    [ResponseCode.IPNotAllowed]: "IP禁止访问",
    [ResponseCode.InternalError]: "系统内部错误",
    [ResponseCode.ExternalError]: "系统外部错误",
    [ResponseCode.SignError]: "签名错误",
};
function generateSignature(method, path_qs, body, key) {
    const str_to_sign = `${method.toUpperCase()}${path_qs}${body}`;
    const hmac = crypto.createHmac("sha256", key);
    const signature = hmac.update(str_to_sign).digest("hex");
    return signature;
}
class GbSuperAssLimitApi {
    requestUrl(pathQs) {
        const url = `${config_1.parentEscortCfg.apiHost}/octopus` + pathQs;
        return url;
    }
    get pathQs() {
        const pathQs = `/gm/gb_super_aas_limit/${this.gameId}/urs/${this.account}`;
        return pathQs;
    }
    get pathQsForBatch() {
        const pathQs = `/gm/gb_super_aas_limit/urs/${this.account}`;
        return pathQs;
    }
    constructor(gameId, account) {
        this.gameId = gameId;
        this.account = account;
    }
    async commonRequest(method, pathQs, body) {
        const url = this.requestUrl(pathQs);
        const bodyStr = body ? JSON.stringify(body) : "";
        const sign = generateSignature(method, pathQs, bodyStr, config_1.parentEscortCfg.secretKey);
        const reqOpt = {
            url,
            method,
            headers: { "X-Server-Sign": sign },
            timeout: config_1.parentEscortCfg.timeout,
            json: undefined,
        };
        if (method === "POST") {
            reqOpt.json = body;
        }
        try {
            const ret = await (0, request_1.request)(reqOpt);
            if (ret && ret.code === ResponseCode.Success) {
                logger.debug({ reqOpt, ret }, "GbSuperAasApiOk");
                return ret;
            }
            else {
                const failMsg = ResponseMessage[ret?.code] || "未知错误";
                if (ret?.code === ResponseCode.RecordNotExist) {
                    logger.warn({ reqOpt, ret, failMsg }, "GbSuperAasApiBizErrorRecordNotExist");
                }
                else {
                    logger.error({ reqOpt, ret, failMsg }, "GbSuperAasApiBizError");
                }
                return ret;
            }
        }
        catch (err) {
            logger.error({ err, reqOpt }, "GbSuperAasApiError");
            return null;
        }
    }
    async get() {
        return this.pickFromBatch();
    }
    async rawGet() {
        return this.commonRequest("GET", this.pathQs);
    }
    async delete() {
        return this.commonRequest("DELETE", this.pathQs);
    }
    /** 批量查询, 此时不应该在url参数中使用gameId*/
    async getBatch() {
        return this.commonRequest("GET", this.pathQsForBatch);
    }
    async post(body) {
        return this.commonRequest("POST", this.pathQs, body);
    }
    static create(gameid, account) {
        return new GbSuperAssLimitApi(gameid, account);
    }
    async pickFromBatch() {
        const res = await this.getBatch();
        const list = res.data;
        if (!res.data)
            return { code: res.code, msg: res.msg, data: null };
        if (!Array.isArray(res.data))
            return { code: res.code, msg: res.msg, data: null };
        const ruleForGameId = list.find((x) => x.gameId === this.gameId);
        if (ruleForGameId) {
            return { code: res.code, msg: res.msg, data: ruleForGameId };
        }
        const ruleForAll = list.find((x) => x.gameId === "all");
        if (ruleForAll) {
            return { code: res.code, msg: res.msg, data: ruleForAll };
        }
        return { code: res.code, msg: res.msg, data: null };
    }
}
exports.GbSuperAssLimitApi = GbSuperAssLimitApi;
//# sourceMappingURL=gbSuperAssLimit.js.map