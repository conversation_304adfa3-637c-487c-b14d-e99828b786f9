"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlayerOnlineStatus = exports.OnlineStatus = void 0;
const moment = require("moment");
const redis_1 = require("../../common/redis");
const util_1 = require("../../common/util");
const logger_1 = require("../../logger");
var OnlineStatus;
(function (OnlineStatus) {
    /** 获取状态不存在的时候 */
    OnlineStatus["INIT"] = "init";
    /** 在线 */
    OnlineStatus["ONLINE"] = "on";
    /** 离线 */
    OnlineStatus["OFFLINE"] = "off";
})(OnlineStatus || (exports.OnlineStatus = OnlineStatus = {}));
class PlayerOnlineStatus {
    constructor(gameId, username) {
        this.gameId = gameId;
        this.username = username;
        this.logger = logger_1.fcmLogger.child({ clazz: "fcm/playerOnlineStatus", gameId, username });
    }
    getKey() {
        return `game:${this.gameId}:user:${this.username}:status`;
    }
    async getState() {
        const ret = await (0, redis_1.getRedis)().hgetallAsync(this.getKey());
        if (ret) {
            return ret;
        }
        return {
            status: OnlineStatus.INIT,
            loginTime: "",
            logoutTime: "",
        };
    }
    async login(ts) {
        const state = await this.getState();
        if (state.status === OnlineStatus.INIT || state.status === OnlineStatus.OFFLINE) {
            await (0, redis_1.getRedis)().hsetAsync(this.getKey(), "status", OnlineStatus.ONLINE, "loginTime", (0, util_1.formatDate)(ts));
        }
        else {
            // login -> login,  如果事件间隔大于1分钟，我们认为这种情况中间丢失了一次logout, 并且结算一次时长
            if (state.loginTime && moment(ts).diff(moment(state.loginTime), "minutes") >= 1) {
                // 保存登录间隔，记录消耗时长
            }
            else {
                // 小于1分钟
                this.logger.warn({ ts, state }, "PlayerLoginAfterLoginTooClose");
            }
        }
    }
    async logout(ts) {
        const state = await this.getState();
        if (state.status === OnlineStatus.ONLINE) {
            await (0, redis_1.getRedis)().hsetAsync(this.getKey(), "status", OnlineStatus.OFFLINE, "logoutTime", (0, util_1.formatDate)(ts));
            // 保存登录间隔，记录消耗时长
        }
        else {
            this.logger.warn({ ts, state }, "PlayerLogoutNotAfterLogin");
        }
    }
    /** 获取今日当前游玩时长, 该函数只计算本次游玩在线时长中的当日部分, 回取出跨日时间以及今日累计时间*/
    async getTodayCurrentPlayTime(today) {
        const state = await this.getState();
        if (state.status === OnlineStatus.ONLINE) {
            const todayZero = moment(today).startOf("day");
            const todayLoginTime = moment.max(todayZero, moment(state.loginTime));
            return moment(today).diff(todayLoginTime, "seconds");
        }
        else {
            return 0;
        }
    }
}
exports.PlayerOnlineStatus = PlayerOnlineStatus;
//# sourceMappingURL=playerDailyOnlineTime.js.map