"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HolidayModel = exports.HolidayCols = void 0;
exports.getYearDefaultHolidays = getYearDefaultHolidays;
exports.refreshHoliday = refreshHoliday;
exports.scrapeHolidayDate = scrapeHolidayDate;
const config_1 = require("../../common/config");
const request_1 = require("../../common/request");
const util_1 = require("../../common/util");
const logger_1 = require("../../logger");
const BaseModel2_1 = require("../../models/BaseModel2");
const logger = logger_1.fcmLogger.child({ clazz: "fcm/holidayModel" });
function getYearDefaultHolidays(year) {
    year = year || new Date().getFullYear();
    return { year, papers: [], days: [] };
}
exports.HolidayCols = ["year", "content", "url"];
class HolidayModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_holiday_cn");
    }
    async getYearHolidays(year) {
        const r = await this.findOne({ year });
        if (r && r.content) {
            return (0, util_1.getJsonInfo)(r.content, getYearDefaultHolidays(year));
        }
        else {
            return getYearDefaultHolidays(year);
        }
    }
}
exports.HolidayModel = new HolidayModelClass();
async function refreshHoliday(year) {
    year = year || new Date().getFullYear();
    for (const urlTpl of config_1.holidayCnCfg.scrapeUrls) {
        const url = urlTpl.replace("{year}", year.toString());
        const data = await scrapeHolidayDate(url, year);
        if (data) {
            const ret = await exports.HolidayModel.createOrUpdate({ year, content: JSON.stringify(data), url });
            logger.info({ url, year, ret }, "saveScrapeContent");
            break;
        }
        else {
            logger.warn({ url, year }, "scrapeFailFromUrl");
        }
    }
}
async function scrapeHolidayDate(fetchUrl, year) {
    const reqOpt = {
        method: "GET",
        url: fetchUrl,
        timeout: config_1.holidayCnCfg.timeout,
    };
    try {
        const ret = await (0, request_1.request)(reqOpt);
        if (ret && ret.year === year) {
            return ret;
        }
        else {
            logger.error({ fetchUrl, year, reqOpt }, "scrapeHolidayDateFail");
        }
    }
    catch (err) {
        logger.warn({ err, fetchUrl, year, reqOpt }, "scrapeHolidayDateError");
        return null;
    }
}
//# sourceMappingURL=holidayModel.js.map