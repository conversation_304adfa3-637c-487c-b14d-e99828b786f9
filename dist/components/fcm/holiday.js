"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HolidayCN = exports.HolidayCNCacheClass = void 0;
const moment = require("moment");
const holidayModel_1 = require("./holidayModel");
const cacheUtil_1 = require("../../services/cacheUtil");
const constants_1 = require("../../common/constants");
const logger_1 = require("../../logger");
const logger = logger_1.fcmLogger.child({ clazz: "fcm/holiday" });
class HolidayCNCacheClass extends cacheUtil_1.GenericLazyCache {
    getTempResultForCacheMiss(year) {
        return (0, holidayModel_1.getYearDefaultHolidays)(year);
    }
    getKey(year) {
        return `holiday_cn_${year}`;
    }
    getExpireTime(params) {
        return 3 * constants_1.ONE_DAY_SECONDS;
    }
    fetchDataSource(year) {
        return holidayModel_1.HolidayModel.getYearHolidays(year);
    }
    static getInstance() {
        if (!HolidayCNCacheClass.instance) {
            this.instance = new HolidayCNCacheClass();
        }
        return HolidayCNCacheClass.instance;
    }
}
exports.HolidayCNCacheClass = HolidayCNCacheClass;
HolidayCNCacheClass.instance = null;
class HolidayCN {
    static async isHoliday(date) {
        const year = date.getFullYear();
        const holidays = await this.getYearHolidays(year);
        if (this.isWeekend(date)) {
            return !holidays.days.some((day) => moment(date).isSame(day.date, "day") && !day.isOffDay);
        }
        else {
            return holidays.days.some((day) => moment(date).isSame(day.date, "day") && day.isOffDay);
        }
    }
    static isWeekend(date) {
        const dayOfWeek = date.getDay();
        return dayOfWeek === 0 || dayOfWeek === 6;
    }
    static async getYearHolidays(year) {
        return HolidayCNCacheClass.getInstance().get(year);
    }
}
exports.HolidayCN = HolidayCN;
//# sourceMappingURL=holiday.js.map