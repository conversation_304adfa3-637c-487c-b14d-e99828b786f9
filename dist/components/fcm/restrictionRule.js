"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameRestrictionsManager = void 0;
const moment = require("moment");
const holiday_1 = require("./holiday");
const playerOnlineStatus_1 = require("./playerOnlineStatus");
const dateUtil_1 = require("../../common/dateUtil");
class GameRestrictionsManager {
    constructor(gameId, username, isAdult, gameRestrictions) {
        this.gameId = gameId;
        this.username = username;
        this.isAdult = isAdult;
        this.gameRestrictions = gameRestrictions;
    }
    /**
     * 获取单次消费限额
     */
    get createOrderLimit() {
        if (this?.gameRestrictions?.create_order_limit) {
            return this.gameRestrictions.create_order_limit;
        }
        else {
            return 0;
        }
    }
    /**
     * 获取月消费限额
     */
    get monthSumLimit() {
        if (this?.gameRestrictions?.month_sum_limit) {
            return this.gameRestrictions.month_sum_limit;
        }
        else {
            return 0;
        }
    }
    /**
     * 获取币种
     */
    get currency() {
        return this?.gameRestrictions?.currency || "CNY";
    }
    /**
     * 获取工作日可在线时长, 0 代表禁止登录
     */
    get onlineTimeLimit() {
        return this?.gameRestrictions?.online_time_limit;
    }
    /**
     * 获取节假日可在线时长, 0 代表禁止登录
     */
    get holidayOnlineTimeLimit() {
        return this?.gameRestrictions?.holiday_online_time_limit;
    }
    /**
     * 获取宵禁开始时间
     */
    get curfewStartTime() {
        if (this.gameRestrictions.curfew_start_time) {
            return moment(this.gameRestrictions.curfew_start_time, "HH:mm:ss").toDate();
        }
        return null;
    }
    /**
     * 获取宵禁结束时间
     */
    get curfewEndTime() {
        if (this.gameRestrictions.curfew_end_time) {
            return moment(this.gameRestrictions.curfew_end_time, "HH:mm:ss").toDate();
        }
        return null;
    }
    /**
     * 获取规则生效时间
     */
    getEffectiveTime() {
        return this.gameRestrictions.effective_time;
    }
    /**
     * 获取规则过期时间
     */
    getExpiredTime() {
        return this.gameRestrictions.expired_time;
    }
    /**
     * 获取限制提示语
     */
    getRestrictionMessage() {
        return this.gameRestrictions.aas_msg;
    }
    /**
     * 获取游戏ID
     */
    getGameId() {
        return this.gameRestrictions.gameId;
    }
    /**
     * 获取限制来源
     */
    getSource() {
        return this.gameRestrictions.source;
    }
    /**
     * 检查规则是否已过期
     */
    isExpired() {
        const expiredTime = this.gameRestrictions.expired_time;
        if (expiredTime) {
            const now = new Date();
            const expirationDate = new Date(expiredTime);
            return now > expirationDate;
        }
        return false;
    }
    /** 检查规则是否开启 */
    isEffective() {
        const effectiveTime = this.gameRestrictions.effective_time;
        const now = new Date();
        const effectiveDate = new Date(effectiveTime);
        return now >= effectiveDate;
    }
    /** 检查规则是否生效 */
    isActive() {
        return this.isEffective() && !this.isExpired();
    }
    /** 是否开启了宵禁规则 */
    isCurfewEnabled() {
        return !!this.curfewStartTime && !!this.curfewEndTime;
    }
    /** 是否在宵禁时间段 */
    isInCurfewTime(date) {
        if (!this.isCurfewEnabled())
            return false;
        return this.isInCurfewBeforeMidnight(date) || this.isInCurfewAfterMidnight(date);
    }
    /** 是否在宵禁上半夜时间*/
    isInCurfewBeforeMidnight(date) {
        return moment(date).isBetween(this.curfewStartTime, moment().endOf("day"));
    }
    /** 是否在宵禁下半夜时间*/
    isInCurfewAfterMidnight(date) {
        return moment(date).isBetween(moment().startOf("day"), this.curfewEndTime);
    }
    /** 获取当日时间限制, 返回0 代表禁止登录 */
    async getTimeLimitByDate(date, isHoliday) {
        const workdayTL = this.onlineTimeLimit;
        const holidayTL = this.holidayOnlineTimeLimit;
        if (isHoliday) {
            return holidayTL;
        }
        else {
            return workdayTL;
        }
    }
    /** 获取当前在线时长 */
    async getDailyOnlineTime(date) {
        return playerOnlineStatus_1.PlayerOnlineStatus.create(this.gameId, this.username).getDailyOnlineTime(date.getTime());
    }
    /** 是否超过在线时间限制 */
    async isReachOnlineTimeLimit(date, isHoliday) {
        const tl = await this.getTimeLimitByDate(date, isHoliday);
        if (tl === 0) {
            /** 0代表禁止登录*/
            return true;
        }
        if (tl > 0) {
            const onlineTime = await this.getDailyOnlineTime(date);
            /** 修正统计偏移量， 如果在线时间加上1分钟大于限制时间, 我们就限制登录 */
            return onlineTime + 60 >= tl;
        }
        else {
            return false;
        }
    }
    static create(gameId, username, isAdult, rule) {
        return new this(gameId, username, isAdult, rule);
    }
    async getKickOffDate(loginTime) {
        /** 宵禁开始踢人时间 */
        let kickoffByCurfew = null;
        if (this.isCurfewEnabled()) {
            kickoffByCurfew = this.curfewStartTime;
        }
        const isHoliday = await holiday_1.HolidayCN.isHoliday(loginTime);
        const tl = await this.getTimeLimitByDate(loginTime, isHoliday);
        /**时长*/
        let kickOffByTimeLimit = null;
        if (tl > 0) {
            kickOffByTimeLimit = moment(loginTime).add(tl, "seconds").toDate();
        }
        if (kickoffByCurfew && kickOffByTimeLimit) {
            return (0, dateUtil_1.minDate)(kickoffByCurfew, kickOffByTimeLimit);
        }
        else {
            return kickoffByCurfew || kickOffByTimeLimit;
        }
    }
}
exports.GameRestrictionsManager = GameRestrictionsManager;
//# sourceMappingURL=restrictionRule.js.map