"use strict";
/**
天谕端游的踢urs指令

  http://{ip}:{port}/kick?urs={urs}&seconds=0
  (GET)

  线上
  ip: *************
  port: 8181
  开发环境：
  ip: *************
  port: 8181

  比如：
  curl -X GET "http://*************:8181/kick?urs=xujiawei205&40163.com&seconds=0"

*  游戏对接人: 米修斯(徐嘉伟) | <EMAIL>
* TBD
*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.TyKickOff = void 0;
const config_all_1 = require("../../../common/config.all");
const request_1 = require("../../../common/request");
const logger_1 = require("../../../logger");
const logger = logger_1.fcmLogger.child({ clazz: "kickoff/tyKickoff" });
class TyKickOff {
    async kick(urs, roleId, reason) {
        const url = config_all_1.parentEscortCfg.kickoff.tyKickApiPrefix + "/kick";
        const qs = { urs, roleId, reason, seconds: 0 };
        const reqOpt = {
            method: "GET",
            url,
            qs,
            json: false,
            includeRes: true,
        };
        try {
            const { res, body } = await (0, request_1.request)(reqOpt);
            if (res.statuscode === 200) {
                logger.info({ res, body, reqOpt }, "CallTyKickOffOk");
            }
            else {
                logger.warn({ res, body, reqOpt }, "CallTyKickOffFail");
            }
            return true;
        }
        catch (err) {
            logger.error({ err, reqOpt }, "CallTyKickOffError");
            return false;
        }
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new TyKickOff();
        }
        return this.instance;
    }
}
exports.TyKickOff = TyKickOff;
TyKickOff.instance = null;
//# sourceMappingURL=tyKickoff.js.map