"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NshKickOff = void 0;
const config_all_1 = require("../../../common/config.all");
const request_1 = require("../../../common/request");
const logger_1 = require("../../../logger");
const logger = logger_1.fcmLogger.child({ clazz: "kickoff/nshKickoff" });
class NshKickOff {
    async kick(urs, playerid, reason) {
        const url = config_all_1.parentEscortCfg.kickoff.nshKickApiPrefix + "/gm_nsh_http_service/kickOff";
        const qs = { urs, playerid, reason, gm: "ccc_fcm_kickoff" };
        const reqOpt = {
            method: "GET",
            url,
            qs,
        };
        try {
            const ret = await (0, request_1.request)(reqOpt);
            logger.info({ ret, reqOpt }, "CallNshKickOffOk");
            return true;
        }
        catch (err) {
            logger.error({ err, reqOpt }, "CallNshKickOffError");
        }
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new NshKickOff();
        }
        return this.instance;
    }
}
exports.NshKickOff = NshKickOff;
NshKickOff.instance = null;
//# sourceMappingURL=nshKickoff.js.map