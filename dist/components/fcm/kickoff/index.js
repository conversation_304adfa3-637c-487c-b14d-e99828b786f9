"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.kickoffPlayer = kickoffPlayer;
const __1 = require("..");
const logger_1 = require("../../../logger");
const constants_1 = require("../constants");
const nshKickoff_1 = require("./nshKickoff");
const tyKickoff_1 = require("./tyKickoff");
const logger = logger_1.fcmLogger.child({ clazz: "fcm/kickoff" });
async function kickoffPlayer(gameId, urs, roleId, reason) {
    logger.info({ gameId, urs, roleId, reason }, "kickoffPlayerStub");
    if (!(0, __1.isEnableKickoffForGameId)(gameId)) {
        logger.warn({ gameId, urs, roleId, reason }, "kickoffPlayerNotEnableForGame");
        return;
    }
    if (gameId === "d30" /* EGameId.NSH */) {
        if (roleId > 0) {
            const isOk = await nshKickoff_1.NshKickOff.getInstance().kick(urs, roleId, reason);
            return isOk;
        }
        else {
            logger.warn({ gameId, urs, roleId, reason }, "kickoffNotTriggerCauseNshRoleIdIsZero");
        }
    }
    else if (gameId === "d21" /* EGameId.TY */) {
        // 天谕踢人只需要urs就可以，无需roleId, 踢人接口使用天谕特殊踢人文案
        const isOk = await tyKickoff_1.TyKickOff.getInstance().kick(urs, roleId || 0, constants_1.KICKOFF_REASON);
        return isOk;
    }
    else {
        logger.warn({ gameId, urs, roleId, reason }, "kickoffPlayerNotSupport");
    }
    return false;
}
//# sourceMappingURL=index.js.map