"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KickoffQueue = void 0;
const config_1 = require("../../common/config");
const logger_1 = require("../../logger");
const kickoff_1 = require("./kickoff");
const constants_1 = require("./constants");
const redis_1 = require("../../common/redis");
const dateUtil_1 = require("../../common/dateUtil");
const delayQueue_1 = require("../../common/delayQueue");
const playerOnlineStatus_1 = require("./playerOnlineStatus");
const logger = logger_1.fcmLogger.child({ clazz: "fcm/kickoffManager" });
class KickoffQueue {
    getName() {
        return this.name;
    }
    constructor(gameId) {
        const queueName = config_1.parentEscortCfg.kickoff.taskQueueName.replace("{{gameId}}", gameId);
        this.queue = new delayQueue_1.DelayQueue(queueName);
        this.gameId = gameId;
    }
    getKickoffJobId(urs) {
        return urs;
    }
    async addJob(job) {
        const addRet = await this.queue.add(job);
        logger.debug({ job, addRet }, "kickoffQueueAddJob");
        return job;
    }
    async getRoleIdByUrs(urs) {
        const val = await (0, redis_1.getRedis)().getAsync(`fcm:kickoff:ursRoleId:${urs}`);
        if (val) {
            return parseInt(val, 10);
        }
        else {
            return 0;
        }
    }
    async setRoleIdByUrs(urs, roleId) {
        return (0, redis_1.getRedis)().setex(`fcm:kickoff:ursRoleId:${urs}`, 10 * 60, "" + roleId);
    }
    async saveUrsRoleId(urs, roleId) {
        const job = await this.getJob(urs);
        const jobId = this.getKickoffJobId(urs);
        if (job) {
            const ret = await this.queue.update(jobId, { roleId });
            logger.debug({ jobId, urs, roleId, ret }, "SaveUrsRoleIdOk");
        }
        else {
            await this.setRoleIdByUrs(urs, roleId);
            logger.debug({ jobId, urs, roleId }, "SaveUrsRoleIdInfoForUse");
        }
        return { jobId, job };
    }
    async getJob(urs) {
        const jobId = this.getKickoffJobId(urs);
        const job = await this.queue.getJob(jobId);
        return job;
    }
    async removeJob(jobId) {
        return this.queue.remove(jobId);
    }
    async addKickoffTask(urs, kickDate) {
        const delay = (0, dateUtil_1.diffDateMilliseconds)(kickDate, new Date());
        if (delay > 0) {
            logger.info({ gameId: this.gameId, urs, kickDate, delay }, "KickOffTaskAdded");
            const roleId = await this.getRoleIdByUrs(urs);
            return this.addJob({
                id: this.getKickoffJobId(urs),
                body: {
                    roleId,
                    urs,
                },
                runAt: kickDate.getTime(),
            });
        }
        else {
            logger.warn({ urs, kickDate }, "KickOffTooEarly");
            return null;
        }
    }
    async removeKickoffTask(urs) {
        const jobId = this.getKickoffJobId(urs);
        const ret = await this.removeJob(jobId);
        logger.info({ ret, jobId, urs }, "KickoffQueueRemoveJob");
    }
    async processKickoffPlayer(job) {
        const body = job.body;
        if (body && body.urs) {
            // 触发踢人成功的时候结算一次在线时长
            await playerOnlineStatus_1.PlayerOnlineStatus.create(this.gameId, body.urs).logout("" + body.roleId, new Date());
            await (0, kickoff_1.kickoffPlayer)(this.gameId, body.urs, body.roleId, constants_1.KICKOFF_REASON);
        }
        else {
            logger.warn({ job }, "ProcessKickoffPlayerCauseNoUrs");
        }
    }
    async schedule(interval = 1000) {
        setInterval(async () => {
            await this.queue.onTick();
        }, interval);
    }
    async consumeTask() {
        const job = await this.queue.popReady();
        if (job && job.id) {
            await this.processKickoffPlayer(job);
            return true;
        }
        else {
            return false;
        }
    }
    static getInstance(gameId) {
        let instance = this.instanceMap.get(gameId);
        if (!instance) {
            instance = new KickoffQueue(gameId);
            this.instanceMap.set(gameId, instance);
        }
        return instance;
    }
}
exports.KickoffQueue = KickoffQueue;
KickoffQueue.instanceMap = new Map();
//# sourceMappingURL=kickoffManager.js.map