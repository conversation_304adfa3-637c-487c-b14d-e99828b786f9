"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isEnableKickoffForGameId = isEnableKickoffForGameId;
exports.getSuperAssLimitWrapRet = getSuperAssLimitWrapRet;
exports.productToGameId = productToGameId;
const config_1 = require("../../common/config");
const logger_1 = require("../../logger");
const gbSuperAssLimit_1 = require("./gbSuperAssLimit");
const holiday_1 = require("./holiday");
const kickoffManager_1 = require("./kickoffManager");
const playerOnlineStatus_1 = require("./playerOnlineStatus");
const restrictionRule_1 = require("./restrictionRule");
const logger = (0, logger_1.clazzLogger)("fcm/index");
function isEnableKickoffForGameId(gameId) {
    return config_1.parentEscortCfg.kickoff.enableGameIds.includes(gameId);
}
async function getSuperAssLimitWrapRet(params) {
    const result = {
        restrict_login: false,
        check_status: 0,
        daily_play_time: 0,
        super_aas_limit: null,
    };
    try {
        let res = null;
        if (config_1.parentEscortCfg.mockSuperAssLimit.enable) {
            res = config_1.parentEscortCfg.mockSuperAssLimit.data;
        }
        else {
            res = await gbSuperAssLimit_1.GbSuperAssLimitApi.create(params.gameId, params.account).get();
        }
        if (res && res.data) {
            result.super_aas_limit = {
                create_order_limit: res.data.create_order_limit,
                month_sum_limit: res.data.month_sum_limit,
                online_time_limit: res.data.online_time_limit,
                holiday_online_time_limit: res.data.holiday_online_time_limit,
                curfew_end_time: res.data.curfew_end_time,
                curfew_start_time: res.data.curfew_start_time,
                aas_msg: res.data.aas_msg,
                source: res.data.source,
                expired_time: res.data.expired_time,
            };
            const gm = restrictionRule_1.GameRestrictionsManager.create(params.gameId, params.account, params.isAdult, res.data);
            if (gm.isInCurfewTime(params.date)) {
                result.restrict_login = true;
                result.check_status = 1 /* ECheckStatusCode.LimitByCurfew */;
            }
            const isHoliday = await holiday_1.HolidayCN.isHoliday(params.date);
            const dailyPlayDuration = await playerOnlineStatus_1.PlayerOnlineStatus.create(params.gameId, params.account).getCurDailyPlayDuration(params.date);
            result.daily_play_time = dailyPlayDuration;
            const isReachOnlineLimit = await gm.isReachOnlineTimeLimit(params.date, isHoliday);
            if (isReachOnlineLimit) {
                result.restrict_login = true;
                result.check_status = isHoliday ? 3 /* ECheckStatusCode.LimitByHolidayTime */ : 2 /* ECheckStatusCode.LimitByOnlineTime */;
            }
            const kickoffDate = await gm.getKickOffDate(params.date);
            if (kickoffDate) {
                if (isEnableKickoffForGameId(params.gameId)) {
                    kickoffManager_1.KickoffQueue.getInstance(params.gameId).addKickoffTask(params.account, kickoffDate);
                }
                else {
                    logger.warn({ gameId: params.gameId, account: params.account }, "KickoffNotEnableForGame");
                }
            }
        }
        else {
            result.check_status = -1 /* ECheckStatusCode.UpStreamFail */;
        }
    }
    catch (err) {
        result.check_status = -1 /* ECheckStatusCode.UpStreamFail */;
    }
    return result;
}
function productToGameId(product) {
    return config_1.parentEscortCfg.productGameIdMap[product] || config_1.parentEscortCfg.defaultGameId;
}
//# sourceMappingURL=index.js.map