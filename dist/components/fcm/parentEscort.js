"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRestrictions = void 0;
const logger_1 = require("../../logger");
const gbSuperAssLimit_1 = require("./gbSuperAssLimit");
const logger = logger_1.fcmLogger.child({ clazz: "fcm/parentEscort" });
/** 通过计费接口来获取家长守护平台设置的防沉迷规则 */
async function getRestrictions(param) {
    try {
        const ret = await gbSuperAssLimit_1.GbSuperAssLimitApi.create(param.gameid, param.account).get();
        if (ret && ret.code === gbSuperAssLimit_1.ResponseCode.Success) {
            logger.debug({ param, reqOpt, ret }, "GetRestrictionsOk");
            return ret.data;
        }
        else {
            const failMsg = ResponseMessage[ret?.code] || "未知错误";
            logger.error({ param, reqOpt, ret, failMsg }, "GetRestrictionsFail");
            return null;
        }
    }
    catch (err) {
        logger.error({ err, param, reqOpt }, "GetRestrictionsError");
    }
}
exports.getRestrictions = getRestrictions;
//# sourceMappingURL=parentEscort.js.map