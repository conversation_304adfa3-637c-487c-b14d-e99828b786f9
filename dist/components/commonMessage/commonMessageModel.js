"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonMessageModel = exports.CommonMessageCols = exports.MessageStatus = exports.RoleType = void 0;
const crossCommDB_1 = require("../../conn/crossCommDB");
const BaseModel2_1 = require("../../models/BaseModel2");
// 定义角色类型枚举
var RoleType;
(function (RoleType) {
    RoleType[RoleType["REAL_PLAYER"] = 0] = "REAL_PLAYER";
    RoleType[RoleType["FAKE_PLAYER"] = 1] = "FAKE_PLAYER";
})(RoleType || (exports.RoleType = RoleType = {}));
var MessageStatus;
(function (MessageStatus) {
    MessageStatus[MessageStatus["NORMAL"] = 0] = "NORMAL";
    MessageStatus[MessageStatus["HIDE"] = -1] = "HIDE";
    MessageStatus[MessageStatus["DEL"] = -2] = "DEL";
})(MessageStatus || (exports.MessageStatus = MessageStatus = {}));
exports.CommonMessageCols = [
    "id", "roleId", "roleType", "roleName",
    "moduleId", "text", "status", "jobId", "gender", "subGender",
    "createTime", "updateTime"
];
class CommonMessageModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_common_message", "id", crossCommDB_1.DBNode.Master, {
            db: (0, crossCommDB_1.getCrossCommDbClient)()
        });
    }
}
exports.CommonMessageModel = new CommonMessageModelClass();
//# sourceMappingURL=commonMessageModel.js.map