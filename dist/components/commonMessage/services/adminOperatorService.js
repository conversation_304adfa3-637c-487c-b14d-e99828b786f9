"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminOperatorService = void 0;
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
const OpenIdRoleModel_1 = require("../../../models/OpenIdRoleModel");
class AdminOperatorService {
    constructor(scope) {
        this.openIdRoleModel = OpenIdRoleModel_1.default;
        this.logger = (0, logger_1.clazzLogger)("adminOperator.service");
        this.scope = scope;
    }
    /**
     * 根据OpenId和Scope获取角色信息
     */
    async getByOpenIdAndScope(openId) {
        try {
            const role = await this.openIdRoleModel.findOne({
                openid: openId,
                scope: this.scope
            });
            if (!role) {
                this.logger.warn({ openId, scope: this.scope }, "GetOpenIdAndScopeNoRecord");
                throw errorCodes_1.errorCode.AdminOperatorNotExists;
            }
            return role;
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists) {
                throw err;
            }
            this.logger.error({ err, openId, scope: this.scope }, "GetOpenIdAndScopeFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 检查OpenId权限
     */
    async checkOpenIdPermission(openId, needAdmin) {
        try {
            const role = await this.getByOpenIdAndScope(openId);
            if (needAdmin) {
                return role.role_type === OpenIdRoleModel_1.AdminRoleType.ADMIN;
            }
            else {
                return role.id > 0;
            }
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists) {
                return false;
            }
            throw err;
        }
    }
    /**
     * 获取管理员登录信息
     */
    getAdminLoginInfo(req) {
        const session = req.session;
        return {
            mail: session.mail
        };
    }
    /**
     * 获取登录信息
     */
    async getLoginInfo(req) {
        try {
            const loginInfo = this.getAdminLoginInfo(req);
            const role = await this.getByOpenIdAndScope(loginInfo.mail);
            return {
                id: role.id,
                openId: role.openid,
                fullName: role.full_name,
                createTime: role.ctime,
                roleType: role.role_type
            };
        }
        catch (err) {
            throw err;
        }
    }
    /**
     * 获取管理员列表基础查询
     */
    getOperatorListBaseQuery(req) {
        return this.openIdRoleModel.scope().where({ scope: this.scope });
    }
    /**
     * 获取管理员列表
     */
    async getOperatorList(params) {
        try {
            const offset = (params.page - 1) * params.pageSize;
            const limit = params.pageSize;
            const listQuery = this.getOperatorListBaseQuery(params)
                .orderBy('id', 'desc')
                .offset(offset)
                .limit(limit);
            const countQuery = this.getOperatorListBaseQuery(params);
            const [rows, total] = await Promise.all([
                this.openIdRoleModel.executeByQuery(listQuery),
                this.openIdRoleModel.countByQuery(countQuery)
            ]);
            if (!rows) {
                this.logger.error({ req: params, scope: this.scope }, "GetAdminOperatorListFail");
                throw errorCodes_1.BaseErrors.DatabaseError;
            }
            const list = rows.map(r => ({
                id: r.id,
                openId: r.openid,
                roleType: r.role_type,
                fullName: r.full_name,
                createTime: r.ctime,
                updateTime: r.utime
            }));
            this.logger.info({ params, total, scope: this.scope }, "GetAdminOperatorListSuccess");
            return { list, total };
        }
        catch (err) {
            this.logger.error({ err, params, scope: this.scope }, "GetAdminOperatorListFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 添加管理员
     */
    async addOperator(params) {
        try {
            const now = Date.now();
            const openIdRole = {
                openid: params.openId,
                full_name: params.fullName,
                scope: this.scope,
                role_type: params.roleType,
                ctime: now,
                utime: now
            };
            const insertId = await this.openIdRoleModel.insert(openIdRole);
            this.logger.info({ openRole: openIdRole, scope: this.scope }, "AddAdminOperatorOk");
            return { id: insertId };
        }
        catch (err) {
            if (err.code === 'ER_DUP_ENTRY') {
                return Promise.reject(errorCodes_1.errorCode.AdminOperatorExists);
            }
            this.logger.error({ err, params, scope: this.scope }, "AddAdminOperatorFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 删除管理员
     */
    async delOperator(params) {
        try {
            const role = await this.getByOpenIdAndScope(params.openId);
            const delOp = await this.openIdRoleModel.findOne({ id: role.id });
            if (!delOp) {
                this.logger.warn({ openId: params.openId, delId: role.id, scope: this.scope }, "FindDelAdminOperatorFail");
                throw errorCodes_1.errorCode.AdminOperatorNotExists;
            }
            const curLoginInfo = await this.getLoginInfo(params);
            if (curLoginInfo.openId === role.openid) {
                this.logger.warn({ openId: params.openId, delId: role.id, scope: this.scope }, "DelAdminOperatorSelf");
                throw errorCodes_1.errorCode.AdminOperatorDelSelf;
            }
            const delRet = await this.openIdRoleModel.deleteByCondition({ id: role.id });
            this.logger.info({ openId: params.openId, delId: role.id, delRowsAffected: delRet.affectedRows, scope: this.scope }, "DelAdminOperatorOk");
            return { id: role.id };
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists || err === errorCodes_1.errorCode.AdminOperatorDelSelf) {
                throw err;
            }
            this.logger.error({ err, params, scope: this.scope }, "DelAdminOperatorFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 更新管理员
     */
    async updateOperator(params) {
        try {
            const curOperator = await this.getByOpenIdAndScope(params.openId);
            const now = Date.now();
            const updateRet = await this.openIdRoleModel.updateByCondition({ id: curOperator.id }, {
                full_name: params.fullName,
                role_type: params.roleType,
                utime: now
            });
            this.logger.info({
                upReq: params,
                updateId: curOperator.id,
                updateRowsAffected: updateRet.affectedRows,
                scope: this.scope
            }, "UpdateAdminOperatorOk");
            return { id: curOperator.id };
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists) {
                throw err;
            }
            this.logger.error({ err, params, scope: this.scope }, "UpdateAdminOperatorFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 检查是否是管理员
     */
    async checkIsAdmin(openId) {
        try {
            const role = await this.getByOpenIdAndScope(openId);
            return role.role_type === OpenIdRoleModel_1.AdminRoleType.ADMIN;
        }
        catch (err) {
            this.logger.error({ err, openId, scope: this.scope }, "CheckIsAdminError");
            return false;
        }
    }
}
exports.AdminOperatorService = AdminOperatorService;
//# sourceMappingURL=adminOperatorService.js.map