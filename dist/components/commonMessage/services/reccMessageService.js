"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.playerSelfReccIdPoolCache = exports.realPlayerReccIdPoolCache = exports.fakePlayerReccIdPoolCache = void 0;
const config_all_1 = require("../../../common/config.all");
const cacheUtil_1 = require("../../../services/cacheUtil");
const commonMessageModel_1 = require("../commonMessageModel");
class FakePlayerReccIdPoolCache extends cacheUtil_1.GenericCache {
    getKey(moduleId) {
        return `common_message:${moduleId}_fake_player_recc_id_pool`;
    }
    getExpireTime(params) {
        return 5 * 60; // 5分钟
    }
    async fetchDataSource(moduleId) {
        const rows = await commonMessageModel_1.CommonMessageModel.find({
            roleType: commonMessageModel_1.RoleType.FAKE_PLAYER,
            moduleId: moduleId,
            status: commonMessageModel_1.MessageStatus.NORMAL
        }, { limit: config_all_1.commonMessageCfg.reccFakePoolSize, cols: ["id"] });
        return rows.map(row => row.id);
    }
}
exports.fakePlayerReccIdPoolCache = new FakePlayerReccIdPoolCache();
class RealPlayerReccIdPoolCache extends cacheUtil_1.GenericCache {
    getExpireTime(params) {
        return 5 * 60; // 5分钟
    }
    getKey(moduleId) {
        return `common_message:${moduleId}_real_player_recc_id_pool`;
    }
    async fetchDataSource(moduleId) {
        const rows = await commonMessageModel_1.CommonMessageModel.find({
            roleType: commonMessageModel_1.RoleType.REAL_PLAYER,
            moduleId: moduleId,
            status: commonMessageModel_1.MessageStatus.NORMAL
        }, { limit: config_all_1.commonMessageCfg.reccPlayerPoolSize, cols: ["id"] });
        return rows.map(row => row.id);
    }
}
exports.realPlayerReccIdPoolCache = new RealPlayerReccIdPoolCache();
class PlayerSelfReccIdPoolCache extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 3 * 60; // 3分钟
    }
    getKey(params) {
        return `common_message:${params.moduleId}_player_self_recc_id_pool:${params.roleId}`;
    }
    async fetchDataSource(params) {
        const rows = await commonMessageModel_1.CommonMessageModel.find({
            roleId: params.roleId,
            moduleId: params.moduleId,
            status: commonMessageModel_1.MessageStatus.NORMAL
        }, { limit: config_all_1.commonMessageCfg.reccSelfPoolSize, cols: ["id"] });
        return rows.map(row => row.id);
    }
}
exports.playerSelfReccIdPoolCache = new PlayerSelfReccIdPoolCache();
//# sourceMappingURL=reccMessageService.js.map