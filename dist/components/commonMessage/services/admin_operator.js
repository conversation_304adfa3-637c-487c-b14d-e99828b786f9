"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminOperatorService = void 0;
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
const OpenIdRoleModel_1 = require("../../../models/OpenIdRoleModel");
class AdminOperatorService {
    constructor(scope) {
        this.openIdRoleModel = OpenIdRoleModel_1.default;
        this.logger = (0, logger_1.clazzLogger)("adminOperator.service");
        this.scope = scope;
    }
    /**
     * 根据OpenId和Scope获取角色信息
     */
    async getByOpenIdAndScope(openId, scope) {
        try {
            const role = await this.openIdRoleModel.findOne({
                openid: openId,
                scope: scope
            });
            if (!role) {
                this.logger.warn({ openId, scope }, "GetOpenIdAndScopeNoRecord");
                throw errorCodes_1.errorCode.AdminOperatorNotExists;
            }
            return role;
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists) {
                throw err;
            }
            this.logger.error({ err, openId, scope }, "GetOpenIdAndScopeFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 检查OpenId权限
     */
    async checkOpenIdPermission(openId, scope, needAdmin) {
        try {
            const role = await this.getByOpenIdAndScope(openId, scope);
            if (needAdmin) {
                return role.role_type === OpenIdRoleModel_1.AdminRoleType.ADMIN;
            }
            else {
                return role.id > 0;
            }
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists) {
                return false;
            }
            throw err;
        }
    }
    /**
     * 获取管理员登录信息
     */
    getAdminLoginInfo() {
        return {};
    }
    /**
     * 获取登录信息
     */
    async getLoginInfo() {
        try {
            const loginInfo = this.getAdminLoginInfo();
            const role = await this.getByOpenIdAndScope(loginInfo.corpMail, this.scope);
            return {
                id: role.id,
                openId: role.openid,
                fullName: role.full_name,
                createTime: role.ctime,
                roleType: role.role_type
            };
        }
        catch (err) {
            throw err;
        }
    }
    /**
     * 获取管理员列表基础查询
     */
    getOperatorListBaseQuery(req) {
        return this.openIdRoleModel.scope().where({ scope: this.scope });
    }
    /**
     * 获取管理员列表
     */
    async getOperatorList(req) {
        try {
            const offset = (req.page - 1) * req.pageSize;
            const limit = req.pageSize;
            const listQuery = this.getOperatorListBaseQuery(req)
                .orderBy('id', 'desc')
                .offset(offset)
                .limit(limit);
            const countQuery = this.getOperatorListBaseQuery(req);
            const [rows, total] = await Promise.all([
                this.openIdRoleModel.executeByQuery(listQuery),
                this.openIdRoleModel.countByQuery(countQuery)
            ]);
            if (!rows) {
                this.logger.error({ req }, "GetAdminOperatorListFail");
                throw errorCodes_1.BaseErrors.DatabaseError;
            }
            const list = rows.map(r => ({
                id: r.id,
                openId: r.openid,
                roleType: r.role_type,
                fullName: r.full_name,
                createTime: r.ctime,
                updateTime: r.utime
            }));
            this.logger.info({ req, total }, "GetAdminOperatorListSuccess");
            return { list, total };
        }
        catch (err) {
            this.logger.error({ err, req }, "GetAdminOperatorListFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 添加管理员
     */
    async addOperator(req) {
        try {
            const now = Date.now();
            const openIdRole = {
                openid: req.openId,
                full_name: req.fullName,
                scope: this.scope,
                role_type: req.roleType,
                ctime: now,
                utime: now
            };
            const insertId = await this.openIdRoleModel.insert(openIdRole);
            this.logger.info({ openRole: openIdRole }, "AddAdminOperatorOk");
            return { id: insertId };
        }
        catch (err) {
            if (err.code === 'ER_DUP_ENTRY') {
                return Promise.reject(errorCodes_1.errorCode.AdminOperatorExists);
            }
            this.logger.error({ err, req }, "AddAdminOperatorFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 删除管理员
     */
    async delOperator(req) {
        try {
            const role = await this.getByOpenIdAndScope(req.openId, this.scope);
            const delOp = await this.openIdRoleModel.findOne({ id: role.id });
            if (!delOp) {
                this.logger.warn({ openId: req.openId, delId: role.id }, "FindDelAdminOperatorFail");
                throw errorCodes_1.errorCode.AdminOperatorNotExists;
            }
            const curLoginInfo = await this.getLoginInfo();
            if (curLoginInfo.openId === role.openid) {
                this.logger.warn({ openId: req.openId, delId: role.id }, "DelAdminOperatorSelf");
                throw errorCodes_1.errorCode.AdminOperatorDelSelf;
            }
            const delRet = await this.openIdRoleModel.deleteByCondition({ id: role.id });
            this.logger.info({ openId: req.openId, delId: role.id, delRowsAffected: delRet.affectedRows }, "DelAdminOperatorOk");
            return { id: role.id };
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists || err === errorCodes_1.errorCode.AdminOperatorDelSelf) {
                throw err;
            }
            this.logger.error({ err, req }, "DelAdminOperatorFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 更新管理员
     */
    async updateOperator(req) {
        try {
            const curOperator = await this.getByOpenIdAndScope(req.openId, this.scope);
            const now = Date.now();
            const updateRet = await this.openIdRoleModel.updateByCondition({ id: curOperator.id }, {
                full_name: req.fullName,
                role_type: req.roleType,
                utime: now
            });
            this.logger.info({
                upReq: req,
                updateId: curOperator.id,
                updateRowsAffected: updateRet.affectedRows
            }, "UpdateAdminOperatorOk");
            return { id: curOperator.id };
        }
        catch (err) {
            if (err === errorCodes_1.errorCode.AdminOperatorNotExists) {
                throw err;
            }
            this.logger.error({ err, req }, "UpdateAdminOperatorFail");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 检查是否是管理员
     */
    async checkIsAdmin(openId, scope) {
        try {
            const role = await this.getByOpenIdAndScope(openId, scope);
            return role.role_type === OpenIdRoleModel_1.AdminRoleType.ADMIN;
        }
        catch (err) {
            this.logger.error({ err, openId, scope }, "CheckIsAdminError");
            return false;
        }
    }
}
exports.AdminOperatorService = AdminOperatorService;
//# sourceMappingURL=admin_operator.js.map