"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminMessageService = void 0;
const commonMessageModel_1 = require("../commonMessageModel");
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
const config_1 = require("../../../common/config");
class AdminMessageService {
    constructor(moduleId) {
        this.commonMessageModel = commonMessageModel_1.CommonMessageModel;
        this.logger = (0, logger_1.clazzLogger)("adminMessage.service");
        this.moduleId = moduleId;
    }
    /**
     * 添加消息
     */
    async addMessage(params) {
        try {
            const now = Date.now();
            const record = {
                roleId: params.roleId,
                roleType: commonMessageModel_1.RoleType.FAKE_PLAYER,
                roleName: params.roleName,
                moduleId: this.moduleId,
                jobId: 0,
                gender: 0,
                subGender: 0,
                text: params.text,
                status: commonMessageModel_1.MessageStatus.NORMAL,
                createTime: now,
                updateTime: now
            };
            const insertId = await this.commonMessageModel.insert(record);
            this.logger.info({ record }, "AdminAddMessageSuccess");
            return insertId;
        }
        catch (err) {
            this.logger.error({ err, params, moduleId: this.moduleId }, "AdminAddMessageFailed");
            throw errorCodes_1.BaseErrors.ServerError;
        }
    }
    /**
     * 获取消息列表
     */
    async getMessageList(params) {
        try {
            // 目前没有ES，用db过滤数据需要用id限制下scan范围
            let minId = 0;
            if (params.keyword && !params.roleId) {
                const maxId = await this.getMessageMaxId(this.moduleId);
                minId = Math.max(0, maxId - config_1.commonMessageCfg.adminMessageSearchMaxCount);
            }
            function baseQuery() {
                let query = commonMessageModel_1.CommonMessageModel.scope().where({ moduleId: params.moduleId });
                // 状态过滤
                if (params.status === 'all') {
                    query = query.whereIn('status', [commonMessageModel_1.MessageStatus.NORMAL, commonMessageModel_1.MessageStatus.HIDE]);
                }
                else if (params.status === 'show') {
                    query = query.whereIn('status', [commonMessageModel_1.MessageStatus.NORMAL]);
                }
                else if (params.status === 'hide') {
                    query = query.where({ status: commonMessageModel_1.MessageStatus.HIDE }); // 使用 Read 状态表示隐藏
                }
                // 角色ID过滤
                if (params.roleId) {
                    query = query.where({ roleId: params.roleId });
                }
                // 关键词搜索
                if (params.keyword) {
                    query = query.where('text', 'like', `%${params.keyword}%`);
                    if (minId > 0) {
                        query = query.where('id', '>', minId);
                    }
                }
                // 排序
                if (params.sortBy) {
                    const sortOrder = params.sortOrder === 'asc' ? 'asc' : 'desc';
                    switch (params.sortBy) {
                        case 'create_time':
                            query = query.orderBy('createTime', sortOrder);
                            break;
                        case 'show_status':
                            query = query.orderBy('status', sortOrder);
                            break;
                        case 'role_type':
                            query = query.orderBy('roleType', sortOrder);
                            break;
                    }
                }
                return query;
            }
            const offset = (params.page - 1) * params.pageSize;
            const listQuery = baseQuery().offset(offset).limit(params.pageSize);
            const countQuery = baseQuery();
            const [list, total] = await Promise.all([
                this.commonMessageModel.executeByQuery(listQuery),
                this.commonMessageModel.countByQuery(countQuery)
            ]);
            this.logger.info({ params, total, moduleId: this.moduleId }, "GetAdminMessageListSuccess");
            return { list, total };
        }
        catch (err) {
            this.logger.error({ err, params, moduleId: this.moduleId }, "GetAdminMessageListFailed");
            throw err;
        }
    }
    /**
     * 删除消息
     */
    async deleteMessages(params) {
        try {
            let deletedCount = 0;
            let upRet = await this.commonMessageModel.updateByCondition({ id: params.ids }, { status: commonMessageModel_1.MessageStatus.DEL });
            deletedCount = upRet.affectedRows;
            this.logger.info({ params, affected: deletedCount }, "AdminDeleteMessagesSuccess");
            return deletedCount;
        }
        catch (err) {
            this.logger.error({ err, params }, "AdminDeleteMessagesFailed");
            throw errorCodes_1.BaseErrors.ServerError;
        }
    }
    /**
     * 更新消息状态
     */
    async updateMessageStatus(params) {
        try {
            const now = Date.now();
            const message = await this.commonMessageModel.findOne({ id: params.id });
            if (!message) {
                throw errorCodes_1.errorCode.RoleInfoNotFound;
            }
            await this.commonMessageModel.updateByCondition({ id: params.id }, { status: params.status, updateTime: now });
            this.logger.info({ params }, "AdminUpdateMessageStatusSuccess");
        }
        catch (err) {
            this.logger.error({ err, params }, "AdminUpdateMessageStatusFailed");
            if (err === errorCodes_1.errorCode.RoleInfoNotFound) {
                throw err;
            }
            throw errorCodes_1.BaseErrors.ServerError;
        }
    }
    async getMessageMaxId(moduleId) {
        let query = this.commonMessageModel.scope().where({ moduleId }).max('id as maxId');
        const rows = await this.commonMessageModel.executeByQuery(query);
        if (rows.length > 0) {
            return rows[0].maxId || 0;
        }
        return 0;
    }
}
exports.AdminMessageService = AdminMessageService;
//# sourceMappingURL=adminMessageService.js.map