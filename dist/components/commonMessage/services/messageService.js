"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonMessageService = void 0;
const config_1 = require("../../../common/config");
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
const envsdkService_1 = require("../../../services/external/envsdkService");
const commonMessageModel_1 = require("../commonMessageModel");
const reccMessageService_1 = require("./reccMessageService");
const _ = require("lodash");
class CommonMessageService {
    constructor(moduleId) {
        this.commonMessageModel = commonMessageModel_1.CommonMessageModel;
        this.logger = (0, logger_1.clazzLogger)("commonMessage.service");
        this.moduleId = moduleId;
    }
    /**
     * 获取玩家留言数量
     */
    async getPlayerMessageCount(roleId) {
        try {
            const count = await this.commonMessageModel.count({
                roleId,
                moduleId: this.moduleId,
                status: commonMessageModel_1.MessageStatus.NORMAL
            });
            this.logger.info({ roleId, count }, "GetPlayerMessageCountSuccess");
            return count;
        }
        catch (err) {
            this.logger.error({ err, roleId }, "GetPlayerMessageCountFailed");
            throw errorCodes_1.CommonErrors.RoleNotExist;
        }
    }
    /**
     * 添加留言
     */
    async add(params) {
        // 检查文本是否为空
        if (!params.text.trim()) {
            await (0, errorCodes_1.BussError)(errorCodes_1.errorCode.MessageTextIsEmpty);
        }
        // 敏感词检查 (留空实现)
        const checkText = [params.rolename, params.text].join(" ");
        const isPass = await this.reviewWords(checkText);
        if (!isPass) {
            await (0, errorCodes_1.BussError)(errorCodes_1.errorCode.ContainSensitive);
        }
        // 检查留言数量上限
        const count = await this.getPlayerMessageCount(params.roleid);
        const maxCount = config_1.commonMessageCfg.maxMessageCount; // 配置值
        if (count >= maxCount) {
            this.logger.warn({ roleId: params.roleid, count }, "MessageCountExceedLimit");
            await (0, errorCodes_1.BussError)(errorCodes_1.errorCode.MessageCountExceedLimit);
        }
        try {
            let now = Date.now();
            const record = {
                roleId: params.roleid,
                roleType: commonMessageModel_1.RoleType.REAL_PLAYER,
                roleName: params.rolename,
                moduleId: params.moduleId || 0,
                jobId: params.jobId || 0,
                gender: params.gender || 0,
                subGender: params.subGender || 0,
                text: params.text,
                status: commonMessageModel_1.MessageStatus.NORMAL,
                createTime: now,
                updateTime: now
            };
            const insertId = await this.commonMessageModel.insert(record);
            this.logger.info({ record }, "AddMessageSuccess");
            return insertId;
        }
        catch (err) {
            this.logger.error({ err, params }, "AddMessageFailed");
            throw errorCodes_1.BaseErrors.DatabaseError;
        }
    }
    /**
     * 获取推荐留言列表
     */
    /**
     * 获取推荐留言列表
     * 从三个留言池中随机抽取留言:
     * 1. 玩家自己的留言池
     * 2. 假玩家留言池
     * 3. 真实玩家留言池
     *
     * 按照配置的数量比例进行抽取,如果数量不足则从假玩家和真实玩家留言池中补充
     *
     * @param roleId - 玩家角色ID
     * @returns 返回推荐的留言列表
     * @throws 获取失败时返回空数组
     */
    async getReccList(roleId) {
        try {
            const selfMessageIdsPool = await reccMessageService_1.playerSelfReccIdPoolCache.get({ roleId, moduleId: this.moduleId });
            const fakePlayerMessageIdsPool = await reccMessageService_1.fakePlayerReccIdPoolCache.get(this.moduleId);
            const realPlayerMessageIdsPool = await reccMessageService_1.realPlayerReccIdPoolCache.get(this.moduleId);
            const pickSelfMessages = this.randomSample(selfMessageIdsPool, config_1.commonMessageCfg.reccSelfPickSize);
            const pickFakeMessages = this.randomSample(fakePlayerMessageIdsPool, config_1.commonMessageCfg.reccPickFakeSize);
            const restSize = config_1.commonMessageCfg.reccPickTotalSize - pickFakeMessages.length - pickSelfMessages.length;
            const pickRealMessages = this.randomSample(realPlayerMessageIdsPool, restSize);
            let pickMessageIds = _.uniq([...pickSelfMessages, ...pickFakeMessages, ...pickRealMessages]);
            // 如果选取的留言总数小于配置的目标数量,从假玩家和真实玩家留言池中补充
            if (pickMessageIds.length < config_1.commonMessageCfg.reccPickTotalSize) {
                let allIdPool = _.uniq([...fakePlayerMessageIdsPool, ...realPlayerMessageIdsPool]).filter(id => !pickMessageIds.includes(id));
                const restIds = this.randomSample(allIdPool, config_1.commonMessageCfg.reccPickTotalSize - pickMessageIds.length);
                pickMessageIds = _.uniq([...pickMessageIds, ...restIds]);
            }
            // 根据选取的ID获取留言详细信息,只返回状态正常的留言
            const pickMessages = await this.commonMessageModel.find({
                id: pickMessageIds,
                status: commonMessageModel_1.MessageStatus.NORMAL
            });
            return pickMessages;
        }
        catch (err) {
            // 发生错误时记录日志并返回空数组
            this.logger.error({ err, moduleId: this.moduleId, roleId }, "GetRecommendedMessageListFailed");
            return [];
        }
    }
    /**
     * 敏感词检查
     */
    async reviewWords(text) {
        const result = await envsdkService_1.default.reviewWords(text);
        return result.validate;
    }
    /**
     * 随机采样辅助函数
     */
    randomSample(array, size) {
        return _.sampleSize(array, size);
    }
}
exports.CommonMessageService = CommonMessageService;
//# sourceMappingURL=messageService.js.map