"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("../../common/config");
class CommonMessageCfg {
    constructor(userConfig = {}) {
        this.config = { ...CommonMessageCfg.defaultConfig, ...userConfig };
    }
    /** 获取当前配置 */
    getConfig() {
        return this.config;
    }
}
CommonMessageCfg.modules = [
    { id: 10000, name: '吃饭奇遇' }
];
CommonMessageCfg.defaultConfig = config_1.commonMessageCfg;
exports.default = CommonMessageCfg;
//# sourceMappingURL=commonMessageCfg.js.map