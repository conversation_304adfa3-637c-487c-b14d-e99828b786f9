"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonMessageService = void 0;
const model_1 = require("./model");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const config_all_1 = require("../../common/config.all");
class CommonMessageService {
    constructor(moduleId) {
        this.commonMessageModel = model_1.CommonMessageModel;
        this.logger = (0, logger_1.clazzLogger)("commonMessage.service");
        this.moduleId = moduleId;
    }
    /**
     * 获取玩家留言数量
     */
    async getPlayerMessageCount(roleId) {
        try {
            const count = await this.commonMessageModel.count({
                roleId,
                moduleId: this.moduleId,
                status: 0 /* Statues.Normal */
            });
            this.logger.info({ roleId, count }, "GetPlayerMessageCountSuccess");
            return count;
        }
        catch (err) {
            this.logger.error({ err, roleId }, "GetPlayerMessageCountFailed");
            throw errorCodes_1.CommonErrors.RoleNotExist;
        }
    }
    /**
     * 添加留言
     */
    async add(params) {
        // 检查文本是否为空
        if (!params.text.trim()) {
            await errorCodes_1.errorCode.MessageTextIsEmpty;
        }
        // 敏感词检查 (留空实现)
        const isPass = await this.reviewWords(params.text);
        if (!isPass) {
            await errorCodes_1.errorCode.ContainSensitive;
        }
        // 检查留言数量上限
        const count = await this.getPlayerMessageCount(params.roleId);
        const maxCount = config_all_1.commonMessageCfg.maxMessageCount; // 配置值
        if (count >= maxCount) {
            this.logger.warn({ roleId: params.roleId, count }, "MessageCountExceedLimit");
            await errorCodes_1.errorCode.MessageCountExceedLimit;
        }
        try {
            let now = Date.now();
            const record = {
                roleId: params.roleId,
                roleType: params.roleType,
                roleName: params.roleName,
                moduleId: params.moduleId || 0,
                text: params.text,
                status: 0 /* Statues.Normal */,
                createTime: now,
                updateTime: now
            };
            const insertId = await this.commonMessageModel.insert(record);
            this.logger.info({ record }, "AddMessageSuccess");
            return insertId;
        }
        catch (err) {
            this.logger.error({ err, params }, "AddMessageFailed");
            throw errorCodes_1.CommonErrors.RoleNotExist;
        }
    }
    /**
     * 获取推荐留言列表
     */
    async getReccList(roleId) {
        try {
            let reccNpcPoolSize = config_all_1.commonMessageCfg.reccNpcPoolSize;
            let reccPlayerPoolSize = config_all_1.commonMessageCfg.reccPlayerPoolSize;
            let reccPickNpcSize = config_all_1.commonMessageCfg.reccPickNpcSize;
            let reccPickTotalSize = config_all_1.commonMessageCfg.reccPickTotalSize;
            // 获取NPC留言
            const npcMessages = await this.commonMessageModel.find({
                roleType: model_1.RoleType.NPC_FAKE_PLAYER,
                moduleId: this.moduleId,
                status: 0 /* Statues.Normal */
            }, { limit: reccNpcPoolSize });
            // 获取玩家留言
            const playerMessages = await this.commonMessageModel.find({
                roleType: model_1.RoleType.REAL_PLAYER,
                moduleId: this.moduleId,
                status: 0 /* Statues.Normal */
            }, { limit: reccPlayerPoolSize });
            // 随机选取
            const pickNpcMessages = this.randomSample(npcMessages, reccPickNpcSize);
            const restSize = reccPickTotalSize - pickNpcMessages.length;
            const pickPlayerMessages = this.randomSample(playerMessages, restSize);
            return [...pickNpcMessages, ...pickPlayerMessages];
        }
        catch (err) {
            this.logger.error({ err, moduleId: this.moduleId, roleId }, "GetRecommendedMessageListFailed");
            return [];
        }
    }
    /**
     * 敏感词检查 (空实现)
     */
    async reviewWords(text) {
        return true;
    }
    /**
     * 随机采样辅助函数
     */
    randomSample(array, size) {
        const shuffled = array.sort(() => 0.5 - Math.random());
        return shuffled.slice(0, size);
    }
}
exports.CommonMessageService = CommonMessageService;
//# sourceMappingURL=service.js.map