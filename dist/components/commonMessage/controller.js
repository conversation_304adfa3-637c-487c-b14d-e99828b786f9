"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonMessageController = exports.ReqSchemas = void 0;
const helper_1 = require("../../helper");
const message_1 = require("./services/message");
const admin_message_1 = require("./services/admin_message");
const ajvCheck_1 = require("../../common/ajvCheck");
const config_1 = require("../../common/config");
const model_1 = require("./model");
/** 有效模块id */
const validModuleIds = config_1.commonMessageCfg.modules.map(m => m.id);
// 请求参数类型定义
exports.ReqSchemas = {
    CommonMessageAdd: {
        type: "object",
        properties: {
            roleid: { type: "integer" },
            roleType: { type: "integer", enum: [1, 2] },
            moduleId: { type: "integer", enum: validModuleIds },
            rolename: { type: "string", minLength: 1, maxLength: 14 },
            text: { type: "string", minLength: 1, maxLength: config_1.commonMessageCfg.maxMessageLenth }
        },
        required: ["roleType", "rolename", "text", "moduleId"]
    },
    CommonMessageReccList: {
        type: "object",
        properties: {
            roleid: { type: "integer" },
            moduleId: { type: "integer", enum: validModuleIds }
        },
        required: ["roleid", "moduleId"]
    },
    AdminMessageAdd: {
        type: "object",
        properties: {
            roleType: { type: "integer", enum: [1, 2] },
            rolename: { type: "string", minLength: 1, maxLength: 14 },
            text: { type: "string", minLength: 1, maxLength: 50 }
        },
        required: ["roleType", "rolename", "text"]
    },
    AdminMessageDel: {
        type: "object",
        properties: {
            ids: {
                type: "array",
                items: { type: "integer" },
                minItems: 1,
                maxItems: 20
            }
        },
        required: ["ids"]
    },
    AdminMessageUpdateStatus: {
        type: "object",
        properties: {
            id: { type: "integer" },
            status: { type: "integer", enum: [model_1.MessageStatus.NORMAL, model_1.MessageStatus.HIDE] }
        },
        required: ["id", "status"]
    }
};
class CommonMessageController {
    constructor() {
    }
    /**
     * 添加留言
     */
    async add(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.CommonMessageAdd);
            const roleId = req.params.roleid;
            const { roleType, rolename: roleName, text, moduleId } = req.params;
            const commonMessageService = new message_1.CommonMessageService(moduleId);
            // 调用service
            const id = await commonMessageService.add({
                roleId,
                roleType,
                roleName,
                moduleId,
                text
            });
            // 返回结果
            res.json({ code: 0, data: { id } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 获取推荐留言列表
     */
    async getReccList(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.CommonMessageReccList);
            const moduleId = req.params.moduleId;
            const roleId = req.params.roleid;
            const commonMessageService = new message_1.CommonMessageService(moduleId);
            // 调用service
            const list = await commonMessageService.getReccList(roleId);
            // 转换返回结果格式
            const formattedList = list.map(item => ({
                id: item.id,
                roleType: item.roleType,
                roleid: item.roleId,
                rolename: item.roleName,
                text: item.text
            }));
            // 返回结果
            res.json({
                code: 0, data: { list: formattedList }
            });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-添加消息
     */
    async adminAdd(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageAdd);
            const { roleType, rolename: roleName, text } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new admin_message_1.AdminMessageService(moduleId);
            const id = await adminMessageService.addMessage({
                roleId: 0,
                roleName,
                roleType,
                text
            });
            res.json({ code: 0, data: { id }
            });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-获取消息列表
     */
    async adminList(req, res, next) {
        try {
            const { page = 1, page_size = 20, status = 'all', roleid, kw, sort_by, sort_order } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new admin_message_1.AdminMessageService(moduleId);
            const { list, total } = await adminMessageService.getMessageList({
                page: Number(page),
                pageSize: Number(page_size),
                status: status,
                roleId: roleid ? Number(roleid) : undefined,
                keyword: kw,
                sortBy: sort_by === 'show_status' ? 'status' : sort_by,
                sortOrder: sort_order
            });
            res.json({ code: 0, data: { list, total } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-删除消息
     */
    async adminDel(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageDel);
            const { ids } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new admin_message_1.AdminMessageService(moduleId);
            const delCnt = await adminMessageService.deleteMessages({ ids });
            res.json({ code: 0, data: { delCnt } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-更新消息状态
     */
    async adminUpdateStatus(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageUpdateStatus);
            const { id, status } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new admin_message_1.AdminMessageService(moduleId);
            await adminMessageService.updateMessageStatus({ id, status });
            res.json({ code: 0, data: { id, updateTime: Date.now() } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
exports.CommonMessageController = CommonMessageController;
exports.default = new CommonMessageController();
//# sourceMappingURL=controller.js.map