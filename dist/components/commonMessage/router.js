"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const commonMessageController_1 = require("./controllers/commonMessageController");
const adminOpertorController_1 = require("./controllers/adminOpertorController");
const config = require("../../common/config");
class CommonMessageRouter {
    constructor() {
        this.apiPrefix = config.server.apiPrefix;
    }
    mount(app) {
        const prefix = this.apiPrefix + "/common_message";
        // game client message apis
        app.post(prefix + "/add", function (req, res, next) {
            commonMessageController_1.default.add(req, res, next);
        });
        app.get(prefix + "/recc_list", function (req, res, next) {
            commonMessageController_1.default.getReccList(req, res, next);
        });
        // admin message apis
        app.post(prefix + "/admin/message/add", function (req, res, next) {
            commonMessageController_1.default.adminAdd(req, res, next);
        });
        app.get(prefix + "/admin/message/list", function (req, res, next) {
            commonMessageController_1.default.adminList(req, res, next);
        });
        app.post(prefix + "/admin/message/del", function (req, res, next) {
            commonMessageController_1.default.adminDel(req, res, next);
        });
        app.post(prefix + "/admin/message/update_status", function (req, res, next) {
            commonMessageController_1.default.adminUpdateStatus(req, res, next);
        });
        // admin operator apis
        app.post(prefix + "/admin/operator/add", function (req, res, next) {
            adminOpertorController_1.default.addOperator(req, res, next);
        });
        app.post(prefix + "/admin/operator/del", function (req, res, next) {
            adminOpertorController_1.default.delOperator(req, res, next);
        });
        app.get(prefix + "/admin/operator/list", function (req, res, next) {
            adminOpertorController_1.default.getOperatorList(req, res, next);
        });
        app.post(prefix + "/admin/operator/update", function (req, res, next) {
            adminOpertorController_1.default.updateOperator(req, res, next);
        });
        app.get(prefix + "/admin/operator/login_info", function (req, res, next) {
            adminOpertorController_1.default.getLoginInfo(req, res, next);
        });
    }
}
exports.default = new CommonMessageRouter();
//# sourceMappingURL=router.js.map