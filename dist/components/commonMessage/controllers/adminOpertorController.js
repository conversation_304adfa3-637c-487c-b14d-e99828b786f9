"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminOperatorController = void 0;
const logger_1 = require("../../../logger");
const adminOperatorService_1 = require("../services/adminOperatorService");
const errorCodes_1 = require("../../../errorCodes");
const helper_1 = require("../../../helper");
const ajvCheck_1 = require("../../../common/ajvCheck");
const ReqSchemas = {
    AdminOperatorList: {
        type: "object",
        properties: {
            page: { type: "integer", default: 1, minimum: 1 },
            pageSize: { type: "integer", default: 10, minimum: 1, maximum: 20 }
        },
        required: ["page", "pageSize"]
    },
    AdminOperatorDel: {
        type: "object",
        properties: {
            openId: { type: "string" }
        },
        required: ["openId"]
    },
    AdminOperatorUpdate: {
        type: "object",
        properties: {
            openId: { type: "string" },
            fullName: { type: "string" },
            roleType: { type: "integer", enum: [0, 1] }
        },
        required: ["openId", "fullName", "roleType"]
    }
};
class AdminOperatorController {
    constructor() {
        this.logger = (0, logger_1.clazzLogger)(AdminOperatorController.name);
        this.adminOperatorService = new adminOperatorService_1.AdminOperatorService(2 /* AdminBizScope.CommonMessage */);
    }
    /**
     * 获取管理员登录信息
     */
    async getLoginInfo(req, res, next) {
        try {
            const loginInfo = await this.adminOperatorService.getLoginInfo(req);
            res.json({ code: 0, data: loginInfo });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 获取管理员列表
     */
    async getOperatorList(req, res, next) {
        try {
            (0, ajvCheck_1.checkParamsByAjv)(req.params, ReqSchemas.AdminOperatorList);
            let params = req.params;
            const listParams = {
                page: params.page,
                pageSize: params.pageSize,
                scope: this.scope
            };
            const list = await this.adminOperatorService.getOperatorList(listParams);
            res.json({ code: 0, data: list });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 添加管理员
     */
    async addOperator(req, res, next) {
        try {
            const params = req.body;
            // 验证必填参数
            if (!params.openId || !params.fullName) {
                await (0, errorCodes_1.BussError)(errorCodes_1.BaseErrors.InvalidArgument);
            }
            const result = await this.adminOperatorService.addOperator(params);
            res.json({ code: 0, data: result });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 删除管理员
     */
    async delOperator(req, res, next) {
        try {
            let params = req.params;
            (0, ajvCheck_1.checkParamsByAjv)(params, ReqSchemas.AdminOperatorDel);
            const result = await this.adminOperatorService.delOperator(params);
            res.json({ code: 0, data: result });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 更新管理员
     */
    async updateOperator(req, res, next) {
        try {
            let params = req.params;
            (0, ajvCheck_1.checkParamsByAjv)(params, ReqSchemas.AdminOperatorUpdate);
            const result = await this.adminOperatorService.updateOperator(params);
            res.json({ code: 0, data: result });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
exports.AdminOperatorController = AdminOperatorController;
exports.default = new AdminOperatorController();
//# sourceMappingURL=adminOpertorController.js.map