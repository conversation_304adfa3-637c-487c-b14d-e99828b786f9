"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonMessageController = exports.ReqSchemas = void 0;
const helper_1 = require("../../../helper");
const messageService_1 = require("../services/messageService");
const adminMessageService_1 = require("../services/adminMessageService");
const ajvCheck_1 = require("../../../common/ajvCheck");
const commonMessageModel_1 = require("../commonMessageModel");
const config_1 = require("../../../common/config");
const ModuleModel_1 = require("../ModuleModel");
/** 有效模块id */
const validModuleIds = ModuleModel_1.default.getAllIds();
// 请求参数类型定义
exports.ReqSchemas = {
    CommonMessageAdd: {
        type: "object",
        properties: {
            roleid: { type: "integer" },
            moduleId: { type: "integer", enum: validModuleIds },
            rolename: { type: "string", minLength: 1, maxLength: 14 },
            text: { type: "string", minLength: 1, maxLength: config_1.commonMessageCfg.maxMessageLenth },
            subGender: { type: "integer", enum: [0, 1] }
        },
        required: ["rolename", "text", "moduleId", "subGender"]
    },
    CommonMessageReccList: {
        type: "object",
        properties: {
            roleid: { type: "integer" },
            moduleId: { type: "integer", enum: validModuleIds }
        },
        required: ["roleid", "moduleId"]
    },
    AdminMessageList: {
        type: "object",
        properties: {
            moduleId: { type: "integer", enum: validModuleIds },
            page: { type: "integer", minimum: 1 },
            page_size: { type: "integer", minimum: 1 },
            status: { type: "string", enum: ["all", "show", "hide"], default: "all" },
            roleid: { type: "integer", default: 0 },
            kw: { type: "string", maxLength: 50 },
            sort_by: { type: "string", enum: ["create_time", "show_status", "role_type"], default: "create_time" },
            sort_order: { type: "string", enum: ["asc", "desc"], default: "desc" }
        },
        required: ["moduleId", "page", "page_size"]
    },
    AdminMessageAdd: {
        type: "object",
        properties: {
            rolename: { type: "string", minLength: 1, maxLength: 14 },
            text: { type: "string", minLength: 1, maxLength: config_1.commonMessageCfg.maxMessageLenth },
            moduleId: { type: "integer", enum: validModuleIds }
        },
        required: ["rolename", "text", "moduleId"]
    },
    AdminMessageDel: {
        type: "object",
        properties: {
            ids: {
                type: "array",
                items: { type: "integer" },
                minItems: 1,
                maxItems: 20
            },
            moduleId: { type: "integer", enum: validModuleIds }
        },
        required: ["ids", "moduleId"]
    },
    AdminMessageUpdateStatus: {
        type: "object",
        properties: {
            id: { type: "integer" },
            status: { type: "integer", enum: [commonMessageModel_1.MessageStatus.NORMAL, commonMessageModel_1.MessageStatus.HIDE] },
            moduleId: { type: "integer", enum: validModuleIds }
        },
        required: ["id", "status", "moduleId"]
    }
};
class CommonMessageController {
    constructor() {
    }
    /**
     * 添加留言
     */
    async add(req, res, next) {
        try {
            let params = req.params;
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(params, exports.ReqSchemas.CommonMessageAdd);
            const commonMessageService = new messageService_1.CommonMessageService(params.moduleId);
            // 调用service
            const id = await commonMessageService.add({
                roleid: params.roleid,
                rolename: params.rolename,
                jobId: params.jobId,
                gender: params.gender,
                subGender: params.subGender,
                moduleId: params.moduleId,
                text: params.text,
                roleType: params.roleType
            });
            // 返回结果
            res.json({ code: 0, data: { id } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 获取推荐留言列表
     */
    async getReccList(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.CommonMessageReccList);
            const moduleId = req.params.moduleId;
            const roleId = req.params.roleid;
            const commonMessageService = new messageService_1.CommonMessageService(moduleId);
            // 调用service
            const list = await commonMessageService.getReccList(roleId);
            // 转换返回结果格式
            const formattedList = list.map(item => {
                let message = {
                    id: item.id,
                    roleid: item.roleId,
                    rolename: item.roleName,
                    gender: item.gender,
                    subGender: item.subGender,
                    jobId: item.jobId,
                    text: item.text,
                    createTime: item.createTime
                };
                return message;
            });
            // 返回结果
            res.json({
                code: 0, data: { list: formattedList }
            });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-添加消息
     */
    async adminMessageAdd(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageAdd);
            const { roleType, rolename: roleName, text } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new adminMessageService_1.AdminMessageService(moduleId);
            const id = await adminMessageService.addMessage({
                roleId: 0, // 假玩家roleId为0
                roleName,
                roleType,
                text
            });
            res.json({
                code: 0, data: { id }
            });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-获取消息列表
     */
    async adminMessageList(req, res, next) {
        try {
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageList);
            const { moduleId, page = 1, page_size = 20, status = 'all', roleid, kw, sort_by, sort_order } = req.params;
            const adminMessageService = new adminMessageService_1.AdminMessageService(moduleId);
            const { list, total } = await adminMessageService.getMessageList({
                moduleId,
                page: Number(page),
                pageSize: Number(page_size),
                status: status,
                roleId: roleid ? Number(roleid) : undefined,
                keyword: kw,
                sortBy: sort_by,
                sortOrder: sort_order
            });
            res.json({ code: 0, data: { list, total } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-删除消息
     */
    async adminMessageDel(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageDel);
            const { ids } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new adminMessageService_1.AdminMessageService(moduleId);
            const delCnt = await adminMessageService.deleteMessages({ ids });
            res.json({ code: 0, data: { delCnt } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-更新消息状态
     */
    async adminMessageUpdateStatus(req, res, next) {
        try {
            // 参数校验
            (0, ajvCheck_1.checkParamsByAjv)(req.params, exports.ReqSchemas.AdminMessageUpdateStatus);
            const { id, status } = req.params;
            const moduleId = req.params.moduleId;
            const adminMessageService = new adminMessageService_1.AdminMessageService(moduleId);
            await adminMessageService.updateMessageStatus({ id, status });
            res.json({ code: 0, data: { id, updateTime: Date.now() } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    /**
     * 管理后台-获取模块列表
     */
    async adminModuleList(req, res, next) {
        try {
            const modules = ModuleModel_1.default.listAll();
            res.json({ code: 0, data: { list: modules } });
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
}
exports.CommonMessageController = CommonMessageController;
exports.default = new CommonMessageController();
//# sourceMappingURL=commonMessageController.js.map