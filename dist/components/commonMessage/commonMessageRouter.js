"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const commonMessageController_1 = require("./controllers/commonMessageController");
const adminOpertorController_1 = require("./controllers/adminOpertorController");
const config = require("../../common/config");
const helper_1 = require("../../helper");
const authCheck_1 = require("../../middlewares/authCheck");
const corpBackendAuth_1 = require("../../middlewares/corpBackendAuth");
class CommonMessageRouter {
    constructor() {
        this.apiPrefix = config.server.apiPrefix;
    }
    mount(app) {
        const prefix = this.apiPrefix + "/common_message";
        // game client message apis
        app.post(prefix + "/add", commonMessageController_1.default.add.bind(commonMessageController_1.default));
        app.get(prefix + "/recc_list", commonMessageController_1.default.getReccList.bind(commonMessageController_1.default));
        (0, helper_1.preUseWhenUrlPrefix)(app, prefix + "/admin", authCheck_1.skipSkeyAuthMiddleware);
        // 检查用户是否具有corp邮箱登录态
        (0, helper_1.useWhenUrlPrefix)(app, prefix + "/admin", corpBackendAuth_1.default.checkCorpLoginAuthMiddleware);
        // 检查用户是否具有commonMessage后台权限
        (0, helper_1.useWhenUrlPrefix)(app, prefix + "/admin", corpBackendAuth_1.default.checkUserRegisitorMiddleware(2 /* AdminBizScope.CommonMessage */));
        const checkIsAdmin = corpBackendAuth_1.default.checkAuthUserIsAdminMiddleware();
        // admin message apis
        app.post(prefix + "/admin/message/add", commonMessageController_1.default.adminMessageAdd.bind(commonMessageController_1.default));
        app.get(prefix + "/admin/message/list", commonMessageController_1.default.adminMessageList.bind(commonMessageController_1.default));
        app.post(prefix + "/admin/message/del", checkIsAdmin, commonMessageController_1.default.adminMessageDel.bind(commonMessageController_1.default));
        app.post(prefix + "/admin/message/update_status", commonMessageController_1.default.adminMessageUpdateStatus.bind(commonMessageController_1.default));
        app.get(prefix + "/admin/modules/list", commonMessageController_1.default.adminModuleList.bind(commonMessageController_1.default));
        // admin operator apis
        app.post(prefix + "/admin/operator/add", checkIsAdmin, adminOpertorController_1.default.addOperator.bind(adminOpertorController_1.default));
        app.post(prefix + "/admin/operator/del", checkIsAdmin, adminOpertorController_1.default.delOperator.bind(adminOpertorController_1.default));
        app.get(prefix + "/admin/operator/list", adminOpertorController_1.default.getOperatorList.bind(adminOpertorController_1.default));
        app.post(prefix + "/admin/operator/update", checkIsAdmin, adminOpertorController_1.default.updateOperator.bind(adminOpertorController_1.default));
        app.get(prefix + "/admin/operator/login_info", adminOpertorController_1.default.getLoginInfo.bind(adminOpertorController_1.default));
    }
}
exports.default = new CommonMessageRouter();
//# sourceMappingURL=commonMessageRouter.js.map