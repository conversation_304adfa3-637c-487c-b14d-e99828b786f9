"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminMessageService = void 0;
const commonMessageModel_1 = require("../commonMessageModel");
const errorCodes_1 = require("../../../errorCodes");
const logger_1 = require("../../../logger");
class AdminMessageService {
    constructor(moduleId) {
        this.commonMessageModel = commonMessageModel_1.CommonMessageModel;
        this.logger = (0, logger_1.clazzLogger)("adminMessage.service");
        this.moduleId = moduleId;
    }
    /**
     * 添加消息
     */
    async addMessage(params) {
        try {
            const now = Date.now();
            const record = {
                roleId: params.roleId,
                roleType: params.roleType,
                roleName: params.roleName,
                moduleId: this.moduleId,
                text: params.text,
                status: 0 /* Statues.Normal */,
                createTime: now,
                updateTime: now
            };
            const insertId = await this.commonMessageModel.insert(record);
            this.logger.info({ record }, "AdminAddMessageSuccess");
            return insertId;
        }
        catch (err) {
            this.logger.error({ err, params, moduleId: this.moduleId }, "AdminAddMessageFailed");
            throw errorCodes_1.BaseErrors.ServerError;
        }
    }
    /**
     * 获取消息列表
     */
    async getMessageList(params) {
        try {
            function baseQuery() {
                let query = commonMessageModel_1.CommonMessageModel.scope().where({ moduleId: this.moduleId });
                // 状态过滤
                if (params.status === 'all') {
                    query = query.whereIn('status', [commonMessageModel_1.MessageStatus.NORMAL, commonMessageModel_1.MessageStatus.HIDE]);
                }
                else if (params.status === 'show') {
                    query = query.whereIn('status', [commonMessageModel_1.MessageStatus.NORMAL]);
                }
                else if (params.status === 'hide') {
                    query = query.where({ status: commonMessageModel_1.MessageStatus.HIDE }); // 使用 Read 状态表示隐藏
                }
                // 角色ID过滤
                if (params.roleId) {
                    query = query.where({ roleId: params.roleId });
                }
                // 关键词搜索
                if (params.keyword) {
                    if (params.roleId > 0) {
                        query = query.where('text', 'like', `%${params.keyword}%`);
                    }
                    else {
                        throw errorCodes_1.errorCode.MessageSearchOnlySupportWhenFilterRoleId;
                    }
                }
                // 排序
                if (params.sortBy) {
                    const sortOrder = params.sortOrder === 'asc' ? 'asc' : 'desc';
                    switch (params.sortBy) {
                        case 'createTime':
                            query = query.orderBy('createTime', sortOrder);
                            break;
                        case 'status':
                            query = query.orderBy('status', sortOrder);
                            break;
                        case 'roleType':
                            query = query.orderBy('roleType', sortOrder);
                            break;
                    }
                }
                return query;
            }
            const offset = (params.page - 1) * params.pageSize;
            const listQuery = baseQuery().offset(offset).limit(params.pageSize);
            const countQuery = baseQuery();
            const [list, total] = await Promise.all([
                this.commonMessageModel.executeByQuery(listQuery),
                this.commonMessageModel.countByQuery(countQuery)
            ]);
            this.logger.info({ params, total, moduleId: this.moduleId }, "GetAdminMessageListSuccess");
            return { list, total };
        }
        catch (err) {
            this.logger.error({ err, params, moduleId: this.moduleId }, "GetAdminMessageListFailed");
            throw err;
        }
    }
    /**
     * 删除消息
     */
    async deleteMessages(params) {
        try {
            let deletedCount = 0;
            let upRet = await this.commonMessageModel.updateByCondition({ id: params.ids }, { status: -1 /* Statues.Deleted */ });
            deletedCount = upRet.affectedRows;
            this.logger.info({ params, affected: deletedCount }, "AdminDeleteMessagesSuccess");
            return deletedCount;
        }
        catch (err) {
            this.logger.error({ err, params }, "AdminDeleteMessagesFailed");
            throw errorCodes_1.BaseErrors.ServerError;
        }
    }
    /**
     * 更新消息状态
     */
    async updateMessageStatus(params) {
        try {
            const now = Date.now();
            const message = await this.commonMessageModel.findOne({ id: params.id });
            if (!message) {
                throw errorCodes_1.errorCode.RoleInfoNotFound;
            }
            await this.commonMessageModel.updateByCondition({ id: params.id }, { status: params.status, updateTime: now });
            this.logger.info({ params }, "AdminUpdateMessageStatusSuccess");
        }
        catch (err) {
            this.logger.error({ err, params }, "AdminUpdateMessageStatusFailed");
            if (err === errorCodes_1.errorCode.RoleInfoNotFound) {
                throw err;
            }
            throw errorCodes_1.BaseErrors.ServerError;
        }
    }
}
exports.AdminMessageService = AdminMessageService;
//# sourceMappingURL=admin_message.js.map