"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BustPhotoComponent = exports.paths = void 0;
const authCheck_1 = require("../../middlewares/authCheck");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/bust_photo/add",
        paramsSchema: type_1.ReqSchemas.BustPhotoAdd,
        operation: operation_1.bustPhotoAdd,
    },
    {
        method: "get",
        url: "/bust_photo/get",
        paramsSchema: type_1.ReqSchemas.BustPhotoGet,
        operation: operation_1.bustPhotoGet,
    },
    {
        method: "get",
        url: "/bust_photo/get_batch",
        paramsSchema: type_1.ReqSchemas.BustPhotoGetBatch,
        operation: operation_1.bustPhotoGetBatch,
    },
    {
        method: "get",
        url: "/bust_photo/server/get_batch",
        paramsSchema: type_1.ReqSchemas.BustPhotoGetBatch,
        before: authCheck_1.authCheckByToken,
        operation: operation_1.bustPhotoGetBatch,
        option: { skipSkey: true },
    },
    {
        method: "get",
        url: "/bust_photo/list",
        paramsSchema: type_1.ReqSchemas.BustPhotoList,
        operation: operation_1.bustPhotoList,
    },
];
exports.BustPhotoComponent = {
    paths: exports.paths,
    prefix: "/bust_photo/",
};
//# sourceMappingURL=index.js.map