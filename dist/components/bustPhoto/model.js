"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BustPhotoModel = exports.BustPhotoCols = void 0;
const BaseModel2_1 = require("../../models/BaseModel2");
exports.BustPhotoCols = ["id", "roleId", "index", "url", "createTime"];
class BustPhotoModelClass extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_bust_photo");
    }
}
exports.BustPhotoModel = new BustPhotoModelClass();
//# sourceMappingURL=model.js.map