"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bustPhotoAdd = bustPhotoAdd;
exports.bustPhotoGet = bustPhotoGet;
exports.bustPhotoGetBatch = bustPhotoGetBatch;
exports.bustPhotoList = bustPhotoList;
const bluebird = require("bluebird");
const config_all_1 = require("../../common/config.all");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const cacheUtil_1 = require("../../services/cacheUtil");
const model_1 = require("./model");
class BustPhotoIndexCacheClass extends cacheUtil_1.GenericCache {
    getExpireTime() {
        return 5 * 60;
    }
    getKey(params) {
        return (0, util_1.cacheKeyGen)("bust_photo_index", { roleId: params.roleId, index: params.index });
    }
    async fetchDataSource(params) {
        const r = await model_1.BustPhotoModel.findOne({ roleId: params.roleId, index: params.index, status: 0 /* Statues.Normal */ }, [
            "id",
            "roleId",
            "index",
            "url",
            "createTime",
        ]);
        return r || null;
    }
}
const BustPhotoIndexCache = new BustPhotoIndexCacheClass();
/** 半身像上传 */
async function bustPhotoAdd(params) {
    const condition = { roleId: params.roleid, index: params.index };
    const r = await model_1.BustPhotoModel.findOne(condition, ["id"]);
    if (r && r.id) {
        await model_1.BustPhotoModel.updateByCondition({ id: r.id }, {
            url: params.url,
            index: params.index,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        });
        return { id: r.id };
    }
    else {
        const props = {
            roleId: params.roleid,
            url: params.url,
            index: params.index,
            status: 0 /* Statues.Normal */,
            createTime: Date.now(),
        };
        const id = await model_1.BustPhotoModel.insert(props);
        BustPhotoIndexCache.del({ roleId: params.roleid, index: params.index });
        return { id: id };
    }
}
/** 角色半身像获取指定位置 */
async function bustPhotoGet(params) {
    const viewerId = params.targetId || params.roleid;
    const r = await BustPhotoIndexCache.get({ roleId: viewerId, index: params.index });
    if (r && r.id) {
        return r;
    }
    else {
        throw errorCodes_1.BustPhotoErrors.PhotoNotFound;
    }
}
async function bustPhotoGetBatch(params) {
    const resp = { list: [] };
    const targetIds = (0, util_1.csvStrToIntArray)(params.targetIds);
    if (targetIds && targetIds.length > 0) {
        if (targetIds.length > 10) {
            throw errorCodes_1.BustPhotoErrors.BatchGetReachLimit;
        }
        const photos = await bluebird.map(targetIds, (roleId) => {
            return BustPhotoIndexCache.get({ roleId, index: params.index });
        }, { concurrency: 5 });
        for (const p of photos) {
            if (p) {
                resp.list.push(p);
            }
        }
    }
    else {
        throw errorCodes_1.BustPhotoErrors.TargetIdsEmpty;
    }
    return resp;
}
/** 角色半身像列表 */
async function bustPhotoList(params) {
    const viewerId = params.targetId || params.roleid;
    const list = await model_1.BustPhotoModel.powerQuery({
        initQuery: model_1.BustPhotoModel.normalScope(),
        where: { roleId: viewerId },
        orderBy: [["index"], ["asc"]],
        pagination: { page: 1, pageSize: config_all_1.bustPhotoCfg.maxSizePerRoleId },
        select: ["id", "roleId", "url", "index", "createTime"],
    });
    return { list };
}
//# sourceMappingURL=operation.js.map