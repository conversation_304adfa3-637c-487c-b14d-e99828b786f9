"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    BustPhotoAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            index: { type: "number", minimum: 1, maximum: 20 },
            url: { type: "string" },
        },
        required: ["roleid", "index", "url"],
    },
    BustPhotoGet: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            targetId: { type: "number" },
            index: { type: "number", minimum: 1, maximum: 20 },
        },
        required: ["roleid", "index"],
    },
    BustPhotoGetBatch: {
        type: "object",
        properties: {
            targetIds: { type: "string" },
            index: { type: "integer", minimum: 1, maximum: 20 },
        },
        required: ["index", "targetIds"],
    },
    BustPhotoList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            targetId: { type: "number" },
        },
        required: ["roleid"],
    },
};
//# sourceMappingURL=type.js.map