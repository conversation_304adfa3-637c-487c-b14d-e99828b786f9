"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
const config_1 = require("../../common/config");
exports.ReqSchemas = {
    ActivityTakePhotoAddPhoto: {
        roleId: { type: Number },
        locationId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationNum },
        imgUrl: { type: String },
    },
    ActivityTakePhotoGetMyPhotos: {
        roleId: { type: Number },
        locationId: { type: Number, required: false, min: 1, max: config_1.ActivityTakePhotoCfg.locationNum },
    },
    ActivityTakePhotoSelectPhoto: {
        roleId: { type: Number },
        locationId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationNum },
        imgId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationImageNum },
    },
    ActivityTakePhotoLockPhoto: {
        roleId: { type: Number },
        locationId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationNum },
        imgId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationImageNum },
    },
    ActivityTakePhotoUnlockPhoto: {
        roleId: { type: Number },
        locationId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationNum },
        imgId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationImageNum },
    },
    ActivityTakePhotoGetLocationSelectedPhoto: {
        roleId: { type: Number },
        targetId: { type: Number },
        locationId: { type: Number, min: 1, max: config_1.ActivityTakePhotoCfg.locationNum },
    },
};
//# sourceMappingURL=type.js.map