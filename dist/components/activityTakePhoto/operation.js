"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activityTakePhotoAddPhoto = activityTakePhotoAddPhoto;
exports.activityTakePhotoGetMyPhotos = activityTakePhotoGetMyPhotos;
exports.activityTakePhotoSelectPhoto = activityTakePhotoSelectPhoto;
exports.activityTakePhotoLockPhoto = activityTakePhotoLockPhoto;
exports.activityTakePhotoUnlockPhoto = activityTakePhotoUnlockPhoto;
exports.activityTakePhotoGetLocationSelectedPhoto = activityTakePhotoGetLocationSelectedPhoto;
const config_all_1 = require("../../common/config.all");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const ActivityTakePhoto_1 = require("../../models/ActivityTakePhoto");
const UserPermission_1 = require("../../models/UserPermission");
/** 上传照片 */
async function activityTakePhotoAddPhoto(params) {
    const { roleId, locationId, imgUrl } = params;
    const record = await ActivityTakePhoto_1.ActivityTakePhotoModel.findOne({ RoleId: roleId, LocationId: locationId });
    if (record) {
        const imgList = (0, util_1.getJsonInfo)(record.ImgList, []);
        if (imgList.length) {
            let updateData = {};
            if (imgList.length < config_all_1.ActivityTakePhotoCfg.locationImageNum) {
                imgList.push({
                    imgUrl,
                    isLocked: false,
                    isSelected: false
                });
                updateData.ImgList = JSON.stringify(imgList);
            }
            else {
                let isOk = false;
                for (let i in imgList) {
                    if (!imgList[i].isLocked) {
                        imgList.splice(Number(i), 1);
                        imgList.push({
                            imgUrl,
                            isLocked: false,
                            isSelected: false
                        });
                        isOk = true;
                        break;
                    }
                }
                if (!isOk)
                    throw errorCodes_1.ActivityTakePhotoErrors.CannotUploadMorePhotos;
                updateData.ImgList = JSON.stringify(imgList);
            }
            await ActivityTakePhoto_1.ActivityTakePhotoModel.updateByCondition({ RoleId: roleId, LocationId: locationId }, updateData);
        }
        else {
            await ActivityTakePhoto_1.ActivityTakePhotoModel.updateByCondition({ RoleId: roleId, LocationId: locationId }, {
                ImgList: JSON.stringify([{
                        imgUrl,
                        isLocked: true,
                        isSelected: true
                    }])
            });
        }
    }
    else {
        await ActivityTakePhoto_1.ActivityTakePhotoModel.insert({
            RoleId: roleId,
            LocationId: locationId,
            ImgList: JSON.stringify([{
                    imgUrl,
                    isLocked: true,
                    isSelected: true
                }])
        });
    }
    const data = {
        isOk: true,
    };
    return data;
}
/** 查询自己的照片 */
async function activityTakePhotoGetMyPhotos(params) {
    const { roleId, locationId } = params;
    const record = await ActivityTakePhoto_1.ActivityTakePhotoModel.find(locationId ? { RoleId: roleId, LocationId: locationId } : { RoleId: roleId });
    const list = record.map(r => {
        const imgList = (0, util_1.getJsonInfo)(r.ImgList, []);
        const imgs = [];
        for (let i in imgList) {
            imgs.push({
                locationId: r.LocationId,
                imgId: Number(i) + 1,
                imgUrl: imgList[i].imgUrl,
                isLocked: imgList[i].isLocked,
                isSelected: imgList[i].isSelected
            });
        }
        return imgs;
    });
    return list;
}
/** 选中照片 */
async function activityTakePhotoSelectPhoto(params) {
    const { roleId, locationId, imgId } = params;
    const record = await ActivityTakePhoto_1.ActivityTakePhotoModel.findOne({ RoleId: roleId, LocationId: locationId });
    if (!record)
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoNotExist;
    const imgList = (0, util_1.getJsonInfo)(record.ImgList, []);
    if (!imgList[imgId - 1])
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoNotExist;
    if (!imgList[imgId - 1].isSelected) {
        for (let i in imgList) {
            if (imgList[i].isSelected) {
                imgList[i].isSelected = false;
                break;
            }
        }
        imgList[imgId - 1].isSelected = true;
        imgList[imgId - 1].isLocked = true;
        await ActivityTakePhoto_1.ActivityTakePhotoModel.updateByCondition({ RoleId: roleId, LocationId: locationId }, { ImgList: JSON.stringify(imgList) });
    }
    const data = {
        isOk: true,
    };
    return data;
}
/** 锁定照片 */
async function activityTakePhotoLockPhoto(params) {
    const { roleId, locationId, imgId } = params;
    const record = await ActivityTakePhoto_1.ActivityTakePhotoModel.findOne({ RoleId: roleId, LocationId: locationId });
    if (!record)
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoNotExist;
    const imgList = (0, util_1.getJsonInfo)(record.ImgList, []);
    if (!imgList[imgId - 1])
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoNotExist;
    if (!imgList[imgId - 1].isLocked) {
        imgList[imgId - 1].isLocked = true;
        await ActivityTakePhoto_1.ActivityTakePhotoModel.updateByCondition({ RoleId: roleId, LocationId: locationId }, { ImgList: JSON.stringify(imgList) });
    }
    const data = {
        isOk: true,
    };
    return data;
}
/** 解锁照片 */
async function activityTakePhotoUnlockPhoto(params) {
    const { roleId, locationId, imgId } = params;
    const record = await ActivityTakePhoto_1.ActivityTakePhotoModel.findOne({ RoleId: roleId, LocationId: locationId });
    if (!record)
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoNotExist;
    const imgList = (0, util_1.getJsonInfo)(record.ImgList, []);
    if (!imgList[imgId - 1])
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoNotExist;
    if (imgList[imgId - 1].isSelected)
        throw errorCodes_1.ActivityTakePhotoErrors.PhotoCannotUnlock;
    if (imgList[imgId - 1].isLocked) {
        imgList[imgId - 1].isLocked = false;
        await ActivityTakePhoto_1.ActivityTakePhotoModel.updateByCondition({ RoleId: roleId, LocationId: locationId }, { ImgList: JSON.stringify(imgList) });
    }
    const data = {
        isOk: true,
    };
    return data;
}
/** 查玩家某地点选中的照片 */
async function activityTakePhotoGetLocationSelectedPhoto(params) {
    const data = { imgUrl: "" };
    const { roleId, targetId, locationId } = params;
    const isAllow = await (0, UserPermission_1.hasPermission)(targetId, UserPermission_1.Permission.ActivityTakePhotoAbnormal);
    if (!isAllow) {
        data.imgUrl = config_all_1.ActivityTakePhotoCfg.locationDefaultImg[locationId] || "";
        return data;
    }
    const record = await ActivityTakePhoto_1.ActivityTakePhotoModel.findOne({ RoleId: targetId, LocationId: locationId });
    if (record) {
        const imgList = (0, util_1.getJsonInfo)(record.ImgList, []);
        if (imgList.length) {
            for (let r of imgList) {
                if (r.isSelected) {
                    data.imgUrl = r.imgUrl;
                    break;
                }
            }
        }
    }
    return data;
}
//# sourceMappingURL=operation.js.map