"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityTakePhotoComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/activity/take_photo/add_photo",
        paramsSchema: type_1.ReqSchemas.ActivityTakePhotoAddPhoto,
        operation: operation_1.activityTakePhotoAddPhoto,
    },
    {
        method: "get",
        url: "/activity/take_photo/get_my_photos",
        paramsSchema: type_1.ReqSchemas.ActivityTakePhotoGetMyPhotos,
        operation: operation_1.activityTakePhotoGetMyPhotos,
    },
    {
        method: "post",
        url: "/activity/take_photo/select_photo",
        paramsSchema: type_1.ReqSchemas.ActivityTakePhotoSelectPhoto,
        operation: operation_1.activityTakePhotoSelectPhoto,
    },
    {
        method: "post",
        url: "/activity/take_photo/lock_photo",
        paramsSchema: type_1.ReqSchemas.ActivityTakePhotoLockPhoto,
        operation: operation_1.activityTakePhotoLockPhoto,
    },
    {
        method: "post",
        url: "/activity/take_photo/unlock_photo",
        paramsSchema: type_1.ReqSchemas.ActivityTakePhotoUnlockPhoto,
        operation: operation_1.activityTakePhotoUnlockPhoto,
    },
    {
        method: "get",
        url: "/activity/take_photo/get_location_selected_photo",
        paramsSchema: type_1.ReqSchemas.ActivityTakePhotoGetLocationSelectedPhoto,
        operation: operation_1.activityTakePhotoGetLocationSelectedPhoto,
    },
];
exports.ActivityTakePhotoComponent = {
    paths: exports.paths,
    prefix: "/activity/take_photo/",
};
//# sourceMappingURL=index.js.map