"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformKafkaMessage = exports.processKafkaMessage = exports.listenKafkaTopic = exports.getConsumer = void 0;
const kafkajs_1 = require("kafkajs");
const logger_1 = require("../../common/logger");
const util_1 = require("./logConsumer/util");
const logger_2 = require("../../logger");
const bluebird = require("bluebird");
const config_all_1 = require("../../common/config.all");
const addYuanbaoLog_1 = require("./logConsumer/addYuanbaoLog");
const logger = (0, logger_2.clazzLogger)('logConsumer.consumer');
let kafka = null;
let consumer = null;
const bunyanLoggerCreator = (level) => {
    const logger = (0, logger_1.getLogger)('kafka_consumer', (0, util_1.toBunyanLogLevel)(level));
    logger.info('initKafkaLog');
    return ({ namespace, level, label, log }) => {
        const { message, ...extra } = log;
        if (level === kafkajs_1.logLevel.ERROR) {
            if (extra?.error == "The group is rebalancing, so a rejoin is needed") {
                logger.warn({ label, extra, namespace }, message);
            }
            else {
                logger.error({ label, extra, namespace }, message);
            }
        }
        else {
            logger.info({ label, extra, namespace }, message);
        }
    };
};
function createKafkaClient() {
    const clientOption = {
        clientId: config_all_1.kafkaCfg.clientId,
        brokers: config_all_1.kafkaCfg.brokers,
        logLevel: kafkajs_1.logLevel[config_all_1.kafkaCfg.logLevel],
        logCreator: bunyanLoggerCreator,
    };
    if (config_all_1.kafkaCfg.sasl && config_all_1.kafkaCfg.sasl.username && config_all_1.kafkaCfg.sasl.password) {
        clientOption.sasl = config_all_1.kafkaCfg.sasl;
    }
    const kafka = new kafkajs_1.Kafka(clientOption);
    return kafka;
}
function getKafkaClient() {
    if (kafka === null) {
        kafka = createKafkaClient();
    }
    return kafka;
}
function getConsumer() {
    if (consumer === null) {
        consumer = createConsumer();
    }
    return consumer;
}
exports.getConsumer = getConsumer;
function createConsumer() {
    const consumer = getKafkaClient().consumer({ groupId: config_all_1.kafkaCfg.groupId });
    return consumer;
}
async function listenKafkaTopic(consumer) {
    logger.info('start listenKafkaTopic');
    try {
        await consumer.connect();
        await consumer.subscribe({ topic: config_all_1.kafkaCfg.topic, fromBeginning: config_all_1.kafkaCfg.fromBeginning });
        await consumer.run({
            partitionsConsumedConcurrently: config_all_1.kafkaCfg.partitionsConsumedConcurrently,
            eachBatch: async (eachBatchPayload) => {
                const { batch } = eachBatchPayload;
                await bluebird.map(batch.messages, async (message) => {
                    return processKafkaMessage(consumer, batch.topic, batch.partition, message);
                }, { concurrency: config_all_1.kafkaCfg.taskConcurrency });
                return;
            },
        });
    }
    catch (err) {
        consumer.logger().error('ListenKafkaTopicFail', { err });
        return null;
    }
}
exports.listenKafkaTopic = listenKafkaTopic;
async function processKafkaMessage(consumer, topic, partition, message) {
    try {
        const logMessage = message.value.toString();
        const logItem = await transformKafkaMessage(consumer, logMessage);
        await persistLogMessage(consumer, logItem);
    }
    catch (err) {
        consumer.logger().error('ProcessKafkaMessageFail', { err, topic, partition, message });
    }
}
exports.processKafkaMessage = processKafkaMessage;
async function persistLogMessage(consumer, logItem) {
    if (!logItem)
        return;
    try {
        switch (logItem.eventName) {
            case 404135 /* LogEventId.CloudGameAddYuanbao */:
                await (0, addYuanbaoLog_1.onCloudGameAddYuanbao)(logItem);
                break;
            default:
                consumer.logger().debug("UnknownEventName", { logItem });
                break;
        }
    }
    catch (err) {
        consumer.logger().error("PersistLogMessageFail", { err, logItem });
    }
}
async function transformKafkaMessage(consumer, message) {
    const logItem = (0, util_1.parseLogRawMessage)(message);
    if (logItem && logItem.eventName && logItem.message) {
        consumer.logger().debug('TransformKafkaMessageOK', { logItem });
        return logItem;
    }
    else {
        consumer.logger().warn('TransformKafkaMessageFail', { logItem });
        return null;
    }
}
exports.transformKafkaMessage = transformKafkaMessage;
//# sourceMappingURL=yuanbaoLogConsumer.js.map