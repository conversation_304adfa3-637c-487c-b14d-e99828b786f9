"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cloudGameDurationShow = cloudGameDurationShow;
exports.cloudGameDurationNotifyChargeYuanbaoEmpty = cloudGameDurationNotifyChargeYuanbaoEmpty;
exports.cloudGameDurationNotifyChargeYuanbaoWrap = cloudGameDurationNotifyChargeYuanbaoWrap;
exports.cloudGameDurationNotifyChargeYuanbao = cloudGameDurationNotifyChargeYuanbao;
exports.cloudGameDurationReceiveDailyAward = cloudGameDurationReceiveDailyAward;
exports.autoInitNormalUserAccountWhenDailyLogin = autoInitNormalUserAccountWhenDailyLogin;
exports.cloudGameDurationIncr = cloudGameDurationIncr;
exports.cloudGameDurationDecr = cloudGameDurationDecr;
exports.cloudGameDurationNotifyBuyMonthCard = cloudGameDurationNotifyBuyMonthCard;
exports.cloudGameDurationMonthCardShow = cloudGameDurationMonthCardShow;
const nanoid_1 = require("nanoid");
const cacheUtil_1 = require("../../common/cacheUtil");
const config_all_1 = require("../../common/config.all");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const monthCardAccount_1 = require("../../models/monthCardAccount");
const monthCardChangeLog_1 = require("../../models/monthCardChangeLog");
const monthCardCharge_1 = require("../../models/monthCardCharge");
const constant_1 = require("./constant");
const service_1 = require("./service");
const logger = (0, logger_1.clazzLogger)("cloudGameDuration/operation");
async function checkUrsAccountInitAndAutoExpire(urs, curDate, userType) {
    const ds = (0, service_1.getDayStr)(curDate);
    const account = await models_1.CloudGameDurationAccountModel.findOne({ urs });
    if (account && account.urs) {
        if (userType === "cloud" && account.ds !== ds) {
            const upRet = await models_1.CloudGameDurationAccountModel.updateById(account.id, { ds, dailyDuration: 0 });
            account.ds = ds;
            account.dailyDuration = 0;
            logger.warn({ account, ds, upRet }, "CheckUrsAccountInitAndAutoExpire");
        }
        return account;
    }
    else {
        await (0, service_1.freeModifyLock)(urs);
        const ds = (0, service_1.getDayStr)(curDate);
        const account = await (0, service_1.initAccount)(urs, constant_1.FIRST_LOGIN_AFTER_LAUNCH_DURATION, constant_1.CLOUD_DAILY_DURATION_PER_DAY, ds, curDate.getTime());
        return account;
    }
}
async function getAccountAndAutoExpire(urs, ds, userType) {
    const account = await models_1.CloudGameDurationAccountModel.findOne({ urs });
    if (account && account.urs) {
        if (userType === "cloud" && account.ds !== ds) {
            const upRet = await models_1.CloudGameDurationAccountModel.updateById(account.id, { ds, dailyDuration: 0 });
            account.ds = ds;
            account.dailyDuration = 0;
            logger.warn({ account, ds, upRet }, "CheckUrsAccountInitAndAutoExpire");
        }
        return account;
    }
    else {
        return null;
    }
}
/** 展示当前urs账号云游戏时长信息 */
async function cloudGameDurationShow(params) {
    const { urs, ts, userType } = params;
    const curDate = new Date(ts > 0 ? ts * 1000 : Date.now());
    const monthStr = (0, service_1.getMonthStr)(curDate);
    const account = await checkUrsAccountInitAndAutoExpire(urs, curDate, userType);
    const yuanbao = await (0, service_1.getMonthYuanbao)(urs, monthStr);
    const data = {
        urs,
        ds: account.ds,
        dailyDuration: account.dailyDuration,
        permanentDuration: account.duration,
        yuanbao,
    };
    if (params.serverId) {
        const monthCard = await monthCardAccount_1.MonthCardAccountModel.getStatus(urs, params.serverId);
        data.monthCard = monthCard;
    }
    return data;
}
/** 一个空实现，只是为了方便游戏侧过渡 */
async function cloudGameDurationNotifyChargeYuanbaoEmpty(params) {
    const data = {
        urs: params.urs,
        ds: "",
        dailyDuration: 0,
        permanentDuration: 0,
        isChange: false,
        dailyDurationDelta: 0,
        permanentDurationDelta: 0,
        yuanbao: {
            month: "",
            chargeNum: params.num,
            awardDuration: 0,
        },
    };
    return data;
}
/** http接口新增一个包装层， 可以通过当日志消费逻辑开启后, 开关开启是否启用真实逻辑 */
async function cloudGameDurationNotifyChargeYuanbaoWrap(params) {
    if (config_all_1.cloudGameDurationCfg.disableYuanbaoChargeByHttp) {
        return cloudGameDurationNotifyChargeYuanbaoEmpty(params);
    }
    else {
        return cloudGameDurationNotifyChargeYuanbao(params);
    }
}
/** 游戏通知充值元宝数量，服务负责折算充值比例 */
async function cloudGameDurationNotifyChargeYuanbao(params) {
    const { ts, userType } = params;
    const curDate = new Date(ts > 0 ? ts * 1000 : Date.now());
    const month = (0, service_1.getMonthStr)(curDate);
    const { urs, orderId } = params;
    const chargeNum = params.num;
    const account = await checkUrsAccountInitAndAutoExpire(urs, curDate, userType);
    // first add charge log
    const orderChangeLog = await models_1.CloudGameYuanbaoChangeLogModel.findOne({ urs, orderId });
    if (orderChangeLog && orderChangeLog.id) {
        await (0, service_1.freeModifyLock)(urs);
        return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.YuanbaoOrderConsumed);
    }
    const logProps = {
        urs,
        chargeNum,
        orderId,
        reason: 1 /* EChangeReasonType.BY_API */,
        createTime: Date.now(),
    };
    const curRet = await models_1.CloudGameYuanbaoChangeLogModel.insert(logProps);
    logger.info({ logProps, curRet: curRet }, "SaveCloudGameDurationChargeYuanbaoRecord");
    // incr current month yuanbao chargeNum
    const incrDuration = await (0, service_1.saveMonthYuanbaoInfo)(urs, month, chargeNum);
    const yuanbao = await (0, service_1.getMonthYuanbao)(urs, month);
    if (incrDuration > 0) {
        await (0, service_1.incrUserPermanentDuration)(account, curDate, incrDuration, 4 /* EChangeReasonType.BY_CHARGE */);
        const data = {
            urs: account.urs,
            ds: account.ds,
            dailyDuration: account.dailyDuration,
            permanentDuration: account.duration + incrDuration,
            isChange: true,
            dailyDurationDelta: 0,
            permanentDurationDelta: incrDuration,
            yuanbao: yuanbao,
        };
        await (0, service_1.freeModifyLock)(urs);
        return data;
    }
    else {
        const data = {
            urs: account.urs,
            ds: account.ds,
            dailyDuration: account.dailyDuration,
            permanentDuration: account.duration,
            isChange: false,
            dailyDurationDelta: 0,
            permanentDurationDelta: 0,
            yuanbao: yuanbao,
        };
        await (0, service_1.freeModifyLock)(urs);
        return data;
    }
}
/** 领取每日登录奖励时长 */
async function cloudGameDurationReceiveDailyAward(params) {
    const { urs, ts, userType } = params;
    if (userType === "normal") {
        return autoInitNormalUserAccountWhenDailyLogin(params);
    }
    const curDate = new Date(ts > 0 ? ts * 1000 : Date.now());
    const ds = (0, service_1.getDayStr)(curDate);
    const account = await getAccountAndAutoExpire(urs, ds, userType);
    const isAwarded = await (0, service_1.checkDailyLoginAwardByDs)(urs, ds);
    if (isAwarded) {
        await (0, service_1.freeModifyLock)(urs);
        return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.LoginAlreadyThisDay);
    }
    const duration = await (0, service_1.saveDailyLoginAwardChangeLog)(urs, ds);
    if (account && account.urs) {
        return (0, service_1.receiveDailyAwardWhenAccountExist)(account, curDate, duration, userType);
    }
    else {
        return (0, service_1.receiveDailyAwardWhenAccountNotExist)(urs, curDate, duration);
    }
}
/**
 * 游戏程序利用这个每日登录领取奖励来对普通用户初始化, 目前需要适配这周形式
 */
async function autoInitNormalUserAccountWhenDailyLogin(params) {
    const { urs, ts } = params;
    const initAccountKey = "cloud_game_duration:urs_init_account:" + urs;
    const ownerId = (0, nanoid_1.nanoid)();
    try {
        const hasLock = await cacheUtil_1.RedisLock.optimistic(initAccountKey, ownerId, 10000, 10, 50);
        if (!hasLock) {
            return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.OperationTooFrequency);
        }
        const account = await models_1.CloudGameDurationAccountModel.findOne({ urs });
        const curDate = new Date(ts > 0 ? ts * 1000 : Date.now());
        const ds = (0, service_1.getDayStr)(curDate);
        const now = curDate.getTime();
        if (account && account.urs) {
            const data = {
                urs,
                ds: account.ds,
                dailyDuration: 0,
                permanentDuration: account.duration,
                isChange: false,
                dailyDurationDelta: 0,
                permanentDurationDelta: 0,
            };
            await (0, service_1.freeModifyLock)(urs);
            return data;
        }
        else {
            const initAccount = {
                urs,
                duration: 0,
                dailyDuration: 0,
                ds: ds,
                createTime: now,
                updateTime: now,
            };
            const insertRet = await models_1.CloudGameDurationAccountModel.insert(initAccount);
            logger.info({ initAccount, insertRet }, "InitAccountForNormalUserOK");
            const data = {
                urs,
                ds,
                dailyDuration: 0,
                permanentDuration: 0,
                isChange: false,
                dailyDurationDelta: 0,
                permanentDurationDelta: 0,
            };
            await (0, service_1.freeModifyLock)(urs);
            return data;
        }
    }
    finally {
        cacheUtil_1.RedisLock.unLock(initAccountKey, ownerId);
    }
}
/** 新增云游戏时长 */
async function cloudGameDurationIncr(params) {
    const { urs, type, duration, ts, userType } = params;
    const curDate = new Date(ts > 0 ? ts * 1000 : Date.now());
    const account = await checkUrsAccountInitAndAutoExpire(urs, curDate, userType);
    if (type === "daily") {
        return (0, service_1.incrUserDailyDuration)(account, curDate, duration);
    }
    else if (type === "permanent") {
        return (0, service_1.incrUserPermanentDuration)(account, curDate, duration, 1 /* EChangeReasonType.BY_API */);
    }
    else {
        // should not go here
        logger.error({ params }, "CloudGameDurationPassNewType");
        return null;
    }
}
/** 扣除云游戏时长 */
async function cloudGameDurationDecr(params) {
    const { urs, type, duration, ts, userType } = params;
    const curDate = new Date(ts > 0 ? ts * 1000 : Date.now());
    const account = await checkUrsAccountInitAndAutoExpire(urs, curDate, userType);
    if (type === 1 /* EDescType.ONLY_DAILY */) {
        return (0, service_1.decrUserDurationOnlyDaily)(account, curDate, duration, 1 /* EChangeReasonType.BY_API */);
    }
    else if (type === 2 /* EDescType.PREFER_DAILY */) {
        return (0, service_1.decrUserDurationPreferDaily)(account, curDate, duration);
    }
    else {
        // should not go here
        logger.error({ params }, "CloudGameDurationDecrUnknownType");
        return null;
    }
}
/** 游戏通知购买月卡信息 */
async function cloudGameDurationNotifyBuyMonthCard(params) {
    const { urs, serverId, duration, buyTime, orderId } = params;
    const curTime = Math.ceil(Date.now() / 1000);
    const lockKey = (0, util_1.cacheKeyGen)("cloud_game_month_card", { urs, serverId });
    const ownerId = (0, nanoid_1.nanoid)();
    const hasLock = await cacheUtil_1.RedisLock.optimistic(lockKey, ownerId, 10000, 10, 50);
    logger.debug({ hasLock }, "cloudGameDurationNotifyBuyMonthCard");
    if (!hasLock) {
        return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.OperationTooFrequency);
    }
    if (Math.abs(curTime - buyTime) >= 180) {
        logger.warn(params, "cloudGameDurationNotifyBuyMonthCardTimeNotNear");
    }
    const data = {
        urs,
        serverId,
        status: 0,
        expireAt: 0,
    };
    const now = Date.now();
    const chargeRecord = await monthCardCharge_1.MonthCardChargeModel.findOne({ orderId, channel: "game" });
    if (chargeRecord && chargeRecord.id) {
        await cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
        return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.MonthCardOrderConsumed);
    }
    const props = {
        urs: params.urs,
        duration,
        createTime: now,
        orderId,
        serverId,
        buyTime,
        channel: "game",
    };
    let id = 0;
    try {
        id = await monthCardCharge_1.MonthCardChargeModel.insert(props);
        logger.info({ props, id }, "saveMonthCardCharge");
    }
    catch (err) {
        logger.error({ err, props }, "saveMonthCardChargeFailed");
        throw err;
    }
    const curAccount = await monthCardAccount_1.MonthCardAccountModel.findOne({ urs, serverId });
    if (curAccount && curAccount.id) {
        if (curAccount.expireAt * 1000 >= now) {
            // 目前月卡还未过期的情况, 直接延长过期时间
            const expireAt = curAccount.expireAt + duration;
            const upProps = {
                updateTime: now,
                expireAt,
            };
            const upRet = await monthCardAccount_1.MonthCardAccountModel.updateById(curAccount.id, upProps);
            const cgProps = {
                urs: urs,
                createTime: now,
                serverId: serverId,
                reason: 1 /* MonthCardChangeReason.CHARGE */,
                duration: duration,
                payload: JSON.stringify({ id: id, msg: "extendMontCard" }),
            };
            await monthCardChangeLog_1.MonthCardChangeLogModel.insert(cgProps);
            logger.info({ urs, serverId, upProps, upRet }, "extendMonthCard");
            data.expireAt = expireAt;
            data.status = monthCardAccount_1.MonthCardAccountModel.getMonthCardStatus(expireAt);
        }
        else {
            // 月卡已经过期， 重新计算过期时间
            const expireAt = params.buyTime + params.duration;
            const upProps = {
                updateTime: now,
                expireAt,
            };
            const upRet = await monthCardAccount_1.MonthCardAccountModel.updateById(curAccount.id, upProps);
            const cgProps = {
                urs: urs,
                createTime: now,
                serverId: serverId,
                reason: 1 /* MonthCardChangeReason.CHARGE */,
                duration: duration,
                payload: JSON.stringify({ id: id, msg: "resetExpireTime" }),
            };
            await monthCardChangeLog_1.MonthCardChangeLogModel.insert(cgProps);
            logger.info({ urs, serverId, upProps, upRet }, "resetExpireTime");
            data.expireAt = expireAt;
            data.status = monthCardAccount_1.MonthCardAccountModel.getMonthCardStatus(expireAt);
        }
    }
    else {
        const expireAt = params.buyTime + params.duration;
        const props = {
            urs: urs,
            createTime: now,
            updateTime: now,
            serverId,
            expireAt,
        };
        const id = await monthCardAccount_1.MonthCardAccountModel.insert(props);
        data.expireAt = expireAt;
        data.status = monthCardAccount_1.MonthCardAccountModel.getMonthCardStatus(expireAt);
        const cgProps = {
            urs: urs,
            createTime: now,
            serverId: serverId,
            reason: 1 /* MonthCardChangeReason.CHARGE */,
            duration: duration,
            payload: JSON.stringify({ id: id, msg: "initMonthCardAccount" }),
        };
        await monthCardChangeLog_1.MonthCardChangeLogModel.insert(cgProps);
        logger.info({ props, id }, "initMonthCardAccount");
    }
    await cacheUtil_1.RedisLock.unLock(lockKey, ownerId);
    return data;
}
/** 展示当前urs账号服务器下月卡信息 (ip白名单授权) */
async function cloudGameDurationMonthCardShow(params) {
    const monthCard = await monthCardAccount_1.MonthCardAccountModel.getStatus(params.urs, params.serverId);
    const data = monthCard;
    return data;
}
//# sourceMappingURL=operation.js.map