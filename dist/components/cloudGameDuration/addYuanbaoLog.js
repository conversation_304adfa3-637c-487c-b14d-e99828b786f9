"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCloudGameAddYuanbao = exports.isAddYuanbaoLog = exports.parseAddYuanbaoLog = exports.parseGameRawLog = exports.ADD_YUANBAO_EVENT_LOG_ID = void 0;
const logger_1 = require("../../logger");
exports.ADD_YUANBAO_EVENT_LOG_ID = 404135;
const logger = (0, logger_1.clazzLogger)("cloudGameDuration.parseAddYuanbaoLog");
const GameRawLogRegex = /[(?<time>\d+-\d+-\d+\s\d+:\d+:\d+)]*\s\S+[\d+\]:\s*[[0-9a-zA-Z ]+]\w+\|\w+\|\[(?<eventId>\d+)](?<eventArgs>.*$)/;
function parseGameRawLog(log) {
    const ret = { eventId: 0, payload: "", args: [] };
    if (!log)
        return;
    const match = log.match(GameRawLogRegex);
    if (match && match.groups) {
        ret.eventId = parseInt(match.groups.eventId);
        ret.payload = match.groups.eventArgs;
        const args = ret.payload.split(",,");
        ret.args = args;
    }
    else {
        logger.warn({ log }, "ParseGameRawLogFailed");
    }
    return ret;
}
exports.parseGameRawLog = parseGameRawLog;
// 2024-10-21 15:51:02 gas[36576]:[Development Build]INFO|PLAYER|[404135]***********,,<EMAIL>,,1000,,***********z1e0f3,,false
function parseAddYuanbaoLog(gs) {
    const ret = { isOk: false, data: null };
    if (isAddYuanbaoLog(gs)) {
        ret.isOk = true;
        const playerIdStr = gs.args[0];
        const playerId = parseInt(playerIdStr, 10);
        const accountName = gs.args[1];
        const yuanbaoStr = gs.args[2];
        const sn = gs.args[3];
        const yuanbao = parseInt(yuanbaoStr, 10);
        const bCloud = gs.args[4] === "true";
        ret.data = {
            playerId,
            accountName,
            yuanbao,
            sn,
            bCloud,
        };
        return ret;
    }
    else {
        logger.warn({ gs }, "ParseAddYuanbaoLogFailed");
        return ret;
    }
}
exports.parseAddYuanbaoLog = parseAddYuanbaoLog;
function isAddYuanbaoLog(log) {
    return log.eventId === exports.ADD_YUANBAO_EVENT_LOG_ID;
}
exports.isAddYuanbaoLog = isAddYuanbaoLog;
function onCloudGameAddYuanbao() { }
exports.onCloudGameAddYuanbao = onCloudGameAddYuanbao;
//# sourceMappingURL=addYuanbaoLog.js.map