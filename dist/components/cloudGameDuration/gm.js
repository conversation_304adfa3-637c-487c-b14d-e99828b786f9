"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const util_1 = require("../../common/util");
const logger_1 = require("../../logger");
const operation_1 = require("./operation");
const logger = (0, logger_1.clazzLogger)("cloudGameDuration/gm");
(0, util_1.callOperation)(operation_1.cloudGameDurationNotifyChargeYuanbao, "cloudGameDurationNotifyChargeYuanbao", logger)({ urs: "<EMAIL>", num: 3000, orderId: "40588400305z5e", userType: "normal", ts: 1647266382 });
//# sourceMappingURL=gm.js.map