"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperationLockTime = exports.CLOUD_DAILY_DURATION_DAY_LIST = exports.CLOUD_DAILY_DURATION_PER_DAY = exports.KEEP_CHARGE_AWARD_TIME = exports.CHARGE_YUANBAO_MAX_DURATION_PER_MONTH = exports.CHARGE_DURATION_PER_YUANBAO = exports.ONLINE_LAUNCH_BEGIN_TIME = exports.FIRST_LOGIN_AFTER_LAUNCH_DURATION = void 0;
/** 每个账号初始拥有12点【永久云游戏时长】*/
exports.FIRST_LOGIN_AFTER_LAUNCH_DURATION = 12;
/**  这个日期之后第一次登录会送永久时长 */
exports.ONLINE_LAUNCH_BEGIN_TIME = new Date('2022-01-20 09:00:00');
/** 每充值10元宝，获得1点永久时长(点数) */
exports.CHARGE_DURATION_PER_YUANBAO = 1;
/** 每月通过此方式获得的时长最多为1080小时（64800分钟） */
exports.CHARGE_YUANBAO_MAX_DURATION_PER_MONTH = 6480;
/* 每月账号累计充值达到以下金额时，额外赠送【永久时长】 */
exports.KEEP_CHARGE_AWARD_TIME = [
    { reach: 100, duration: 18 },
    { reach: 300, duration: 30 },
    { reach: 1000, duration: 48 },
    { reach: 3000, duration: 72 }
];
/** 没有在“指定日期的初始当日时长”表内配置的日期，初始当日云游戏时间默认为此值（单位：点数） */
exports.CLOUD_DAILY_DURATION_PER_DAY = 3;
exports.CLOUD_DAILY_DURATION_DAY_LIST = [
    { ds: "2022-01-13", duration: 12 },
    { ds: "2022-01-14", duration: 12 },
    { ds: "2022-01-15", duration: 12 },
    { ds: "2022-01-16", duration: 12 },
    { ds: "2022-01-17", duration: 12 },
    { ds: "2022-01-18", duration: 12 },
    { ds: "2022-01-19", duration: 12 },
    { ds: "2022-01-20", duration: 12 },
    { ds: "2022-01-21", duration: 12 },
    { ds: "2022-01-22", duration: 12 },
    { ds: "2022-01-23", duration: 12 },
    { ds: "2022-01-24", duration: 12 },
    { ds: "2022-01-25", duration: 12 },
    { ds: "2022-01-26", duration: 12 },
    { ds: "2022-01-27", duration: 12 },
    { ds: "2022-01-28", duration: 12 },
    { ds: "2022-01-29", duration: 12 },
    { ds: "2022-01-30", duration: 12 },
    { ds: "2022-01-31", duration: 12 },
    { ds: "2022-02-01", duration: 24 },
    { ds: "2022-02-02", duration: 12 },
    { ds: "2022-02-03", duration: 12 },
    { ds: "2022-02-04", duration: 12 },
    { ds: "2022-02-05", duration: 12 },
    { ds: "2022-02-06", duration: 12 },
    { ds: "2022-02-07", duration: 12 },
    { ds: "2022-02-08", duration: 12 },
    { ds: "2022-02-09", duration: 12 },
    { ds: "2022-02-10", duration: 12 },
    { ds: "2022-02-11", duration: 12 },
    { ds: "2022-02-12", duration: 12 },
    { ds: "2022-02-13", duration: 12 },
    { ds: "2022-02-14", duration: 12 },
    { ds: "2022-02-15", duration: 24 },
    { ds: "2022-02-16", duration: 12 }
];
exports.OperationLockTime = 5000; // 5s
//# sourceMappingURL=constant.js.map