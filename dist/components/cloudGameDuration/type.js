"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    CloudGameDurationShow: {
        urs: { type: String },
        serverId: { type: Number, required: false },
        userType: { type: String, required: false, default: "normal", values: ["normal", "cloud"] },
        ts: { type: Number, required: false },
    },
    CloudGameDurationMonthCardShow: {
        urs: { type: String },
        serverId: { type: Number },
    },
    CloudGameDurationNotifyChargeYuanbao: {
        urs: { type: String },
        userType: { type: String, required: false, default: "normal", values: ["normal", "cloud"] },
        orderId: { type: String },
        num: { type: Number },
        ts: { type: Number, required: false },
    },
    CloudGameDurationNotifyBuyMonthCard: {
        urs: { type: String },
        serverId: { type: Number },
        orderId: { type: String },
        buyTime: { type: Number },
        duration: { type: Number },
    },
    CloudGameDurationReceiveDailyAward: {
        urs: { type: String },
        userType: { type: String, required: false, default: "normal", values: ["normal", "cloud"] },
        ts: { type: Number, required: false },
    },
    CloudGameDurationIncr: {
        urs: { type: String },
        userType: { type: String, required: false, default: "normal", values: ["normal", "cloud"] },
        type: { type: String, values: ["daily", "permanent"] },
        duration: { type: Number },
        ts: { type: Number, required: false },
    },
    CloudGameDurationDecr: {
        urs: { type: String },
        userType: { type: String, required: false, default: "normal", values: ["normal", "cloud"] },
        type: { type: Number, values: [1, 2] },
        duration: { type: Number },
        ts: { type: Number, required: false },
    },
};
//# sourceMappingURL=type.js.map