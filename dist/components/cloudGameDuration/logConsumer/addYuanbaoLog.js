"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ADD_YUANBAO_EVENT_LOG_ID = void 0;
exports.parseAddYuanbaoLog = parseAddYuanbaoLog;
exports.isAddYuanbaoLog = isAddYuanbaoLog;
exports.onCloudGameAddYuanbaoLog = onCloudGameAddYuanbaoLog;
const logger_1 = require("../../../logger");
const operation_1 = require("../operation");
exports.ADD_YUANBAO_EVENT_LOG_ID = 404135;
const logger = (0, logger_1.clazzLogger)("cloudGameDuration.parseAddYuanbaoLog");
// 2024-10-21 15:51:02 gas[36576]:[Development Build]INFO|PLAYER|[404135]***********,,<EMAIL>,,1000,,***********z1e0f3,,false
// e.<PERSON>Id, e.<PERSON><PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON>, e.Sn, e.bCloud	玩家ID，玩家URS，元宝充值数量，订单号，是否云游戏
function parseAddYuanbaoLog(gs) {
    const ret = { isOk: false, data: null };
    if (isAddYuanbaoLog(gs)) {
        ret.isOk = true;
        const playerIdStr = gs.args[0];
        const playerId = parseInt(playerIdStr, 10);
        const accountName = gs.args[1];
        const yuanbaoStr = gs.args[2];
        const sn = gs.args[3];
        const yuanbao = parseInt(yuanbaoStr, 10);
        const bCloud = gs.args[4] === "true";
        ret.data = {
            playerId,
            accountName,
            yuanbao,
            sn,
            bCloud,
        };
        return ret;
    }
    else {
        logger.warn({ gs }, "ParseAddYuanbaoLogFailed");
        return ret;
    }
}
function isAddYuanbaoLog(log) {
    return log.eventId === exports.ADD_YUANBAO_EVENT_LOG_ID;
}
async function onCloudGameAddYuanbaoLog(gs) {
    const parseRet = parseAddYuanbaoLog(gs);
    if (parseRet.isOk) {
        const pd = parseRet.data;
        try {
            const data = await (0, operation_1.cloudGameDurationNotifyChargeYuanbao)({
                urs: pd.accountName,
                orderId: pd.sn,
                num: pd.yuanbao,
                userType: pd.bCloud ? "cloud" : "normal",
            });
            logger.info({ gs, data }, "onCloudGameAddYuanbaoOk");
            return data;
        }
        catch (err) {
            if (err && err?.code < 0 && err.msg) {
                logger.warn({ err, pd }, "onCloudGameAddYuanbaoWarn");
            }
            else {
                logger.error({ err, pd }, "onCloudGameAddYuanbaoError");
            }
        }
    }
    else {
        logger.warn({ gs }, "ParseAddYuanbaoLogFailed");
        return null;
    }
}
//# sourceMappingURL=addYuanbaoLog.js.map