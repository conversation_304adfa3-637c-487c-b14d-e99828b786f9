"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameRawLogRegex = void 0;
exports.toBunyanLogLevel = toBunyanLogLevel;
exports.parseGameRawLog = parseGameRawLog;
const kafkajs_1 = require("kafkajs");
const logger_1 = require("../../../logger");
const logger = (0, logger_1.clazzLogger)("logConsumer.util");
exports.GameRawLogRegex = /\[*(?<time>\d+-\d+-\d+\s\d+:\d+:\d+)]*\s\S+[\d+\]:\s*[[0-9a-zA-Z ]+]\w+\|\w+\|\[(?<eventId>\d+)](?<eventArgs>.*$)/m;
function toBunyanLogLevel(level) {
    switch (level) {
        case kafkajs_1.logLevel.ERROR:
            return "error";
        case kafkajs_1.logLevel.NOTHING:
            return "trace";
        case kafkajs_1.logLevel.WARN:
            return "warn";
        case kafkajs_1.logLevel.INFO:
            return "info";
        case kafkajs_1.logLevel.DEBUG:
            return "debug";
    }
}
function parseGameRawLog(log) {
    const ret = { eventId: 0, payload: "", args: [], eventTime: new Date() };
    if (!log)
        return;
    const match = log.match(exports.GameRawLogRegex);
    if (match && match.groups) {
        ret.eventId = parseInt(match.groups.eventId);
        ret.payload = match.groups.eventArgs;
        ret.eventTime = new Date(match.groups.time);
        const args = ret.payload.split(",,");
        ret.args = args;
    }
    else {
        logger.warn({ log }, "ParseGameRawLogFailed");
    }
    return ret;
}
//# sourceMappingURL=util.js.map