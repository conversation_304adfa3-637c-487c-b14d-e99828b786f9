"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConsumer = getConsumer;
exports.listenKafkaTopic = listenKafkaTopic;
exports.processKafkaMessage = processKafkaMessage;
exports.transformKafkaMessage = transformKafkaMessage;
const kafkajs_1 = require("kafkajs");
const logger_1 = require("../../../common/logger");
const util_1 = require("./util");
const logger_2 = require("../../../logger");
const bluebird = require("bluebird");
const config_1 = require("../../../common/config");
const addYuanbaoLog_1 = require("./addYuanbaoLog");
const addMonthCardLog_1 = require("./addMonthCardLog");
const logger = (0, logger_2.clazzLogger)("logConsumer.consumer");
const kafkaCfg = config_1.cloudGameDurationCfg.kafkaCfg;
let kafka = null;
let consumer = null;
const bunyanLoggerCreator = (level) => {
    const logger = (0, logger_1.getLogger)("kafka_consumer", (0, util_1.toBunyanLogLevel)(level));
    logger.info("initKafkaLog");
    return ({ namespace, level, label, log }) => {
        const { message, ...extra } = log;
        if (level === kafkajs_1.logLevel.ERROR) {
            logger.warn({ label, extra, namespace }, message);
        }
        else {
            logger.info({ label, extra, namespace }, message);
        }
    };
};
function createKafkaClient() {
    const clientOption = {
        clientId: kafkaCfg.clientId,
        brokers: kafkaCfg.brokers,
        logLevel: kafkajs_1.logLevel[kafkaCfg.logLevel],
        logCreator: bunyanLoggerCreator,
        requestTimeout: 60000,
        retry: {
            initialRetryTime: 1000,
            retries: 10 // KafkaJS 默认会对超时等可重试错误进行重试
        },
    };
    if (kafkaCfg.sasl && kafkaCfg.sasl.username && kafkaCfg.sasl.password) {
        clientOption.sasl = kafkaCfg.sasl;
    }
    const kafka = new kafkajs_1.Kafka(clientOption);
    return kafka;
}
function getKafkaClient() {
    if (kafka === null) {
        kafka = createKafkaClient();
    }
    return kafka;
}
function getConsumer() {
    if (consumer === null) {
        consumer = createConsumer();
    }
    return consumer;
}
function createConsumer() {
    const consumer = getKafkaClient().consumer({ groupId: kafkaCfg.groupId });
    return consumer;
}
async function listenKafkaTopic(consumer) {
    logger.info("start listenKafkaTopic");
    try {
        await consumer.connect();
        await consumer.subscribe({ topic: kafkaCfg.topic, fromBeginning: kafkaCfg.fromBeginning });
        await consumer.run({
            partitionsConsumedConcurrently: kafkaCfg.partitionsConsumedConcurrently,
            eachBatch: async (eachBatchPayload) => {
                const { batch } = eachBatchPayload;
                await bluebird.map(batch.messages, async (message) => {
                    return processKafkaMessage(consumer, batch.topic, batch.partition, message);
                }, { concurrency: kafkaCfg.taskConcurrency });
                return;
            },
        });
    }
    catch (err) {
        consumer.logger().error("ListenKafkaTopicFail", { err });
        return null;
    }
}
async function processKafkaMessage(consumer, topic, partition, message) {
    try {
        if (!message.value) {
            return;
        }
        const logMessage = message.value.toString();
        const logItem = await transformKafkaMessage(consumer, logMessage);
        await persistLogMessage(consumer, logItem);
    }
    catch (err) {
        consumer.logger().error("ProcessKafkaMessageFail", { err, topic, partition, message });
    }
}
async function persistLogMessage(consumer, logItem) {
    if (!logItem)
        return;
    try {
        switch (logItem.eventId) {
            case 404135 /* LogEventId.CloudGameAddYuanbao */:
                await (0, addYuanbaoLog_1.onCloudGameAddYuanbaoLog)(logItem);
                break;
            case 404353 /* LogEventId.CloudGameBuyMonthCard */:
                await (0, addMonthCardLog_1.onCloudGameAddBuyMonthCardLog)(logItem);
                break;
            default:
                consumer.logger().debug("UnknownEventId", { eventId: logItem.eventId, payload: logItem.payload });
                break;
        }
    }
    catch (err) {
        consumer.logger().error("PersistLogMessageFail", { err, logItem });
    }
}
async function transformKafkaMessage(consumer, message) {
    const logItem = (0, util_1.parseGameRawLog)(message);
    if (logItem && logItem.eventId) {
        consumer.logger().debug("TransformKafkaMessageOK", { logItem });
        return logItem;
    }
    else {
        consumer.logger().warn("TransformKafkaMessageFail", { logItem });
        return null;
    }
}
//# sourceMappingURL=logConsumer.js.map