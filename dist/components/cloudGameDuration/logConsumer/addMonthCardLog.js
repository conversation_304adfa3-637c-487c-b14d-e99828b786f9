"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseAddMonthCardLog = parseAddMonthCardLog;
exports.isAddMonthCardLog = isAddMonthCardLog;
exports.onCloudGameAddBuyMonthCardLog = onCloudGameAddBuyMonthCardLog;
const constants_1 = require("../../../common/constants");
const logger_1 = require("../../../logger");
const operation_1 = require("../operation");
const logger = (0, logger_1.clazzLogger)("cloudGameDuration.parseAddMonthCardLog");
// Log Format Example from doc:
// 2025-02-13 14:14:29 gas[28056]:[2067273]INFO|PLAYER|[404353]#<EMAIL>,,101601001,,30,,01000000E903013000010000C58DAD67,219
// [404353]#urs,,用户名（可能为0）,,增加天数,,OrderSN,,服务器ID
/**
 * Parses the game log structure for adding a month card.
 * @param gs The game log structure.
 * @returns A ParseLogRet object containing the parsed data or indicating failure.
 */
function parseAddMonthCardLog(gs) {
    const ret = { isOk: false, data: null };
    if (isAddMonthCardLog(gs)) {
        if (gs.args.length < 5) {
            logger.warn({ gs }, "ParseAddMonthCardLogFailed_InvalidArgsLength");
            return ret;
        }
        const ursWithPrefix = gs.args[0];
        // args[1] is username, skip it.
        const daysAddedStr = gs.args[2];
        const sn = gs.args[3];
        const serverIdStr = gs.args[4];
        const serverId = parseInt(serverIdStr, 10);
        if (!ursWithPrefix || !ursWithPrefix.startsWith("#")) {
            logger.warn({ gs }, "ParseAddMonthCardLogFailed_InvalidUrsPrefix");
            return ret;
        }
        const urs = ursWithPrefix.substring(1);
        const daysAdded = parseInt(daysAddedStr, 10);
        if (isNaN(daysAdded)) {
            logger.warn({ gs }, "ParseAddMonthCardLogFailed_InvalidDaysAdded");
            return ret;
        }
        const buyTime = Math.floor(gs.eventTime.getTime() / 1000);
        ret.isOk = true;
        ret.data = {
            urs,
            daysAdded,
            sn,
            serverId,
            buyTime,
        };
        return ret;
    }
    else {
        logger.warn({ gs }, "ParseAddMonthCardLogFailed_NotMonthCardEvent");
        return ret;
    }
}
/**
 * Checks if the game log structure corresponds to an add month card event.
 * @param log The game log structure.
 * @returns True if it is an add month card log, false otherwise.
 */
function isAddMonthCardLog(log) {
    return log.eventId === 404353 /* LogEventId.CloudGameBuyMonthCard */;
}
/**
 * Handles the cloud game add month card log event.
 * Parses the log and calls the notification service.
 * @param gs The game log structure.
 * @returns The result from the notification service, or null if parsing/processing fails.
 */
async function onCloudGameAddBuyMonthCardLog(gs) {
    const parseRet = parseAddMonthCardLog(gs);
    if (parseRet.isOk) {
        const pd = parseRet.data;
        try {
            const duration = pd.daysAdded * constants_1.ONE_DAY_SECONDS;
            const data = await (0, operation_1.cloudGameDurationNotifyBuyMonthCard)({
                urs: pd.urs,
                orderId: pd.sn,
                duration,
                serverId: pd.serverId,
                channel: "game",
                buyTime: pd.buyTime,
            });
            logger.info({ gs, parsedData: pd, serviceResult: data }, "onCloudGameAddMonthCardLogOk");
            return data;
        }
        catch (err) {
            if (err && typeof err === 'object' && 'code' in err && typeof err.code === 'number' && err.code < 0 && 'msg' in err) {
                logger.warn({ err, parsedData: pd }, "onCloudGameAddMonthCardLogWarn_ServiceError");
            }
            else {
                logger.error({ err, parsedData: pd }, "onCloudGameAddMonthCardLogError_Unexpected");
            }
            return null;
        }
    }
    else {
        logger.warn({ gs }, "onCloudGameAddMonthCardLogSkipped_ParseFailed");
        return null;
    }
}
//# sourceMappingURL=addMonthCardLog.js.map