"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudGameDurationComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/cloud_game_duration/show",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationShow,
        operation: operation_1.cloudGameDurationShow,
    },
    {
        method: "get",
        url: "/cloud_game_duration/month_card/show",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationMonthCardShow,
        operation: operation_1.cloudGameDurationMonthCardShow,
    },
    {
        method: "post",
        url: "/cloud_game_duration/notify_charge_yuanbao",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationNotifyChargeYuanbao,
        operation: operation_1.cloudGameDurationNotifyChargeYuanbaoWrap,
    },
    {
        method: "post",
        url: "/cloud_game_duration/notify_buy_month_card",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationNotifyBuyMonthCard,
        operation: operation_1.cloudGameDurationNotifyBuyMonthCard,
    },
    {
        method: "post",
        url: "/cloud_game_duration/receive_daily_award",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationReceiveDailyAward,
        operation: operation_1.cloudGameDurationReceiveDailyAward,
    },
    {
        method: "post",
        url: "/cloud_game_duration/incr",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationIncr,
        operation: operation_1.cloudGameDurationIncr,
    },
    {
        method: "post",
        url: "/cloud_game_duration/decr",
        paramsSchema: type_1.ReqSchemas.CloudGameDurationDecr,
        operation: operation_1.cloudGameDurationDecr,
    },
];
exports.CloudGameDurationComponent = {
    paths: exports.paths,
    prefix: "/cloud_game_duration/",
};
//# sourceMappingURL=index.js.map