"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDayStr = getDayStr;
exports.getMonthStr = getMonthStr;
exports.saveDailyLoginAwardChangeLog = saveDailyLoginAwardChangeLog;
exports.checkDailyLoginAwardByDs = checkDailyLoginAwardByDs;
exports.markDailyLoginAwardToday = markDailyLoginAwardToday;
exports.getMonthYuanbao = getMonthYuanbao;
exports.receiveDailyAwardWhenAccountExist = receiveDailyAwardWhenAccountExist;
exports.incrUserDailyDuration = incrUserDailyDuration;
exports.receiveDailyAwardWhenAccountNotExist = receiveDailyAwardWhenAccountNotExist;
exports.initAccount = initAccount;
exports.deleteAccount = deleteAccount;
exports.incrUserPermanentDuration = incrUserPermanentDuration;
exports.addPermanentDurationChangeLog = addPermanentDurationChangeLog;
exports.addDailyDurationChangeLog = addDailyDurationChangeLog;
exports.decrUserDurationOnlyDaily = decrUserDurationOnlyDaily;
exports.decrUserDurationPreferDaily = decrUserDurationPreferDaily;
exports.saveMonthYuanbaoInfo = saveMonthYuanbaoInfo;
exports.dailyDurationExpire = dailyDurationExpire;
exports.checkModifyFreq = checkModifyFreq;
exports.getModifyLock = getModifyLock;
exports.freeModifyLock = freeModifyLock;
const _ = require("lodash");
const cacheUtil_1 = require("../../common/cacheUtil");
const dateUtil_1 = require("../../common/dateUtil");
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const helper_1 = require("../../helper");
const logger_1 = require("../../logger");
const models_1 = require("../../models");
const constant_1 = require("./constant");
const config_1 = require("../../common/config");
const logger = (0, logger_1.clazzLogger)("cloudGameDuration/operation");
function getDayStr(date) {
    return (0, util_1.formatDate)(date.getTime(), "yyyy-MM-dd");
}
function getMonthStr(date) {
    return (0, util_1.formatDate)(date.getTime(), "yyyy-MM");
}
function getDailyDurationByDateStr(ds) {
    const r = constant_1.CLOUD_DAILY_DURATION_DAY_LIST.find((r) => r.ds === ds) || { ds, duration: constant_1.CLOUD_DAILY_DURATION_PER_DAY };
    return r.duration;
}
async function saveDailyLoginAwardChangeLog(urs, ds) {
    const duration = getDailyDurationByDateStr(ds);
    const markRet = await markDailyLoginAwardToday(urs, ds);
    logger.info({ urs, ds, markRet }, "saveDailyLoginAwardChangeLogOk");
    return duration;
}
async function checkDailyLoginAwardByDs(urs, ds) {
    const isLogin = await models_1.CloudGameDailyLoginModel.exists({ urs, ds });
    return isLogin;
}
async function markDailyLoginAwardToday(urs, ds) {
    const record = {
        urs,
        ds,
        createTime: Date.now(),
    };
    const insertRet = await models_1.CloudGameDailyLoginModel.insert(record);
    logger.info({ record, insertRet }, "markDailyLoginAwardTodayOk");
    return record;
}
function getAwardDurationByYuanbaoNum(chargeNum) {
    const directDuration = Math.floor(chargeNum / 10) * constant_1.CHARGE_DURATION_PER_YUANBAO;
    const keepChargeDuration = _.sumBy(constant_1.KEEP_CHARGE_AWARD_TIME.filter((r) => r.reach <= chargeNum), (e) => e.duration);
    return Math.min(directDuration + keepChargeDuration, constant_1.CHARGE_YUANBAO_MAX_DURATION_PER_MONTH);
}
async function getMonthYuanbao(urs, month) {
    const monthInfo = await models_1.CloudGameYuanbaoMonthModel.findOne({ month, urs });
    if (monthInfo) {
        const data = {
            month,
            chargeNum: monthInfo.chargeNum,
            awardDuration: getAwardDurationByYuanbaoNum(monthInfo.chargeNum),
        };
        return data;
    }
    else {
        const data = {
            month,
            chargeNum: 0,
            awardDuration: 0,
        };
        return data;
    }
}
async function receiveDailyAwardWhenAccountExist(account, curDate, incrDuration, userType) {
    const { urs } = account;
    if (userType === "cloud") {
        const ret = await incrUserDailyDuration(account, curDate, incrDuration);
        const isAfterOnlineLaunch = (0, dateUtil_1.isAfter)(curDate, constant_1.ONLINE_LAUNCH_BEGIN_TIME);
        if (isAfterOnlineLaunch) {
            const firstLoginAward = await models_1.CloudGameDurationChangeLogModel.findOne({
                urs,
                reason: 2 /* EChangeReasonType.FIRST_LOGIN_AFTER_LAUNCH */,
            });
            if (!firstLoginAward) {
                const ret2 = await incrUserPermanentDuration(account, curDate, constant_1.FIRST_LOGIN_AFTER_LAUNCH_DURATION, 2 /* EChangeReasonType.FIRST_LOGIN_AFTER_LAUNCH */);
                ret.permanentDuration = ret2.permanentDuration;
                ret.permanentDurationDelta = ret2.permanentDurationDelta;
            }
        }
        await freeModifyLock(urs);
        return ret;
    }
    else {
        // 非云用户无非处理当日奖励
        await freeModifyLock(urs);
        const data = {
            urs,
            ds: account.ds,
            dailyDuration: account.dailyDuration,
            permanentDuration: account.duration,
            isChange: false,
            dailyDurationDelta: 0,
            permanentDurationDelta: 0,
        };
        return data;
    }
}
async function incrUserDailyDuration(account, curDate, incrDuration) {
    const urs = account.urs;
    const ds = getDayStr(curDate);
    const query = models_1.CloudGameDurationAccountModel.scope()
        .where("urs", urs)
        .where("ds", ds)
        .increment("dailyDuration", incrDuration);
    const incrRet = await models_1.CloudGameDurationAccountModel.executeByQuery(query);
    if (incrRet.affectedRows !== 1) {
        logger.warn({ account, incrRet }, "IncrAccountDailyDurationFailed");
        await freeModifyLock(urs);
        return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.ReceiveDailyAwardFailed);
    }
    else {
        await addDailyDurationChangeLog({
            urs,
            ds,
            duration: incrDuration,
            reason: 3 /* EChangeReasonType.DAILY_LOGIN */,
            createTime: curDate.getTime(),
        });
        const data = {
            urs,
            ds,
            dailyDuration: account.dailyDuration + incrDuration,
            permanentDuration: account.duration,
            isChange: true,
            dailyDurationDelta: incrDuration,
            permanentDurationDelta: 0,
        };
        await freeModifyLock(urs);
        return data;
    }
}
async function receiveDailyAwardWhenAccountNotExist(urs, curDate, dailyDuration) {
    const now = curDate.getTime();
    const ds = getDayStr(curDate);
    const isAfterOnlineLaunch = (0, dateUtil_1.isAfter)(curDate, constant_1.ONLINE_LAUNCH_BEGIN_TIME);
    const accountInitSeconds = isAfterOnlineLaunch ? constant_1.FIRST_LOGIN_AFTER_LAUNCH_DURATION : 0;
    await initAccount(urs, accountInitSeconds, dailyDuration, ds, now);
    if (isAfterOnlineLaunch) {
        await addFirstLoginAfterLaunchDurationLog(urs, now);
    }
    await addDailyDurationChangeLog({
        urs,
        ds,
        duration: dailyDuration,
        reason: 3 /* EChangeReasonType.DAILY_LOGIN */,
        createTime: now,
    });
    const data = {
        urs,
        ds,
        dailyDuration,
        permanentDuration: accountInitSeconds,
        isChange: true,
        dailyDurationDelta: dailyDuration,
        permanentDurationDelta: accountInitSeconds,
    };
    await freeModifyLock(urs);
    return data;
}
async function addFirstLoginAfterLaunchDurationLog(urs, now) {
    return addPermanentDurationChangeLog({
        urs,
        duration: constant_1.FIRST_LOGIN_AFTER_LAUNCH_DURATION,
        reason: 2 /* EChangeReasonType.FIRST_LOGIN_AFTER_LAUNCH */,
        createTime: now,
    });
}
async function initAccount(urs, accountInitSeconds, dailyDuration, ds, now) {
    const initAccount = {
        urs,
        duration: accountInitSeconds,
        dailyDuration,
        ds: ds,
        createTime: now,
        updateTime: now,
    };
    try {
        const insertId = await models_1.CloudGameDurationAccountModel.insert(initAccount);
        logger.info({ initAccount, insertId }, "InitLoginDurationAccountOK");
        const newRecord = Object.assign({}, initAccount, { id: insertId });
        return newRecord;
    }
    catch (err) {
        if (err && err.code === "ER_DUP_ENTRY") {
            const newRecord = await models_1.CloudGameDurationAccountModel.findOne({ urs });
            return newRecord;
        }
        else {
            throw err;
        }
    }
}
async function deleteAccount(urs) {
    const ret = await models_1.CloudGameDurationAccountModel.deleteByCondition({ urs });
    return ret;
}
async function incrUserPermanentDuration(account, curDate, incrDuration, reason) {
    const urs = account.urs;
    const ds = getDayStr(curDate);
    const query = models_1.CloudGameDurationAccountModel.scope().where("urs", urs).increment("duration", incrDuration);
    const incrRet = await models_1.CloudGameDurationAccountModel.executeByQuery(query);
    if (incrRet.affectedRows !== 1) {
        logger.warn({ account, incrRet }, "IncrAccountPermanentDurationFailed");
        await freeModifyLock(urs);
        return (0, errorCodes_1.BussError)(errorCodes_1.CloudGameDurationErrors.ReceiveDailyAwardFailed);
    }
    else {
        await addPermanentDurationChangeLog({
            urs,
            duration: incrDuration,
            reason: reason,
            createTime: curDate.getTime(),
        });
        const data = {
            urs,
            ds,
            dailyDuration: account.dailyDuration,
            permanentDuration: account.duration + incrDuration,
            isChange: true,
            dailyDurationDelta: 0,
            permanentDurationDelta: incrDuration,
        };
        await freeModifyLock(urs);
        return data;
    }
}
async function addPermanentDurationChangeLog(prop) {
    const insertId = await models_1.CloudGameDurationChangeLogModel.insert(prop);
    return insertId;
}
async function addDailyDurationChangeLog(prop) {
    const insertId = await models_1.CloudGameDailyDurationChangeLogModel.insert(prop);
    return insertId;
}
async function decrUserDurationOnlyDaily(account, curDate, duration, reason) {
    const ds = getDayStr(curDate);
    const urs = account.urs;
    const query = models_1.CloudGameDurationAccountModel.scope()
        .where("urs", urs)
        .where("ds", ds)
        .where("dailyDuration", ">=", duration)
        .decrement("dailyDuration", duration);
    const changeRet = await models_1.CloudGameDurationAccountModel.executeByQuery(query);
    if (changeRet.affectedRows !== 1) {
        logger.warn({ account, ds, changeRet }, "DecrAccountDailyDurationFailed");
        await freeModifyLock(urs);
        return createUnChangeDurationRes(account);
    }
    else {
        await addDailyDurationChangeLog({
            urs,
            ds,
            duration: -duration,
            reason,
            createTime: Date.now(),
        });
        const data = {
            urs,
            ds,
            dailyDuration: account.dailyDuration - duration,
            permanentDuration: account.duration,
            isChange: true,
            dailyDurationDelta: -duration,
            permanentDurationDelta: 0,
        };
        await freeModifyLock(urs);
        return data;
    }
}
function createUnChangeDurationRes(account) {
    const data = {
        urs: account.urs,
        ds: account.ds,
        dailyDuration: account.dailyDuration,
        permanentDuration: account.duration,
        isChange: false,
        dailyDurationDelta: 0,
        permanentDurationDelta: 0,
    };
    return data;
}
async function decrUserDurationPreferDaily(account, curDate, duration) {
    const reason = 1 /* EChangeReasonType.BY_API */;
    const ret = await decrUserDurationOnlyDaily(account, curDate, duration, reason);
    if (ret.isChange) {
        return ret;
    }
    else {
        // 当日时长余额不足, 需要扣除当日加上永久
        const urs = account.urs;
        const restDuration = Math.max(duration - account.dailyDuration, 0);
        const ds = getDayStr(curDate);
        const query = models_1.CloudGameDurationAccountModel.raw(`update ?? set \`dailyDuration\` = \`dailyDuration\` - ?, \`duration\` = \`duration\` - ?  where \`urs\` = ? and \`ds\` = ? and \`duration\` >= ? and \`dailyDuration\` >= ?`, [
            models_1.CloudGameDurationAccountModel.tableName,
            account.dailyDuration,
            restDuration,
            urs,
            ds,
            restDuration,
            account.dailyDuration,
        ]);
        const changeRet = await models_1.CloudGameDurationAccountModel.executeByQuery(query);
        if (changeRet.affectedRows !== 1) {
            logger.warn({ account, ds, changeRet }, "DecrAccountPermanentDurationFailed");
            const ret = createUnChangeDurationRes(account);
            await freeModifyLock(urs);
            return ret;
        }
        else {
            await addDailyDurationChangeLog({
                urs,
                ds,
                duration: -account.dailyDuration,
                reason,
                createTime: Date.now(),
            });
            await addPermanentDurationChangeLog({
                urs,
                duration: -restDuration,
                reason,
                createTime: Date.now(),
            });
            const data = {
                urs,
                ds,
                dailyDuration: 0,
                permanentDuration: account.duration - restDuration,
                isChange: true,
                dailyDurationDelta: -account.dailyDuration,
                permanentDurationDelta: -restDuration,
            };
            await freeModifyLock(urs);
            return data;
        }
    }
}
/** 本次充值元宝应该对应的赠送时长 */
async function saveMonthYuanbaoInfo(urs, month, chargeNum) {
    const monthYuanbao = await models_1.CloudGameYuanbaoMonthModel.findOne({ urs, month });
    if (monthYuanbao && monthYuanbao.id) {
        const insertRet = await models_1.CloudGameYuanbaoMonthModel.increment({ id: monthYuanbao.id }, "chargeNum", chargeNum);
        logger.info({ monthYuanbao, insertRet, chargeNum }, "InitYuanbaoMonthRecord");
        const newChargeNum = monthYuanbao.chargeNum + chargeNum;
        const newDuration = getAwardDurationByYuanbaoNum(newChargeNum);
        const oldDuration = getAwardDurationByYuanbaoNum(monthYuanbao.chargeNum);
        return newDuration - oldDuration;
    }
    else {
        const insertProp = { urs, month, chargeNum };
        const insertRet = await models_1.CloudGameYuanbaoMonthModel.insert(insertProp);
        logger.info({ insertProp, insertRet }, "InitYuanbaoMonthRecord");
        const newDuration = getAwardDurationByYuanbaoNum(chargeNum);
        return newDuration;
    }
}
async function dailyDurationExpire(date) {
    const ds = getDayStr(date);
    const query = models_1.CloudGameDurationAccountModel.scope().whereNot("ds", ds).update("dailyDuration", 0).update("ds", ds);
    const ret = await models_1.CloudGameDurationAccountModel.executeByQuery(query);
    return ret.changedRows;
}
const monthCardUrls = [config_1.server.apiPrefix + "/cloud_game_duration/notify_buy_month_card"];
async function checkModifyFreq(req, res, next) {
    const urs = req.params.urs;
    const method = req.method;
    if (method === "POST" && monthCardUrls.indexOf(req.route.path) === -1) {
        try {
            const isLocked = await getModifyLock(urs);
            if (isLocked) {
                return res.send(errorCodes_1.CloudGameDurationErrors.OperationTooFrequency);
            }
            else {
                next();
            }
        }
        catch (err) {
            freeModifyLock(urs);
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    else {
        // 所有get无副作用，不需要lock
        next();
    }
}
async function getModifyLock(urs) {
    const lock = cacheUtil_1.OperationInterval.create((0, util_1.cacheKeyGen)("cloud_game_duration_modify_lock", { urs }));
    const isLock = await lock.locked(constant_1.OperationLockTime);
    return isLock;
}
async function freeModifyLock(urs) {
    const lock = cacheUtil_1.OperationInterval.create((0, util_1.cacheKeyGen)("cloud_game_duration_modify_lock", { urs }));
    const ret = await lock.unlock();
    return ret;
}
//# sourceMappingURL=service.js.map