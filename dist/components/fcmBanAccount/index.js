"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FcmBanAccountComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "get",
        url: "/fcm/ban_account/show",
        paramsSchema: type_1.ReqSchemas.FcmBanAccountShow,
        operation: operation_1.fcmBanAccountShow,
    },
    {
        method: "post",
        url: "/fcm/ban_account/add",
        paramsSchema: type_1.ReqSchemas.FcmBanAccountAdd,
        operation: operation_1.fcmBanAccountAdd,
    },
    {
        method: "post",
        url: "/fcm/ban_account/remove",
        paramsSchema: type_1.ReqSchemas.FcmBanAccountRemove,
        operation: operation_1.fcmBanAccountRemove,
    },
];
exports.FcmBanAccountComponent = {
    paths: exports.paths,
    prefix: "/fcm/ban_account/",
};
//# sourceMappingURL=index.js.map