"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFcmBanAccountInfo = getFcmBanAccountInfo;
const models_1 = require("../../models");
async function getFcmBanAccountInfo(urs) {
    const data = {
        isBanned: false,
        banTime: 0,
    };
    const row = await models_1.FcmBanAccountModel.findOne({ urs });
    if (row && row.id) {
        data.banTime = row.banTime;
        if (data.banTime === 0) {
            // 未设定时间， 永久封禁
            data.isBanned = true;
        }
        else {
            // 否则封禁时间截止时间需要大于当前时间
            data.isBanned = data.banTime > 0 && data.banTime > Date.now();
        }
    }
    return data;
}
//# sourceMappingURL=service.js.map