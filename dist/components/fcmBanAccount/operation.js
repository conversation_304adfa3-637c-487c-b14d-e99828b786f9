"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fcmBanAccountShow = fcmBanAccountShow;
exports.fcmBanAccountAdd = fcmBanAccountAdd;
exports.fcmBanAccountRemove = fcmBanAccountRemove;
const models_1 = require("../../models");
const service_1 = require("./service");
/** 查询账号封禁信息 */
async function fcmBanAccountShow(params) {
    const { urs } = params;
    const info = await (0, service_1.getFcmBanAccountInfo)(urs);
    const data = { urs, ...info };
    return data;
}
/** 添加账号封禁 */
async function fcmBanAccountAdd(params) {
    const { urs } = params;
    const banTime = params.banTime > 0 ? params.banTime : 0;
    const insertProps = {
        urs,
        banTime
    };
    const updateProps = { banTime };
    const ret = await models_1.FcmBanAccountModel.createOrUpdate(insertProps, updateProps);
    const isOk = ret.affectedRows > 0;
    const data = { isOk };
    return data;
}
/** 解除账号封禁 */
async function fcmBanAccountRemove(params) {
    const { urs } = params;
    const ret = await models_1.FcmBanAccountModel.deleteByCondition({ urs });
    const isOk = ret.affectedRows > 0;
    const data = { isOk };
    return data;
}
//# sourceMappingURL=operation.js.map