"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bazaarPostAdd = bazaarPostAdd;
exports.bazaarPostList = bazaarPostList;
exports.bazaarPostSelfList = bazaarPostSelfList;
exports.bazaarPostInterpret = bazaarPostInterpret;
exports.bazaarCommentAdd = bazaarCommentAdd;
exports.bazaarPostCommentList = bazaarPostCommentList;
exports.bazaarRatingAdd = bazaarRatingAdd;
exports.ratingAdd = ratingAdd;
exports.bazaarHotTopic = bazaarHotTopic;
exports.getDayStrForAstrology = getDayStrForAstrology;
exports.bazaarGroupDivinationReport = bazaarGroupDivinationReport;
exports.bazaarUserProfile = bazaarUserProfile;
exports.bazaarUserProfileForServer = bazaarUserProfileForServer;
exports.bazaarUserProfileUpdate = bazaarUserProfileUpdate;
exports.bazaarUserRegister = bazaarUserRegister;
exports.bazaarUserReceiveRatingList = bazaarUserReceiveRatingList;
exports.HoroscopePlanetaryAspects = HoroscopePlanetaryAspects;
exports.HoroscopeDailyForecast = HoroscopeDailyForecast;
exports.diceResultInterpret = diceResultInterpret;
exports.commentRank = commentRank;
exports.commentRankWeekly = commentRankWeekly;
const bazaarPostService_1 = require("../../services/astrology/bazaarPostService");
const astrologyConstant_1 = require("../../services/astrology/astrologyConstant");
const horoscopeService_1 = require("../../services/external/horoscopeService");
const bazaarCommentService_1 = require("../../services/astrology/bazaarCommentService");
const astrologyCommentRankService_1 = require("../../services/astrology/astrologyCommentRankService");
const astrologyWeeklyCommentRankService_1 = require("../../services/astrology/astrologyWeeklyCommentRankService");
const bazaarRatingService_1 = require("../../services/astrology/bazaarRatingService");
const bazaarTopicService_1 = require("../../services/astrology/bazaarTopicService");
const config_1 = require("../../common/config");
const dateUtil_1 = require("../../common/dateUtil");
const logger_1 = require("../../logger");
const astrologyUserService_1 = require("../../services/astrology/astrologyUserService");
const errorCode_1 = require("../../errors/errorCode");
const astrologyConstant_2 = require("../../constants/astrologyConstant");
const astrologyExternalService_1 = require("../../services/astrology/astrologyExternalService");
const util_1 = require("../../common/util");
const envsdkService_1 = require("../../services/external/envsdkService");
const moment = require("moment");
const logger = (0, logger_1.clazzLogger)("AstrologyOperation");
async function reviewTextSensitive(text) {
    const { validate } = await envsdkService_1.default.reviewWords(text);
    if (!validate) {
        throw errorCode_1.errorCodesV2.ContentSensitive;
    }
    return validate;
}
async function bazaarPostAdd(ctx, params) {
    const now = Date.now();
    (0, util_1.checkValidText)(params.question, config_1.astrologyCfg.questionMaxLen);
    const checkText = [params.question, params.topicText].join(" ");
    await reviewTextSensitive(checkText);
    const bazaarPost = {
        RoleId: params.roleid,
        TopicId: params.topicId,
        Question: params.question,
        DicePlanet: params.dice.planet,
        DiceConstellation: params.dice.constellation,
        DiceHouse: params.dice.house,
        CommentCount: 0,
        CreateTime: now,
        UpdateTime: now,
        Status: 0 /* Statues.Normal */,
    };
    const id = await bazaarPostService_1.BazaarPostService.getInstance().addBazaarPost(ctx, bazaarPost);
    const bazaarTopicService = bazaarTopicService_1.BazaarTopicService.getInstance();
    await bazaarTopicService.saveTopic(ctx, params.topicId, params.topicText);
    await bazaarTopicService.incrDailyTopicHot(ctx, params.topicId, now);
    if (params.isSyncMoment) {
        astrologyExternalService_1.AstrologyExternalService.getInstance().addMoment(ctx, params);
    }
    const data = {
        id,
    };
    return data;
}
async function convertBazaarPostListToShowList(ctx, roleId, bazaarPostList) {
    const postRoleIds = bazaarPostList.map(item => item.RoleId);
    const roleInfoMap = await astrologyUserService_1.AstrologyUserService.getInstance().getShowRoleInfoMap(ctx, postRoleIds);
    const userInfoMap = await astrologyUserService_1.AstrologyUserService.getInstance().getUserInfoMap(ctx, postRoleIds);
    const bazaarCommentService = bazaarCommentService_1.BazaarCommentService.getInstance();
    const postIds = bazaarPostList.map(item => item.ID);
    const commentedPostIds = await bazaarCommentService.filterCommentedPostIds(ctx, roleId, postIds);
    return bazaarPostService_1.BazaarPostService.getInstance().convertToBazaarPostShowItemList(bazaarPostList, roleInfoMap, userInfoMap, commentedPostIds);
}
async function bazaarPostList(ctx, params) {
    const bazaarPostService = bazaarPostService_1.BazaarPostService.getInstance();
    const topicIds = params.topicIds ? (0, util_1.csvStrToIntArray)(params.topicIds) : [];
    const bazaarPostList = await bazaarPostService.listBazaarPublicPost(ctx, params.roleid, topicIds, {
        page: params.page,
        pageSize: params.pageSize,
    });
    const list = await convertBazaarPostListToShowList(ctx, params.roleid, bazaarPostList);
    const total = await bazaarPostService.countBazaarPublicPost(ctx, params.roleid, topicIds);
    const resp = {
        list,
        total,
    };
    return resp;
}
async function bazaarPostSelfList(ctx, params) {
    const bazaarPostService = bazaarPostService_1.BazaarPostService.getInstance();
    const topicIds = params.topicIds ? (0, util_1.csvStrToIntArray)(params.topicIds) : [];
    const bazaarPostList = await bazaarPostService.listBazaarUserSelfPost(ctx, params.roleid, topicIds, {
        page: params.page,
        pageSize: params.pageSize,
    });
    const list = await convertBazaarPostListToShowList(ctx, params.roleid, bazaarPostList);
    const total = await bazaarPostService.countBazaarUserSelfPost(ctx, params.roleid, topicIds);
    const resp = {
        list,
        total,
    };
    return resp;
}
/**
 * 发起解读，把对应问题和占卜结果发送AI, 并返回解读结果
 * [伏羲提供的上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794)
 */
async function bazaarPostInterpret(ctx, params) {
    const bazaarPost = await bazaarPostService_1.BazaarPostService.getInstance().getBazaarPost(ctx, params.postId);
    const diceResult = (0, astrologyConstant_1.toAstrologyBazaarDiceTuple)({
        planet: bazaarPost.DicePlanet,
        constellation: bazaarPost.DiceConstellation,
        house: bazaarPost.DiceHouse,
    });
    const apiRes = await horoscopeService_1.HoroscopeService.getInstance().getDiceResult(ctx, bazaarPost.Question, diceResult);
    const resp = {
        title: apiRes.title || "",
        content: apiRes.content || [],
    };
    return resp;
}
async function bazaarCommentAdd(ctx, params) {
    (0, util_1.checkValidText)(params.text, config_1.astrologyCfg.commentMaxLen);
    await reviewTextSensitive(params.text);
    const bazaarPostService = bazaarPostService_1.BazaarPostService.getInstance();
    const bazaarCommentService = bazaarCommentService_1.BazaarCommentService.getInstance();
    const bazaarPost = await bazaarPostService.verifyBazaarPost(ctx, params.postId);
    const now = Date.now();
    const bazaarComment = {
        PostId: params.postId,
        PostRoleId: bazaarPost.RoleId,
        RoleId: params.roleid,
        Text: params.text,
        CreateTime: now,
        UpdateTime: now,
        Status: 0 /* Statues.Normal */,
    };
    await bazaarCommentService.checkUserCommented(ctx, params.roleid, params.postId);
    const id = await bazaarCommentService.addBazaarComment(ctx, bazaarComment);
    await bazaarPostService.incrCommentCount(ctx, bazaarPost.ID);
    await bazaarPostService.incrPostDailyHot(ctx, bazaarPost.ID, now);
    // 增加用户解惑次数
    try {
        const rankService = astrologyCommentRankService_1.AstrologyCommentRankService.getInstance();
        await rankService.incrementUserCommentCount(ctx, params.roleid);
    }
    catch (err) {
        // 解惑次数更新失败不影响主流程，只记录日志
        logger.error({ ctx, err, roleId: params.roleid }, "incrementUserCommentCountError");
    }
    const data = {
        id,
    };
    return data;
}
async function bazaarPostCommentList(ctx, params) {
    const bazaarPostService = bazaarPostService_1.BazaarPostService.getInstance();
    const bazaarCommentService = bazaarCommentService_1.BazaarCommentService.getInstance();
    const bazaarPost = await bazaarPostService.verifyBazaarPost(ctx, params.postId);
    const bazaarCommentList = await bazaarCommentService.listBazaarCommentList(ctx, bazaarPost.ID, {
        page: params.page,
        pageSize: params.pageSize,
    });
    const commentRoleIds = bazaarCommentList.map(item => item.RoleId);
    const showRoleInfoMap = await astrologyUserService_1.AstrologyUserService.getInstance().getShowRoleInfoMap(ctx, commentRoleIds);
    const userInfoMap = await astrologyUserService_1.AstrologyUserService.getInstance().getUserInfoMap(ctx, commentRoleIds);
    const commentIds = bazaarCommentList.map(item => item.ID);
    const ratingCoreMap = await bazaarRatingService_1.BazaarRatingService.getInstance().getRatingCoreMap(ctx, commentIds);
    const total = await bazaarCommentService.countBazaarCommentByPostId(ctx, bazaarPost.ID);
    const list = bazaarCommentService.convertToBazaarCommentShowItemList(bazaarCommentList, showRoleInfoMap, userInfoMap, ratingCoreMap);
    const resp = {
        list,
        total,
    };
    return resp;
}
async function bazaarRatingAdd(ctx, params) {
    if (params.text) {
        (0, util_1.checkValidText)(params.text, config_1.astrologyCfg.ratingTextMaxLen);
        await reviewTextSensitive(params.text);
    }
    const bazaarCommentService = bazaarCommentService_1.BazaarCommentService.getInstance();
    const bazaarComment = await bazaarCommentService.verifyBazaarComment(ctx, params.commentId);
    if (bazaarComment.PostRoleId !== params.roleid) {
        throw errorCode_1.errorCodesV2.BazaarRatingNotSelf;
    }
    const ratingService = bazaarRatingService_1.BazaarRatingService.getInstance();
    const now = Date.now();
    const rating = {
        CommentId: params.commentId,
        ToRoleId: bazaarComment.RoleId,
        PostId: bazaarComment.PostId,
        FromRoleId: params.roleid,
        Star: params.star,
        Text: params.text,
        CreateTime: now,
        UpdateTime: now,
        Status: 0 /* Statues.Normal */,
    };
    await ratingService.checkUserRated(ctx, params.roleid, params.commentId);
    const id = await ratingService.addBazaarRating(ctx, rating);
    astrologyUserService_1.AstrologyUserService.getInstance().updateRatingStat(ctx, bazaarComment.RoleId, params.star, now);
    const data = {
        id,
        createTime: now,
    };
    return data;
}
async function ratingAdd(ctx, params) {
    if (params.text) {
        (0, util_1.checkValidText)(params.text, config_1.astrologyCfg.ratingTextMaxLen);
        await reviewTextSensitive(params.text);
    }
    const ratingService = bazaarRatingService_1.BazaarRatingService.getInstance();
    const now = Date.now();
    const rating = {
        CommentId: 0,
        ToRoleId: params.toRoleId,
        PostId: 0,
        FromRoleId: params.fromRoleId,
        Star: params.star,
        Text: params.text,
        CreateTime: now,
        UpdateTime: now,
        Status: 0 /* Statues.Normal */,
    };
    const id = await ratingService.addBazaarRating(ctx, rating);
    astrologyUserService_1.AstrologyUserService.getInstance().updateRatingStat(ctx, params.toRoleId, params.star, now);
    const data = {
        id,
        createTime: now,
    };
    return data;
}
/**
 * 今日热门话题
 * @param ctx
 * @param params
 * @returns
 */
async function bazaarHotTopic(ctx, params) {
    const bazaarTopicService = bazaarTopicService_1.BazaarTopicService.getInstance();
    const currentTime = Date.now();
    const ds = getDayStrForAstrology(new Date(currentTime));
    const list = await bazaarTopicService.getDailyTopicHotList(ctx, ds, config_1.astrologyCfg.hotTopicSize);
    const topicIds = list.map(item => item.TopicId);
    const topicIdToTextMap = await bazaarTopicService.getTopicIdToTextMap(ctx, topicIds);
    const showList = bazaarTopicService.convertToTopicShowList(list, topicIdToTextMap);
    const data = {
        list: showList,
        lastRefreshTime: currentTime,
    };
    return data;
}
/**
 *
 * @param timestamp
 * 每日时间范围是上一日的晚上8点到今天的晚上8点，所以计算yyyyMMdd的时候要特殊处理
 * 举个例子，如果timestamp是2025-06-16 20:00:00，那么返回的yyyyMMdd是20250617
 * 如果是2025-06-15 21:00:00，那么返回的yyyyMMdd是20250616
 */
function getDayStrForAstrology(date) {
    const hour = date.getHours();
    // 如果时间在晚上8点(20:00)之后，返回下一天的日期
    if (hour >= 20) {
        date.setDate(date.getDate() + 1);
    }
    return (0, dateUtil_1.getDayStrV2)(date);
}
async function bazaarGroupDivinationReport(ctx, params) {
    const bazaarTopicService = bazaarTopicService_1.BazaarTopicService.getInstance();
    const currentTime = params.timestamp > 0 ? new Date(params.timestamp) : new Date();
    const yesterday = moment(currentTime).subtract(1, 'days').toDate();
    const ds = getDayStrForAstrology(yesterday);
    const generatedAt = (0, dateUtil_1.formatDate)(currentTime, "yyyy-MM-dd HH:mm:ss");
    const mostFocusedTopic = await bazaarTopicService.getMostFocusedTopicId(ctx, ds);
    const averageFortuneScore = await astrologyUserService_1.AstrologyUserService.getInstance().getForecastFortuneDailyAvgScore(ctx, ds);
    const mostCommentedQuestion = await bazaarPostService_1.BazaarPostService.getInstance().getMostCommentedPostQuestion(ctx, ds);
    const data = {
        reportDate: ds,
        mostFocusedTopic,
        averageFortuneScore,
        mostCommentedQuestion,
        generatedAt: generatedAt,
    };
    return data;
}
async function bazaarUserProfile(ctx, params) {
    const resp = await astrologyUserService_1.AstrologyUserService.getInstance().getUserShowProfile(ctx, params.roleid);
    return resp;
}
async function bazaarUserProfileForServer(ctx, params) {
    const resp = await astrologyUserService_1.AstrologyUserService.getInstance().getUserShowProfileForServer(ctx, params.roleid);
    return resp;
}
async function bazaarUserProfileUpdate(ctx, params) {
    if (params.birthTime && !(0, dateUtil_1.validateBirthTimeFormat)(params.birthTime)) {
        throw errorCode_1.errorCodesV2.BirthTimeFormatInvalid;
    }
    await astrologyUserService_1.AstrologyUserService.getInstance().updateProfile(ctx, params.roleid, params);
    const resp = null;
    return resp;
}
async function bazaarUserRegister(ctx, params) {
    const astrologyUserService = astrologyUserService_1.AstrologyUserService.getInstance();
    if (!(0, dateUtil_1.validateBirthTimeFormat)(params.birthTime)) {
        throw errorCode_1.errorCodesV2.BirthTimeFormatInvalid;
    }
    await astrologyUserService.verifyUserNotRegister(ctx, params.roleid);
    const resp = await astrologyUserService.registerUser(ctx, params.roleid, params);
    return resp;
}
async function bazaarUserReceiveRatingList(ctx, params) {
    const bazaarRatingService = bazaarRatingService_1.BazaarRatingService.getInstance();
    const list = await bazaarRatingService.listReceiveBazaarRatingList(ctx, params.roleid, {
        page: params.page,
        pageSize: params.pageSize,
    });
    const total = await bazaarRatingService.countPlayerReceiveRating(ctx, params.roleid);
    const ratingRoleIds = list.map(item => item.FromRoleId);
    const roleInfoMap = await astrologyUserService_1.AstrologyUserService.getInstance().getShowRoleInfoMap(ctx, ratingRoleIds);
    const userInfoMap = await astrologyUserService_1.AstrologyUserService.getInstance().getUserInfoMap(ctx, ratingRoleIds);
    const showList = bazaarRatingService.convertToBazaarRatingShowItemList(list, roleInfoMap, userInfoMap);
    const resp = {
        list: showList,
        total,
    };
    return resp;
}
async function HoroscopePlanetaryAspects(ctx, params) {
    const apiRes = await horoscopeService_1.HoroscopeService.getInstance().getPlanetaryAspects(ctx, params.timestamp);
    const resp = {
        title: apiRes.title,
        content: apiRes.content,
    };
    return resp;
}
async function HoroscopeDailyForecast(ctx, params) {
    const ds = getDayStrForAstrology(new Date(params.timestamp));
    const astrologyUserService = astrologyUserService_1.AstrologyUserService.getInstance();
    const profile = await astrologyUserService.getProfile(ctx, params.roleid);
    if (!profile) {
        logger.warn({ ctx, roleId: params.roleid }, "HoroscopeDailyForecastUserNotFound");
        throw errorCode_1.errorCodesV2.AstrologyUserNotFound;
    }
    const fortuneType = (0, astrologyConstant_2.convertToEAstrologyUserFortuneTypeStr)(params.fortune);
    const timeInterval = params.timeInterval == "today" ? 1 /* HoroScopeTimeInterval.TODAY */ : 2 /* HoroScopeTimeInterval.THIS_WEEK */;
    const userInfo = astrologyUserService.convertToUserHoroscopeInfo(profile);
    const apiRes = await horoscopeService_1.HoroscopeService.getInstance().getUserHoroscope(ctx, userInfo, params.timestamp, timeInterval, fortuneType);
    const fortuneScore = apiRes.score;
    const data = {
        fortune: apiRes.fortune,
        time: apiRes.time,
        user_constellation: apiRes.user_constellation,
        in_water_reversal: apiRes.in_water_reversal,
        day_for_water_reversal_remain: apiRes.day_for_water_reversal_remain,
        day_for_next_water_reversal: apiRes.day_for_next_water_reversal,
        title: apiRes.title,
        content: apiRes.content,
        score: fortuneScore,
        lucky_color: apiRes.lucky_color,
        lucky_color_code: apiRes.lucky_color_code,
    };
    if (timeInterval === 1 /* HoroScopeTimeInterval.TODAY */) {
        astrologyUserService_1.AstrologyUserService.getInstance().updateUserDailyForecast(ctx, params.roleid, ds, fortuneScore);
    }
    return data;
}
async function diceResultInterpret(ctx, params) {
    const diceResult = (0, astrologyConstant_1.toAstrologyBazaarDiceTuple)({
        planet: params.dice.planet,
        constellation: params.dice.constellation,
        house: params.dice.house,
    });
    const apiRes = await horoscopeService_1.HoroscopeService.getInstance().getDiceResult(ctx, params.question, diceResult);
    const resp = {
        title: apiRes.title,
        content: apiRes.content,
    };
    return resp;
}
/**
 * 获取解惑次数排行榜
 */
async function commentRank(ctx, params) {
    const rankService = astrologyCommentRankService_1.AstrologyCommentRankService.getInstance();
    const rankData = await rankService.getCommentRank(ctx);
    return {
        list: rankData.list,
        updateTime: rankData.updateTime
    };
}
/**
 * 获取解惑次数周排行榜
 */
async function commentRankWeekly(ctx, params) {
    const weeklyRankService = astrologyWeeklyCommentRankService_1.AstrologyWeeklyCommentRankService.getInstance();
    const rankData = await weeklyRankService.getWeeklyCommentRank(ctx);
    return {
        list: rankData.list,
        updateTime: rankData.updateTime,
        weekStart: rankData.weekStart,
        weekEnd: rankData.weekEnd
    };
}
//# sourceMappingURL=operation.js.map