"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = exports.diceSchema = void 0;
exports.diceSchema = {
    type: "object",
    properties: {
        planet: {
            type: "string",
            enum: [
                "sun",
                "moon",
                "mercury",
                "venus",
                "mars",
                "jupiter",
                "saturn",
                "uranus",
                "neptune",
                "pluto",
                "southNode",
                "northNode",
            ],
        },
        constellation: {
            type: "string",
            enum: [
                "aries",
                "taurus",
                "gemini",
                "cancer",
                "leo",
                "virgo",
                "libra",
                "scorpio",
                "sagittarius",
                "capricorn",
                "aquarius",
                "pisces",
            ],
        },
        house: { type: "integer", minimum: 1, maximum: 12 }
    }
};
exports.ReqSchemas = {
    BazaarPostAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            topicId: { type: "integer" },
            question: { type: "string" },
            dice: exports.diceSchema,
            isSyncMoment: { type: "boolean" },
        },
        required: ["roleid", "topicId", "question", "dice", "isSyncMoment"],
    },
    BazaarPostList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            page: { type: "number", minimum: 1, default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10 },
            topicIds: { type: "string" },
        },
        required: ["roleid"],
    },
    BazaarPostSelfList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            page: { type: "number", minimum: 1, default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10 },
            topicIds: { type: "string" },
        },
        required: ["roleid"],
    },
    BazaarPostInterpret: {
        type: "object",
        properties: { roleid: { type: "number" }, postId: { type: "number" } },
        required: ["roleid", "postId"],
    },
    BazaarCommentAdd: {
        type: "object",
        properties: { roleid: { type: "number" }, postId: { type: "integer" }, text: { type: "string" } },
        required: ["roleid", "postId", "text"],
    },
    BazaarPostCommentList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            postId: { type: "number" },
            page: { type: "number", minimum: 1, default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10 },
        },
        required: ["roleid", "postId"],
    },
    BazaarRatingAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            star: { type: "integer", minimum: 1, maximum: 5 },
            text: { type: "string" },
        },
        required: ["roleid"],
    },
    RatingAdd: {
        type: "object",
        properties: {
            star: { type: "integer", minimum: 1, maximum: 5 },
            text: { type: "string" },
        },
    },
    BazaarHotTopic: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
    BazaarGroupDivinationReport: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            timestamp: { type: "integer" }
        }, required: ["roleid", "timestamp"]
    },
    BazaarUserRegister: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            gender: { type: "number", enum: [0, 1] },
            birthTime: { type: "string" },
            birthPlace: {
                type: "object",
                required: ["province", "city", "district"],
                properties: {
                    province: { type: "string" },
                    city: { type: "string" },
                    district: { type: "string" },
                },
            },
            currentPlace: {
                type: "object",
                required: ["province", "city", "district"],
                properties: {
                    province: { type: "string" },
                    city: { type: "string" },
                    district: { type: "string" },
                },
            },
            astroLevel: { type: "integer" },
            astroType: { type: "integer" },
        },
        required: ["roleid", "gender", "birthTime", "birthPlace", "currentPlace", "astroLevel", "astroType"],
    },
    BazaarUserProfile: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
    BazaarUserProfileForServer: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
    BazaarUserProfileUpdate: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            gender: { type: "number", enum: [0, 1] },
            birthTime: { type: "string" },
            birthPlace: {
                type: "object",
                required: ["province", "city", "district"],
                properties: {
                    province: { type: "string" },
                    city: { type: "string" },
                    district: { type: "string" },
                },
            },
            currentPlace: {
                type: "object",
                required: ["province", "city", "district"],
                properties: {
                    province: { type: "string" },
                    city: { type: "string" },
                    district: { type: "string" },
                },
            },
        },
    },
    BazaarUserRatingList: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            page: { type: "number", minimum: 1, default: 1 },
            pageSize: { type: "number", maximum: 20, default: 10, minimum: 1 },
        },
        required: ["roleid", "page", "pageSize"],
    },
    HoroscopePlanetaryAspects: {
        type: "object",
        properties: { roleid: { type: "number" }, timestamp: { type: "number" } },
        required: ["roleid", "timestamp"],
    },
    HoroscopeDailyForecast: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            timestamp: { type: "integer" },
            timeInterval: { type: "string", enum: ["today", "week"] },
            fortune: { type: "string", enum: ["basic", "wealth", "career", "love"] },
        },
        required: ["roleid", "timestamp", "timeInterval", "fortune"],
    },
    DiceResultInterpret: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            question: { type: "string" },
            dice: exports.diceSchema,
        },
        required: ["roleid", "question", "dice"],
    }
};
//# sourceMappingURL=type.js.map