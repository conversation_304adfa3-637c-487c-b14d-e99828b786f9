"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/astrology/bazaar/post/add",
        paramsSchema: type_1.ReqSchemas.BazaarPostAdd,
        operation: operation_1.bazaarPostAdd,
    },
    {
        method: "get",
        url: "/astrology/bazaar/post/list",
        paramsSchema: type_1.ReqSchemas.BazaarPostList,
        operation: operation_1.bazaarPostList,
    },
    {
        method: "get",
        url: "/astrology/bazaar/post/self_list",
        paramsSchema: type_1.ReqSchemas.BazaarPostSelfList,
        operation: operation_1.bazaarPostSelfList,
    },
    {
        method: "get",
        url: "/astrology/bazaar/post/interpret",
        paramsSchema: type_1.ReqSchemas.BazaarPostInterpret,
        operation: operation_1.bazaarPostInterpret,
    },
    {
        method: "post",
        url: "/astrology/bazaar/comment/add",
        paramsSchema: type_1.ReqSchemas.BazaarCommentAdd,
        operation: operation_1.bazaarCommentAdd,
    },
    {
        method: "get",
        url: "/astrology/bazaar/comment/list",
        paramsSchema: type_1.ReqSchemas.BazaarPostCommentList,
        operation: operation_1.bazaarPostCommentList,
    },
    {
        method: "get",
        url: "/astrology/bazaar/hot_topic",
        paramsSchema: type_1.ReqSchemas.BazaarHotTopic,
        operation: operation_1.bazaarHotTopic,
    },
    {
        method: "get",
        url: "/astrology/bazaar/group_divination_report",
        paramsSchema: type_1.ReqSchemas.BazaarGroupDivinationReport,
        operation: operation_1.bazaarGroupDivinationReport,
    },
    {
        method: "post",
        url: "/astrology/bazaar/user/register",
        paramsSchema: type_1.ReqSchemas.BazaarUserRegister,
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.bazaarUserRegister,
        option: {
            skipSkey: true,
        },
    },
    {
        method: "get",
        url: "/astrology/bazaar/user/profile",
        paramsSchema: type_1.ReqSchemas.BazaarUserProfile,
        operation: operation_1.bazaarUserProfile,
    },
    {
        method: "get",
        url: "/astrology/server/bazaar/user/profile",
        paramsSchema: type_1.ReqSchemas.BazaarUserProfileForServer,
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.bazaarUserProfileForServer,
        option: {
            skipSkey: true,
        },
    },
    {
        method: "post",
        url: "/astrology/bazaar/user/profile/update",
        paramsSchema: type_1.ReqSchemas.BazaarUserProfileUpdate,
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.bazaarUserProfileUpdate,
        option: {
            skipSkey: true,
        },
    },
    {
        method: "post",
        url: "/astrology/bazaar/rating/add",
        paramsSchema: type_1.ReqSchemas.BazaarRatingAdd,
        operation: operation_1.bazaarRatingAdd,
    },
    {
        method: "post",
        url: "/astrology/rating/add",
        before: gameIpLimit_1.gameIpLimit,
        paramsSchema: type_1.ReqSchemas.RatingAdd,
        operation: operation_1.ratingAdd,
        option: {
            skipSkey: true,
        },
    },
    {
        method: "get",
        url: "/astrology/bazaar/user/rating/receive_list",
        paramsSchema: type_1.ReqSchemas.BazaarUserRatingList,
        operation: operation_1.bazaarUserReceiveRatingList,
    },
    {
        method: "get",
        url: "/astrology/horoscope/planetary_aspects",
        paramsSchema: type_1.ReqSchemas.HoroscopePlanetaryAspects,
        operation: operation_1.HoroscopePlanetaryAspects,
    },
    {
        method: "get",
        url: "/astrology/horoscope/daily_forecast",
        paramsSchema: type_1.ReqSchemas.HoroscopeDailyForecast,
        operation: operation_1.HoroscopeDailyForecast,
    },
    {
        method: "post",
        url: "/astrology/dice_result/interpret",
        paramsSchema: type_1.ReqSchemas.DiceResultInterpret,
        operation: operation_1.diceResultInterpret,
    },
    {
        method: "get",
        url: "/astrology/comment/rank",
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.commentRank,
        option: {
            skipSkey: true,
        },
    },
    {
        method: "get",
        url: "/astrology/comment/rank_weekly",
        before: gameIpLimit_1.gameIpLimit,
        operation: operation_1.commentRankWeekly,
        option: {
            skipSkey: true,
        },
    },
];
exports.AstrologyComponent = {
    paths: exports.paths,
    prefix: "/astrology/",
    version: "v2"
};
//# sourceMappingURL=index.js.map