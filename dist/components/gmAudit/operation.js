"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gmAuditUpdateStatus = gmAuditUpdateStatus;
const models_1 = require("../../models");
/** 设置图片视频审核状态 */
async function gmAuditUpdateStatus(params) {
    let isOk = false;
    if (params.mediaType === "guild_photo_wall_photo") {
        const ret = await models_1.GuildPhotoWallPhotoModel.updateById(params.resourceId, { auditStatus: params.auditStatus });
        isOk = ret.affectedRows > 0;
    }
    else if (params.mediaType === "multi_garden_photo_wall_photo") {
        const ret = await models_1.MultiGardenPhotoWallPhotoModel.updateById(params.resourceId, { auditStatus: params.auditStatus });
        isOk = ret.affectedRows > 0;
    }
    const data = { isOk };
    return data;
}
//# sourceMappingURL=operation.js.map