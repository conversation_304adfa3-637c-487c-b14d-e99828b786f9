"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appearancePaintUpdate = appearancePaintUpdate;
exports.appearancePaintGet = appearancePaintGet;
const RoleInfos_1 = require("../../models/RoleInfos");
const profile_1 = require("../../services/profile");
async function appearancePaintUpdate(params) {
    const updateProps = {};
    if (params.headPaintId >= 0) {
        updateProps.HeadPaintId = params.headPaintId;
    }
    if (params.bodyPaintId >= 0) {
        updateProps.BodyPaintId = params.bodyPaintId;
    }
    if (Object.keys(updateProps).length > 0) {
        await RoleInfos_1.RoleInfoModel.updateByCondition({ RoleId: params.roleid }, updateProps);
        (0, profile_1.refreshProfileCache)(params.roleid);
    }
    const data = {
        isOK: true,
    };
    return data;
}
async function appearancePaintGet(params) {
    const info = await RoleInfos_1.RoleInfoModel.findOne({ RoleId: params.roleid }, ["RoleId", "HeadPaintId", "BodyPaintId"]);
    if (info && info.RoleId) {
        const data = {
            roleId: info.RoleId,
            headPaintId: info.HeadPaintId,
            bodyPaintId: info.BodyPaintId,
        };
        return data;
    }
}
//# sourceMappingURL=operation.js.map