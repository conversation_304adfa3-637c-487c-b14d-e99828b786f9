"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppearancePaintComponent = exports.paths = void 0;
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/appearance_paint/update",
        paramsSchema: type_1.ReqSchemas.AppearancePaintUpdate,
        operation: operation_1.appearancePaintUpdate,
    },
    {
        method: "get",
        url: "/appearance_paint/get",
        paramsSchema: type_1.ReqSchemas.AppearancePaintGet,
        operation: operation_1.appearancePaintGet,
    },
];
exports.AppearancePaintComponent = {
    paths: exports.paths,
    prefix: "/appearance_paint/",
};
//# sourceMappingURL=index.js.map