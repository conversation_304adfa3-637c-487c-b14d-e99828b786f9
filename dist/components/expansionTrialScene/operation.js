"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expansionTrialSceneSetReward = expansionTrialSceneSetReward;
exports.expansionTrialSceneGetReward = expansionTrialSceneGetReward;
exports.expansionTrialSceneQueryReward = expansionTrialSceneQueryReward;
const expansionUrsReward_1 = require("../../models/expansionUrsReward");
const _ = require("lodash");
/** 服务器设置奖励 */
async function expansionTrialSceneSetReward(params) {
    const data = {
        status: 1
    };
    try {
        const { urs, reward } = params;
        const ursArr = urs.split(",");
        const now = Date.now();
        let query = expansionUrsReward_1.ExpansionUrsRewardModel.scope();
        if (ursArr[0])
            query.orWhere({ Urs: ursArr[0] });
        if (ursArr[1]) {
            query.orWhere({ Urs: ursArr[1] });
            query.orWhere({ Mobile: ursArr[1] });
        }
        const record = await expansionUrsReward_1.ExpansionUrsRewardModel.executeByQuery(query);
        if (record && record.length > 0) {
            let queryUpate = _.cloneDeep(query);
            queryUpate.update({ Reward: reward, UpdateTime: now });
            await expansionUrsReward_1.ExpansionUrsRewardModel.executeByQuery(queryUpate);
            data.status = 2;
        }
        else {
            const insertProps = {
                Urs: ursArr[0],
                Mobile: ursArr[1],
                Reward: reward,
                CreateTime: now,
                UpdateTime: now
            };
            await expansionUrsReward_1.ExpansionUrsRewardModel.insert(insertProps);
        }
    }
    catch (err) {
        data.status = -2;
    }
    return data;
}
/** 服务器领取奖励 */
async function expansionTrialSceneGetReward(params) {
    const data = {
        status: 1
    };
    const { urs } = params;
    const ursArr = urs.split(",");
    let query = expansionUrsReward_1.ExpansionUrsRewardModel.scope();
    if (ursArr[0])
        query.orWhere({ Urs: ursArr[0] });
    if (ursArr[1]) {
        query.orWhere({ Urs: ursArr[1] });
        query.orWhere({ Mobile: ursArr[1] });
    }
    const record = await expansionUrsReward_1.ExpansionUrsRewardModel.executeByQuery(query);
    if (record && record.length > 0) {
        for (let r of record) {
            if (r.Status === 1) {
                data.status = -1;
                break;
            }
        }
        if (data.status != -1) {
            let queryUpate = _.cloneDeep(query);
            queryUpate.update({ Status: 1, UpdateTime: Date.now() });
            await expansionUrsReward_1.ExpansionUrsRewardModel.executeByQuery(queryUpate);
            data.reward = record[0].Reward;
        }
    }
    else {
        data.status = -2;
    }
    return data;
}
/** 客户端查询奖励 */
async function expansionTrialSceneQueryReward(params) {
    const data = {
        status: 1,
        reward: ""
    };
    const { urs } = params;
    const ursArr = urs.split(",");
    let query = expansionUrsReward_1.ExpansionUrsRewardModel.scope();
    if (ursArr[0])
        query.orWhere({ Urs: ursArr[0] });
    if (ursArr[1]) {
        query.orWhere({ Urs: ursArr[1] });
        query.orWhere({ Mobile: ursArr[1] });
    }
    const record = await expansionUrsReward_1.ExpansionUrsRewardModel.executeByQuery(query);
    if (record && record.length > 0) {
        data.status = record[0].Status;
        data.reward = record[0].Reward;
        for (let r of record) {
            if (r.Status === 1) {
                data.status = r.Status;
                data.reward = r.Reward;
                break;
            }
        }
    }
    else {
        data.status = -2;
    }
    return data;
}
//# sourceMappingURL=operation.js.map