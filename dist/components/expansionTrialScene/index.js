"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpansionTrialSceneComponent = exports.paths = void 0;
const gameIpLimit_1 = require("../../auth/gameIpLimit");
const operation_1 = require("./operation");
const type_1 = require("./type");
exports.paths = [
    {
        method: "post",
        url: "/expansion_trial_scene/set_reward",
        before: [gameIpLimit_1.gameIpLimit],
        paramsSchema: type_1.ReqSchemas.ExpansionTrialSceneSetReward,
        operation: operation_1.expansionTrialSceneSetReward,
    },
    {
        method: "post",
        url: "/expansion_trial_scene/get_reward",
        before: [gameIpLimit_1.gameIpLimit],
        paramsSchema: type_1.ReqSchemas.ExpansionTrialSceneGetReward,
        operation: operation_1.expansionTrialSceneGetReward,
    },
    {
        method: "get",
        url: "/expansion_trial_scene/query_reward",
        paramsSchema: type_1.ReqSchemas.ExpansionTrialSceneQueryReward,
        operation: operation_1.expansionTrialSceneQueryReward,
    },
];
exports.ExpansionTrialSceneComponent = {
    paths: exports.paths,
    prefix: "/expansion_trial_scene/",
};
//# sourceMappingURL=index.js.map