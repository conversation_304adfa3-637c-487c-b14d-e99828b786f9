"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Formater = void 0;
var TaskType;
(function (TaskType) {
    TaskType[TaskType["FILL_NORMAL"] = 1] = "FILL_NORMAL";
    TaskType[TaskType["FILL_ONCE"] = 2] = "FILL_ONCE";
    TaskType[TaskType["PARSE"] = 3] = "PARSE";
})(TaskType || (TaskType = {}));
class Formater {
    static create(raw) {
        return new Formater(raw);
    }
    constructor(raw) {
        this.AsyncTaskList = [];
        this.SyncTaskList = [];
        this.raw = raw;
    }
    fill(key, fetcher, option) {
        this.addTask(key, TaskType.FILL_NORMAL, fetcher, option);
        return this;
    }
    fillOnce(key, fetcher, option) {
        this.addTask(key, TaskType.FILL_ONCE, fetcher, option);
        return this;
    }
    format(key, handler) {
        this.SyncTaskList.push({ key, handler });
        return this;
    }
    addTask(key, type, dataFetcher, option) {
        let task;
        if (type === TaskType.FILL_NORMAL)
            task = new FillTask(key, dataFetcher, option);
        if (type === TaskType.FILL_ONCE)
            task = new FillOnceTask(key, dataFetcher, option);
        this.AsyncTaskList.push(task);
        return this;
    }
    async execute() {
        let raw = this.raw;
        let data = raw;
        let promiseList = this.AsyncTaskList.map(async (task) => {
            return task.execute(raw);
        });
        await Promise.all(promiseList);
        raw.forEach(item => {
            this.SyncTaskList.forEach(task => {
                item[task.key] = task.handler(item[task.key]);
            });
        });
        return data;
    }
}
exports.Formater = Formater;
class AsyncTask {
}
class FillTask extends AsyncTask {
    constructor(key, dataFecher, option) {
        super();
        this.key = key;
        this.dataFetcher = dataFecher;
        this.option = option;
    }
    async execute(raw) {
        let key = this.key;
        let promiseList = raw.map(async (item) => {
            return this.dataFetcher(item, this.option);
        });
        let data = await Promise.all(promiseList);
        for (let i = 0; i < data.length; i++) {
            raw[i][key] = data[i];
        }
    }
}
class FillOnceTask extends AsyncTask {
    constructor(key, dataFecher, option) {
        super();
        this.key = key;
        this.dataFetcher = dataFecher;
        this.option = option;
    }
    async execute(raw) {
        let key = this.key;
        let data = await this.dataFetcher(raw, this.option);
        for (let i = 0; i < data.length; i++) {
            let dataItem = data[i];
            let resultItem = raw[i];
            for (let j in key) {
                let keyName = key[j];
                if (dataItem[keyName] !== undefined)
                    resultItem[keyName] = dataItem[keyName];
                else
                    resultItem[keyName] = null;
            }
        }
    }
}
//# sourceMappingURL=formater.js.map