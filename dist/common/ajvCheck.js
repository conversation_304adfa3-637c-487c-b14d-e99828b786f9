"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkParamsByAjv = checkParamsByAjv;
const ajv_1 = require("ajv");
const errorCodes_1 = require("../errorCodes");
const ajv_keywords_1 = require("ajv-keywords");
const ajv = new ajv_1.default({
    useDefaults: true, //使用默认值
    coerceTypes: true, //强制类型转换
});
(0, ajv_keywords_1.default)(ajv);
const validateGlobal = new Map();
function checkParamsByAjv(params, schema) {
    const schemaStr = JSON.stringify(schema);
    let validate;
    if (validateGlobal.get(schemaStr)) {
        validate = validateGlobal.get(schemaStr);
    }
    else {
        validate = ajv.compile(schema);
        validateGlobal.set(schemaStr, validate);
    }
    if (validate(params)) {
        return true;
    }
    else {
        const error = validate.errors[0];
        throw { code: errorCodes_1.BaseErrors.InvalidArgument.code, msg: error.instancePath + " " + error.message };
    }
}
//# sourceMappingURL=ajvCheck.js.map