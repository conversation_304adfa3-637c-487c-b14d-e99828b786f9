"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.httpPost = exports.httpGet = void 0;
exports.request = request;
exports.get = get;
exports.post = post;
const bluebird = require("bluebird");
const rq = require("request");
const config_1 = require("./config");
const logger_1 = require("../logger");
const mdRequest = rq.defaults({
    forever: true,
    timeout: config_1.requestCfg.timeout,
    json: true,
});
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function request(option) {
    return new bluebird(function (resolve, reject) {
        return mdRequest(option, function (err, res, body) {
            if (err) {
                if (err.code === "ETIMEDOUT" || err.code === "ESOCKETTIMEDOUT") {
                    logger_1.requestLogger.warn({ err: err, option: option }, "RequestOutApiTimeout");
                }
                else {
                    logger_1.requestLogger.error({ err: err, option: option }, "RequestOutApiFailed");
                }
                reject(err);
            }
            else {
                if (option.includeRes) {
                    resolve({ res, body });
                }
                else {
                    resolve(body);
                }
                if (config_1.testCfg.request_debug) {
                    logger_1.requestLogger.debug({ option, res, body }, "RequestOutApiOK");
                }
            }
        });
    });
}
function get(url, data, option) {
    return request(Object.assign({
        url: url,
        method: "GET",
        qs: data,
    }, option || {}));
}
function post(url, data, option) {
    return request(Object.assign({
        url: url,
        method: "POST",
        form: data,
    }, option || {}));
}
exports.httpGet = get;
exports.httpPost = post;
//# sourceMappingURL=request.js.map