"use strict";
const defaultCfg = require("./config.all");
let config = Object.assign({}, defaultCfg);
config.db = {
    connectionLimit: 20,
    port: 3306,
    host: "127.0.0.1",
    user: "root",
    password: "root",
    database: "nsh_md",
    charset: 'utf8mb4'
};
config.slaveDb = config.db;
config.dbTraffic = {
    enable: false,
    select: { master: 1, slave: 1 },
    debug: true,
};
config.testCfg.skip_audit = true;
config.testCfg.req_log = true;
config.testCfg.test_env = true;
config.testCfg.skip_ip_auth = true;
config.testCfg.skip_skey_check = true;
config.testCfg.db_debug = true;
config.testCfg.redis_debug = true;
config.testCfg.skip_openId_auth = true;
config.testCfg.source_map_support = true;
config.yinyunLog.printInConsole = true;
config.redis = {
    hosts: ["127.0.0.1"],
    host: "127.0.0.1",
    port: 6379,
    password: "",
    db: 0,
    no_ready_check: true,
    prefix: "nsh_md:",
};
config.MomentWeekRenqi = {
    maxAddDaily: 5,
    like: 1,
    comment: 1,
    forward: 1,
};
config.WeekRankCfg = {
    mockTime: false,
    mockDate: "2018-12-23 11:00",
};
config.officialAccount = {
    skipUrsCheck: true,
};
config.JWT_TOKEN_SECRET = "LxeEfV@8mDwMzY#NXKn6"; // jwt token 生成密钥
config.NOS_CFG.accessKey = "80daf02d6f7042b190ef3358091cc335";
config.NOS_CFG.secretKey = "2918bfd8aa064e2b8d7cc6429a9d45eb";
config.weekStat = {
    isMockDate: true,
    mockDate: "2019-06-03",
};
config.GameItem.url = "http://***************:23950/nsh_reward_http_service/claim_rewards";
config.WEEK_RENQI_REWARD_CONFIG = {
    isMockDate: false,
    mockDate: "2019-07-27",
    beginDate: "2019-07-17",
};
config.TopicAdminUrs = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
];
config.activityServerIps = ["**************", "**************", "**************"];
config.corsOrigins = [
    /^https?:\/\/localhost(:\d+)?/,
    /^https?:\/\/***************(:\d+)?/,
    /^https?:\/\/.*\.163\.com(:\d+)?/,
    /^https?:\/\/.*\.netease\.com(:\d+)?/,
];
config.Features.game_club = true;
config.guildPolemicCfg = {
    tokenExpire: 5 * 60 * 1000, // 5分钟
    skipPubToken: false,
    hotSize: 200,
    hotExpireDays: 7,
    loseHotSeconds: 14400,
    hotListExpireSeconds: 10 * 60
};
if (process.env.NODE_ENV === "test") {
    config = require("./configTest");
}
module.exports = config;
//# sourceMappingURL=config.example.js.map