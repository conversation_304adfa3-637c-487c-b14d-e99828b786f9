"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLogger = createLogger;
exports.getLogger = getLogger;
const Bunyan = require("bunyan");
const fs = require("fs");
const path = require("path");
const _ = require("lodash");
const config = require("./config");
const util_1 = require("./util");
const config_1 = require("./config");
const bunyanPoPoAlert_1 = require("./bunyanPoPoAlert");
const constants_1 = require("./constants");
const logCfg = config.log;
const LogDir = logCfg.dir || path.join(__dirname, "../../log");
const logPath = (fileName) => path.join(LogDir, fileName);
function FileStream(option) {
    this.name = option.name;
}
FileStream.prototype.write = function (rec) {
    const logFilePrefix = logCfg.prefix || "";
    const fullName = logFilePrefix + "_" + this.name + ".log";
    rec.schema = logCfg.schema || constants_1.NSH_MD_LOG_SCHEMA;
    rec.ts = (0, util_1.formatDate)(new Date(), "yyyy-MM-dd HH:mm:ss");
    rec.env = logCfg.env || "release";
    fs.appendFile(logPath(fullName), JSON.stringify(rec) + "\n", (err) => {
        if (err) {
            console.error("FileStreamWriteError", err);
        }
    });
};
function createLogger(params) {
    params = _.defaultsDeep(params, {
        serializers: {
            req: Bunyan.stdSerializers.req,
            res: Bunyan.stdSerializers.res,
            err: Bunyan.stdSerializers.err,
        },
    });
    return new Bunyan(params);
}
function getLogger(name, level) {
    const streams = [
        {
            level: level || logCfg.level || "info",
            type: "raw",
            stream: new FileStream({ name: name }),
        },
        {
            level: "error",
            type: "raw",
            stream: new FileStream({ name: name + "_error" }),
        },
        {
            level: "fatal",
            type: "raw",
            stream: new FileStream({ name: name + "_fatal" }),
        },
    ];
    if (logCfg.printInConsole && process.env.NODE_ENV !== "production") {
        streams.push({
            level: "debug",
            type: null,
            stream: process.stdout,
        });
    }
    if (config_1.bunyanPoPoAlertCfg.enable) {
        const cfg = config_1.bunyanPoPoAlertCfg;
        let atUids = "";
        if (cfg.atUids) {
            atUids = cfg.atUids.join(",");
        }
        streams.push({
            level: cfg.level || "error",
            type: "raw",
            stream: new bunyanPoPoAlert_1.BunyanPoPoAlertStream({
                webhookUrl: cfg.webhookUrl,
                secretKey: cfg.secretKey,
                project: cfg.project,
                biz: cfg.biz,
                env: cfg.env,
                minNotifyInterval: cfg.minNotifyInterval,
                timeout: cfg.timeout,
                onError: (err) => {
                    console.error(err);
                },
                atUids,
            }),
        });
    }
    return createLogger({
        name: name,
        streams: streams,
    });
}
//# sourceMappingURL=logger.js.map