"use strict";
const defaultCfg = require("./config.all");
let config = Object.assign({}, defaultCfg);
config.yinyunLog.printInConsole = false;
config.log.printInConsole = false;
config.db = {
    connectionLimit: 20,
    port: 17226,
    host: 'apps.danlu.netease.com',
    user: 'root',
    password: 'unit_test8',
    database: 'nsh_md_ut',
    charset: 'utf8mb4'
};
config.slaveDb = config.db;
config.testDb = config.db;
config.testCfg.skip_audit = true;
config.testCfg.test_env = true;
config.testCfg.skip_ip_auth = true;
config.testCfg.skip_skey_check = true;
config.testCfg.skip_level = false;
config.testCfg.skip_token = true;
config.testCfg.skip_neDun_verify = true;
config.testCfg.no_forward_proxy = true;
config.redis = {
    hosts: ['127.0.0.1'],
    host: '127.0.0.1',
    port: 6379,
    password: '',
    db: 0,
    no_ready_check: true,
    prefix: 'nsh_md_ut:'
};
config.Features.game_club = true;
//@ts-ignore
config.cookieCfg = {
    path: '/'
};
module.exports = config;
//# sourceMappingURL=configTest.example.js.map