"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisLock = exports.OperationInterval = exports.StringCache = void 0;
const redis_1 = require("./redis");
const bluebird = require("bluebird");
class StringCache {
    constructor() {
        this.expire = null;
    }
    async get() {
        const result = await (0, redis_1.getRedis)().existsAsync(this.key);
        if (result === redis_1.IExistResult.NotExist) {
            const str = await this.refresh();
            return str;
        }
        else {
            const str = await (0, redis_1.getRedis)().getAsync(this.key);
            return str;
        }
    }
    async refresh() {
        const content = await this.fetchDataSource();
        await (0, redis_1.getRedis)().setAsync(this.key, content);
        if (this.expire !== null) {
            await (0, redis_1.getRedis)().expireAsync(this.key, this.expire);
        }
        return content;
    }
}
exports.StringCache = StringCache;
class OperationInterval {
    constructor(key) {
        this.key = key;
    }
    static create(key) {
        return new this(key);
    }
    async locked(interval = 500) {
        const ret = await (0, redis_1.getRedis)().setAsync(this.key, "1", redis_1.ExpireType.PX, interval, "NX" /* SET_OPTION.NX */);
        return !ret;
    }
    async unlock() {
        await (0, redis_1.getRedis)().del(this.key);
    }
}
exports.OperationInterval = OperationInterval;
class RedisLock {
    static fullKey(key) {
        return key + ":lock";
    }
    static async lock(key, ttl, ownerId) {
        const k = this.fullKey(key);
        const ret = await (0, redis_1.getRedis)().setAsync(k, ownerId, redis_1.ExpireType.PX, ttl, "NX" /* SET_OPTION.NX */);
        return !!ret;
    }
    static async unLock(key, ownerId) {
        const k = this.fullKey(key);
        const ret = await (0, redis_1.getRedis)().getAsync(k);
        if (ret && ret === ownerId) {
            await (0, redis_1.getRedis)().delAsync(k);
            return true;
        }
        return false;
    }
    static async optimistic(key, ownerId, ttl, maxAttempts, wait) {
        let attempts = 0;
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        async function tryLock() {
            attempts += 1;
            const ret = await self.lock(key, ttl, ownerId);
            if (ret) {
                return ret;
            }
            else {
                if (attempts >= maxAttempts) {
                    return false;
                }
                else {
                    await bluebird.delay(wait);
                    const ret = await tryLock();
                    return ret;
                }
            }
        }
        const ret = await tryLock();
        return ret;
    }
}
exports.RedisLock = RedisLock;
//# sourceMappingURL=cacheUtil.js.map