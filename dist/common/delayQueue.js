"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DelayQueue = void 0;
const redis_1 = require("./redis");
class JobPool {
    constructor(keyName) {
        this.keyName = keyName;
    }
    async addJob(job) {
        const redis = await (0, redis_1.getRedis)();
        const jobDetail = JSON.stringify(job);
        return redis.hsetAsync(this.keyName, "" + job.id, jobDetail);
    }
    async updateJob(jobId, body) {
        const job = await this.getJobById(jobId);
        if (job) {
            if (job.body) {
                job.body = { ...job.body, ...body };
            }
            else {
                job.body = body;
            }
            const ret = await this.addJob(job);
            return ret;
        }
        else {
            return null;
        }
    }
    async getJobById(jobId) {
        const redis = await (0, redis_1.getRedis)();
        const jobDetail = await redis.hgetAsync(this.keyName, jobId);
        const job = JSON.parse(jobDetail);
        return job;
    }
    async removeJob(jobId) {
        const redis = await (0, redis_1.getRedis)();
        return redis.hdelAsync(this.keyName, jobId);
    }
}
class DelayBucket {
    constructor(keyName) {
        this.keyName = keyName;
    }
    async addJob(job) {
        const redis = await (0, redis_1.getRedis)();
        const result = await redis.zaddAsync(this.keyName, job.runAt, job.id);
        return result;
    }
    async getTriggerJobIds(now) {
        const redis = await (0, redis_1.getRedis)();
        //@ts-ignore
        const jobIds = (await redis.zrangebyscoreAsync(this.keyName, 0, now, "LIMIT", 0, 1));
        return jobIds;
    }
    async removeJob(jobId) {
        const redis = await (0, redis_1.getRedis)();
        const result = redis.zremAsync(this.keyName, jobId);
        return result;
    }
}
class ReadyQueue {
    constructor(keyName) {
        this.keyName = keyName;
    }
    async add(jobId) {
        const redis = await (0, redis_1.getRedis)();
        const result = await redis.lpushAsync(this.keyName, jobId);
        return result;
    }
    async pop() {
        const redis = await (0, redis_1.getRedis)();
        const result = await redis.lpopAsync(this.keyName);
        return result;
    }
}
class DelayQueue {
    constructor(keyName) {
        this.jobPool = new JobPool(keyName + "_job_pool");
        this.delayBucket = new DelayBucket(keyName + "_delay_bucket");
        this.readyQueue = new ReadyQueue(keyName + "_ready_queue");
    }
    async addJob(job) {
        const r1 = await this.jobPool.addJob(job);
        const r2 = await this.delayBucket.addJob(job);
        return [r1, r2];
    }
    async add(job) {
        return this.addJob(job);
    }
    async update(jobId, body) {
        const ret = await this.jobPool.updateJob(jobId, body);
        return ret;
    }
    async remove(jobId) {
        return this.jobPool.removeJob(jobId);
    }
    async getJob(jobId) {
        return this.jobPool.getJobById(jobId);
    }
    async onTick() {
        const now = Date.now();
        const jobIds = await this.delayBucket.getTriggerJobIds(now);
        for (const id of jobIds) {
            await this.readyQueue.add(id);
            await this.delayBucket.removeJob(id);
        }
    }
    async popReady() {
        const jobId = await this.readyQueue.pop();
        if (jobId) {
            const job = this.jobPool.getJobById(jobId);
            this.jobPool.removeJob(jobId);
            return job;
        }
    }
}
exports.DelayQueue = DelayQueue;
//# sourceMappingURL=delayQueue.js.map