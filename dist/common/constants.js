"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ctxCorpAuthInfoKey = exports.NSH_MD_LOG_SCHEMA = exports.HuaijiuMinServerId = exports.CheatSkeyInTestEnv = exports.TopicListCacheTime = exports.TopicListSize = exports.PlayerAnnalAllWeek = exports.ServerAnnalAllWeek = exports.DEFAULT_EXPIRE = exports.ServerAnnalAction = exports.RENQI_MOMENT_IMAGE_LIMIT = exports.EHighLight = exports.MAX_AVATAR_AUDIT_DURATION = exports.IdentityAdminKey = exports.MomentDetailExportCfg = exports.TopicExportCfg = exports.MAX_CLUB_SIZE = exports.CLUB_CERTIFIER_PLAYER_DEFAULT_TAG = exports.NOS_ALLOW_EXT_NAMES = exports.NOS_DEFAULT_EXPIRES = exports.NOS_MIME_LIMIT = exports.CLUB_PLAYER_DEFAULT_AVATAR = exports.CLUB_DEFAULT_ICON = exports.ApplyCommanderSmsName = exports.noRankDesc = exports.ClubGender = exports.PAGE_SCHEMA = exports.RoleTypeFilter = exports.ClubCfg = exports.GuildPolemicStyle = exports.EShowPhoto = exports.EPicMediaType = exports.EAuditStatus = exports.ONE_WEEK_SECONDS = exports.ONE_DAY_SECONDS = exports.ONE_HOUR_SECONDS = exports.TEN_MIN_SECONDS = exports.ONE_MIN_SECONDS = exports.MAX_MOMENT_TEXT_SIZE = exports.RecentVisitorSize = exports.RecommendedFollowView = exports.AuditStatues = exports.ErrorCodes = exports.TABLE_NAMES = void 0;
exports.TABLE_NAMES = {
    profile: "nsh_profile",
    roleInfo: "nsh_roleinfo",
    follow: "nsh_follow",
    message: "nsh_message",
    moment: "nsh_moment",
    momentForward: "nsh_moment_forward",
    momentLike: "nsh_moment_like",
    momentTopic: "nsh_topic_moment",
    topic: "nsh_topic",
    comment: "nsh_comment",
    commentLike: "nsh_comment_like",
    inform: "nsh_inform",
    event: "nsh_event",
    flower: "nsh_event_flower",
    activityTopic: "nsh_activity_topic",
    activityTopicMoment: "nsh_activity_topic_moment",
    officialAccount: "nsh_official_account",
    officialAccountMoment: "nsh_official_account_moment",
    officialAccountUnFollow: "nsh_official_account_unfollow",
    officialAccountAdmin: "nsh_official_account_admin",
    identity: "nsh_identity",
    identityBind: "nsh_identity_bind",
    honor: "nsh_honor",
    honorBind: "nsh_honor_bind",
    hongyeCard: "nsh_card_hongye",
    hongyeCardCollect: "nsh_card_hongye_collect",
    gardenPhoto: "nsh_garden_photo",
    transfer: "nsh_transfer",
    cardComment: "nsh_card_comment",
    cardCommentLike: "nsh_card_comment_like",
    cardInform: "nsh_card_inform",
    cardLike: "nsh_card_like",
    eventFlower: "nsh_event_flower",
    garden: "nsh_garden",
    garden_ban_message: "nsh_garden_ban_message",
    garden_message: "nsh_garden_message",
    garden_photo: "nsh_garden_photo",
    guild_polemic: "nsh_guild_polemic",
    guild_polemic_like: "nsh_guild_polemic_like",
    club: "nsh_club",
    clubMember: "nsh_club_member",
    clubUser: "nsh_club_user",
    certifiedPlayerApply: "nsh_certified_player_apply",
    certifiedPlayerApplyVideo: "nsh_certified_player_apply_video",
    certifiedPlayerApplyImage: "nsh_certified_player_apply_image",
    certifiedPlayer: "nsh_certified_player",
    clubHonor: "nsh_club_honor",
    clubMatch: "nsh_club_match",
    clubYuezhan: "nsh_club_yuezhan",
    clubPlayerMatch: "nsh_club_player_match",
    clubHonorBind: "nsh_club_honor_bind",
    clubPlayerHonorBind: "nsh_club_player_honor_bind",
    clubPlayerTag: "nsh_club_player_tag",
    clubBindRole: "nsh_club_bind_role",
    clubCommanderRecommend: "nsh_club_commander_recommend",
    openIdAdmin: "nsh_openid_admin",
    competition: "nsh_competition",
    transferSignUp: "nsh_transfer_signup",
    wishList: "nsh_wishlist",
    copyMoment: "nsh_copy_moment",
    fcmPi: "nsh_fcm_pi",
    serverAnnalEvent: "nsh_server_annal_event",
    serverAnnalEventAction: "nsh_server_annal_event_action",
    serverAnnalFengyun: "nsh_server_annal_fengyun",
    playerAnnalEvent: "nsh_player_annal_event",
    fakeAdult: "BlackAdultAsMinor",
    // 小岛玩法相关数据库
    IsletCheckInPhoto: "nsh_islet_checkin_photo",
    IsletCheckInRank: "nsh_islet_checkin_rank",
    IsletEventLog: "nsh_islet_event_log",
    IsletInteractRank: "nsh_islet_interact_rank",
    IsletLoadingImage: "nsh_islet_loading_image",
    IsletSticker: "nsh_islet_sticker",
    // 表情包相关书库
    Meme: "nsh_meme",
    // 星巫相关数据库
    astrologyUser: "nsh_astrology_user",
    astrologyUserDailyForecast: "nsh_astrology_user_daily_forecast",
    astrologyCommentRank: "nsh_astrology_comment_rank",
    astrologyBazaarPost: "nsh_astrology_bazaar_post",
    astrologyBazaarComment: "nsh_astrology_bazaar_comment",
    astrologyBazaarRating: "nsh_astrology_bazaar_rating",
    astrologyPostTopic: "nsh_astrology_post_topic",
    astrologyPostTopicDailyHot: "nsh_astrology_post_topic_daily_hot",
    astrologyPostDailyHot: "nsh_astrology_post_daily_hot",
};
var ErrorCodes;
(function (ErrorCodes) {
    ErrorCodes[ErrorCodes["InvalidParams"] = -2] = "InvalidParams";
})(ErrorCodes || (exports.ErrorCodes = ErrorCodes = {}));
var AuditStatues;
(function (AuditStatues) {
    AuditStatues[AuditStatues["Auditing"] = 0] = "Auditing";
    AuditStatues[AuditStatues["Pass"] = 1] = "Pass";
    AuditStatues[AuditStatues["Reject"] = -1] = "Reject";
})(AuditStatues || (exports.AuditStatues = AuditStatues = {}));
var RecommendedFollowView;
(function (RecommendedFollowView) {
    RecommendedFollowView[RecommendedFollowView["NONE"] = 0] = "NONE";
    RecommendedFollowView[RecommendedFollowView["NEED_VIEW"] = 1] = "NEED_VIEW";
    RecommendedFollowView[RecommendedFollowView["VIEWED"] = 2] = "VIEWED";
})(RecommendedFollowView || (exports.RecommendedFollowView = RecommendedFollowView = {}));
exports.RecentVisitorSize = 50;
exports.MAX_MOMENT_TEXT_SIZE = 120;
exports.ONE_MIN_SECONDS = 60;
exports.TEN_MIN_SECONDS = 600;
exports.ONE_HOUR_SECONDS = 3600;
exports.ONE_DAY_SECONDS = 1 * 24 * 3600;
exports.ONE_WEEK_SECONDS = 7 * 24 * 3600;
var EAuditStatus;
(function (EAuditStatus) {
    EAuditStatus[EAuditStatus["Init"] = 0] = "Init";
    EAuditStatus[EAuditStatus["PASS"] = 1] = "PASS";
    EAuditStatus[EAuditStatus["Reject"] = -1] = "Reject";
    EAuditStatus[EAuditStatus["Auditing"] = 2] = "Auditing";
})(EAuditStatus || (exports.EAuditStatus = EAuditStatus = {}));
var EPicMediaType;
(function (EPicMediaType) {
    EPicMediaType["Image"] = "0";
    EPicMediaType["Video"] = "1";
    EPicMediaType["GIF"] = "2";
})(EPicMediaType || (exports.EPicMediaType = EPicMediaType = {}));
var EShowPhoto;
(function (EShowPhoto) {
    EShowPhoto[EShowPhoto["Show"] = 1] = "Show";
    EShowPhoto[EShowPhoto["Hide"] = 0] = "Hide";
})(EShowPhoto || (exports.EShowPhoto = EShowPhoto = {}));
var GuildPolemicStyle;
(function (GuildPolemicStyle) {
    GuildPolemicStyle[GuildPolemicStyle["horizontal"] = 1] = "horizontal";
    GuildPolemicStyle[GuildPolemicStyle["vertical"] = 2] = "vertical";
})(GuildPolemicStyle || (exports.GuildPolemicStyle = GuildPolemicStyle = {}));
exports.ClubCfg = {
    maxSize: 200,
};
var RoleTypeFilter;
(function (RoleTypeFilter) {
    RoleTypeFilter[RoleTypeFilter["All"] = 1] = "All";
    RoleTypeFilter[RoleTypeFilter["Operator"] = 2] = "Operator";
    RoleTypeFilter[RoleTypeFilter["Commander"] = 3] = "Commander";
    RoleTypeFilter[RoleTypeFilter["BothCertified"] = 4] = "BothCertified";
})(RoleTypeFilter || (exports.RoleTypeFilter = RoleTypeFilter = {}));
exports.PAGE_SCHEMA = {
    page: { type: Number, min: 1, default: 1 },
    pageSize: { type: Number, min: 1, max: 20, default: 10 },
};
var ClubGender;
(function (ClubGender) {
    ClubGender[ClubGender["MALE"] = 1] = "MALE";
    ClubGender[ClubGender["FEMALE"] = 2] = "FEMALE";
})(ClubGender || (exports.ClubGender = ClubGender = {}));
exports.noRankDesc = "暂无";
exports.ApplyCommanderSmsName = "nsh_julebu";
exports.CLUB_DEFAULT_ICON = "https://hi-163-nsh.nosdn.127.net/club/images/club_detault_icon.jpg";
exports.CLUB_PLAYER_DEFAULT_AVATAR = "https://hi-163-nsh.nosdn.127.net/club/images/club_player_default_avatar.jpg";
exports.NOS_MIME_LIMIT = "image/jpeg;image/png;video/mp4;video/quicktime";
exports.NOS_DEFAULT_EXPIRES = 10; // 10分钟
exports.NOS_ALLOW_EXT_NAMES = ["jpg", "jpeg", "png", "mp4", "mov"];
exports.CLUB_CERTIFIER_PLAYER_DEFAULT_TAG = { name: "官方认证选手", rare: "N" };
exports.MAX_CLUB_SIZE = 5000;
exports.TopicExportCfg = {
    title: [
        "心情ID",
        "角色ID",
        "角色名",
        "心情文本",
        "图片列表",
        "视频列表",
        "状态",
        "点赞数",
        "评论数",
        "转发数",
        "创建时间",
    ],
    titleKey: [
        "id",
        "roleId",
        "roleName",
        "text",
        "imgList",
        "videoList",
        "status",
        "likeCount",
        "commentCount",
        "forwardCount",
        "createTime",
    ],
};
exports.MomentDetailExportCfg = {
    title: ["心情ID", "角色ID", "角色名", "文本", "类型", "创建时间"],
    titleKey: ["momentId", "roleId", "roleName", "text", "action", "createTime"],
};
exports.IdentityAdminKey = "identity_admin_permission";
exports.MAX_AVATAR_AUDIT_DURATION = 2 * 3600 * 1000;
var EHighLight;
(function (EHighLight) {
    EHighLight[EHighLight["Normal"] = 1] = "Normal";
    EHighLight[EHighLight["HighLight"] = 2] = "HighLight";
})(EHighLight || (exports.EHighLight = EHighLight = {}));
/**
 * 上周周人气影响动态附带图片数量
 */
exports.RENQI_MOMENT_IMAGE_LIMIT = [
    { minRenqi: 0, count: 4 },
    { minRenqi: 200, count: 8 },
];
var ServerAnnalAction;
(function (ServerAnnalAction) {
    ServerAnnalAction[ServerAnnalAction["UpVote"] = 1] = "UpVote";
    ServerAnnalAction[ServerAnnalAction["DownVote"] = 2] = "DownVote";
})(ServerAnnalAction || (exports.ServerAnnalAction = ServerAnnalAction = {}));
exports.DEFAULT_EXPIRE = 5 * 60; // 5 min
exports.ServerAnnalAllWeek = "all";
exports.PlayerAnnalAllWeek = "all";
exports.TopicListSize = 50;
exports.TopicListCacheTime = 10 * 60; // 10 分钟
exports.CheatSkeyInTestEnv = "CHEAT_SKEY_ONLY_FOR_TEST";
exports.HuaijiuMinServerId = 5000;
exports.NSH_MD_LOG_SCHEMA = "com.netease.leihuo.ccc.base.model.tables.v1.NshMdLog";
/**
 * corp登录信息在Context中的key
 */
exports.ctxCorpAuthInfoKey = "ctxCorpAuthInfoKey";
//# sourceMappingURL=constants.js.map