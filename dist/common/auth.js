"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthClass = exports.Session = void 0;
const config = require("./config");
const jwt = require("jwt-simple");
const redis_1 = require("./redis");
const constants_1 = require("./constants");
const config_1 = require("./config");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("common/auth");
const secret = config.JWT_TOKEN_SECRET;
var LoginFailCode;
(function (LoginFailCode) {
    LoginFailCode[LoginFailCode["InvalidSkey"] = -2] = "InvalidSkey";
    LoginFailCode[LoginFailCode["IdMisMatch"] = -3] = "IdMisMatch";
    LoginFailCode[LoginFailCode["NoSession"] = -4] = "NoSession";
    LoginFailCode[LoginFailCode["SkeyExpired"] = -5] = "SkeyExpired";
})(LoginFailCode || (LoginFailCode = {}));
class Session {
    static async get(id) {
        let key = this.getSessionKey(id);
        let session = await (0, redis_1.getRedisForSkey)().hgetallAsync(key);
        if (session) {
            await (0, redis_1.getRedisForSkey)().hsetAsync(key, "access", Date.now());
        }
        return session;
    }
    static set(id, data) {
        let key = this.getSessionKey(id);
        return (0, redis_1.getRedisForSkey)().hmsetAsync(key, data);
    }
    static getSessionKey(id) {
        return "sess:" + id;
    }
    static setExpire(id, seconds) {
        let key = this.getSessionKey(id);
        return (0, redis_1.getRedisForSkey)().expireAsync(key, seconds);
    }
    static remove(id) {
        let key = this.getSessionKey(id);
        return (0, redis_1.getRedisForSkey)().delAsync(key);
    }
}
exports.Session = Session;
class AuthClass {
    static async start(idType, params, sessData) {
        let time = sessData.time || Date.now();
        let idVal = this.getIdVal(idType, params);
        let sKey = jwt.encode([time, idType, idVal], secret);
        sessData.time = time;
        let sessKey = this.getSessionKey(idType, idVal);
        await Session.set(sessKey, sessData);
        await Session.setExpire(sessKey, AuthClass.ONE_DAY_SECONDS * (idType === "role" ? 60 : 1)); // 设置session过期时间
        return { skey: sKey.replace(AuthClass.HEADER, "") };
    }
    static stop(idType, params) {
        let sessKey = this.getSessionKey(idType, this.getIdVal(idType, params));
        return Session.remove(sessKey);
    }
    static getSessionKey(idType, idVal) {
        let sessKey = idType + ":" + idVal;
        return sessKey;
    }
    static getIdVal(idType, params) {
        return "" + params[idType + "id"];
    }
    static getVal(idKey, params) {
        return "" + params[idKey];
    }
    static async check(params, idKey) {
        let sKey = AuthClass.HEADER + params.skey;
        let self = this;
        try {
            if (config_1.testCfg.cheat_skey_enable && params.skey === constants_1.CheatSkeyInTestEnv) {
                logger.warn({ params, warn: "MakeSureNotRunInProduction" }, "CheatSkeyEnabled");
                return { validate: true, session: { roleId: params.roleid } };
            }
            const decoded = jwt.decode(sKey, secret, false, "HS256");
            const skeyTime = decoded[0] + "";
            const idType = decoded[1];
            const idVal = "" + decoded[2];
            const sessionKey = self.getSessionKey(idType, idVal);
            const session = await Session.get(sessionKey);
            let idValFromParams = idKey ? self.getVal(idKey, params) : self.getIdVal(idType, params);
            if (idValFromParams !== idVal) {
                logger.warn("IdMisMatch", { idVal, params, idValFromParams, idKey });
                return { validate: false, error: { code: LoginFailCode.IdMisMatch, msg: "Id mismatch" } };
            }
            else {
                if (!session) {
                    return { validate: false, error: { code: LoginFailCode.NoSession, msg: "No Session" } };
                }
                else if (session.time !== skeyTime && skeyTime !== "LONG_TERM") {
                    // 4、skey已过期
                    return { validate: false, error: { code: LoginFailCode.SkeyExpired, msg: "Skey outDated" } };
                }
                else {
                    return { validate: true, session: session };
                }
            }
        }
        catch (ex) {
            return { validate: false, error: { code: LoginFailCode.InvalidSkey, msg: "Invalid Skey" } };
        }
    }
}
exports.AuthClass = AuthClass;
AuthClass.ONE_DAY_SECONDS = 24 * 3600;
AuthClass.HEADER = jwt.encode("", secret).split(".")[0] + ".";
//# sourceMappingURL=auth.js.map