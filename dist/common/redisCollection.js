"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisSet = exports.FixedStringList = exports.Queue = void 0;
const redis_1 = require("./redis");
const bluebird = require("bluebird");
const _ = require("lodash");
const util_1 = require("../common/util");
let redis = (0, redis_1.getRedis)();
class Queue {
    constructor(options) {
        this.key = options.key;
        this.md5Prefix = options.md5Prefix || 'md5_key:';
    }
    push(obj) {
        const self = this;
        const str = JSON.stringify(obj);
        const contentMd5 = (0, util_1.hexMd5)(str);
        const reHashKey = this.md5Prefix + contentMd5;
        return redis.rpushAsync(self.key, reHashKey).then(len => {
            return redis.setAsync(self.getReHashFullKey(reHash<PERSON>ey), str);
        });
    }
    isReHashKey(str) {
        return _.isString(str) && str.startsWith(this.md5Prefix);
    }
    getReHashFullKey(reHashKey) {
        return this.key + ':' + reHashKey;
    }
    len() {
        return redis.llenAsync(this.key);
    }
    getThenDelete(key) {
        let content = null;
        return redis.getAsync(key)
            .then(s => {
            content = s;
            return redis.delAsync(key);
        }).then(() => {
            return content;
        });
    }
    pop() {
        let self = this;
        return redis.lpopAsync(this.key)
            .then(ele => {
            if (self.isReHashKey(ele)) {
                return self.getThenDelete(self.getReHashFullKey(ele));
            }
            else {
                return ele;
            }
        }).then(raw => {
            if (raw) {
                return JSON.parse(raw);
            }
            else {
                return raw;
            }
        });
    }
    async peekTop(size) {
        const data = await (0, redis_1.getRedis)().lrangeAsync(this.key, 0, size);
        const list = [];
        const self = this;
        for (let k of data) {
            let actualKey = k;
            if (self.isReHashKey(k)) {
                actualKey = self.getReHashFullKey(k);
            }
            const content = await (0, redis_1.getRedis)().getAsync(actualKey);
            if (content) {
                let item = (0, util_1.getJsonInfo)(content, null);
                if (!item) {
                    list.push(item);
                }
            }
        }
        return list;
    }
    popx(n) {
        let self = this;
        return self.len().then(length => {
            let len = Math.min(n, length);
            return bluebird.map(new Array(len), () => {
                return self.pop();
            }, { concurrency: 5 });
        }).then(list => {
            return _.filter(list, ele => !!ele); // pop 过程中可能被其他进程消费导致 pop 出空元素
        });
    }
}
exports.Queue = Queue;
// 固定列表长度字符串列表
class FixedStringList {
    constructor(options) {
        this.key = options.key;
        this.size = options.size;
        this.redis = options.redis || redis;
    }
    push(str) {
        const self = this;
        const redis = self.redis;
        return redis.lpushAsync(this.key, str)
            .then(() => {
            redis.ltrimAsync(self.key, 0, self.size - 1);
        });
    }
    getAll() {
        return this.redis.lrangeAsync(this.key, 0, this.size - 1);
    }
    remove(str) {
        return this.redis.lremAsync(this.key, 0, str);
    }
}
exports.FixedStringList = FixedStringList;
class RedisSet {
    constructor(key) {
        this.key = key;
    }
    async add(member) {
        return (0, redis_1.getRedis)().saddAsync(this.key, member);
    }
    async remove(member) {
        return (0, redis_1.getRedis)().sremAsync(this.key, member);
    }
    async addBatch(members) {
        return (0, redis_1.getRedis)().saddAsync(this.key, ...members);
    }
    async isMember(member) {
        let result = await (0, redis_1.getRedis)().sismemberAsync(this.key, member);
        return result === redis_1.IExistResult.Exist;
    }
}
exports.RedisSet = RedisSet;
//# sourceMappingURL=redisCollection.js.map