"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheInRedis = cacheInRedis;
exports.smartMemorize = smartMemorize;
let _ = require("lodash");
let uuid = require("node-uuid");
const util = require("./util");
const redis_1 = require("../common/redis");
const bluebird = require("bluebird");
const util_1 = require("util");
const util_2 = require("./util");
const constants_1 = require("./constants");
const config_1 = require("./config");
const REDIS_KEY_NOT_EXIST_OR_EXPIRED = -2;
/**
 * generate a cache version function
 * @param func
 * @param {object} option
 * @param {(string|callback)} option.cacheKey
 * @param {number} option.expire  expire time in milliseconds
 * @param {object} option.context
 * @param {callback} option.onUpdateValue
 * @return {callback}
 */
function cacheInRedis(func, option) {
    let cacheKey;
    option = _.defaults(option, { cacheKey: uuid() });
    let redis = option.redis || (0, redis_1.getRedis)();
    if (_.isString(option.cacheKey)) {
        cacheKey = option.cacheKey;
    }
    function cacheVersionFunc() {
        let args = _.toArray(arguments);
        let context = option.context || null;
        if (_.isFunction(option.cacheKey)) {
            cacheKey = option.cacheKey.apply(context, args);
        }
        return redis.ttlAsync(cacheKey).then(function (ttl) {
            if (ttl === REDIS_KEY_NOT_EXIST_OR_EXPIRED) {
                return bluebird.resolve(func.apply(context, args)).then(function (value) {
                    if (_.isFunction(option.onUpdateValue)) {
                        let uvArgs = _.concat([value], args);
                        option.onUpdateValue.apply(context, uvArgs);
                    }
                    if (value === null || value === undefined) {
                        value = "";
                    }
                    var setArgs = [cacheKey, JSON.stringify(value)];
                    if (option.expire && option.expire > 0) {
                        setArgs.push("PX");
                        setArgs.push(option.expire);
                    }
                    return redis.setAsync.apply(redis, setArgs).then(function () {
                        return value;
                    });
                });
            }
            else {
                return redis.getAsync(cacheKey).then(function (value) {
                    return util.getJsonInfo(value);
                });
            }
        });
    }
    return cacheVersionFunc;
}
function smartMemorize(fn, option) {
    const seconds = option.expireSeconds || constants_1.DEFAULT_EXPIRE;
    return async function (...args) {
        if (config_1.smartMemorizeCfg.disableCache) {
            const refreshValue = await fn(...args);
            return refreshValue;
        }
        const key = option.keyGen(...args);
        let cacheValueStr = await (0, redis_1.getRedis)().getAsync(key);
        if ((0, util_1.isNullOrUndefined)(cacheValueStr)) {
            const newValue = await fn(...args);
            const newValueStr = JSON.stringify(newValue);
            await (0, redis_1.getRedis)().setAsync(key, newValueStr, redis_1.ExpireType.EX, seconds);
            return newValue;
        }
        else {
            const cacheValue = (0, util_2.getJsonInfo)(cacheValueStr);
            return cacheValue;
        }
    };
}
//# sourceMappingURL=cacheService.js.map