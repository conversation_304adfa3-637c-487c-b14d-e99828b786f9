"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.pyq = exports.qnm = exports.Constants = void 0;
var config = require('./config');
var util = require('./util');
var _ = require('lodash');
const textReglist_1 = require("../data/textReglist");
const util_1 = require("./util");
const constants_1 = require("./constants");
var appIdMap = {
    qn: 8001,
    qnm: 8100 /*,
    tx3: 8002,
    dh2: 8004,
    dh3: 8005*/
};
exports.Constants = {
    STATUS_NORMAL: 0,
    STATUS_READ: 1,
    STATUS_DELETE: -1,
    STATUS_REJECT: -2,
    STATUS_AUDIT_INIT: 0,
    STATUS_AUDIT_PASS: 1,
    STATUS_AUDIT_REJECT: -1,
    STATUS_AUDIT_PICK: 2,
    GM_ROLE_ID: 1002,
    JL_ROLE_ID: 1005, // 金陵情报官
};
exports.qnm = {
    api: require('./config').QNM_API,
    getRoleInfoTable: function (serverId) {
        if (serverId < 500 || serverId >= 1000) { // 小于500是网易服。大于等于1000是苹果服，有urs信息
            return config.TABLE_CFG.qnm.roleInfo;
        }
        return config.TABLE_CFG.qnm.roleInfo_other;
    },
    getRolePropTable: function (serverId) {
        if (serverId < 500 || serverId >= 1000) {
            return config.TABLE_CFG.qnm.roleProp;
        }
        return config.TABLE_CFG.qnm.roleProp_other;
    },
    getServerHash: function () {
        var defer = require('q').defer();
        require('../services/nsh_md/server/list').getHash(function (hash) {
            defer.resolve(hash);
        });
        return defer.promise;
    },
    fmtTime: function (time) {
        return time && (time + '').length === 10 ? time * 1000 : time;
    },
    getRoleId: function (roleId, getRaw) {
        return roleId;
    },
    /**
     * 限制恶意构造重复文本通过卡死正则
     * example "aaabbbccc" => "aabbcc"
     */
    fixTooLongRepeatStr(str) {
        let repeat = 1;
        let newStr = '';
        let preChar = '';
        for (let c of str) {
            if (preChar === c) {
                repeat += 1;
            }
            else {
                repeat = 1;
            }
            if (repeat <= 2) {
                newStr += c;
            }
            preChar = c;
        }
        return newStr;
    },
    textFilter: function (str) {
        var RegList = textReglist_1.TextRegList;
        str = str.replace(/<link\s(.+?)=(.+?)>/ig, ''); // 链接不参与不过滤
        str = exports.qnm.fixTooLongRepeatStr(str);
        if (!str) {
            return false;
        }
        for (var i = 0, l = RegList.length; i < l; i++) {
            var item = RegList[i], expr = item[0], flag = item[1], reg = new RegExp(expr, flag);
            if (reg.test(str)) {
                return '' + i;
            }
        }
        return false;
    }
};
exports.pyq = {
    getAllowHash: function (roleInfo) {
        let friendList = (0, util_1.csvStrToIntArray)(roleInfo.FriendList);
        let followingList = roleInfo.FollowingList || [];
        let privacy = roleInfo.Privacy || { limitComment: false };
        let limitComment = privacy.limitComment;
        if (limitComment) {
            let allowHash = {};
            for (let id of friendList) {
                allowHash[id] = 1;
            }
            for (let id of followingList) {
                allowHash[id] = 1;
            }
            return allowHash;
        }
        else {
            return undefined;
        }
    },
    canComment: function (role, roleId, allowHash) {
        allowHash = allowHash || exports.pyq.getAllowHash(role);
        return !!(roleId == role.RoleId || !allowHash || allowHash[roleId]);
    },
    setPhotoView: function (item, roleId) {
        var rid = item.RoleId, photo = item.Photo, showPhoto = item.ShowPhoto, photoAudit = item.PhotoAudit;
        if (!rid) {
            return;
        }
        photo = getPhotoView(photo, showPhoto, photoAudit, rid, roleId);
        item.Photo = photo;
        return photo;
    },
    setImgListView: function (item, roleId) {
        var rid = item.RoleId, imgList = item.ImgList, imgAudit = item.ImgAudit;
        var list = getImgListView(imgList, imgAudit, rid, roleId, '250y160');
        item.ImgList = list;
        return list;
    },
    getImgList: function (imgList) {
        return getImgList(imgList, '250x250&crop=0_45_250_160');
    },
    checkBanState: function (req, res, state, callback) {
        var msgHash = {
            Img: '上传图片',
            Photo: '上传头像',
            Tape: '上传录音签名',
            Signature: '上传空间个人签名',
            Moment: '发布或评论朋友圈状态',
            //            Comment: '评论朋友圈',
            Message: '空间留言',
            Location: '修改地理位置'
        };
        var profile = require('../services/profile');
        profile.getBanState(req.params.roleid, state).then(callback).fail(function (banned) {
            banned === true
                ? res.send({ code: 1, msg: '您已被管理员禁止' + msgHash[state] })
                : res.send(util.response(banned));
        });
    }
};
var md = {
    getAvatarView: function (photo) {
        return photo + '?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc='; // 审核中水印
    },
    setAvatarView: function (item, roleId) {
        var rid = item.UserId || item.ID, photo = item.Avatar, photoAudit = item.AvaAuthStatus;
        if (!rid) {
            return;
        }
        photo = getPhotoView(photo, 1, photoAudit, rid, roleId);
        item.Avatar = photo;
        return photo;
    },
    setImgListView: function (item, roleId) {
        var rid = item.UserId, imgList = item.ImgList, imgAudit = item.ImgAudit;
        var list;
        if (_.isString(imgList)) {
            list = getImgListView(imgList, imgAudit, rid, roleId, '150y150');
            item.ImgList = list;
        }
        return list;
    },
    getTopic: function (text) {
        if (text && /^#[cC][0-9a-fA-F]{6}/.test(text)) {
            return null;
        }
        if (text && /#([^0-9RGBKYWPQrnuU#][^#\s]{1,19})#/.test(text)) {
            return RegExp.$1;
        }
        return null;
    }
};
function getPhotoView(photo, showPhoto, photoAudit, viewRoleId, photoRoleId) {
    if (showPhoto === constants_1.EShowPhoto.Show) {
        if (photoAudit === constants_1.EAuditStatus.Reject) {
            return "";
        }
        else {
            return photo;
        }
    }
    else {
        return "";
    }
}
const parseUrl = require('url').parse;
function pipeNosUrl(url, ...rest) {
    const urlObj = parseUrl(url);
    const nosArgs = _.slice(arguments, 1);
    let pipeArgs = nosArgs;
    let concatUrl = url;
    if (!urlObj.query) {
        const firstArg = _.first(nosArgs);
        pipeArgs = _.tail(nosArgs);
        if (firstArg) {
            concatUrl += '?' + firstArg;
        }
    }
    if (!_.isEmpty(pipeArgs)) {
        _.forEach(pipeArgs, arg => {
            concatUrl += '%7c' + arg; // 管道”|”在URL中编码为”%7c”
        });
    }
    return concatUrl;
}
function getImgListView(imgList, imgAudit, rid, roleId, size) {
    var list = [];
    var crop = size.indexOf('crop') >= 0; // 手游图片需要裁剪
    let denyPic = 'http://hi-163-common.nosdn.127.net/common/deny.jpg';
    var skipAudit = config.testCfg.skip_audit;
    imgList = imgList ? imgList.split(',') : [];
    imgAudit = imgAudit ? imgAudit.split(',') : [];
    var thumbArgs = 'imageView&thumbnail=' + size;
    var waterMarkSuffix = 'watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=';
    var auditDefaultImage = 'http://hi-163-nsh.nosdn.127.net/dynamicPicture/auditing.png';
    _.forEach(imgList, (url, i) => {
        const audit = parseInt(imgAudit[i]) || exports.Constants.STATUS_AUDIT_INIT;
        const isSelf = (rid == roleId);
        if (skipAudit) { // 内网测试直接跳过审核
            url && list.push({ pic: url, thumb: pipeNosUrl(url, thumbArgs) });
        }
        else {
            if (audit === exports.Constants.STATUS_AUDIT_PASS) {
                list.push({ pic: url, thumb: pipeNosUrl(url, thumbArgs) });
            }
            else if (audit === exports.Constants.STATUS_AUDIT_REJECT) {
                if (isSelf) {
                    list.push({ pic: denyPic, thumb: pipeNosUrl(denyPic, (crop ? 'imageView&crop=0_45_150_96' : '')) });
                }
            }
            else { //审核中
                if (isSelf) {
                    list.push({ pic: url, thumb: pipeNosUrl(url, thumbArgs, waterMarkSuffix) });
                }
                else {
                    list.push({ pic: auditDefaultImage, thumb: pipeNosUrl(auditDefaultImage, thumbArgs) });
                }
            }
        }
    });
    return list;
}
function getImgList(imgList, size) {
    var list = [];
    var skipAudit = config.testCfg.skip_audit;
    for (var i = 0, k = imgList.length; i < k; i++) {
        var url = imgList[i], thumbArgs = '?imageView&thumbnail=' + size;
        if (skipAudit) { // 内网测试直接跳过审核
            url && list.push({
                pic: url,
                thumb: url + thumbArgs
            });
            continue;
        }
        url && list.push({
            pic: url,
            thumb: url + thumbArgs + '%7Cwatermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=' // 审核中水印
        });
    }
    return list;
}
function parseStr(str) {
    var SuperColor = {
        'P': 'ff40ff',
        'N': 'fffe91',
        'M': 'ff1200',
        'A': '9CF46C',
        'R': 'ff0000',
        'G': '00ff00',
        'B': '0000ff',
        'Y': 'ffff00',
        'W': 'ffffff',
        'K': '000000',
        'V': '8000ff'
    };
    var SpecialStyle = {
        'u': 'text-decoration:underline;'
    };
    var strList = _.compact(str.split("#n"));
    var itemId = '';
    var cjbId = '';
    var parseStrList = [];
    strList.forEach(function (str) {
        var parseStr = (str || '').trim()
            .replace(/#f[\da-fA-F]{1,6}/g, '') // Event中的鬼器样式，直接无视
            .replace(/#eid([\d-]+)$/g, '')
            .replace(/^#o(\d{7,8})(#o\d{7,8})?(#r.*)$/g, function (match, id) {
            itemId = id;
            return arguments[3] || '';
        }).replace(/#o(\d{8})/g, function (match, id) {
            return '<b class="bs b_' + id + '"></b>';
        }).replace(/#o(\d{7})/g, function (match, id) {
            cjbId = id;
            return '';
        }).replace(/#([uPNMARGBYWKV])(.*?)((?=#[rce])|$)/g, function (match, char, content) {
            var style = SpecialStyle[char] || ('color:#' + SuperColor[char]);
            return '<span style="' + style + '">' + content + '</span>';
        }).replace(/#e([\da-fA-F]{1,6})(.*?)((?=#[c])|$)/g, function (match, color, content) {
            var len = color.length;
            color = '#' + (len === 3 || len === 6 ? color : util.pad(color, 6));
            var style = 'text-shadow:1px 0 1px ' + color + ', -1px 0 1px ' + color + ', 0 1px 1px ' + color + ', 0 -1px 1px ' + color;
            return '<span style="' + style + '">' + content + '</span>';
        }).replace(/#c([\da-fA-F]{1,6})(.*?)((?=#[c])|$)/g, function (match, color, content) {
            var len = color.length;
            color = '#' + (len === 3 || len === 6 ? color : util.pad(color, 6));
            return '<span style="color:' + color + '">' + content + '</span>';
        }).replace(/#r/g, '<br/>');
        parseStrList.push(parseStr);
    });
    str = parseStrList.join("");
    return { str: str, id: itemId, cjbId: cjbId };
}
function parseSkill(str) {
    var item = parseStr(str);
    return item.str;
}
function parseEquipItem(data) {
    var str = parseSkill(data.equipname) + '<br/>';
    str += '<span>' + data.type + '</span><span class="fr">' + data.levellimit + '</span><br/>';
    str += '装备评分 ' + data.score + '<br/>';
    var jewelIds = data.jewelids || []; // 宝石装备信息
    if (jewelIds.length) {
        var stStr = '<b class="jewel_';
        var edStr = '"></b>';
        str += stStr + jewelIds.join(edStr + stStr) + edStr + '<br/>'; // holenum
    }
    str += '<b class="entry">基础属性</b><br/>';
    var props = data.props || [];
    for (var i = 0, l = props.length; i < l; i++) {
        str += ' ' + parseSkill(props[i]) + '<br/>';
    }
    str += '<b class="entry">升级完美度</b> ' + getStars(data.perfection || 0) + '<br/>'; // 实际星级=传递的强化完美度*0.5，perfection 0~10之间的整数   intensifylevel
    str += '<b class="entry">耐久度</b> ' + data.duration + '<br/>';
    str += '<b class="entry">词条属性</b><br/>'; // 词条洗炼积分暂无
    var words = data.words || [];
    for (i = 0, l = words.length; i < l; i++) {
        str += ' ' + parseSkill(words[i]) + '<br/>';
    }
    var jewelWords = data.jewelwords || [];
    if (!_.isEmpty(jewelWords)) {
        str += '<b class="entry">宝石属性</b><br/>';
        for (i = 0, l = jewelWords.length; i < l; i++) {
            str += ' ' + parseSkill(jewelWords[i]) + '<br/>';
        }
    }
    var gemWords = data.gemwords || [];
    if (!_.isEmpty(gemWords)) {
        str += '<b class="entry">' + parseSkill(data.gemgroup) + '</b><br/>'; // 石之灵
        for (i = 0, l = gemWords.length; i < l; i++) {
            str += ' ' + parseSkill(gemWords[i]) + '<br/>';
        }
    }
    return str;
}
function getStars(val) {
    var arr = [];
    arr.length = Math.floor(val / 2) + 1;
    return arr.join('★') + (val % 2 === 1 ? '☆' : '');
}
exports.md = md;
exports.appIdMap = appIdMap;
exports.appMap = (function () {
    var map = {};
    for (var key in appIdMap) {
        map[appIdMap[key]] = key;
    }
    return map;
}());
exports.getPhotoView = getPhotoView;
//# sourceMappingURL=data.js.map