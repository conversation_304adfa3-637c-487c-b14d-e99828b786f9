"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BunyanPoPoAlertStream = exports.BunyanLevel = void 0;
const util = require("util");
const crypto = require("crypto");
const request = require("request");
const util_1 = require("./util");
var BunyanLevel;
(function (BunyanLevel) {
    BunyanLevel[BunyanLevel["trace"] = 10] = "trace";
    BunyanLevel[BunyanLevel["debug"] = 20] = "debug";
    BunyanLevel[BunyanLevel["info"] = 30] = "info";
    BunyanLevel[BunyanLevel["warn"] = 40] = "warn";
    BunyanLevel[BunyanLevel["error"] = 50] = "error";
    BunyanLevel[BunyanLevel["fatal"] = 60] = "fatal";
})(BunyanLevel || (exports.BunyanLevel = BunyanLevel = {}));
class BaseError extends Error {
    constructor(name, context) {
        super();
        this.context = context;
        this.name = name;
    }
}
class HttpRequestError extends Error {
    constructor(name, url, context) {
        super();
        this.url = url;
        this.context = context;
        this.name = name || "HttpRequestError";
    }
}
const noop = () => { };
function defaultFormatter(record, levelName, context) {
    const maxAllowMsgLength = 2048;
    const levelStr = levelName.toLocaleUpperCase();
    const lines = [`主题：【警报】 业务 ${context.project}-${context.biz}-${context.env} 异常`];
    lines.push(`级别：${levelStr}`);
    lines.push(`详情：${util.inspect(record)}`);
    let text = lines.join("\n");
    if (text.length > maxAllowMsgLength) {
        text = text.slice(0, maxAllowMsgLength) + "...";
    }
    return {
        text: text,
    };
}
class BunyanPoPoAlertStream {
    constructor(options) {
        if (!options.webhookUrl) {
            throw new BaseError("InvalidArgument", { webhookUrl: this.webhookUrl });
        }
        this.webhookUrl = options.webhookUrl;
        this.secretKey = options.secretKey;
        this.biz = options.biz;
        this.project = options.project;
        this.env = options.env;
        this.customFormatter = options.customFormatter || defaultFormatter;
        this.minNotifyInterval = options.minNotifyInterval || 60;
        this.timeout = options.timeout || 5000;
        this.lastNotifyTime = 0;
        this.onError = options.onError || noop;
        (this.atAll = options.atAll === true), (this.atUids = options.atUids);
    }
    md5(content) {
        return crypto.createHash("md5").update(content).digest("hex");
    }
    genToken(params) {
        const keys = Object.keys(params).sort();
        let signStr = keys.map((k) => params[k]).join("");
        signStr = signStr + this.secretKey;
        return this.md5(signStr);
    }
    updateLastNotifyTime() {
        this.lastNotifyTime = Date.now();
    }
    isShouldRateLimit() {
        return Date.now() - this.lastNotifyTime < this.minNotifyInterval * 1000;
    }
    toPoPoLevel(level) {
        if (level === BunyanLevel.error || level === BunyanLevel.fatal) {
            return "error";
        }
        else if (level === BunyanLevel.warn) {
            return "warn";
        }
        else {
            return "info";
        }
    }
    async write(record) {
        if (this.isShouldRateLimit()) {
            return this.onError(new BaseError("AlarmRateLimited", record));
        }
        try {
            const parsedRecord = typeof record === "string" ? JSON.parse(record) : record;
            const levelName = BunyanLevel[parsedRecord.level];
            const content = this.customFormatter(parsedRecord, levelName, {
                project: this.project,
                biz: this.biz,
                env: this.env,
            });
            const atUids = this.atUids;
            if (!this.atAll && atUids) {
                const atStr = (0, util_1.csvStrToArray)(this.atUids)
                    .map((at) => `@${at}`)
                    .join(" ");
                content.text = content.text + "\n" + atStr;
            }
            const level = this.toPoPoLevel(parsedRecord.level);
            const reqBody = {
                atAll: this.atAll,
                project: this.project,
                biz: this.biz,
                env: this.env,
                atUids: this.atUids,
                msg: content.text,
                level,
                timestamp: Date.now(),
            };
            try {
                await this.sendNotification(reqBody);
            }
            catch (err) {
                this.onError(err);
            }
        }
        catch (err) {
            this.onError(err);
        }
        finally {
            this.updateLastNotifyTime();
        }
    }
    async sendNotification(reqBody) {
        const [res, body] = await this.sendPoPoMsg(reqBody);
        if (body && body.code !== 0) {
            throw new HttpRequestError("ApiReturnCodeError", this.webhookUrl, {
                statusCode: res.statusCode,
                headers: res.headers,
                body: JSON.stringify(body),
            });
        }
    }
    async sendPoPoMsg(reqBody) {
        return new Promise((resolve, reject) => {
            const token = this.genToken(reqBody);
            request.post({
                url: this.webhookUrl,
                timeout: this.timeout,
                json: true,
                body: reqBody,
                headers: {
                    token: token,
                },
            }, function (err, res, body) {
                if (err) {
                    reject(err);
                }
                resolve([res, body]);
            });
        });
    }
}
exports.BunyanPoPoAlertStream = BunyanPoPoAlertStream;
//# sourceMappingURL=bunyanPoPoAlert.js.map