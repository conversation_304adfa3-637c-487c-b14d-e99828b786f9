"use strict";
/**
 * Created by z<PERSON><PERSON> on 2017/3/30.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaoPaoAlarm = void 0;
exports.paoPaoAlarm3 = paoPaoAlarm3;
const request = require("./request");
const util = require("./util");
const os = require("os");
const _ = require("lodash");
const config_1 = require("./config");
const paopao_alarm_1 = require("@leihuo/paopao-alarm");
const PAO_PAO_MSG_URL = "http://***********:10013/redmine/popomsg4tf.php";
const DEFAULT_RECEIVER = ["hzwangzhenhua"];
const hostname = os.hostname();
class PaoPaoAlarm {
    static formatMsgObj(msgObj) {
        msgObj.time = msgObj.time || util.formatDate(Date.now());
        msgObj.hostname = hostname;
        let msg = "";
        _.forEach(msgObj, (value, key) => {
            if (!_.isString(value)) {
                value = JSON.stringify(value);
            }
            msg = msg + key + " : " + value + "\n";
        });
        return msg;
    }
    static _send(msg, to) {
        const key = util.hexMd5("ThunderFire" + msg);
        to = to || DEFAULT_RECEIVER;
        return request.post(PAO_PAO_MSG_URL, {
            msg: msg,
            to: to.join(','),
            key: key,
        });
    }
    static sendJson(obj, to) {
        const msg = PaoPaoAlarm.formatMsgObj(obj);
        return PaoPaoAlarm._send(msg, to);
    }
}
exports.PaoPaoAlarm = PaoPaoAlarm;
async function paoPaoAlarm3(str, to) {
    to = to || config_1.alarmPaoPao3Cfg.alarmOpenId + '';
    if (to) {
        await paopao_alarm_1.SendPaoPao3Class.init(config_1.alarmPaoPao3Cfg.url).sendPaoPao(str, to);
    }
    ;
}
//# sourceMappingURL=paopaoAlarm.js.map