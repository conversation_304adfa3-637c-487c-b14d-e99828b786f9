"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fillObject = fillObject;
exports.fillObjectByFetcher = fillObjectByFetcher;
exports.fillObjectByAsyncFetcher = fillObjectByAsyncFetcher;
exports.fillArrayByFetcher = fillArrayByFetcher;
exports.mergeObject = mergeObject;
exports.fillArrayByAsyncFetcher = fillArrayByAsyncFetcher;
function fillObject(src, ext) {
    return Object.assign({}, src, ext);
}
function fillObjectByFetcher(src, df) {
    let ext = df(src);
    return fillObject(src, ext);
}
async function fillObjectByAsyncFetcher(src, df) {
    let ext = await df(src);
    return fillObject(src, ext);
}
function fillArrayByFetcher(arr, df) {
    let extArr = df(arr);
    return mergeObject(arr, extArr);
}
function mergeObject(arr, ext) {
    return arr.map((s, i) => {
        return fillObject(s, ext[i]);
    });
}
async function fillArrayByAsyncFetcher(arr, df) {
    let extArr = await df(arr);
    return mergeObject(arr, extArr);
}
//# sourceMappingURL=fillUtil.js.map