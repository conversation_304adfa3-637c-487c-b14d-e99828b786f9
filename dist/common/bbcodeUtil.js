"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const _ = require("lodash");
const parseUrl = require('url').parse;
class CharStream {
    constructor(input) {
        this.index = 0;
        this.input = input;
        this.index = 0;
    }
    peek() {
        return this.input[this.index];
    }
    next() {
        this.index += 1;
        return this.peek();
    }
    eof() {
        return !this.peek();
    }
}
class TokenStream {
    constructor(charStream) {
        this.charStream = charStream;
        this.current = null;
    }
    peek() {
        return this.current || (this.current = this.readNext());
    }
    eof() {
        return !this.peek();
    }
    readNext() {
        if (this.charStream.eof()) {
            return null;
        }
        const ch = this.charStream.peek();
        if (ch === '[') {
            return this._maybeReadTag();
        }
        else {
            return this._readText();
        }
    }
    next() {
        const token = this.current;
        this.current = null;
        return token || this.readNext();
    }
    _maybeReadTag() {
        const input = this.charStream;
        let ch = input.next();
        let tagContent = '';
        let isTag = false;
        while (!input.eof()) {
            ch = input.peek();
            if (ch === ']') {
                isTag = true;
                input.next();
                break;
            }
            else if (ch === '[') {
                break;
            }
            else {
                tagContent += ch;
                input.next();
            }
        }
        const validTags = ['b', 'i', 'url', 'color'];
        if (isTag) {
            const closeTagFlag = tagContent[0] === '/';
            let tagType = 'OpenTag';
            if (closeTagFlag) {
                tagContent = tagContent.substr(1);
                tagType = 'CloseTag';
            }
            const tagAttr = tagContent.split('=');
            const tagName = tagAttr[0];
            if (_.includes(validTags, tagName)) {
                return { type: tagType, tagName: tagName, value: tagAttr[1] || null };
            }
            else {
                return { type: 'Text', value: '[' + tagContent + ']' };
            }
        }
        else {
            return { type: 'Text', value: '[' + tagContent };
        }
    }
    _readText() {
        const input = this.charStream;
        let text = '';
        while (!input.eof()) {
            let ch = input.peek();
            if (ch === '[') {
                break;
            }
            else {
                text += ch;
                input.next();
            }
        }
        return { type: 'Text', value: text };
    }
}
class BBCodeParser {
    constructor(tokenStream) {
        this.tokenStream = tokenStream;
    }
    parse() {
        return this._parseTopLevel();
    }
    _parseTag() {
        let input = this.tokenStream;
        let children = [];
        let tag = input.peek();
        input.next();
        while (!input.eof()) {
            const current = input.peek();
            if (current.type === 'OpenTag') {
                children.push(this._parseTag());
            }
            else if (current.type === 'CloseTag') {
                break;
            }
            else {
                children.push(current);
            }
            input.next();
        }
        return { type: 'Tag', tagName: tag.tagName, value: tag.value, children: children };
    }
    _parseTopLevel() {
        let children = [];
        let input = this.tokenStream;
        while (!input.eof()) {
            const token = input.peek();
            if (token.type === 'Text') {
                children.push(token);
            }
            else {
                children.push(this._parseTag());
            }
            input.next();
        }
        return { type: 'Root', children: children };
    }
}
function bbCodeToAst(bbText) {
    return new BBCodeParser(new TokenStream(new CharStream(bbText))).parse();
}
function tagElementToHtml(node) {
    if (node.tagName === 'url') {
        return urlElementToHtml(node);
    }
    else if (node.tagName === 'color') {
        return `<span style="color: ${node.value};">${parentElementToHtml(node)}</span>`;
    }
    else {
        return `<${node.tagName}>${parentElementToHtml(node)}</${node.tagName}>`;
    }
}
function checkLinkInWhiteList(url) {
    const hostWhiteList = [
        'http://xqn.netease.com',
        'https://cc.163.com/v/xqnyh/n/',
        'https://xqn.163.com',
        'https://www.16163.com/zt/xqn'
    ];
    return _.some(hostWhiteList, validHost => {
        const left = parseUrl(url);
        const right = parseUrl(validHost);
        return left.host === right.host;
    });
}
function urlElementToHtml(node) {
    let href = '';
    const firstChild = _.first(node.children);
    if (node.value) {
        href = node.value;
    }
    else if (firstChild && firstChild.type === 'Text') {
        href = firstChild.value;
    }
    else {
        // keep href empty
    }
    if (checkLinkInWhiteList(href)) {
        return `<a href="${href}" target="_blank">${parentElementToHtml(node)}</a>`;
    }
    else {
        return `<a>${parentElementToHtml(node)}</a>`;
    }
}
function parentElementToHtml(parentNode) {
    return _.map(parentNode.children, node => {
        if (node.type === 'Text') {
            return node.value;
        }
        else {
            return tagElementToHtml(node);
        }
    }).join('');
}
function bbTextToHtml(bbText) {
    const htmlSafe = _.escape(bbText);
    const asts = bbCodeToAst(htmlSafe);
    const countText = textCount(asts);
    if (countText > 0) {
        return parentElementToHtml(asts);
    }
    else {
        return htmlSafe;
    }
}
function textCount(node) {
    let count = 0;
    if (node.children) {
        node.children.forEach(cNode => {
            count += textCount(cNode);
        });
        return count;
    }
    else {
        return node.value.length;
    }
}
module.exports = {
    bbCodeToAst: bbCodeToAst,
    bbTextToHtml: bbTextToHtml
};
//# sourceMappingURL=bbcodeUtil.js.map