"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rsyslog = exports.Severity = exports.Facility = void 0;
const os = require("os");
const dgram = require("dgram");
let sysLogBind;
if (process.platform === 'linux') {
    sysLogBind = require('../../node_modules/bunyan-syslog/build/Release/syslog');
}
else {
    sysLogBind = {
        syslog: function (p, msg) { }
    };
}
const HOSTNAME = os.hostname();
var Facility;
(function (Facility) {
    Facility[Facility["local0"] = 16] = "local0";
})(Facility || (exports.Facility = Facility = {}));
var Severity;
(function (Severity) {
    Severity[Severity["ERROR"] = 3] = "ERROR";
    Severity[Severity["INFO"] = 6] = "INFO";
    Severity[Severity["DEBUG"] = 7] = "DEBUG";
})(Severity || (exports.Severity = Severity = {}));
class Rsyslog {
    constructor(options) {
        this.facility = options.facility || Facility.local0;
        this.severity = options.severity || Severity.INFO;
        this.hostname = HOSTNAME;
        this.host = options.host || '127.0.0.1';
        this.port = options.port || 514;
    }
    log(msg) {
        this.sendByNative(msg);
    }
    send(message) {
        let buf = Buffer.from(message);
        let client = dgram.createSocket("udp4");
        client.send(buf, 0, buf.length, this.port, this.host, function (err, bytes) {
            if (err) {
                throw err;
            }
            client.close();
        });
    }
    sendByNative(msg) {
        let p = this.facility * 8 + this.severity;
        sysLogBind.syslog(p, msg);
    }
    format(tag, msg) {
        let p = this.facility * 8 + this.severity;
        let time = new Date().toJSON();
        let line = `<${p}>${time} ${this.hostname} [${tag}]:` + msg;
        return line;
    }
}
exports.Rsyslog = Rsyslog;
//# sourceMappingURL=rsysLog.js.map