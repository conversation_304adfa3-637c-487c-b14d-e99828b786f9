"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IExistResult = exports.ExpireType = exports.GeoUnit = void 0;
exports.getRedis = getRedis;
exports.getGeoRedis = getGeoRedis;
exports.getRedisForSkey = getRedisForSkey;
const redis = require("redis");
const _ = require("lodash");
const bluebird = require("bluebird");
const config = require("../common/config");
const logger_1 = require("../logger");
bluebird.promisifyAll(redis.RedisClient.prototype, {
    promisifier: promisifier,
});
bluebird.promisifyAll(redis.Multi.prototype, {
    promisifier: promisifier,
});
function promisifier(originalMethod) {
    return function promisified() {
        // eslint-disable-next-line prefer-rest-params
        const args = [].slice.call(arguments);
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        return new Promise(function (resolve, reject) {
            args.push(function (err, data) {
                if (err) {
                    reject(err);
                }
                else {
                    resolve(data);
                }
            });
            originalMethod.apply(self, args);
        }).catch((err) => {
            logger_1.logger.error("RedisCommendError", { method: originalMethod.name, args: args });
            throw err;
        });
    };
}
function getRedisClient(config) {
    if (!config.password) {
        delete config.password;
    }
    const client = redis.createClient(config);
    return client;
}
class RedisClass {
    constructor(config) {
        this.instance = null;
        this.redisConfig = config;
    }
    retry_strategy() {
        this.instance = null;
    }
    getInstance() {
        const redisConfig = _.assign({}, this.redisConfig, { retry_strategy: this.retry_strategy.bind(this) });
        if (this.instance === null) {
            this.instance = getRedisClient(redisConfig);
        }
        return this.instance;
    }
}
class RedisPool {
    constructor(config) {
        this.pool = [];
        for (const h of config.hosts) {
            const cfg = Object.assign({}, config, { host: h });
            const rc = new RedisClass(cfg);
            this.pool.push(rc);
        }
    }
    getInstance() {
        const idx = _.random(0, this.pool.length - 1);
        return this.pool[idx].getInstance();
    }
}
const redisPool = new RedisPool(config.redis);
function getRedis() {
    return redisPool.getInstance();
}
const GLOBAL_REDIS_CLIENT = getRedis();
const GeoRedisPool = new RedisPool(config.geoRedis);
function getGeoRedis() {
    return GeoRedisPool.getInstance();
}
const SkeyRedisPool = new RedisPool(config.redisSkeyCfg);
function getRedisForSkey() {
    return SkeyRedisPool.getInstance();
}
GLOBAL_REDIS_CLIENT.on("error", function (err) {
    const str = JSON.stringify({ type: "reborn", err: err });
    logger_1.logger.error("redis_error", str);
});
if (config.testCfg.redis_debug) {
    const monitorClient = GLOBAL_REDIS_CLIENT.duplicate();
    monitorClient.monitor(function (err) {
        if (err) {
            logger_1.logger.error(err);
        }
        else {
            logger_1.logger.info("RedisClient: Entering monitoring mode.");
        }
    });
    monitorClient.on("monitor", function (time, args) {
        const commend = _.upperCase(args[0]);
        const commendArgs = _.tail(args)
            .filter((r) => {
            return !_.isFunction(r);
        })
            .join(" ");
        logger_1.logger.info("RedisClient" + ": " + commend + " " + commendArgs);
    });
}
var GeoUnit;
(function (GeoUnit) {
    GeoUnit["ml"] = "ml";
    GeoUnit["km"] = "km";
    GeoUnit["ft"] = "ft";
    GeoUnit["mi"] = "mi";
})(GeoUnit || (exports.GeoUnit = GeoUnit = {}));
var ExpireType;
(function (ExpireType) {
    /** seconds */
    ExpireType["EX"] = "EX";
    /** mille seconds */
    ExpireType["PX"] = "PX";
})(ExpireType || (exports.ExpireType = ExpireType = {}));
var IExistResult;
(function (IExistResult) {
    IExistResult[IExistResult["Exist"] = 1] = "Exist";
    IExistResult[IExistResult["NotExist"] = 0] = "NotExist";
})(IExistResult || (exports.IExistResult = IExistResult = {}));
//# sourceMappingURL=redis.js.map