"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisDistributedLock = void 0;
exports.createDistributedLock = createDistributedLock;
const redis_1 = require("./redis");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("common/redisDistributedLock");
/**
 * Redis分布式锁工具类
 *
 * 特性：
 * - 使用SET NX EX命令原子性获取锁
 * - 使用Lua脚本原子性释放锁，防止误删
 * - 支持锁超时自动释放，防止死锁
 * - 支持重试机制
 * - 完善的日志记录
 *
 * 使用示例：
 * ```typescript
 * const lock = new RedisDistributedLock("my_lock_key");
 * const result = await lock.acquire({ timeoutSeconds: 30 });
 * if (result.acquired) {
 *   try {
 *     // 执行需要加锁的业务逻辑
 *   } finally {
 *     await lock.release(result.lockValue!);
 *   }
 * }
 * ```
 */
class RedisDistributedLock {
    constructor(lockKey) {
        this.lockKey = lockKey;
    }
    /**
     * 获取分布式锁
     */
    async acquire(options = {}) {
        const { timeoutSeconds = 30, retryCount = 1, retryIntervalMs = 100 } = options;
        const lockValue = this.generateLockValue();
        for (let attempt = 0; attempt <= retryCount; attempt++) {
            try {
                // 使用SET NX EX命令原子性获取锁
                const result = await (0, redis_1.getRedis)().setAsync(this.lockKey, lockValue, redis_1.ExpireType.EX, timeoutSeconds, "NX" /* SET_OPTION.NX */);
                if (result === 'OK') {
                    logger.debug({
                        lockKey: this.lockKey,
                        lockValue,
                        timeoutSeconds,
                        attempt
                    }, "distributedLockAcquired");
                    return {
                        acquired: true,
                        lockValue
                    };
                }
                // 获取锁失败，如果还有重试次数则等待后重试
                if (attempt < retryCount) {
                    logger.debug({
                        lockKey: this.lockKey,
                        attempt,
                        retryCount
                    }, "distributedLockAcquireRetry");
                    await this.sleep(retryIntervalMs);
                }
            }
            catch (err) {
                logger.error({
                    err,
                    lockKey: this.lockKey,
                    lockValue,
                    attempt
                }, "distributedLockAcquireError");
                return {
                    acquired: false,
                    error: err instanceof Error ? err.message : String(err)
                };
            }
        }
        logger.warn({
            lockKey: this.lockKey,
            retryCount
        }, "distributedLockAcquireFailed");
        return {
            acquired: false,
            error: "Failed to acquire lock after retries"
        };
    }
    /**
     * 释放分布式锁
     * 使用Lua脚本确保只有锁的持有者才能释放锁
     */
    async release(lockValue) {
        try {
            // Lua脚本：检查锁的值是否匹配，匹配则删除
            const luaScript = `
                if redis.call("GET", KEYS[1]) == ARGV[1] then
                    return redis.call("DEL", KEYS[1])
                else
                    return 0
                end
            `;
            const result = await (0, redis_1.getRedis)().evalAsync(luaScript, 1, this.lockKey, lockValue);
            if (result === 1) {
                logger.debug({
                    lockKey: this.lockKey,
                    lockValue
                }, "distributedLockReleased");
                return true;
            }
            else {
                logger.warn({
                    lockKey: this.lockKey,
                    lockValue
                }, "distributedLockReleaseNotOwner");
                return false;
            }
        }
        catch (err) {
            logger.error({
                err,
                lockKey: this.lockKey,
                lockValue
            }, "distributedLockReleaseError");
            return false;
        }
    }
    /**
     * 尝试获取锁并执行回调函数
     * 自动处理锁的获取和释放
     */
    async withLock(callback, options = {}) {
        const lockResult = await this.acquire(options);
        if (!lockResult.acquired) {
            throw new Error(`Failed to acquire lock: ${lockResult.error || 'Unknown error'}`);
        }
        try {
            return await callback();
        }
        finally {
            await this.release(lockResult.lockValue);
        }
    }
    /**
     * 生成唯一的锁值
     */
    generateLockValue() {
        return `${process.pid}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * 睡眠指定毫秒数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * 获取锁的键名
     */
    getLockKey() {
        return this.lockKey;
    }
}
exports.RedisDistributedLock = RedisDistributedLock;
/**
 * 创建分布式锁实例的便捷函数
 */
function createDistributedLock(lockKey) {
    return new RedisDistributedLock(lockKey);
}
//# sourceMappingURL=redisDistributedLock.js.map