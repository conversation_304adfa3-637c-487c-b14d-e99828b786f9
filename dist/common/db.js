"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DBNode = void 0;
exports.query = query;
exports.add = add;
exports.addBatch = addBatch;
exports.del = del;
exports.update = update;
exports.push = push;
exports.addOnDup = addOnDup;
exports.execSql = execSql;
exports.transact = transact;
exports.execute = execute;
exports.transactByKnexQuerys = transactByKnexQuerys;
// mysql数据库连接通用类
const mysql = require("mysql"), assert = require("assert"), util = require("./util");
const bluebird = require("bluebird");
const _ = require("lodash");
const logger_1 = require("../logger");
const Q = require("q");
const config = require("../common/config");
const paopaoAlarm_1 = require("./paopaoAlarm");
function extendDBConfig(config) {
    /*兼容问题， 之前配置的key名字没和mysql模块保持一致*/
    if (config.pwd && !config.password) {
        config.password = config.pwd;
    }
    if (config.db && !config.database) {
        config.database = config.db;
    }
    const defaultOption = {
        connectionLimit: 25,
    };
    return _.defaults(config, defaultOption);
}
var DBNode;
(function (DBNode) {
    DBNode["Master"] = "MASTER";
    DBNode["Slave"] = "SLAVE";
})(DBNode || (exports.DBNode = DBNode = {}));
//使用once确保只是被调用一次
const getPoolCluster = _.once(function getPoolCluster() {
    const masterConfig = extendDBConfig(config.db);
    const poolCluster = mysql.createPoolCluster({
        canRetry: true,
        removeNodeErrorCount: 100,
        restoreNodeTimeout: 5 * 1000,
        defaultSelector: "RR",
    });
    poolCluster.add(DBNode.Master, masterConfig);
    if (config.slaveDb) {
        const slaveConfig = extendDBConfig(config.slaveDb);
        poolCluster.add(DBNode.Slave, slaveConfig);
    }
    return poolCluster;
});
const poolCluster = getPoolCluster();
poolCluster.on("online", function (nodeId) {
    changeNodeStatus(nodeId, "online");
});
poolCluster.on("offline", function (nodeId) {
    changeNodeStatus(nodeId, "offline");
});
poolCluster.on("remove", function (nodeId) {
    changeNodeStatus(nodeId, "remove");
});
function changeNodeStatus(nodeId, status) {
    const msg = `PoolCluster Node ${status}`;
    logger_1.dbLogger.warn({ nodeId }, msg);
    paopaoAlarm_1.PaoPaoAlarm.sendJson({ nodeId: nodeId, msg: msg });
}
const masterPool = poolCluster.of(DBNode.Master);
function query(args, opt) {
    const table = args.table, cols = args.cols ? args.cols.join(",") : "*", condition = args.filter ? " where " + getCondition(args.filter) : "", order = args.order ? " order by " + getOrder(args.order) : "", group = args.group ? " group by " + args.group : "", pageSize = args.pageSize || 50, start = (args.page || 0) * pageSize;
    const sql = "select " +
        cols +
        " from " +
        table +
        condition +
        group +
        order +
        (args.pageSize ? " limit " + start + "," + pageSize : "") +
        (args && args.conn ? " for update" : "") +
        ";"; // 事务中的select语句统一加锁，防止并发请求数据冲突
    return execSql(sql, args);
}
function add(args, onDup) {
    const table = args.table, values = args.values, keyArr = [], valArr = [];
    for (const key in values) {
        const rawVal = values[key];
        keyArr.push(key);
        valArr.push(getSqlVal(rawVal));
    }
    const sql = "insert" +
        (args.ignore ? " ignore" : "") +
        " into `" +
        table +
        "` (" +
        keyArr.join(",") +
        ") values (" +
        valArr.join(",") +
        ")" +
        (onDup ? " on duplicate key update " + mysql.escape(onDup) : "") +
        ";";
    return execSql(sql, args);
}
function addBatch(args, opt) {
    let table = args.table, values = args.values, itemArr = [], keyArr = [], valArr = [], item = values[0];
    for (const key in item) {
        keyArr.push(key);
        valArr.push(getSqlVal(item[key]));
    }
    itemArr.push("(" + valArr.join(",") + ")");
    for (let i = 1, l = values.length; i < l; i++) {
        valArr = [];
        item = values[i];
        for (let j = 0, k = keyArr.length; j < k; j++) {
            valArr.push(getSqlVal(item[keyArr[j]]));
        }
        itemArr.push("(" + valArr.join(",") + ")");
    }
    const sql = "insert into `" + table + "` (" + keyArr.join(",") + ") values " + itemArr.join(",") + ";";
    return execSql(sql, args);
}
function del(args, opt) {
    const table = args.table, condition = " where " + getCondition(args.filter);
    const sql = "delete from `" + table + "`" + condition + ";";
    return execSql(sql, args);
}
function update(args, opt) {
    const table = args.table, condition = args.filter ? " where " + getCondition(args.filter) : "", values = args.values, updArr = [];
    for (const key in values) {
        const rawVal = values[key];
        const val = getSqlVal(rawVal);
        updArr.push(key + "=" + val);
    }
    const sql = "update `" + table + "` set " + updArr.join(",") + condition + ";";
    return execSql(sql, args);
}
async function push(args, opt) {
    const option = util.extend({ holdClient: true }, args);
    const results = await query(option);
    const exists = results.length;
    const hookVal = opt && opt.hookVal;
    const hookRet = hookVal(args.values, results[0]);
    if (hookRet instanceof bluebird) {
        await hookRet;
    }
    if (exists) {
        return update(args);
    }
    else {
        return add(mergeArgs(args));
    }
}
function debugQueryInfo(err, sql, results) {
    const showInfo = { err: err || undefined, sql: sql, ret: undefined };
    if (config.testCfg.db_debug_show_result) {
        showInfo.ret = results;
    }
    if (config.testCfg.db_debug) {
        logger_1.dbLogger.info(showInfo, "ExecuteSql");
    }
}
function addOnDup(args) {
    return add(mergeArgs(args), args.values);
}
function mergeArgs(args) {
    args.values = args.values || {};
    for (const key in args.filter) {
        args.values[key] = args.filter[key];
    }
    args.create && (args.values[args.create] = Date.now());
    return args;
}
function getCondition(filter, isOr) {
    const condition = [];
    for (const key in filter) {
        let str = "";
        const val = filter[key];
        const type = Object.prototype.toString.call(val);
        if (type === "[object Null]") {
            str = key;
        }
        else if (type === "[object Array]") {
            str = val.length === 1 ? key + "=" + getSqlVal(val[0]) : key + " in (" + getSqlVal(val) + ")";
        }
        else if (type === "[object Object]") {
            str = "(" + getCondition(val, true) + ")";
        }
        else {
            str = key + "=" + getSqlVal(val);
        }
        str && condition.push(str);
    }
    return condition.join(isOr ? " or " : " and ");
}
function getOrder(order) {
    const orders = [];
    for (const key in order) {
        const orderVal = order[key];
        orders.push(key + (orderVal ? " " + orderVal : ""));
    }
    return orders.join(",");
}
function getSqlVal(val) {
    if (val === null) {
        return "null";
    }
    const type = Object.prototype.toString.call(val);
    switch (type) {
        case "[object Undefined]":
            return "null";
        case "[object Object]":
            return getCondition(val);
    }
    return mysql.escape(val);
}
// 老的db模块
function execSql(sql, option) {
    const defer = Q.defer();
    option = option || {};
    const queryNode = getPreferDbNodeBySql(sql);
    let queryConn = poolCluster.of(queryNode);
    if (option.conn) {
        queryConn = option.conn;
    }
    queryConn.query(sql, function (err, results, fields) {
        debugQueryInfo(err, sql, results);
        if (err) {
            option.conn ? defer.reject(err) : assert.ifError(err);
        }
        defer.resolve(results || []);
    });
    return defer.promise;
}
function transact(transactions, option) {
    const defer = Q.defer();
    masterPool.getConnection(function (err, conn) {
        assert.ifError(err);
        conn.beginTransaction(function (err) {
            assert.ifError(err);
            runNext();
        });
        function runNext(result) {
            const query = transactions.shift();
            if (query) {
                try {
                    query(conn, result).then(runNext).catch(onFinal);
                }
                catch (ex) {
                    onFinal(ex);
                }
                return;
            }
            conn.commit(function (err) {
                onFinal(err, result);
            });
        }
        function onFinal(err, result) {
            if (err) {
                conn.rollback();
            }
            conn.release();
            err ? defer.reject(err) : defer.resolve(result);
        }
    });
    return defer.promise;
}
var DBQueryType;
(function (DBQueryType) {
    DBQueryType[DBQueryType["SELECT"] = 0] = "SELECT";
    DBQueryType[DBQueryType["CREATE"] = 1] = "CREATE";
    DBQueryType[DBQueryType["UPDATE"] = 2] = "UPDATE";
    DBQueryType[DBQueryType["DELETE"] = 3] = "DELETE";
    DBQueryType[DBQueryType["OTHER"] = 4] = "OTHER";
})(DBQueryType || (DBQueryType = {}));
function getDbQueryTypeBySql(sql) {
    const sqlUp = sql.toUpperCase();
    const checkTypes = ["SELECT", "CREATE", "UPDATE", "DELETE"];
    const type = checkTypes.find((t) => sqlUp.startsWith(t + " "));
    if (type) {
        return DBQueryType[type];
    }
    else {
        return DBQueryType.OTHER;
    }
}
function getPreferDbNodeBySql(sql) {
    if (config.dbTraffic.enable) {
        const type = getDbQueryTypeBySql(sql);
        // 暂时支持SELECT分流即可
        if (type === DBQueryType.SELECT) {
            if (config.dbTraffic.select) {
                let node = DBNode.Master;
                const traffic = config.dbTraffic.select;
                const masterRatio = traffic.master / (traffic.master + traffic.slave);
                if (Math.random() <= masterRatio) {
                    node = DBNode.Master;
                }
                else {
                    node = DBNode.Slave;
                }
                if (config.dbTraffic.debug) {
                    logger_1.dbLogger.info("DbTraffic", { sql: sql, dbNode: node });
                }
                return node;
            }
        }
        return DBNode.Master;
    }
    else {
        return DBNode.Master;
    }
}
/**
 * execute sql
 *
 * @param {String} sql
 * @param {Object} option
 * @returns {Object} rows
 */
function execute(sql, option) {
    const preferDb = getPreferDbNodeBySql(sql);
    option = _.defaults(option, { dbNode: preferDb }); //默认使用Master节点
    if (!config.slaveDb && option.dbNode === DBNode.Slave) {
        //slave配置不存在的时候重新指回master节点
        logger_1.dbLogger.warn("app try to use slave db, but slave db config not exist");
        option.dbNode = DBNode.Master;
    }
    const timeStart = Date.now();
    const queryPool = poolCluster.of(option.dbNode);
    return new bluebird(function (resolve, reject) {
        queryPool.query(sql, function (err, rows) {
            const timeEnd = Date.now();
            const duration = timeEnd - timeStart;
            if (config.dbSlowLogCfg.enable && duration >= config.dbSlowLogCfg.threshold) {
                logger_1.slowSqlLogger.warn({ sql, duration: `${duration}ms` }, "DetectSlowSql");
            }
            debugQueryInfo(err, sql, rows);
            if (err) {
                reject(err);
            }
            else {
                resolve(rows);
            }
        });
    });
}
function transactByKnexQuerys(knexQuerys) {
    const querys = _.map(knexQuerys, (knexQuery) => {
        return (conn) => {
            return new bluebird((resolve, reject) => {
                const sql = knexQuery.toString();
                conn.query(sql, (err, rows) => {
                    debugQueryInfo(err, sql, rows);
                    if (err) {
                        reject(err);
                    }
                    else {
                        resolve(rows);
                    }
                });
            });
        };
    });
    return transact(querys);
}
//# sourceMappingURL=db.js.map