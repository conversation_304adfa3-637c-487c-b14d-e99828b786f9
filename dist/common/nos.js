"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NOS_CONSTANTS = void 0;
exports.getToken = getToken;
exports.getObjUrl = getObjUrl;
exports.validateUrl = validateUrl;
// NOS直传服务
const crypto = require("crypto");
const util = require("./util");
const _ = require("lodash");
const config_1 = require("./config");
const constants_1 = require("./constants");
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function getToken(product, params, callbackUrl) {
    const info = genUploadInfo(params), objectName = info.path, objUrl = getObjUrl(product);
    const bucket = config_1.NOS_CFG.buckets[product];
    const region = bucket.region || "HZ"; // 默认杭州
    const bucketName = bucket.name;
    const validDuration = (params.expires || constants_1.NOS_DEFAULT_EXPIRES) * 60;
    const expires = Math.floor(Date.now() / 1000) + validDuration;
    const putPolicy = {
        Bucket: bucketName,
        Region: region,
        Object: objectName,
        MimeLimit: constants_1.NOS_MIME_LIMIT,
        Expires: expires,
        ReturnBody: '{"code":0,"data":{"bucketname":"$(Bucket)","url":"' + objUrl + '"}}',
    };
    const encodedPutPolicy = Buffer.from(JSON.stringify(putPolicy), "utf-8").toString("base64");
    const encodedSign = crypto.createHmac("sha256", config_1.NOS_CFG.secretKey).update(encodedPutPolicy).digest("base64");
    const token = "UPLOAD " + config_1.NOS_CFG.accessKey + ":" + encodedSign + ":" + encodedPutPolicy;
    return {
        token: token,
        bucketname: bucketName,
        objectname: objectName,
        prefix: objUrl.replace("$(Object)", "").replace("$(Bucket)", bucketName),
        expires: putPolicy.Expires,
    };
}
function getObjUrl(product, objectName) {
    const nosHost = "$(Bucket).nos-jd.163yun.com"; // 启用CDN加速路径
    const url = "https://" + nosHost + "/$(Object)";
    return objectName ? url.replace("$(Bucket)", config_1.NOS_CFG.buckets[product]).replace("$(Object)", objectName) : url;
}
function genUploadInfo(params) {
    let dir, name, type = params.type;
    const extName = params.extname;
    if (type === "role" || type === "child" || type === "fengshui" || type === "land") {
        // 角色形象等图片直接覆盖
        name = type === "child" ? params.picid : params.roleid;
        const md5 = util.hexMd5(name);
        dir = type + "/" + md5.substr(0, 2) + "/" + md5.substr(30, 2);
    }
    else {
        const info = util.genUploadPath();
        name = info.name;
        const type = params.type || "upload";
        dir = type + "/" + info.dir;
    }
    if (extName) {
        name = name + "." + _.trim(extName);
    }
    return { path: dir + "/" + name, dir: dir, name: name };
}
function validateUrl(url) {
    return /^https?:\/\/.*(nosdn\.127\.net|nos\.netease\.com|nos-jd\.163yun\.com)/.test(url);
}
exports.NOS_CONSTANTS = {
    CDN_PREFIX: "https://hi-163-nsh.nosdn.127.net",
    NOS_JD_PREFIX: ["https://hi-163-nsh.nos-jd.163yun.com", "http://hi-163-nsh.nos-jd.163yun.com"],
};
//# sourceMappingURL=nos.js.map