"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getValidCookie = getValidCookie;
exports.validateCookieByUrsApi = validateCookieByUrsApi;
exports.validateFromLocal = validateFromLocal;
const httpLib = require("./request");
const _ = require("lodash");
const config_1 = require("./config");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("common/ursCookie");
const Config = {
    Hosts: config_1.ursCookie.hosts,
    productid: config_1.ursCookie.productId,
    cookieNames: ['NTES_SESS', 'NTES_YD_SESS', 'NTES_OSESS', 'NTES_PASSPORT', 'NTES_YD_PASSPORT']
};
function formatSsn(ssn) {
    if (ssn.includes('@')) {
        return ssn;
    }
    else {
        return ssn + '@163.com';
    }
}
async function callValidateApi(uri, cookieName, cookieValue) {
    let body = await httpLib.request({
        method: 'POST',
        uri: uri,
        qs: {
            productid: Config.productid,
            cookieName: cookieName,
            cookie: cookieValue,
            recreate: 0
        }
    });
    if (body.retCode) {
        if (body.retCode === 200) {
            const data = body.data;
            data.ssn = formatSsn(data.ssn);
            return { validate: true, data: data, msg: '' };
        }
        else {
            return { validate: false, data: null, msg: body.msg };
        }
    }
    else {
        let msg = 'Call urs Api ' + uri + ' failed';
        return { validate: false, data: null, msg: msg };
    }
}
function getValidCookie(cookies) {
    const cookeName = _.find(Config.cookieNames, name => {
        return !!cookies[name];
    });
    return { cookieName: cookeName, cookieValue: cookies[cookeName] };
}
function validateCookieByUrsApi(checkCookie) {
    return callValidateApi(Config.Hosts[0], checkCookie.cookieName, checkCookie.cookieValue)
        .catch(() => {
        return callValidateApi(Config.Hosts[1], checkCookie.cookieName, checkCookie.cookieValue);
    });
}
// 测试机无法使用ursCookie校验接口，从登录cookie取出来
function validateFromLocal(cookies, mockSsn) {
    logger.warn({ cookies }, "UrsCookieValidateByLocal");
    const pInfo = cookies['P_INFO'];
    let cookie = getValidCookie(cookies);
    if (pInfo && cookie && cookie.cookieValue) {
        let tokens = pInfo.split('|');
        let ssn = tokens[0];
        let mobile = tokens[tokens.length - 1];
        ssn = formatSsn(ssn);
        let data = {
            ssn: mockSsn || ssn,
            mobile: mobile,
            autologin: '0',
            userip: '',
            createtime: 0,
            cookieCreateTime: 0,
            alias: '',
            misc: ''
        };
        return { validate: true, data: data, msg: '' };
    }
    else {
        return { validate: false, data: null, msg: '请先登录网易通行证' };
    }
}
//# sourceMappingURL=ursCoookie.js.map