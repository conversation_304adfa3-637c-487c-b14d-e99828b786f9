"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.jdNosClient = exports.nosClient = void 0;
const bluebird = require("bluebird");
const NosClient = require("nos-node-sdk");
const _ = require("lodash");
const config_1 = require("../common/config");
function createNosClient(options) {
    let endPoint = options.endPoint;
    let port = options.port || 80;
    let nosClient = new NosClient();
    nosClient.setAccessId(config_1.NOS_CFG.accessKey);
    nosClient.setSecretKey(config_1.NOS_CFG.secretKey);
    nosClient.setEndpoint(endPoint);
    nosClient.setPort(port);
    return nosClient;
}
const ApiMethodsPromisify = [
    'abort_multipart_upload',
    'complete_multipart_upload',
    'delete_objects',
    'delete_object',
    'get_object_stream',
    'get_object_file',
    'head_object',
    'create_multipart_upload',
    'list_multipart_upload',
    'list_objects',
    'list_parts',
    'copy_object',
    'move_object',
    'put_object_stream',
    'put_file',
    'put_big_file',
    'upload_part'
];
/**
 * Generate promise version sdk method, new method name is origin name with _async suffix
 */
ApiMethodsPromisify.forEach(function (methodName) {
    let originFunc = NosClient.prototype[methodName];
    NosClient.prototype[methodName + '_async'] = function () {
        let self = this;
        let args = _.slice(arguments);
        return new bluebird(function (resolve, reject) {
            args.push(function (value) {
                resolve(value);
            });
            try {
                originFunc.apply(self, args);
            }
            catch (err) {
                reject(err);
            }
        });
    };
});
exports.nosClient = createNosClient({ endPoint: 'nos.netease.com' });
exports.jdNosClient = createNosClient({
    endPoint: 'nos-jd.163yun.com'
});
//# sourceMappingURL=nosClient.js.map