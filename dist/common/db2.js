"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DBClient = void 0;
const db_1 = require("./db");
class DBClient {
    async execute(sql, option) {
        let result = await (0, db_1.execute)(sql, option);
        return result;
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new DBClient();
        }
        return this.instance;
    }
}
exports.DBClient = DBClient;
DBClient.instance = null;
//# sourceMappingURL=db2.js.map