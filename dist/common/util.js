"use strict";
/* eslint-disable @typescript-eslint/no-unused-vars,  @typescript-eslint/no-var-requires */
Object.defineProperty(exports, "__esModule", { value: true });
exports.co = void 0;
exports.trim = trim;
exports.response = response;
exports.formatDate = formatDate;
exports.currying = currying;
exports.extend = extend;
exports.pad = pad;
exports.toCamelCase = toCamelCase;
exports.toSnakeCase = toSnakeCase;
exports.hexMd5 = hexMd5;
exports.genUploadPath = genUploadPath;
exports.toLowerCaseKey = toLowerCaseKey;
exports.toSnakeCaseKey = toSnakeCaseKey;
exports.getDayStr = getDayStr;
exports.addToList = addToList;
exports.rmvFromList = rmvFromList;
exports.getJsonInfo = getJsonInfo;
exports.hashKeyList = hashKeyList;
exports.unifyKeys = unifyKeys;
exports.unifyUpResults = unifyUpResults;
exports.keyToRecordHash = keyToRecordHash;
exports.csvStrToArray = csvStrToArray;
exports.arrayToCsv = arrayToCsv;
exports.csvStrToIntArray = csvStrToIntArray;
exports.countBy = countBy;
exports.keysToCamelCase = keysToCamelCase;
exports.emojiStrip = emojiStrip;
exports.uniq = uniq;
exports.IsJsonString = IsJsonString;
exports.mergeIds = mergeIds;
exports.getRepeatCsv = getRepeatCsv;
exports.getMatches = getMatches;
exports.replaceHttps = replaceHttps;
exports.isBetweenDates = isBetweenDates;
exports.contains = contains;
exports.fillCsvStr = fillCsvStr;
exports.removeLeadingZero = removeLeadingZero;
exports.getListByPagination = getListByPagination;
exports.getPageMeta = getPageMeta;
exports.getPageDataByList = getPageDataByList;
exports.keyToRecordMap = keyToRecordMap;
exports.keyToRecordMapByKeys = keyToRecordMapByKeys;
exports.isValidCallbackName = isValidCallbackName;
exports.getCell = getCell;
exports.isAll = isAll;
exports.mapToList = mapToList;
exports.hexSha256 = hexSha256;
exports.getTimeStampSeconds = getTimeStampSeconds;
exports.decodeUtf8HexString = decodeUtf8HexString;
exports.repeat = repeat;
exports.getSunWeekStartDayStr = getSunWeekStartDayStr;
exports.p = p;
exports.cacheKeyGen = cacheKeyGen;
exports.pickBy = pickBy;
exports.omitBy = omitBy;
exports.aesEncrypt = aesEncrypt;
exports.aesDecrypt = aesDecrypt;
exports.isNumeric = isNumeric;
exports.getFirstXForwardedForIp = getFirstXForwardedForIp;
exports.getIp = getIp;
exports.callOperation = callOperation;
exports.isServerEnvOriginal = isServerEnvOriginal;
exports.isServerEnvHuaijiu = isServerEnvHuaijiu;
exports.isRoleInfoInHuaijiu = isRoleInfoInHuaijiu;
exports.isBase64Str = isBase64Str;
exports.encodeToBase64 = encodeToBase64;
exports.isNosUrl = isNosUrl;
exports.isValidPhotoUrl = isValidPhotoUrl;
exports.redisKeyGen = redisKeyGen;
exports.isFpUrl = isFpUrl;
exports.isEmptyStr = isEmptyStr;
exports.checkValidText = checkValidText;
// 通用工具
const bluebird = require("bluebird");
const _ = require("lodash");
const crypto = require("crypto");
const moment = require("moment");
const config_1 = require("./config");
function trim(str) {
    if (typeof str === "string") {
        const normalStr = str.replace(/^\s+|\s+$/g, "");
        return emojiStrip(normalStr);
    }
    else {
        return str;
    }
}
function substr(str, len) {
    // eslint-disable-next-line no-control-regex
    const chineseRegex = /[^\x00-\xff]/g;
    const strLength = str.replace(chineseRegex, "**").length;
    if (strLength <= len) {
        return str;
    }
    let newStr = "";
    let newLength = 0;
    for (let i = 0; i < strLength; i++) {
        const singleChar = str.charAt(i).toString();
        newLength += singleChar.match(chineseRegex) ? 2 : 1;
        if (newLength > len) {
            break;
        }
        newStr += singleChar;
    }
    return newStr;
}
function response(data, code, msg) {
    if (data instanceof Error) {
        return {
            code: data.name,
            msg: data.message,
        };
    }
    if (data && data.code && data.msg) {
        return data;
    }
    return {
        code: code === undefined ? 0 : code,
        msg: msg,
        data: data,
    };
}
function curl(url, option) {
    const Q = require("q"), restify = require("restify"), BufferHelper = require("bufferhelper"), iconv = require("iconv-lite");
    const defer = Q.defer(), arr = url.split("/"), host = arr.slice(0, 3).join("/"), path = "/" + arr.slice(3).join("/");
    restify
        .createClient({
        connectTimeout: 1000,
        retry: false,
        url: host,
    })
        .get(path, function (err, req) {
        if (err)
            return defer.reject(err); // connection error
        req.on("result", function (err, res) {
            if (err)
                return defer.reject(err); // HTTP status code >= 400
            const bufferHelper = new BufferHelper();
            res.on("data", function (chunk) {
                bufferHelper.concat(chunk);
            });
            res.on("end", function () {
                const buffer = bufferHelper.toBuffer();
                const encoding = option && option.encoding;
                const content = iconv.decode(buffer, encoding || "utf-8");
                defer.resolve(content);
            });
        });
    });
    return defer.promise;
}
function format(source, params) {
    const getPrototype = Object.prototype.toString;
    params =
        params !== null && /\[object Array\]|\[object Object\]/.test(getPrototype.call(params))
            ? params
            : // eslint-disable-next-line prefer-rest-params
                Array.prototype.slice.call(arguments, 1);
    return source.replace(/#\{(.+?)\}/g, function (match, key) {
        let replacer = params[key];
        if ("[object Function]" === getPrototype.call(replacer)) {
            replacer = replacer(key);
        }
        return replacer === undefined || replacer === null ? "" : replacer;
    });
}
function formatDate(timestamp, format) {
    format = format || "yyyy-MM-dd HH:mm:ss";
    const date = new Date(timestamp), year = date.getFullYear(), month = date.getMonth() + 1, day = date.getDate(), hour = date.getHours(), minute = date.getMinutes(), second = date.getSeconds();
    return format
        .replace("yyyy", year)
        .replace("MM", pad(month, 2))
        .replace("dd", pad(day, 2))
        .replace("HH", pad(hour, 2))
        .replace("mm", pad(minute, 2))
        .replace("ss", pad(second, 2));
}
function currying(fn, ...rest) {
    // eslint-disable-next-line prefer-rest-params
    const args = [].slice.call(arguments, 1);
    return function () {
        // eslint-disable-next-line prefer-rest-params
        const newArgs = args.concat([].slice.call(arguments));
        return fn.apply(this, newArgs);
    };
}
function extend(dest, src) {
    for (const key in src) {
        dest[key] = src[key];
    }
    return dest;
}
function unicode(str) {
    return str.replace(/[\u00FF-\uFFFF]/g, function ($0) {
        return "\\u" + $0.charCodeAt().toString(16);
    });
}
function pad(str, len) {
    str = "" + (str || "");
    const padLen = len - str.length;
    if (padLen > 0) {
        const arr = [];
        arr.length = padLen + 1;
        str = arr.join("0") + str;
    }
    return str;
}
function toCamelCase(str) {
    str = "" + (str || "");
    return str[0].toLowerCase() + str.substr(1);
}
function toSnakeCase(str) {
    return _.snakeCase(str);
}
// 手动解析请求参数：由于游戏发送数据中的content参数使用%格式化了，不能直接使用通用的querystring包解析
function getParams(str) {
    const params = {};
    const arr = str.split("&");
    for (let i = 0, l = arr.length; i < l; i++) {
        const tmp = arr[i].split("=");
        params[tmp[0]] = tmp[1];
    }
    return params;
}
function hexMd5(content, option) {
    option = option || {};
    const crypto = require("crypto");
    const md5 = crypto.createHash("md5");
    option.encoding ? md5.update(content, option.encoding, "hex") : md5.update(content); // encoding: 'utf8/gbk'
    return md5.digest(option.output || "hex");
}
//  token校验
function checkToken(content, params, option) {
    const config = require("./config");
    const tokenKey = config.TOKEN_SALT;
    const md5Key = content ? hexMd5(content, option) : "";
    const token = hexMd5("" + params.time + params.roleid + md5Key + tokenKey);
    return token === params.token;
}
// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
function dateFormat(date, fmt) {
    //author: meizz
    if (!date)
        return;
    const o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds(), //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (const k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
        }
    }
    return fmt;
}
function genUploadPath() {
    const uuid = require("node-uuid");
    const now = new Date(), year = now.getFullYear(), month = now.getMonth() + 1 + "", date = now.getDate() + "", name = uuid.v1().replace(/-/g, ""), dir = year + (month.length === 1 ? "0" + month : month) + "/" + (date.length === 1 ? "0" + date : date);
    return { path: dir + "/" + name, dir: dir, name: name };
}
function toLowerCaseKey(item, recursive) {
    if (!recursive) {
        // eslint-disable-next-line no-var
        var newItem = {};
        // eslint-disable-next-line no-var
        for (var key in item) {
            newItem[key.toLowerCase()] = item[key];
        }
        return newItem;
    }
    let typeStr = Object.prototype.toString.call(item);
    if (typeStr === "[object Object]") {
        newItem = {};
        for (key in item) {
            let val = item[key];
            typeStr = Object.prototype.toString.call(val);
            // eslint-disable-next-line no-constant-condition
            if (typeStr === "[object Object]" || "[object Array]") {
                val = toLowerCaseKey(val, true);
            }
            newItem[key.toLowerCase()] = val;
        }
        return newItem;
    }
    else if (typeStr === "[object Array]") {
        for (let i = 0, l = item.length; i < l; i++) {
            item[i] = toLowerCaseKey(item[i], true);
        }
    }
    return item;
}
function toSnakeCaseKey(item, recursive, matcher) {
    const defaultMatcher = (x) => true;
    matcher = matcher || defaultMatcher;
    if (!recursive) {
        // eslint-disable-next-line no-var
        var newItem = {};
        for (const key in item) {
            if (matcher(key)) {
                newItem[_.snakeCase(key)] = item[key];
            }
            else {
                newItem[key] = item[key];
            }
        }
        return newItem;
    }
    let typeStr = Object.prototype.toString.call(item);
    if (typeStr === "[object Object]") {
        newItem = {};
        for (const key in item) {
            let val = item[key];
            typeStr = Object.prototype.toString.call(val);
            // eslint-disable-next-line no-constant-condition
            if (typeStr === "[object Object]" || "[object Array]") {
                val = toSnakeCaseKey(val, true, matcher);
            }
            if (matcher(key)) {
                newItem[_.snakeCase(key)] = item[key];
            }
            else {
                newItem[key] = item[key];
            }
        }
        return newItem;
    }
    else if (typeStr === "[object Array]") {
        for (let i = 0, l = item.length; i < l; i++) {
            item[i] = toSnakeCaseKey(item[i], true, matcher);
        }
    }
    return item;
}
function getDayStr(timestamp) {
    const date = timestamp ? new Date(timestamp) : new Date();
    return formatDate(date, "yyyyMMdd");
}
function addToList(id, oldStr, maxNum) {
    const list = [id];
    if (oldStr) {
        const hash = {};
        hash[id] = 1;
        let num = 1;
        const arr = oldStr.split(",");
        for (let i = 0, l = arr.length; i < l; i++) {
            if (maxNum && num >= maxNum) {
                break;
            }
            const curId = arr[i];
            if (curId && !hash[curId]) {
                list.push(curId);
                hash[curId] = 1;
                num++;
            }
        }
    }
    return list.join(",");
}
function rmvFromList(id, oldStr) {
    const tmpStr = "," + oldStr + ",", str = "," + id;
    return tmpStr.replace(str, "").replace(/^,|,$/g, "");
}
function deDup(rawList) {
    const list = [];
    const hash = {};
    for (let i = 0, l = rawList.length; i < l; i++) {
        const cur = rawList[i];
        if (!hash[cur]) {
            list.push(cur);
            hash[cur] = 1;
        }
    }
    return list;
}
function mkDirsSync(dirRoot, subPath, mode) {
    const fs = require("fs");
    const path = require("path");
    const pathSep = "/";
    if (!fs.existsSync(dirRoot + pathSep + subPath)) {
        let curPath = dirRoot;
        subPath.split(pathSep).forEach(function (dirname) {
            curPath = path.join(curPath, dirname);
            if (!fs.existsSync(curPath)) {
                if (!fs.mkdirSync(curPath, mode)) {
                    return false;
                }
            }
        });
    }
    return true;
}
function mvFile(src, dest) {
    const fs = require("fs");
    const readStream = fs.createReadStream(src);
    const writeStream = fs.createWriteStream(dest);
    readStream.pipe(writeStream);
    readStream.on("end", function () {
        fs.unlinkSync(src);
    });
}
function getJsonInfo(str, defaultValue) {
    const result = defaultValue || {};
    if (!str) {
        return result;
    }
    try {
        return JSON.parse(str);
    }
    catch (err) {
        return result;
    }
}
function hashKeyList(hash) {
    const list = [];
    for (const key in hash) {
        list.push(key);
    }
    return list;
}
function unifyCols(item, cols, tbCols) {
    const hash = {};
    for (const col in tbCols) {
        hash[col.toLowerCase()] = col;
    }
    const result = {};
    for (let i = 0, l = cols.length; i < l; i++) {
        const arr = cols[i].split(/\s+as\s+/);
        const retKey = arr[1] || arr[0];
        const key = hash[arr[0].toLowerCase()];
        key && (result[retKey] = item[key]);
    }
    return result;
}
function unifyKeys(info, tbCols) {
    const hash = {};
    for (const col in tbCols) {
        hash[col.toLowerCase()] = col;
    }
    const result = {};
    for (const k in info) {
        const key = hash[k.toLowerCase()];
        key && (result[key] = info[k]);
    }
    return result;
}
function unifyValues(info) {
    const result = {};
    for (const key in info) {
        let value = info[key];
        if (value === undefined) {
            continue;
        }
        if (value === null) {
            value = "";
        }
        result[key] = value;
    }
    return result;
}
function unifyRetValues(info, tbCols) {
    const result = {};
    for (const key in info) {
        let value = info[key];
        if (value === "") {
            value = null;
        }
        else if (value && tbCols[key] === "number") {
            value = parseInt(value, 10);
        }
        result[key] = value;
    }
    return result;
}
function unifyResults(results) {
    const list = [];
    for (let i = 0, l = results.length; i < l; i++) {
        const item = results[i];
        if (item) {
            list.push(item);
        }
    }
    return list;
}
function unifyUpResults(results) {
    const result = {};
    for (let i = 0, l = results.length; i < l; i++) {
        const item = results[i];
        for (const k in item) {
            const v = item[k];
            typeof v === "number" && (result[k] = (result[k] || 0) + v);
        }
    }
    return result;
}
function keyToRecordHash(records, key) {
    return _.reduce(records, function (result, record) {
        result[record[key]] = record;
        return result;
    }, {});
}
function embeddedOn(item, arrayB, aKey, BKey, embeddedKey) {
    const keyToArrayB = keyToRecordHash(arrayB, BKey);
    const embeddedFunc = function (record) {
        let embeddedRecord;
        if (_.isArray(record[aKey])) {
            embeddedRecord = record[aKey].map(function (r) {
                return keyToArrayB[r];
            });
        }
        else {
            embeddedRecord = keyToArrayB[record[aKey]];
        }
        record[embeddedKey] = embeddedRecord || null;
        return record;
    };
    if (_.isArray(item)) {
        return _.map(item, embeddedFunc);
    }
    else {
        embeddedFunc(item);
        return item;
    }
}
function capitalizeFirstLetter(string) {
    return string[0].toUpperCase() + string.slice(1);
}
function csvStrToArray(str, delimiter = ",") {
    if (str) {
        return _.chain(_.split(str, delimiter)).value();
    }
    else {
        return [];
    }
}
function arrayToCsv(arr, joinChar = ",") {
    return arr.join(joinChar);
}
function csvStrToIntArray(str, delimiter) {
    const arr = csvStrToArray(str, delimiter);
    const list = [];
    for (const e of arr) {
        if (e) {
            const num = parseInt(e, 10);
            list.push(num);
        }
    }
    return list;
}
function isJson(str) {
    try {
        JSON.parse(str);
    }
    catch (e) {
        return false;
    }
    return true;
}
function decodeGBKUri(str) {
    const iconv = require("iconv-lite");
    const bytes = [];
    for (let i = 0; i < str.length;) {
        if (str[i] === "%") {
            i++;
            bytes.push(parseInt(str.substring(i, i + 2), 16));
            i += 2;
        }
        else {
            bytes.push(str.charCodeAt(i));
            i++;
        }
    }
    const buf = new buffer_1.Buffer(bytes);
    return iconv.decode(buf, "gbk");
}
function decodeGBKUriSafe(str) {
    try {
        return decodeGBKUri(str);
    }
    catch (err) {
        return str;
    }
}
function runAsync(promise) {
    return promise.catch(function (err) {
        require("../common/logger").error(err);
    });
}
/**
 * 获取被两个'#'包裹的文本
 * @param {String} str
 * @return {Array} texts
 */
function getHashedText(str) {
    const regex = /#[^#]+#/g;
    let matches = str.match(regex);
    matches = _.map(matches, (s) => s.substr(1, s.length - 2));
    return matches;
}
// 过滤对象中所有key不为UserName的属性
function filterUrs(obj) {
    return filterProperty(obj, (key, value) => key !== "UserName");
}
function filterProperty(obj, predicate) {
    if (_.isArray(obj)) {
        return _.map(obj, (x) => filterUrs(x));
    }
    else if (_.isObject(obj)) {
        const newObj = {};
        _.forEach(obj, (value, key) => {
            if (predicate(key, value)) {
                newObj[key] = filterUrs(value);
            }
        });
        return newObj;
    }
    else {
        return obj;
    }
}
function readUntil(str, prediction) {
    let readStr = "";
    let i = 0;
    while (str[i]) {
        if (prediction(str[i], i)) {
            break;
        }
        readStr += str[i++];
    }
    return readStr;
}
//res.hi 不支持https
const HTTPS_HOST_NAME_MAP = {
    "res.hi.netease.com": "hi.res.netease.com",
};
function toHttps(str) {
    const url = require("url");
    const urlObj = url.parse(str);
    if (urlObj.protocol === "http:") {
        urlObj.protocol = "https:";
        const newHostName = HTTPS_HOST_NAME_MAP[urlObj.hostname];
        if (newHostName) {
            urlObj.host = newHostName;
            if (urlObj.port) {
                urlObj.host = newHostName + ":" + urlObj.port;
            }
            else {
                url.host = newHostName;
            }
            urlObj.hostname = newHostName;
        }
        return url.format(urlObj);
    }
    else {
        return str;
    }
}
function countBy(iterate, predication) {
    return iterate && _.sumBy(iterate, (x) => (predication(x) ? 1 : 0));
}
function keysToCamelCase(obj) {
    if (obj === null || typeof obj !== "object") {
        return obj;
    }
    else if (_.isArray(obj)) {
        return _.map(obj, (item) => keysToCamelCase(item));
    }
    else {
        const camelCaseObj = {};
        _.keys(obj).forEach((key) => {
            camelCaseObj[_.camelCase(key)] = keysToCamelCase(obj[key]);
        });
        return camelCaseObj;
    }
}
function getMondayZero(time) {
    const today = time ? new Date(time) : new Date();
    const today_zero = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime(), // 今天凌晨时间戳
    today_day = today.getDay() || 7; // 当天周几
    return today_zero - 86400 * 1000 * (today_day - 1);
}
function hashJoin(arr1, arr2, joinBy) {
    return _.map(arr1, (x) => {
        const where = {};
        where[joinBy] = x[joinBy];
        return _.assign({}, x, _.find(arr2, where));
    });
}
function emojiStrip(str) {
    if (str) {
        return str.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, "");
    }
    else {
        return "";
    }
}
const co = (gen) => bluebird.coroutine(gen)();
exports.co = co;
function uniq(arr) {
    return _.uniq(arr);
}
function IsJsonString(str) {
    try {
        JSON.parse(str);
    }
    catch (e) {
        return false;
    }
    return true;
}
function mergeIds(arr1, arr2) {
    const set = new Set();
    for (const e of arr1) {
        set.add(e);
    }
    for (const e of arr2) {
        set.add(e);
    }
    const ret = Array.from(set);
    return ret;
}
function getRepeatCsv(size, repeatChar) {
    return _.fill(new Array(size), repeatChar).join(",");
}
function getMatches(string, regex, index = 1) {
    const matches = [];
    let match;
    while ((match = regex.exec(string))) {
        matches.push(match[index]);
    }
    return matches;
}
function replaceHttps(url) {
    if (url) {
        return url.replace(/^http:\/\//, "https://");
    }
    else {
        return "";
    }
}
function isBetweenDates(start, end) {
    const now = Date.now();
    return start.getTime() <= now && end.getTime() >= now;
}
function contains(arr, ele) {
    const set = new Set(arr);
    return set.has(ele);
}
function fillCsvStr(char, len) {
    const csvStr = new Array(len).fill(char).join(",");
    return csvStr;
}
function removeLeadingZero(str) {
    return str.replace(/^0+/, "");
}
function getListByPagination(arr, pagination) {
    const hotListChunks = _.chunk(arr, pagination.pageSize);
    const curPageList = hotListChunks[pagination.page - 1] || [];
    return curPageList;
}
function getPageMeta(pagination, totalCount) {
    const totalPage = Math.ceil(totalCount / pagination.pageSize);
    const curPage = Math.min(pagination.page, totalPage);
    return { curPage: curPage, totalPage: totalPage, totalCount: totalCount };
}
function getPageDataByList(list, pagination) {
    const chunks = _.chunk(list, pagination.pageSize);
    return chunks[pagination.page - 1] || [];
}
function keyToRecordMap(records, key) {
    const map = new Map();
    for (const r of records) {
        map.set(r[key], r);
    }
    return map;
}
function keyToRecordMapByKeys(records, keys) {
    const map = new Map();
    for (const r of records) {
        const key = keys.map(k => r[k]).join("_");
        map.set(key, r);
    }
    return map;
}
function isValidCallbackName(name) {
    return /^[0-9a-zA-Z_-]+$/.test(name);
}
function getCell(arr, c) {
    const index = c.charCodeAt(0) - "A".charCodeAt(0);
    return arr[index];
}
function isAll(arr, predicate) {
    for (const r of arr) {
        if (!predicate(r)) {
            return false;
        }
    }
    return true;
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function mapToList(map, props) {
    const list = [];
    for (const [key, value] of map) {
        list.push({ [props[0]]: key, [props[1]]: value });
    }
    return list;
}
function hexSha256(payload) {
    return crypto.createHash("sha256").update(payload).digest("hex");
}
function getTimeStampSeconds() {
    return Math.round(Date.now() / 1000);
}
function decodeUtf8HexString(bufStr) {
    if (bufStr && bufStr.startsWith("\\x")) {
        const urlEncode = bufStr.replace(/\\x/g, "%");
        return decodeURIComponent(urlEncode);
    }
    else {
        return bufStr;
    }
}
function repeat(ele, size) {
    const arr = [];
    for (let i = 0; i < size; i++) {
        arr.push(ele);
    }
    return arr;
}
function getSunWeekStartDayStr(date) {
    return moment(date).startOf("week").format("YYYY-MM-DD");
}
function p(obj, message) {
    const info = JSON.stringify(obj, null, 2);
    console.log(info, message);
}
function cacheKeyGen(kp, cacheObj) {
    const coKey = Object.keys(cacheObj)
        .filter((k) => !!cacheObj[k])
        .map((k) => _.snakeCase(k) + "_" + cacheObj[k])
        .join(":");
    if (coKey) {
        return kp + ":" + coKey;
    }
    else {
        return kp;
    }
}
function pickBy(obj, props) {
    const keys = Object.keys(obj);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pickObj = {};
    const propSet = new Set(props);
    for (const k of keys) {
        if (propSet.has(k)) {
            pickObj[k] = obj[k];
        }
    }
    return pickObj;
}
function omitBy(obj, keys) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const ret = {};
    const excludeSet = new Set(keys);
    for (const key in obj) {
        if (!excludeSet.has(key)) {
            ret[key] = obj[key];
        }
    }
    return ret;
}
const ALG_STRING = "aes-128-cbc";
const buffer_1 = require("buffer");
const constants_1 = require("./constants");
const errorCode_1 = require("../errors/errorCode");
/**
 *  带16个buffer长度的特定的AES加密
 *http://brucewar.cn/2017/05/06/Node-js-aes-128-cbc%E5%8A%A0%E5%AF%86%E5%92%8C%E8%A7%A3%E5%AF%86/ 参考资料
 * @param {*} data
 * @param {*} key
 * @return {*}
 */
function aesEncrypt(data, key) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ALG_STRING, key, iv);
    cipher.setAutoPadding(true);
    const cipherChunks = [];
    cipherChunks.push(cipher.update(data, "utf8", "binary"));
    cipherChunks.push(cipher.final("binary"));
    const bufferList = buffer_1.Buffer.concat([iv, buffer_1.Buffer.from(cipherChunks.join(""), "binary")]);
    return buffer_1.Buffer.from(bufferList).toString("base64");
}
/**
 * AES解密
 *
 * @param {*} data
 * @param {*} key
 * @return {*}
 */
function aesDecrypt(encrypted, key) {
    const iv = crypto.randomBytes(16);
    const decipher = crypto.createDecipheriv(ALG_STRING, key, buffer_1.Buffer.from(iv));
    decipher.setAutoPadding(true);
    const cipherChunks = [];
    cipherChunks.push(decipher.update(buffer_1.Buffer.from(encrypted, "base64").toString("binary"), "binary", "utf8"));
    cipherChunks.push(decipher.final("utf8"));
    return cipherChunks.join("");
}
function isNumeric(n) {
    return !isNaN(parseFloat(n)) && isFinite(n);
}
function getFirstXForwardedForIp(ipStr) {
    const ipList = csvStrToArray(ipStr);
    return ipList[0];
}
function getIp(req) {
    return (getFirstXForwardedForIp(req.headers["x-forwarded-for"]) ||
        req.headers["x-real-ip"] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        req.connection.socket.remoteAddress);
}
function callOperation(op, opName, logger) {
    async function wrapOp(...args) {
        try {
            logger.info({ ...args }, `Call${opName}Start`);
            const ret = await op(...args);
            logger.info({ ...args, ret }, `Call${opName}End`);
            return ret;
        }
        catch (err) {
            logger.info({ ...args, err }, `Call${opName}Error`);
        }
    }
    return wrapOp;
}
// 是否是经典服
function isServerEnvOriginal() {
    // 默认经典服，兼容为正确配置的情况
    if (!config_1.server.deployEnv) {
        return true;
    }
    return config_1.server.deployEnv === "original";
}
// 是否是怀旧服
function isServerEnvHuaijiu() {
    return config_1.server.deployEnv === "huaijiu";
}
function isRoleInfoInHuaijiu(roleId) {
    const serverId = roleId % 10000;
    return serverId >= constants_1.HuaijiuMinServerId;
}
const BASE64_REGEXP = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{4})$/;
function isBase64Str(str) {
    return BASE64_REGEXP.test(str);
}
/** Encode to base64 */
function encodeToBase64(str) {
    return buffer_1.Buffer.from(str).toString("base64");
}
function isNosUrl(url) {
    return !!/nosdn\.127\.net/.exec(url) || !!/nos\.netease\.com/.exec(url) || !!/nos-jd\.163yun\.com/.exec(url);
}
function isValidPhotoUrl(url) {
    return !!/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/.exec(url);
}
function redisKeyGen(kp, cacheObj) {
    const coKey = Object.keys(cacheObj)
        .filter((k) => !!cacheObj[k])
        .map((k) => _.snakeCase(k) + "_" + cacheObj[k])
        .join(":");
    const key = kp + ":" + coKey;
    return key;
}
function isFpUrl(url) {
    return !!url && url.includes("fp.ps.netease.com");
}
function isEmptyStr(str) {
    return !str || _.trim(str) === "";
}
function checkValidText(text, maxLen) {
    if (isEmptyStr(text)) {
        throw errorCode_1.errorCodesV2.TextIsEmpty;
    }
    if (text.length > maxLen) {
        throw errorCode_1.errorCodesV2.TextIsTooLong;
    }
}
//# sourceMappingURL=util.js.map