"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isWithInRange = isWithInRange;
exports.endOfDay = endOfDay;
exports.isBefore = isBefore;
exports.isAfter = isAfter;
exports.startOfMinute = startOfMinute;
exports.startOfHour = startOfHour;
exports.formatDate = formatDate;
exports.getDayStrV2 = getDayStrV2;
exports.format = format;
exports.addMonths = addMonths;
exports.parseTimestamp = parseTimestamp;
exports.minDate = minDate;
exports.diffDateMilliseconds = diffDateMilliseconds;
exports.formatDuration = formatDuration;
exports.validateBirthTimeFormat = validateBirthTimeFormat;
const moment = require("moment");
const util_1 = require("./util");
function isWithInRange(date, startDate, endDate) {
    const time = date.getTime();
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    if (startTime > endTime) {
        throw new Error("The start of the range cannot be after the end of the range");
    }
    return time >= startTime && time <= endTime;
}
// 返回一个新的实例
function cloneDate(date) {
    return new Date(date.getTime());
}
function endOfDay(date) {
    const newDate = cloneDate(date);
    newDate.setHours(23, 59, 59, 999);
    return newDate;
}
function isBefore(date1, date2) {
    return date1.getTime() < date2.getTime();
}
function isAfter(date1, date2) {
    return date1.getTime() > date2.getTime();
}
function startOfMinute(date) {
    const newDate = cloneDate(date);
    newDate.setSeconds(0, 0);
    return newDate;
}
function startOfHour(date) {
    const newDate = cloneDate(date);
    newDate.setMinutes(0, 0, 0);
    return newDate;
}
function formatDate(date, dateFormat) {
    return format(date, dateFormat);
}
function getDayStrV2(date) {
    return format(date, "yyyyMMdd");
}
function format(date, format) {
    format = format || "yyyy-MM-dd HH:mm:ss";
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();
    return format
        .replace("yyyy", year)
        .replace("MM", (0, util_1.pad)(month, 2))
        .replace("dd", (0, util_1.pad)(day, 2))
        .replace("HH", (0, util_1.pad)(hour, 2))
        .replace("mm", (0, util_1.pad)(minute, 2))
        .replace("ss", (0, util_1.pad)(second, 2));
}
function addMonths(date, amount) {
    const newDate = cloneDate(date);
    return moment(newDate).add(amount, "months").toDate();
}
function parseTimestamp(timestamp) {
    const parsed = parseFloat(timestamp.toString());
    if (!isNaN(parsed) && isFinite(parsed)) {
        const milliseconds = timestamp.toString().length === 13 ? parsed : parsed * 1000;
        const date = new Date(milliseconds);
        if (date.toISOString().slice(0, 4) !== "1970") {
            return date;
        }
    }
    return null;
}
function minDate(date1, date2) {
    return date1.getTime() < date2.getTime() ? date1 : date2;
}
/**
 * Calculates the difference in milliseconds between two dates.
 *
 * @param date1 - The first date.
 * @param date2 - The second date.
 * @returns The difference in milliseconds between the two dates.
 */
function diffDateMilliseconds(date1, date2) {
    return date1.getTime() - date2.getTime();
}
function formatDuration(duration) {
    const d = moment.duration(duration);
    let str = "";
    if (d.months() > 0) {
        str = d.months() + "个月";
    }
    if (d.days() > 0) {
        str += d.days() + "天";
    }
    if (d.hours() > 0) {
        str = d.hours() + "小时";
    }
    if (d.minutes() > 0) {
        str += d.minutes() + "分";
    }
    if (d.seconds() > 0) {
        str += d.seconds() + "秒";
    }
    return str;
}
function validateBirthTimeFormat(birthTime) {
    const parsedDate = moment(birthTime, 'YYYY-MM-DD HH:mm', true);
    return parsedDate.isValid();
}
//# sourceMappingURL=dateUtil.js.map