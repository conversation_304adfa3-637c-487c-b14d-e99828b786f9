"use strict";
console.log("🚀 Loading config from ci");
const _ = require("lodash");
const defaultCfg = require("./config.all");
const path = require("path");
const config = _.cloneDeep(defaultCfg);
config.yinyunLog.printInConsole = process.env.LOG_CONSOLE === "true";
config.db = {
    connectionLimit: 5,
    host: "md-test.leihuo.netease.com",
    port: 3307,
    user: "unit_test",
    password: "unit_test",
    database: "d30_md_ut",
    charset: "utf8mb4",
};
config.slaveDb = config.db;
config.testDb = config.db;
config.testCfg.skip_audit = true;
config.testCfg.test_env = true;
config.testCfg.skip_ip_auth = true;
config.testCfg.skip_skey_check = true;
config.testCfg.skip_token = true;
config.testCfg.skip_neDun_verify = true;
config.testCfg.no_forward_proxy = true;
config.testCfg.request_debug = process.env.RQ_DEBUG === "true";
config.testCfg.db_debug = process.env.DB_DEBUG === "true";
config.testCfg.redis_debug = process.env.REDIS_DEBUG === "true";
config.testCfg.skip_openId_auth = true;
config.testCfg.req_log = process.env.REQ_LOG === "true";
config.fakeAdultDB = config.db;
config.log.printInConsole = process.env.LOG_CONSOLE === "true";
config.redis = {
    hosts: ["md-test.leihuo.netease.com"],
    host: "md-test.leihuo.netease.com",
    port: 16379,
    db: 0,
    password: "unit_test",
    no_ready_check: false,
    prefix: "nsh_md_ut:",
};
config.Features.server_annal = true;
config.Features.guild_photo_wall = true;
config.Features.activityTakePhoto = true;
config.Features.garden = true;
config.log.dir = path.join(__dirname, "../../log");
config.log.yunyingdir = config.log.dir;
//@ts-ignore
config.cookieCfg = {
    path: "/",
};
config.testCfg.skip_skey_check = true;
config.testCfg.skip_level = true;
config.clubWebCfg.loginCookieCheck = true;
config.Features.fcmBlackAuth = true;
config.Features.damageStat = true;
config.Features.skillCombo = true;
config.Features.appearancePaint = true;
config.bunyanPoPoAlertCfg.enable = false;
module.exports = config;
//# sourceMappingURL=config.ci.js.map