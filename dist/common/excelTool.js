"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseXlsxContent = parseXlsxContent;
exports.generateOneSheetExcelBuff = generateOneSheetExcelBuff;
const xlsx = require("node-xlsx");
const helper_1 = require("../helper");
const logger_1 = require("../logger");
async function parseXlsxContent(sheet1Data, parseCfg, sheetName) {
    if (!sheet1Data || sheet1Data.length == 0) {
        throw { msg: `${sheetName || ""},表格数据错误` };
    }
    if (JSON.stringify(sheet1Data[0]) !== JSON.stringify(parseCfg.title)) {
        logger_1.logger.info(`sheetTitle:${JSON.stringify(sheet1Data[0])},actualTitle:${JSON.stringify(parseCfg.title)}`);
        throw { msg: `${sheetName || ""},表头必须是${parseCfg.title}` };
    }
    let list = [];
    for (let i = 1; i < sheet1Data.length; i++) {
        let tmpInfo = {};
        for (let j in sheet1Data[i]) {
            tmpInfo[parseCfg.titleKey[j]] = sheet1Data[i][j];
        }
        try {
            await (0, helper_1.checkParams)(tmpInfo, parseCfg.keyFormat);
        }
        catch (err) {
            logger_1.logger.info(`${sheetName || ""},第${i + 1}行数据错误, ${JSON.stringify(err)}`);
            throw { msg: `${sheetName || ""},第${i + 1}行数据错误` };
        }
        list.push(tmpInfo);
    }
    return list;
}
async function generateOneSheetExcelBuff(list, cfg, sheetName = 'sheet1') {
    let sheetData = [cfg.title];
    for (let item of list) {
        let tmpArr = [];
        for (let key of cfg.titleKey) {
            tmpArr.push(item[key] !== undefined ? item[key] : null);
        }
        sheetData.push(tmpArr);
    }
    return xlsx.build([
        {
            name: sheetName,
            data: sheetData,
            options: {},
        },
    ]);
}
//# sourceMappingURL=excelTool.js.map