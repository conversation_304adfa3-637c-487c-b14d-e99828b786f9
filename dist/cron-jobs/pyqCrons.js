"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config = require("../common/config");
const HotMomentsCache = require("../services/HotMomentsCache");
const taskManager_1 = require("./taskManager");
const delayTasks_1 = require("./delayTasks");
const recalMomentHotValue_1 = require("./recalMomentHotValue");
const weekRenQiMigrator_1 = require("../services/weekRenQiMigrator");
const follow_1 = require("../services/follow");
const nshServerList_1 = require("../services/nshServerList");
const moments_1 = require("../services/official_accounts/moments");
const playerTransfer_1 = require("../services/playerTransfer");
const config_1 = require("../common/config");
const fcm_1 = require("../services/fcm");
const logger_1 = require("../logger");
const MarriageTransferService = require("../services/marriageTransfer");
const service_1 = require("../components/marriagePhotoWall/service");
const holidayModel_1 = require("../components/fcm/holidayModel");
const holiday_1 = require("../components/fcm/holiday");
const healthApp_1 = require("../healthApp");
const astrologyCommentRankService_1 = require("../services/astrology/astrologyCommentRankService");
const context_1 = require("../context");
const astrologyWeeklyCommentRankService_1 = require("../services/astrology/astrologyWeeklyCommentRankService");
let TaskSwitch = config.CronTaskSwitch;
(0, healthApp_1.healthAppListen)(config.cronPort.pyqCrons);
let manager = taskManager_1.TaskManager.create("pyqCrons", TaskSwitch);
// 热度值随时间衰减
manager.add({
    name: "RefreshMomentHotValue",
    cronTime: "35 */2 * * *",
    execute: async function () {
        return (0, recalMomentHotValue_1.updatePyqMomentsHot)();
    },
    checkUrl: config.cronTaskCheckUrl.RefreshMomentHotValue
});
// 重建热门列表缓存
manager.add({
    name: "RebuildHotMomentCache",
    cronTime: "*/10 * * * *", // At every 10th minute
    execute: async function () {
        return HotMomentsCache.refreshAll();
    },
    checkUrl: config.cronTaskCheckUrl.RebuildHotMomentCache
});
manager.add({
    name: "PublicOfficialMoment",
    cronTime: taskManager_1.CronPattern.EVERY_MINUTE,
    execute: async function () {
        return (0, moments_1.triggerPublicMoment)();
    },
});
manager.add({
    name: "RefreshServerList",
    cronTime: taskManager_1.CronPattern.EVERY_THIRTY_MINUTE,
    execute: async function () {
        const serverList = await nshServerList_1.ServerList.refresh();
        logger_1.cronLogger.info({ serverList }, "RefreshServerListFinish");
    },
    checkUrl: config.cronTaskCheckUrl.RefreshServerList
});
manager.add({
    name: "WeekRenQiMigrator",
    cronTime: taskManager_1.CronPattern.EVERY_TEN_MINUTE,
    execute: async function () {
        return (0, weekRenQiMigrator_1.autoMigrateWeekRenQi)();
    },
});
manager.add({
    name: "RefreshRecommendList",
    cronTime: taskManager_1.CronPattern.EVERY_MINUTE,
    execute: async function () {
        return (0, follow_1.refreshRecommendCache)();
    },
    checkUrl: config.cronTaskCheckUrl.RefreshRecommendList
});
manager.add({
    name: "TriggerDelayTask",
    cronTime: taskManager_1.CronPattern.EVERY_SECOND,
    execute: async function () {
        return (0, delayTasks_1.triggerDelayTask)();
    },
});
manager.add({
    name: "ProcessReadyTask",
    cronTime: taskManager_1.CronPattern.EVERY_SECOND,
    execute: async function () {
        return (0, delayTasks_1.processReadyTask)();
    },
});
manager.add({
    name: "FixUnTransfer",
    cronTime: taskManager_1.CronPattern.EVERY_TEN_MINUTE,
    execute: async function () {
        return (0, playerTransfer_1.fixUnTransfer)();
    },
});
manager.add({
    name: "FcmStatAlarmCheck",
    cronTime: '59 23 * * *',
    execute: async function () {
        return (0, fcm_1.fcmStatAlarmCheck)();
    },
});
manager.add({
    name: "AutoCorrectHonorBindData",
    cronTime: '30 4 * * *',
    execute: async function () {
        return (0, playerTransfer_1.autoCorrectHonorBindData)();
    }
});
manager.add({
    // 防沉迷数据上传, 1s处罚10次上传，每次尝试去最大获取128条合并在一次上传
    name: 'BehaviorCollectionUpload',
    cronTime: taskManager_1.CronPattern.EVERY_SECOND,
    execute: async function name() {
        for (let i = 0; i < config_1.FcmWlcCfg.behaviorCollectQPS; i++) {
            await (0, fcm_1.consumeBehaviorQueue)(config_1.FcmWlcCfg.maxBatchBehaviorUpload);
        }
    }
});
manager.add({
    name: "FixMarriageUnTransfer",
    cronTime: taskManager_1.CronPattern.EVERY_TEN_MINUTE,
    execute: async function () {
        return MarriageTransferService.fixUnTransfer();
    },
});
manager.add({
    name: "FixMarriagePhotoWallDuplicate",
    cronTime: taskManager_1.CronPattern.EVERY_TEN_MINUTE,
    execute: async function () {
        return (0, service_1.fixPhotoWallDuplicate)();
    },
    runOnInit: true
});
manager.add({
    name: "HolidayCNDataSync",
    cronTime: config.holidayCnCfg.cronPattern,
    execute: async function () {
        const month = new Date().getMonth();
        const year = new Date().getFullYear();
        if (month >= 10) {
            // 国务院在10月以后可能公布下一年假期, 所以10月以后可以刷新下一年
            await (0, holidayModel_1.refreshHoliday)(year + 1);
        }
        else {
            await (0, holidayModel_1.refreshHoliday)(year);
        }
        await holiday_1.HolidayCNCacheClass.getInstance().refresh(year);
    },
    runOnInit: true,
});
manager.add({
    name: "AstrologyCommentRankRefresh",
    cronTime: config.astrologyCfg.commentRankRefreshCronTime,
    execute: async function () {
        const ctx = context_1.Context.emptyContext();
        Promise.all([
            astrologyCommentRankService_1.AstrologyCommentRankService.getInstance().warmupCache(ctx),
            astrologyWeeklyCommentRankService_1.AstrologyWeeklyCommentRankService.getInstance().warmupCache(ctx),
        ]);
    },
});
if (require.main === module) {
    manager.runAll();
}
else {
    exports.manager = manager;
}
//# sourceMappingURL=pyqCrons.js.map