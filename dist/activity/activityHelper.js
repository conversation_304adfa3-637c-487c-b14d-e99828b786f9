"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PHP_API = exports.PlayerApplyMarker = exports.API_HOST = void 0;
exports.getActivityApiHost = getActivityApiHost;
const config_1 = require("../common/config");
const redis_1 = require("../common/redis");
function getActivityApiHost() {
    if (config_1.testCfg.test_env) {
        return 'http://***************:8081';
    }
    else {
        return 'https://nshssl.hi.163.com/file_mg';
    }
}
exports.API_HOST = getActivityApiHost();
class PlayerApplyMarker {
    constructor(roleId, name, expire) {
        this.roleId = roleId;
        this.name = name;
        this.expire = expire;
        this.key = '';
        this.key = `activity:${name}:role:${roleId}:mark`;
    }
    async markApply() {
        let ret = await (0, redis_1.getRedis)().setexAsync(this.key, this.expire, '1');
        return ret;
    }
    async isFirst() {
        let ret = await (0, redis_1.getRedis)().existsAsync(this.key);
        return ret === redis_1.IExistResult.NotExist;
    }
    static create(params) {
        return new this(params.roleId, params.name, params.expire);
    }
}
exports.PlayerApplyMarker = PlayerApplyMarker;
var PHP_API;
(function (PHP_API) {
    PHP_API.API_SUCC_CODE = 1;
})(PHP_API || (exports.PHP_API = PHP_API = {}));
//# sourceMappingURL=activityHelper.js.map