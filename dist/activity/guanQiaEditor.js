"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onAttendTopic = onAttendTopic;
exports.isFirstApply = isFirstApply;
exports.markApply = markApply;
const util_1 = require("../common/util");
const activityHelper_1 = require("./activityHelper");
const logger_1 = require("../logger");
const PyqActivityTopic_1 = require("../models/PyqActivityTopic");
const PyqMoments_1 = require("../models/PyqMoments");
const httpLib = require("../common/request");
const RoleInfos_1 = require("../models/RoleInfos");
const serverList_1 = require("../services/serverList");
const config_all_1 = require("../common/config.all");
let logger = logger_1.activityLogger.child({ activity: 'GuanQia' });
const TOPIC_NAME = '关卡大赛';
const APPLY_LIMIT = {
    MIN_LEVEL: 65,
    FIRST: {
        MIN_PHOTO: 3,
        MIN_VIDEO: 1,
    },
    OTHER: {
        MIN_PHOTO: 1,
        MIN_VIDEO: 1,
    },
    START_DATE: new Date('2019-09-04 16:00'),
    END_DATE: new Date('2019-09-16 16:00')
};
const APPLY_API = activityHelper_1.API_HOST + '/public/share/common_activity_vote/nsh/zhuangyuan2019/apply/chusai/server';
function isApplyOpen() {
    return (0, util_1.isBetweenDates)(APPLY_LIMIT.START_DATE, APPLY_LIMIT.END_DATE);
}
async function onAttendTopic(params) {
    let name = await PyqActivityTopic_1.ActivityTopic.getNameById(params.topicId);
    if (name === TOPIC_NAME) {
        if (isApplyOpen()) {
            return attendByMomentId(params.momentId).catch(err => {
                logger.error('onAttendError', { err: err });
            });
        }
        else {
            logger.warn('ApplyClose', { start: APPLY_LIMIT.START_DATE, end: APPLY_LIMIT.END_DATE });
        }
    }
}
function getMark(roleId) {
    return activityHelper_1.PlayerApplyMarker.create({ roleId: roleId, name: 'guanQia', expire: 90 * config_all_1.ONE_DAY_SECONDS });
}
async function isFirstApply(roleId) {
    let mark = getMark(roleId);
    return mark.isFirst();
}
async function markApply(roleId) {
    let mark = getMark(roleId);
    return mark.markApply();
}
async function isMatchApplyLimit(record, imgUrls, videoUrls) {
    let isFirst = await isFirstApply(record.RoleId);
    let rule;
    if (isFirst) {
        rule = APPLY_LIMIT.FIRST;
    }
    else {
        rule = APPLY_LIMIT.OTHER;
    }
    let match = imgUrls.length >= rule.MIN_PHOTO || videoUrls.length >= rule.MIN_VIDEO;
    if (!match) {
        logger.info('NotMatchApplyLimit', { record: record, first: isFirst, imgCount: imgUrls.length, videoCount: videoUrls.length });
    }
    return match;
}
async function attendByMomentId(momentId) {
    let record = await PyqMoments_1.Moment.findById(momentId, ['ID', 'Text', 'ImgList', 'VideoList', 'RoleId']);
    if (record) {
        let imgUrls = (0, util_1.csvStrToArray)(record.ImgList);
        let videoUrls = (0, util_1.csvStrToArray)(record.VideoList);
        let isMatchApply = await isMatchApplyLimit(record, imgUrls, videoUrls);
        if (isMatchApply && !PyqActivityTopic_1.ActivityTopic.isOnlyContainTopic(record.Text)) {
            let text = PyqActivityTopic_1.ActivityTopic.removeTopicLink(record.Text);
            return requestAttendApi(record, imgUrls, videoUrls, text);
        }
    }
}
async function getPlayerInfo(roleId) {
    let record = await RoleInfos_1.RoleInfo.findOne({ RoleId: roleId }, ['RoleName', 'UserName', 'ServerId', 'Gender', 'SubGender', 'JobId', 'Level']);
    return record;
}
async function requestAttendApi(record, imgs, videos, intr) {
    let roleId = record.RoleId;
    imgs = imgs.map(url => (0, util_1.replaceHttps)(url));
    videos = videos.map(url => (0, util_1.replaceHttps)(url));
    let playerInfo = await getPlayerInfo(roleId);
    let serverHash = await (0, serverList_1.getServerHash)();
    if (playerInfo) {
        let serverName = serverHash[playerInfo.ServerId];
        let data = {
            momentId: record.ID,
            urs: playerInfo.UserName,
            roleid: roleId,
            rolename: playerInfo.RoleName,
            grade: playerInfo.Level,
            classid: playerInfo.JobId,
            subGender: playerInfo.SubGender,
            imgs: imgs,
            video: videos,
            mark: intr,
            serverid: playerInfo.ServerId,
            servername: serverName
        };
        if (playerInfo.Level >= APPLY_LIMIT.MIN_LEVEL) {
            logger.info('Prepare attend', data);
            let result = await httpLib.request({
                method: "POST",
                url: APPLY_API,
                body: data
            });
            if (result.code == activityHelper_1.PHP_API.API_SUCC_CODE) {
                await markApply(roleId);
                logger.info('ApplySuccess', { result: result, data: data });
            }
            else {
                logger.info('ApplyFailed', { result: result, data: data });
            }
            return result;
        }
        else {
            logger.info('LevelNotReach', { level: playerInfo.Level, roleId: roleId });
        }
    }
    else {
        logger.error('RoleInfoNotFound', { roleId: roleId });
    }
}
//# sourceMappingURL=guanQiaEditor.js.map