"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Context = void 0;
const util_1 = require("./common/util");
class Context {
    constructor() {
        this.values = new Map();
    }
    set(key, value) {
        if (this.req) {
            if (!this.req.__md_container) {
                this.req.__md_container = new Map();
            }
            this.req.__md_container.set(key, value);
        }
        else {
            this.values.set(key, value);
        }
    }
    setReq(req) {
        this.request = req;
    }
    get(key) {
        if (this.req) {
            if (!this.req.__md_container) {
                return undefined;
            }
            return this.req.__md_container.get(key);
        }
        else {
            return this.values.get(key);
        }
    }
    get req() {
        return this.request;
    }
    toJSON() {
        if (this.request) {
            const info = {
                requestId: this.request.id(),
                url: this.request.url,
            };
            return info;
        }
        else {
            return null;
        }
    }
    getIp() {
        const req = this.req;
        if (req && req.headers) {
            return (0, util_1.getIp)(req);
        }
        return "";
    }
    static createWithRequest(req) {
        const ctx = new Context();
        ctx.setReq(req);
        return ctx;
    }
    static emptyContext() {
        const ctx = new Context();
        return ctx;
    }
}
exports.Context = Context;
//# sourceMappingURL=context.js.map