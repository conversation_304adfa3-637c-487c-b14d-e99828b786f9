"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertToEAstrologyUserFortuneTypeStr = convertToEAstrologyUserFortuneTypeStr;
function convertToEAstrologyUserFortuneTypeStr(fortuneType) {
    switch (fortuneType) {
        case "basic" /* EAstrologyUserFortuneType.BASIC */:
            return "\u57FA\u672C\u8FD0\u52BF" /* HoroscopeFortuneType.BASIC */;
        case "wealth" /* EAstrologyUserFortuneType.WEALTH */:
            return "\u8D22\u8FD0" /* HoroscopeFortuneType.WEALTH */;
        case "career" /* EAstrologyUserFortuneType.CAREER */:
            return "\u4E8B\u4E1A\u4E0E\u5B66\u4E1A" /* HoroscopeFortuneType.CAREER */;
        case "love" /* EAstrologyUserFortuneType.LOVE */:
            return "\u60C5\u611F" /* HoroscopeFortuneType.LOVE */;
        default:
            throw new Error(`Invalid fortune type: ${fortuneType}`);
    }
}
//# sourceMappingURL=astrologyConstant.js.map