"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fillMomentsInfo = void 0;
exports.get = get;
exports.refreshHomeMomentCount = refreshHomeMomentCount;
exports.getCount = getCount;
exports.getOne = getOne;
exports.getHot = getHot;
exports.getAllHot = getAllHot;
exports.getMoments = getMoments;
exports.getMomentsCount = getMomentsCount;
exports.fillInfo = fillInfo;
exports.addMoment = addMoment;
exports.addMomentWithPreApprovedImages = addMomentWithPreApprovedImages;
exports.addToTopic = addToTopic;
exports.likeMoment = likeMoment;
exports.addComment = addComment;
exports.delMomentByOfficial = delMomentByOfficial;
exports.delMoment = delMoment;
exports.delComment = delComment;
exports.getHotMoments = getHotMoments;
exports.calHot = calHot;
exports.refreshCommentsCache = refreshCommentsCache;
exports.auditMomentImageList = auditMomentImageList;
exports.auditMomentVideoList = auditMomentVideoList;
exports.getLastFinishCopyId = getLastFinishCopyId;
exports.copyPlayerMoments = copyPlayerMoments;
exports.publicNormalMomentScope = publicNormalMomentScope;
/* eslint-disable @typescript-eslint/no-explicit-any */
const bluebird = require("bluebird");
const _ = require("lodash");
const config_1 = require("../common/config");
const constants_1 = require("../common/constants");
const data_1 = require("../common/data");
const db = require("../common/db");
const redis_1 = require("../common/redis");
const util = require("../common/util");
const service_1 = require("../components/adminMomentPick/service");
const errorCodes_1 = require("../errorCodes");
const eventBus_1 = require("../eventBus");
const helper_1 = require("../helper");
const logger_1 = require("../logger");
const CopyMoment_1 = require("../models/CopyMoment");
const OfficialAccount_1 = require("../models/OfficialAccount");
const OfficialAccountMoment_1 = require("../models/OfficialAccountMoment");
const PyqComment_1 = require("../models/PyqComment");
const PyqFollow_1 = require("../models/PyqFollow");
const PyqMomentForward_1 = require("../models/PyqMomentForward");
const PyqMomentLike_1 = require("../models/PyqMomentLike");
const PyqMoments_1 = require("../models/PyqMoments");
const PyqProfile_1 = require("../models/PyqProfile");
const PyqTopicMoment_1 = require("../models/PyqTopicMoment");
const HotMomentCache = require("./HotMomentsCache");
const activityTopic_1 = require("./activityTopic");
const atPlayer_1 = require("./atPlayer");
const comment_1 = require("./comment");
const FollowService = require("./follow");
const guildPolemic_1 = require("./guildPolemic");
const identity_1 = require("./identity");
const imageAudit_1 = require("./imageAudit");
const inform = require("./inform");
const momentCounter_1 = require("./momentCounter");
const momentForward_1 = require("./momentForward");
const momentWeekRenqi_1 = require("./momentWeekRenqi");
const follow_1 = require("./official_accounts/follow");
const profile = require("./profile");
const profile_1 = require("./profile");
const recentVisitor_1 = require("./recentVisitor");
const redDot_1 = require("./redDot");
const sendImageAudit_1 = require("./sendImageAudit");
const yunyinLog_1 = require("./yunyinLog");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const dataUtil = require("../common/data");
const Bluebird = require("bluebird");
const baseService_1 = require("./baseService");
const logger = (0, logger_1.clazzLogger)("service/moment");
const BASIC_MOMENT_COLS = [
    "ID",
    "RoleId",
    "Text",
    "ImgList",
    "ImgAudit",
    "ZanList",
    "CreateTime",
    "HotState",
    "VideoList",
    "VideoAudit",
    "VideoCoverList",
    "VideoCoverAudit",
    "VisualRange",
    "CanCommentByAll",
    "CanForward",
    "CanCommentByStranger",
];
async function getPyqPlayerIds(roleId) {
    const ids = await FollowService.getFollowIds(roleId);
    return [roleId, ...ids];
}
async function get(params) {
    let queryRoleIds = [];
    const targetId = params.targetid;
    if (targetId) {
        queryRoleIds = [targetId];
        params.justFollow = false;
        // 查看指定玩家动态, 添加最近访问记录
        (0, recentVisitor_1.addVisitorId)(targetId, params.roleid);
        if (params.roleid !== params.targetid) {
            const isFollowEachOther = await PyqFollow_1.Follow.isFollowEachOther(params.roleid, params.targetid);
            params.justFollow = !isFollowEachOther;
        }
    }
    else {
        params.isViewFriendMoments = true;
        queryRoleIds = await getPyqPlayerIds(params.roleid);
    }
    return getMoments(queryRoleIds, params);
}
async function refreshHomeMomentCount(roleId) {
    const queryRoleIds = await getPyqPlayerIds(roleId);
    await (0, momentCounter_1.refreshPlayerHomeMomentsCount)(roleId, queryRoleIds);
}
async function getCount(params) {
    const targetId = params.targetid;
    const roleId = params.roleId;
    let justFollow = false;
    if (roleId !== targetId) {
        const isFollowEachOther = await PyqFollow_1.Follow.isFollowEachOther(roleId, targetId);
        justFollow = !isFollowEachOther;
    }
    if (targetId) {
        const count = await (0, momentCounter_1.getPlayerMomentsCount)(targetId, justFollow, roleId);
        return count;
    }
    else {
        throw { code: -1, msg: "必须有targetid" };
    }
}
function getOne(params) {
    const momentId = params.id;
    return db
        .query({
        table: constants_1.TABLE_NAMES.moment,
        cols: BASIC_MOMENT_COLS,
        filter: { ID: momentId, Status: dataUtil.Constants.STATUS_NORMAL },
    })
        .then(function (rows) {
        if (_.isEmpty(rows)) {
            throw { code: -1, msg: "该心情不存在" };
        }
        else {
            return rows;
        }
    })
        .then(util.currying(fillInfo, params))
        .then(function (list) {
        return (0, identity_1.fillIdentity)(list);
    })
        .then(function (list) {
        return list[0];
    });
}
function getHot(params) {
    const serverId = params.serverid || params.server;
    return getHotMoments(params, serverId);
}
function getAllHot(params) {
    return getHotMoments(params, "all");
}
exports.hide = function (req, callback) {
    hideMoment(req.params).then(callback).catch(callback);
};
exports.like = function (params, callback) {
    likeMoment(params).then(callback).catch(callback);
};
exports.comment = function (params, callback) {
    addComment(params).then(callback).catch(callback);
};
exports.uncomment = function (params, callback) {
    delComment(params).then(callback).catch(callback);
};
async function getMoments(roleList, params) {
    let totalCount = 0;
    if (!roleList || !roleList.length) {
        return { list: [], totalCount: 0 };
    }
    const roleId = params.roleid;
    let rows = [];
    if (roleList.length === 1 && OfficialAccount_1.OfficialAccount.isOfficialAccount(roleList[0])) {
        const isPublicOfficial = await OfficialAccount_1.OfficialAccount.isPublic(roleList[0]);
        if (isPublicOfficial) {
            rows = await getOfficialMoments(roleList[0], params);
        }
        else {
            return { list: [], totalCount: 0 };
        }
    }
    else {
        rows = await getBasicMoments(roleList, roleId, params);
    }
    if (params.isViewFriendMoments) {
        totalCount = await (0, momentCounter_1.getPlayerHomeMomentsCount)(params.roleid, roleList);
    }
    else {
        totalCount = await (0, momentCounter_1.getPlayerMomentsCount)(params.targetid, params.justFollow, roleId);
    }
    const list = await fillInfo(params, rows);
    if (params.isViewFriendMoments) {
        (0, redDot_1.viewFriendsMoments)(roleId);
    }
    const result = (0, helper_1.formatResult)({ list: list, count: totalCount });
    return result;
}
async function getBasicMoments(roleList, roleId, params) {
    const query = PyqMoments_1.Moment.scope();
    if (params.isViewFriendMoments) {
        const followEachOthers = await PyqFollow_1.Follow.filterFollowingMe(roleList, roleId);
        followEachOthers.push(roleId);
        const justFollow = _.difference(roleList, followEachOthers);
        query.where(function () {
            this.where(function () {
                this.whereIn("RoleId", justFollow).where("VisualRange", PyqMoments_1.EMomentVisualRange.all);
            });
            this.orWhere(function () {
                this.whereIn("RoleId", followEachOthers).whereIn("VisualRange", [
                    PyqMoments_1.EMomentVisualRange.all,
                    PyqMoments_1.EMomentVisualRange.friend,
                ]);
            });
            this.orWhere("RoleId", roleId);
        });
    }
    else if (params.justFollow) {
        query.where("RoleId", roleList[0]).where("VisualRange", PyqMoments_1.EMomentVisualRange.all);
    }
    else if (roleId != roleList[0]) {
        query.where("RoleId", roleList[0]).whereIn("VisualRange", [PyqMoments_1.EMomentVisualRange.all, PyqMoments_1.EMomentVisualRange.friend]);
    }
    else {
        query.where("RoleId", roleId);
    }
    query
        .where("Status", dataUtil.Constants.STATUS_NORMAL)
        .where(function () {
        this.whereRaw("Text!=''").orWhereRaw(`RoleId = ${roleId} or locate(',1', concat(',', ImgAudit))>0`);
    })
        .select(BASIC_MOMENT_COLS)
        .orderBy("CreateTime", "DESC")
        .limit(params.pageSize)
        .offset((params.page - 1) * params.pageSize);
    return await PyqMoments_1.Moment.executeByQuery(query);
}
async function getOfficialMoments(officialRoleId, params) {
    const cols = BASIC_MOMENT_COLS.map((col) => {
        return "m." + col;
    });
    const query = PyqMoments_1.Moment.normalScope()
        .from(PyqMoments_1.Moment.tableName + " as m ")
        .leftJoin(OfficialAccountMoment_1.OfficialAccountMoment.tableName + " as o", "m.Id", "o.MomentId")
        .select(cols)
        .where("m.RoleId", officialRoleId)
        .orderBy("o.TopTime", "desc")
        .orderBy("m.CreateTime", "desc");
    const rows = await PyqMoments_1.Moment.queryWithPagination(query, params);
    return rows;
}
async function getMomentsCount(roleList) {
    if (!roleList || !roleList.length) {
        return { count: 0 };
    }
    const filter = {
        RoleId: roleList,
        Status: dataUtil.Constants.STATUS_NORMAL,
        // or: { "Text!=''": null, "locate(',1',concat(',',ImgAudit))>0": null }
    };
    return db
        .query({
        table: constants_1.TABLE_NAMES.moment,
        cols: ["count(*) as mcount"],
        filter: filter,
    })
        .then(function (res) {
        return { count: res[0].mcount };
    });
}
function getForwardCountHash(momentIds) {
    const hash = {};
    return bluebird
        .mapSeries(momentIds, (mId) => {
        return momentForward_1.ForwardCountService.getForwardCount(mId).then((count) => {
            // @ts-ignore
            hash[mId] = count;
        });
    })
        .then(() => {
        return hash;
    });
}
function getPreviousForwards(momentIds) {
    let validMomentIds;
    let result;
    return bluebird
        .map(momentIds, (mId) => {
        return momentForward_1.PreviousForwardService.getPreviousForwards(mId).then((info) => {
            return { MomentId: mId, PreviousForwards: info };
        });
    })
        .then((rows) => {
        result = rows;
        const mIds = _.flatMap(rows, (row) => _.map(row.PreviousForwards, (x) => x.ID));
        //这里需要过滤下Status=0， 前面可能从缓存中读出来，保留了删除了的之前转发心情
        return PyqMoments_1.Moment.find({ ID: mIds, Status: data_1.Constants.STATUS_NORMAL }, { cols: ["ID"] });
    })
        .then((rows) => {
        validMomentIds = _.map(rows, "ID");
        _.forEach(result, (info) => {
            info.PreviousForwards = _.filter(info.PreviousForwards, (m) => _.includes(validMomentIds, m.ID));
        });
        return result;
    });
}
async function getIsFollowing(moments, curRoleId) {
    const roleIds = moments.map((x) => x.RoleId);
    const meFollowRoleIds = await PyqFollow_1.Follow.filterMeFollow(roleIds, curRoleId);
    const meFollowOfficialIds = await (0, follow_1.getFollowOfficialIds)(curRoleId);
    const followIdSet = new Set([].concat(meFollowRoleIds, meFollowOfficialIds));
    const result = moments.map((x) => {
        return followIdSet.has(x.RoleId);
    });
    return result;
}
async function fillInfo(params, moments) {
    const TOP_ZAN_SIZE = 20;
    const curRoleId = parseInt(params.roleid, 10);
    if (_.isEmpty(moments)) {
        return [];
    }
    const momentIds = _.map(moments, "ID");
    const queryComments = moments.map((m) => {
        const commentCount = PyqMoments_1.Moment.getCommentsCountFromHotStateStr(m.HotState);
        const item = { momentId: m.ID, commentCount: commentCount };
        return item;
    });
    function getForwardMomentsHash() {
        return PyqMomentForward_1.MomentForward.getForwardOriginMomentsHash(momentIds);
    }
    function getProfileRoleInfos(roleIds, curRoleId, queryFollowingRoleIds) {
        return profile.getRoleInfo(roleIds, curRoleId, {
            includeFollowingList: true,
            queryFollowingRoleIds: queryFollowingRoleIds || [],
            queryBlackList: true,
            formatPrivacy: true,
        });
    }
    function getRelatedRoleIds(moments, comments, forwardMoments, previousForwardsRoleIds) {
        const commentRoleIds = _.flatMap(comments, (c) => [c.RoleId, c.ReplyId]);
        const momentRoleIds = _.map(moments, "RoleId");
        const topZanMomentRoleIds = _.flatMap(moments, (m) => getTopZanRoleIds(m, TOP_ZAN_SIZE));
        const forwardMomentRoleIds = _.map(forwardMoments, "RoleId");
        return _.compact(_.union(momentRoleIds, topZanMomentRoleIds, commentRoleIds, forwardMomentRoleIds, previousForwardsRoleIds));
    }
    function isUserCanComment(momentOwnerProfile, curRoleId) {
        return data_1.pyq.canComment(momentOwnerProfile, curRoleId, data_1.pyq.getAllowHash(momentOwnerProfile));
    }
    function getMixinRoleInfo(roleInfo) {
        if (!roleInfo)
            return null;
        const frameId = PyqProfile_1.Profile.getFrameIdFromExpressionBase(roleInfo.ExpressionBase);
        const showPhoto = PyqProfile_1.Profile.getPhotoView(roleInfo.Photo, roleInfo.PhotoAudit);
        const record = {
            roleId: roleInfo.RoleId,
            roleName: roleInfo.RoleName,
            gender: roleInfo.Gender || 0,
            subGender: roleInfo.SubGender || 0,
            voiceGender: roleInfo.SnsGender || 0,
            jobId: roleInfo.JobId,
            level: roleInfo.Level,
            headPaintId: roleInfo.HeadPaintId,
            bodyPaintId: roleInfo.BodyPaintId,
            photo: showPhoto,
            photoAudit: roleInfo.PhotoAudit || constants_1.EAuditStatus.Init,
            frameId: frameId,
            bInBlacklist: roleInfo.bInBlacklist,
            privacy: roleInfo.Privacy,
        };
        if (config_1.Features.lianghao) {
            record.lianghaoId = roleInfo.lianghaoId;
            record.lianghaoOutTimeStamp = roleInfo.lianghaoOutTimeStamp;
        }
        return record;
    }
    function getIsUserLikedHash(momentIds, curRoleId) {
        const query = PyqMomentLike_1.MomentLike.normalScope()
            .whereIn("MomentId", momentIds)
            .where("RoleId", curRoleId)
            .select("MomentId");
        return PyqMomentLike_1.MomentLike.executeByQuery(query).then((rows) => {
            const likedMIds = new Set(_.map(rows, "MomentId"));
            return _.reduce(momentIds, (hash, mId) => {
                hash[mId] = likedMIds.has(mId);
                return hash;
            }, {});
        });
    }
    function getTopZanRoleIds(moment, size) {
        const list = util.csvStrToIntArray(moment.ZanList);
        const officialLikes = list.filter((id) => OfficialAccount_1.OfficialAccount.isOfficialAccount(id));
        const normalLikes = list.filter((id) => !OfficialAccount_1.OfficialAccount.isOfficialAccount(id));
        const newList = _.concat(officialLikes, normalLikes);
        return _.take(newList, size);
    }
    const data = await bluebird.all([
        (0, comment_1.getMomentTopComments)(queryComments),
        getForwardMomentsHash(),
        getForwardCountHash(momentIds),
        getPreviousForwards(momentIds),
        getIsUserLikedHash(momentIds, curRoleId),
        getIsFollowing(moments, curRoleId),
    ]);
    let [comments, forwardMomentsHash, forwardCountHash, previousForwardsList, isUserLikedHash, isFollowing] = data;
    const previousForwardsRoleIds = _.map(_.flatMap(previousForwardsList, (r) => _.get(r, "PreviousForwards")), "RoleId");
    const queryRoleIds = getRelatedRoleIds(moments, comments, _.values(forwardMomentsHash), previousForwardsRoleIds);
    const queryFollowingRoleIds = _.map(moments, "RoleId");
    const roleIdToProfiles = await getProfileRoleInfos(queryRoleIds, curRoleId, queryFollowingRoleIds);
    const officialProfileMap = await profile.getOfficialProfileMap(queryRoleIds);
    _.mapValues(forwardMomentsHash, (moment) => {
        //转发引用的心情删除也需要被查询出来， 并且把图片置空，文本变成该心情已经删除
        const officialProfile = officialProfileMap.get(moment.RoleId);
        let isForwardDeleted = false;
        if (officialProfile && !officialProfile.isPublic) {
            isForwardDeleted = true;
        }
        else {
            isForwardDeleted = moment.Status !== data_1.Constants.STATUS_NORMAL;
        }
        moment.videos = getMomentVideoViews(moment, curRoleId);
        moment.imgPlayType = getMomentImgPlayType(moment.videos);
        if (isForwardDeleted) {
            moment.Text = "该心情已删除";
            moment.ImgList = "";
            moment.ImgAudit = "";
            moment.videos = [];
        }
        data_1.pyq.setImgListView(moment, curRoleId); // 设置图片显示状态
        util.extend(moment, getMixinRoleInfo(roleIdToProfiles[moment.RoleId]));
        return moment;
    });
    _.forEach(previousForwardsList, (row) => {
        if (row.PreviousForwards) {
            _.forEach(row.PreviousForwards, (forward) => {
                util.extend(forward, { roleinfo: getMixinRoleInfo(roleIdToProfiles[forward.RoleId]) });
            });
        }
    });
    comments = _.map(comments, (c) => {
        util.extend(c, getMixinRoleInfo(roleIdToProfiles[c.RoleId]));
        if (c.ReplyId) {
            c.ReplyInfo = getMixinRoleInfo(roleIdToProfiles[c.ReplyId]);
        }
        return c;
    });
    const commentGroupByMomentId = _.groupBy(comments, "TargetId");
    const previousForwardsHash = util.keyToRecordHash(previousForwardsList, "MomentId");
    const isPickFetcher = await (0, service_1.getIsPickerFetcher)(params.refreshPickInfo);
    return _.map(moments, (moment, momentIndex) => {
        data_1.pyq.setImgListView(moment, curRoleId); // 设置图片显示状态
        //@ts-ignore
        moment.videos = getMomentVideoViews(moment, curRoleId);
        //@ts-ignore
        moment.imgPlayType = getMomentImgPlayType(moment.videos);
        const hotState = PyqMoments_1.Moment.formatHotState(moment.HotState);
        const zanRoleInfoList = _.map(getTopZanRoleIds(moment, TOP_ZAN_SIZE), function (roleId) {
            if (roleIdToProfiles[roleId]) {
                return getMixinRoleInfo(roleIdToProfiles[roleId]);
            }
            else {
                logger.warn({ roleId }, "RoleInfoNotFound");
                return { roleId: roleId };
            }
        });
        const profileInfo = roleIdToProfiles[moment.RoleId] || {};
        const commentList = commentGroupByMomentId[moment.ID] || [];
        const forwardMoment = forwardMomentsHash[moment.ID] || {};
        const forwardCount = forwardCountHash[moment.ID] || 0;
        const previousForwards = previousForwardsHash[moment.ID].PreviousForwards || [];
        const isUserLiked = isUserLikedHash[moment.ID];
        const officialProfile = officialProfileMap.get(moment.RoleId);
        const bePicked = isPickFetcher(moment.ID);
        const mixinProps = {
            likeCount: hotState.like,
            likeUsers: zanRoleInfoList,
            CommentCount: hotState.comment + hotState.reply,
            commentList: commentList,
            bePicked,
            IsUserLiked: isUserLiked,
            canComment: isUserCanComment(profileInfo, curRoleId),
            ForwardCount: forwardCount,
            ForwardMoment: forwardMoment,
            PreviousForwards: previousForwards,
            isFollowing: isFollowing[momentIndex],
        };
        //@ts-ignore
        moment = _.omit(moment, ["HotState", "ZanList", "VideoList", "VideoAudit", "VideoCoverList", "VideoCoverAudit"]);
        return _.assign(moment, mixinProps, getMixinRoleInfo(profileInfo), officialProfile);
    });
}
var ImgPlayType;
(function (ImgPlayType) {
    ImgPlayType[ImgPlayType["NO_VIDEO"] = 0] = "NO_VIDEO";
    ImgPlayType[ImgPlayType["VIDEO"] = 1] = "VIDEO";
})(ImgPlayType || (ImgPlayType = {}));
function getMomentImgPlayType(videos) {
    return videos.length > 0 ? ImgPlayType.VIDEO : ImgPlayType.NO_VIDEO;
}
function getMomentVideoViews(moment, curRoleId) {
    const videos = util.csvStrToArray(moment.VideoList);
    const videoAudits = util.csvStrToIntArray(moment.VideoAudit);
    const videoCovers = util.csvStrToArray(moment.VideoCoverList);
    const videoCoverAudit = util.csvStrToIntArray(moment.VideoCoverAudit);
    const videoList = [];
    const defaultCover = "http://hi-163-nsh.nosdn.127.net/dynamicPicture/auditing.png?imageView";
    for (let i = 0; i < videos.length; i++) {
        const isViewBySelf = moment.RoleId === curRoleId;
        // 未设置封面的情况下处理成封面审核通过
        if (!videoCovers[i]) {
            videoCoverAudit[i] = constants_1.AuditStatues.Pass;
        }
        if (videoAudits[i] === constants_1.AuditStatues.Reject || videoCoverAudit[i] === constants_1.AuditStatues.Reject) {
            videoList.push({ video: "", cover: defaultCover });
        }
        else {
            if (isViewBySelf) {
                videoList.push({ video: videos[i], cover: videoCovers[i] || "" });
            }
            else {
                if (videoAudits[i] === constants_1.AuditStatues.Pass && videoCoverAudit[i] === constants_1.AuditStatues.Pass) {
                    videoList.push({ video: videos[i], cover: videoCovers[i] || "" });
                }
                else {
                    videoList.push({ video: "", cover: defaultCover });
                }
            }
        }
        if (config_1.testCfg.skip_audit) {
            videoList.push({ video: videos[i], cover: videoCovers[i] });
        }
    }
    return videoList;
}
async function getWeightFactorReg() {
    const str = await (0, redis_1.getRedis)().getAsync("moment:weight_factor_regex");
    const obj = util.getJsonInfo(str, []) || [];
    const regexs = obj.map((item) => {
        return new RegExp(item);
    });
    return regexs;
}
async function getWeightFactor() {
    const factor = await (0, redis_1.getRedis)().getAsync("moment:weight_factor");
    if (!factor) {
        return 0.5;
    }
    else {
        return _.toNumber(factor);
    }
}
async function addMoment(params) {
    return addMomentInternal(params);
}
async function addMomentWithPreApprovedImages(params) {
    return addMomentInternal(params);
}
async function getInitialHotStateString(text, hotFactor = 1) {
    const reg = await getWeightFactorReg();
    const weightFactor = await getWeightFactor();
    let momentFactor = 1;
    if (reg.some((item) => item.test(text))) {
        momentFactor = weightFactor;
    }
    const hotState = JSON.stringify({ weight: momentFactor, hotFactor });
    return hotState;
}
function addToTopic(text, momentId) {
    const subject = dataUtil.md.getTopic(text);
    if (!subject) {
        return null;
    }
    let topicId;
    const now = Date.now();
    return db
        .push({
        table: constants_1.TABLE_NAMES.topic,
        values: { MomentList: momentId, Hot: 1, UpdateTime: now },
        filter: { Subject: subject },
    }, {
        hookVal: function (values, exist) {
            const oldList = exist && exist.MomentList;
            if (oldList) {
                topicId = exist.ID;
                values.Hot = (parseInt(exist.Hot, 10) || 0) + 1;
                values.MomentList = util.addToList(momentId, oldList);
            }
            else {
                values.CreateTime = Date.now();
            }
        },
    })
        .then(function (results) {
        PyqTopicMoment_1.MomentTopic.addToTopic(subject, momentId);
        return topicId || results.insertId;
    });
}
function likeMoment(params) {
    const undo = params.action === "undo";
    const roleId = params.roleid;
    const momentId = params.id;
    const Constants = dataUtil.Constants;
    const ZAN_LIST_MAX_SIZE = 20; // zanList最多缓存的角色id数量
    const hotFactor = PyqMoments_1.MomentModel.checkHotFactorParams(params.hotFactor);
    let ownerId;
    return PyqMoments_1.Moment.findOne({ ID: momentId, Status: Constants.STATUS_NORMAL }, ["ID", "RoleId", "ZanList", "HotState"])
        .then((moment) => {
        if (!moment) {
            throw { code: -1, msg: "该心情不存在" };
        }
        else {
            ownerId = moment.RoleId;
            return moment;
        }
    })
        .then((moment) => {
        const zanList = moment.ZanList;
        const isLiked = ("," + zanList + ",").indexOf("," + roleId) >= 0;
        const hotState = PyqMoments_1.MomentModel.formatHotState(moment.HotState);
        if (isLiked || hotState.like < ZAN_LIST_MAX_SIZE) {
            return [moment, isLiked];
        }
        else {
            return PyqMomentLike_1.MomentLike.findOne({ RoleId: roleId, MomentId: momentId, Status: Constants.STATUS_NORMAL }, [
                "ID",
            ]).then((momentLike) => {
                return [moment, !!momentLike];
            });
        }
    })
        .then((result) => {
        const [moment, isLiked] = result;
        if (isLiked !== undo) {
            throw { code: -1, message: undo ? "您尚未点过赞" : "您已经点过赞了" };
        }
        const updatePyqMoment = () => {
            const values = { ZanList: undefined };
            if (moment.ZanList) {
                values.ZanList = undo
                    ? util.rmvFromList(roleId, moment.ZanList)
                    : util.addToList(roleId, moment.ZanList, ZAN_LIST_MAX_SIZE);
            }
            else {
                values.ZanList = `${roleId}`;
            }
            const updateHotStep = undo ? -1 : 1;
            util.extend(values, updateHot(moment, "like", updateHotStep, { keepDuration: true, hotFactor })); // 更新动态热度
            return PyqMoments_1.Moment.updateByCondition({ ID: moment.ID }, values);
        };
        const updatePyqMomentLike = () => {
            const status = undo ? Constants.STATUS_DELETE : Constants.STATUS_NORMAL;
            return PyqMomentLike_1.MomentLike.createOrUpdate({ RoleId: roleId, MomentId: momentId, TargetId: ownerId, CreateTime: Date.now(), Status: status }, { Status: status });
        };
        return bluebird.all([updatePyqMoment(), updatePyqMomentLike()]);
    })
        .then(function () {
        (0, momentWeekRenqi_1.addWeekRenQiForLike)(roleId, ownerId);
        const relateId = "like:" + momentId + "-" + roleId; // 唯一ID，用于删除?
        if (undo) {
            inform.del({ RelateId: relateId }); // 删除通知
            (0, yunyinLog_1.addMomentActionLog)(roleId, momentId, "cancelLike");
            const payload = { roleId: roleId, momentId: momentId };
            eventBus_1.EventBus.emit(eventBus_1.Events.CANCEL_LIKE_MOMENT, payload);
        }
        else {
            inform.add({ RoleId: roleId, TargetId: ownerId, ObjectId: momentId, RelateId: relateId, Type: "like" }); // 添加通知
            const payload = { roleId: roleId, momentId: momentId };
            eventBus_1.EventBus.emit(eventBus_1.Events.LIKE_MOMENT, payload);
            (0, yunyinLog_1.addMomentActionLog)(roleId, momentId, "like");
        }
        return null;
    });
}
function addComment(params) {
    let momentId = params.id, roleId = params.roleid, replyId = params.replyid, ownerId, text = util.trim(params.text);
    const hotFactor = PyqMoments_1.MomentModel.checkHotFactorParams(params.hotFactor);
    if (!text) {
        throw { code: -1, msg: "评论内容不能为空" };
    }
    let theMoment;
    return db
        .query({
        table: constants_1.TABLE_NAMES.moment,
        filter: { ID: momentId, Status: dataUtil.Constants.STATUS_NORMAL },
    })
        .then(function (results) {
        theMoment = results[0];
        if (!theMoment) {
            throw { code: -1, msg: "评论的动态不存在" };
        }
        ownerId = theMoment.RoleId;
        return ownerId == roleId
            ? true
            : bluebird
                .all([
                PyqProfile_1.Profile.findOne({ RoleId: ownerId }, ["RoleId", "Privacy", "FriendList", "BlackList"]),
                FollowService.getFollowIds(ownerId),
            ])
                .spread(function (profile, followingIds) {
                if (!theMoment.CanCommentByAll) {
                    throw { code: -1, msg: "该动态不允许评论" };
                }
                profile.FollowingList = followingIds;
                profile.Privacy = PyqProfile_1.Profile.formatPrivacy(profile.Privacy);
                if (!theMoment.CanCommentByStranger) {
                    profile.Privacy.limitComment = true;
                }
                if (!data_1.pyq.canComment(profile, roleId)) {
                    throw { code: -1, msg: "您没有评论该动态的权限" };
                }
                const profileRecord = profile;
                if (profileRecord && profileRecord.BlackList) {
                    const blacklist = util.csvStrToIntArray(profileRecord.BlackList);
                    const blSet = new Set(blacklist);
                    if (blSet.has(roleId)) {
                        throw { code: -1, msg: "您已被拉黑" };
                    }
                }
            });
    })
        .then(function () {
        const commentFilter = {
            RoleId: roleId,
            TargetId: momentId,
        };
        if (replyId) {
            commentFilter["ReplyId"] = replyId;
        }
        return db
            .query({
            table: constants_1.TABLE_NAMES.comment,
            cols: ["Text"],
            filter: commentFilter,
        })
            .then(function (results) {
            results = _.map(results, "Text");
            if (_.indexOf(results, text) != -1) {
                throw { code: -1, msg: "您不能评论重复内容" };
            }
        });
    })
        .then(function () {
        return db
            .add({
            table: constants_1.TABLE_NAMES.comment,
            values: {
                RoleId: roleId,
                TargetId: momentId,
                ReplyId: replyId,
                Text: text,
                CreateTime: Date.now(),
            },
        })
            .then(function (result) {
            // 更新动态热度
            const updateHotType = ownerId == roleId ? "reply" : "comment";
            const values = updateHot(theMoment, updateHotType, 1, { keepDuration: true, hotFactor: hotFactor });
            values &&
                db.update({
                    table: constants_1.TABLE_NAMES.moment,
                    values: values,
                    filter: { ID: momentId },
                });
            // 添加通知
            const commentId = result.insertId;
            const targetId = replyId || ownerId;
            (0, momentWeekRenqi_1.addWeekRenQiForComment)(roleId, targetId);
            inform.add({
                RoleId: roleId,
                TargetId: replyId || ownerId,
                ObjectId: momentId,
                RelateId: "comment:" + commentId,
                Text: text,
                Type: replyId ? "reply" : "comment",
            });
            const commentPayload = { roleId: roleId, commentId: commentId, momentId: momentId };
            eventBus_1.EventBus.emit(eventBus_1.Events.ADD_COMMENT, commentPayload);
            (0, yunyinLog_1.addCommentLog)({ roleId: roleId, momentId: momentId, replayId: replyId, text: text, commentId: commentId });
            return { id: commentId };
        });
    });
}
// 官v删除心情
async function delMomentByOfficial(params) {
    const momentId = params.id;
    const roleId = params.roleid;
    const result = await PyqMoments_1.Moment.softDeleteByCondition({ ID: momentId });
    const isDeleted = result.affectedRows > 0;
    if (isDeleted) {
        await processDelMoment(roleId, momentId);
    }
    return result;
}
async function processDelMoment(momentId, roleId, hotFactor = 1) {
    await HotMomentCache.onDeleteMoments([momentId]);
    await PyqComment_1.Comment.clearMomentCommentsCache(momentId);
    eventBus_1.EventBus.emit(eventBus_1.Events.DELETE_MOMENT, { momentId: momentId, hotFactor });
    // 删除相关通知
    inform.del({
        ObjectId: momentId,
    });
    // 清除count缓存
    (0, yunyinLog_1.addMomentActionLog)(roleId, momentId, "del");
    (0, momentCounter_1.refreshPlayerMomentsCount)(roleId);
}
async function delMoment(params) {
    const momentId = params.id;
    const roleId = params.roleid;
    const result = await PyqMoments_1.Moment.softDeleteByCondition({ ID: momentId, RoleId: params.roleid });
    const isDeleted = result.changedRows > 0;
    if (isDeleted) {
        const hotFactor = PyqMoments_1.MomentModel.checkHotFactorParams(params.hotFactor);
        await processDelMoment(momentId, roleId, hotFactor);
    }
    return result;
}
function hideMoment(params) {
    const roleId = params.roleId;
    const rid = params.targetid;
    return db
        .update({
        table: constants_1.TABLE_NAMES.moment,
        values: { HideList: rid },
        filter: { RoleId: roleId },
    }, {
        hookVal: function (values, exist) {
            if (exist && exist.HideList) {
                values.HideList = util.addToList(rid, exist.HideList);
            }
        },
    })
        .then(function (result) {
        return result.affectedRows;
    });
}
var DelCommentCode;
(function (DelCommentCode) {
    DelCommentCode[DelCommentCode["PermissionDeny"] = -1] = "PermissionDeny";
    DelCommentCode[DelCommentCode["ItemDeleted"] = -2] = "ItemDeleted";
})(DelCommentCode || (DelCommentCode = {}));
function delComment(params) {
    let itemInfo;
    const commentId = params.id;
    const roleId = params.roleid;
    const hotFactor = PyqMoments_1.MomentModel.checkHotFactorParams(params.hotFactor);
    return db
        .query({
        table: constants_1.TABLE_NAMES.comment + " as c," + constants_1.TABLE_NAMES.moment + " as m",
        cols: ["m.*", "c.RoleId as CommentRoleId", "c.Status as CommentStatus"],
        filter: { "c.TargetId=m.ID": null, "c.ID": commentId, IdOr: { "c.RoleId": roleId, "m.RoleId": roleId } },
    })
        .then(function (results) {
        itemInfo = results[0];
        if (!itemInfo) {
            return bluebird.reject({ code: DelCommentCode.PermissionDeny, msg: "您没有删除该条评论的权限" });
        }
        if (itemInfo.CommentStatus != dataUtil.Constants.STATUS_NORMAL) {
            return bluebird.reject({ code: DelCommentCode.ItemDeleted, msg: "该条评论已删除" });
        }
        return db.update({
            table: constants_1.TABLE_NAMES.comment,
            values: { Status: dataUtil.Constants.STATUS_DELETE },
            filter: { ID: commentId },
        });
    })
        .then(function (result) {
        const commentCount = PyqMoments_1.Moment.getCommentsCountFromHotStateStr(itemInfo.HotState);
        (0, comment_1.tryToDelMomentTopComments)(commentId, { momentId: itemInfo.ID, commentCount: commentCount });
        const payload = { roleId: roleId, commentId: commentId };
        eventBus_1.EventBus.emit(eventBus_1.Events.DEL_COMMENT, payload);
        const values = updateHot(itemInfo, itemInfo.RoleId == itemInfo.CommentRoleId ? "reply" : "comment", -1, {
            keepDuration: true,
            hotFactor,
        });
        values &&
            db.update({
                table: constants_1.TABLE_NAMES.moment,
                values: values,
                filter: { ID: itemInfo.ID },
            });
        // 删除相关通知
        inform.del({
            RelateId: "comment:" + commentId,
        });
        refreshCommentsCache([commentId]);
        return result;
    });
}
function getHotMoments(params, serverId) {
    const curRoleId = params.roleid;
    params.isHotMoments = true;
    const MAX_PAGE_SIZE = 20;
    const DefaultPageSize = 10;
    const MAX_HOT_MOMENTS_SIZE = 200;
    const lastHotIndex = Math.max(0, Math.min(MAX_HOT_MOMENTS_SIZE, params.lastid || 0));
    const limit = Math.max(0, Math.min(MAX_PAGE_SIZE, params.page_size || DefaultPageSize));
    let pageSize = params.pageSize || DefaultPageSize;
    let page = params.page || 0;
    let totalCount = 0;
    let useLastId = true; // 默认使用原来的方式
    let usePage = false;
    if (params.page !== undefined) {
        useLastId = false;
        usePage = true;
    }
    if (typeof pageSize === "string")
        pageSize = parseInt(pageSize, 10);
    if (typeof page === "string")
        page = parseInt(page, 10);
    return bluebird
        .resolve()
        .then(function () {
        serverId = serverId || "all";
        return HotMomentCache.getMoments(serverId, curRoleId);
    })
        .then(function (ms) {
        totalCount = ms.length;
        const moments = [];
        _.forEach(ms, function (m, index) {
            if (useLastId) {
                m.hot_index = index + 1;
                if (m.hot_index > lastHotIndex && m.hot_index < lastHotIndex + limit + 1) {
                    moments.push(m);
                }
            }
            else if (usePage) {
                // use page
                m.hot_index = index + 1;
                const lower = (page - 1) * pageSize;
                const upper = page * pageSize;
                if (index >= lower && index < upper) {
                    moments.push(m);
                }
            }
            else {
                // error
            }
        });
        return moments;
    })
        .then(function (moments) {
        const mIds = _.map(moments, "ID");
        return PyqMoments_1.Moment.findByIds(mIds, BASIC_MOMENT_COLS).then(function (colsNotInCache) {
            const idToCols = util.keyToRecordHash(colsNotInCache, "ID");
            return _.map(moments, function (m) {
                return _.assign(m, idToCols[m.ID]);
            });
        });
    })
        .then(util.currying(fillInfo, params))
        .then(function (list) {
        return (0, identity_1.fillIdentity)(list);
    })
        .then(function (list) {
        let result = { list: list, count: totalCount };
        result = (0, helper_1.formatResult)(result);
        return result;
    });
}
function updateHot(item, type, step, option) {
    const info = calHot(item, type, step, option);
    const hot = info.hot;
    const state = info.state;
    const updateVal = { HotState: "", Hot: 0 };
    updateVal.HotState = JSON.stringify(state);
    updateVal.Hot = hot;
    return updateVal;
}
function calHot(item, type, step, option) {
    option = _.defaults(option, { keepDuration: false });
    const hotFactor = option.hotFactor !== undefined ? option.hotFactor : 1;
    const hotState = item.HotState;
    let state = { publish_duration: undefined };
    if (hotState) {
        try {
            state = JSON.parse(hotState);
        }
        catch (err) {
            logger.warn("ParseHotStateError", { err: err, hotState: hotState, item, type, step });
        }
    }
    if (type && step) {
        const val = (state[type] || 0) + step;
        state[type] = val > 0 ? val : 0;
        const hotKey = `${type}Hot`;
        const hotVal = (state[hotKey] || 0) + step * hotFactor;
        state[hotKey] = hotVal > 0 ? Number(hotVal.toFixed(2)) : 0;
    }
    if (!(option.keepDuration && state.publish_duration)) {
        const now = option.now || Date.now();
        state.publish_duration = Math.round((now - item.CreateTime) / 1000); // 发表持续时间，单位秒
    }
    const hot = PyqMoments_1.Moment.getHotFromState(state);
    return type ? { hot: hot, state: state } : hot;
}
function refreshCommentsCache(commentIds) {
    const query = PyqComment_1.Comment.scope().distinct("TargetId").whereIn("ID", commentIds);
    return PyqComment_1.Comment.executeByQuery(query).then(function (rows) {
        const momentIds = _.map(rows, "TargetId");
        return bluebird.each(momentIds, function (momentId) {
            return PyqComment_1.Comment.refreshMomentCommentsCache(momentId);
        });
    });
}
exports.fillMomentsInfo = fillInfo;
async function addMomentInternal(params) {
    const roleId = params.roleid, text = util.trim(params.text);
    const imgUrls = util.trim(params.imgs);
    const preApprovedImgs = params.preApprovedImgs || [];
    let imgList = [];
    let newMomentId;
    const hotFactor = PyqMoments_1.MomentModel.checkHotFactorParams(params.hotFactor);
    if (!text && !imgUrls) {
        await bluebird.reject({ code: -1, msg: "动态内容不能为空" });
    }
    if (imgUrls) {
        try {
            imgList = util.csvStrToArray(imgUrls);
        }
        catch (ex) {
            await bluebird.reject({ code: -1, msg: "发布图片失败" });
        }
        for (let i = 0, l = imgList.length; i < l; i++) {
            imgList[i] = util.trim(imgList[i]);
            if (!(0, baseService_1.isValidResourceUrl)(imgList[i])) {
                await bluebird.reject({ code: -1, msg: "非法的图片地址" });
            }
        }
    }
    const addImg = imgList && imgList.length;
    if (imgList.length >= 0) {
        const imageLimit = await (0, profile_1.getAddMomentImageLimit)(roleId);
        if (imgList.length > imageLimit) {
            await bluebird.reject(errorCodes_1.MdErrors.AddMomentLimitExceed);
        }
    }
    let videoUrlList = [];
    let videoCoverList = [];
    if (params.videos) {
        //@ts-ignore
        videoUrlList = util.csvStrToArray(params.videos);
        //@ts-ignore
        videoCoverList = util.csvStrToArray(params.videoImgs);
    }
    const videoAudit = util.getRepeatCsv(videoUrlList.length, "0");
    const videoCoverAudit = util.getRepeatCsv(videoCoverList.length, "0");
    const hotState = await getInitialHotStateString(text, hotFactor);
    // Generate ImgAudit based on pre-approved images
    const imgAudit = preApprovedImgs.length > 0
        ? imgList.map(img => {
            return preApprovedImgs.includes(img) ? constants_1.EAuditStatus.PASS.toString() : dataUtil.Constants.STATUS_AUDIT_INIT.toString();
        }).join(',')
        : PyqMoments_1.Moment.getInitialImgAudit(imgList);
    const values = {
        RoleId: roleId,
        Text: text,
        ImgList: addImg ? imgList.join(",") : undefined,
        ImgAudit: imgAudit,
        VideoList: videoUrlList.join(","),
        VideoAudit: videoAudit,
        VideoCoverList: videoCoverList.join(","),
        VideoCoverAudit: videoCoverAudit,
        HotState: hotState,
        CreateTime: Date.now(),
        VisualRange: params.visualRange ? params.visualRange : PyqMoments_1.EMomentVisualRange.all,
        CanCommentByAll: params.canCommentByAll !== undefined ? params.canCommentByAll : 1,
        CanForward: params.canForward !== undefined ? params.canForward : 1,
        CanCommentByStranger: params.canCommentByStranger !== undefined ? params.canCommentByStranger : 1,
    };
    const tableName = constants_1.TABLE_NAMES.moment;
    const subject = dataUtil.md.getTopic(text);
    const values2 = util.extend({}, values);
    return db
        .add({
        table: tableName,
        values: values2,
    })
        .then(function (result) {
        newMomentId = result.insertId;
    })
        .then(async function () {
        const mid = newMomentId;
        (0, yunyinLog_1.addMomentLog)({
            roleId: roleId,
            momentId: mid,
            text: text,
            imgList: imgList,
            videoList: videoUrlList,
            videoCoverList: videoCoverList,
            topic: subject,
        });
        // 清除count缓存
        (0, momentCounter_1.refreshPlayerMomentsCount)(roleId);
        addToTopic(text, mid); // [2016.9.6] 生成话题
        (0, activityTopic_1.processTopicMoment)(roleId, newMomentId, imgList, videoUrlList, params.topicId);
        const getFormattedImgList = function () {
            if (addImg) {
                const moment = _.clone(values);
                data_1.pyq.setImgListView(moment, roleId);
                return moment.ImgList;
            }
            else {
                return [];
            }
        };
        // Only send images for audit that are not pre-approved
        const imagesToAudit = preApprovedImgs.length > 0
            ? imgList.filter(img => !preApprovedImgs.includes(img))
            : imgList;
        if (imagesToAudit.length > 0) {
            const picId = (0, imageAudit_1.genPicIdFromInfo)({ id: newMomentId, type: "moment_image" });
            (0, sendImageAudit_1.sendPic)(imagesToAudit, { roleId: roleId, picId: picId, media: constants_1.EPicMediaType.Image, ip: params.ip });
        }
        if (videoUrlList.length > 0) {
            const picId = (0, imageAudit_1.genPicIdFromInfo)({ id: newMomentId, type: "moment_video" });
            (0, sendImageAudit_1.sendPic)(videoUrlList, { roleId: roleId, picId: picId, media: constants_1.EPicMediaType.Video, ip: params.ip });
        }
        if (videoCoverList.length > 0) {
            const picId = (0, imageAudit_1.genPicIdFromInfo)({ id: newMomentId, type: "moment_video_cover" });
            (0, sendImageAudit_1.sendPic)(videoCoverList, { roleId: roleId, picId: picId, media: constants_1.EPicMediaType.Image, ip: params.ip });
        }
        (0, momentCounter_1.refreshPlayerMomentsCount)(roleId);
        (0, atPlayer_1.processAtPlayerWhenAddMoment)(roleId, mid, text);
        const context = params.context || "";
        const addRet = {
            id: mid,
            //@ts-ignore
            imglist: getFormattedImgList(),
            context: context,
        };
        const polemicId = (0, guildPolemic_1.getGuildPolemicIdFromLink)(params.text);
        if (polemicId) {
            const share = await (0, guildPolemic_1.sharePolemic)({ roleid: params.roleid, id: polemicId });
            addRet.guildPolemic = { id: polemicId, shareCount: share.shareCount };
        }
        return addRet;
    });
}
function auditMomentImageList(roleId, imgList, mId, ip) {
    return (0, sendImageAudit_1.sendPic)(imgList, {
        // 提交图片审核
        roleId: roleId,
        picId: (0, imageAudit_1.genPicIdFromInfo)({ type: "moment_image", id: "" + mId }),
        media: constants_1.EPicMediaType.Image,
        ip: ip,
    });
}
function auditMomentVideoList(roleId, videoList, mId, ip) {
    return (0, sendImageAudit_1.sendPic)(videoList, {
        // 提交视频审核
        roleId: roleId,
        picId: (0, imageAudit_1.genPicIdFromInfo)({ type: "moment_video", id: "" + mId }),
        media: constants_1.EPicMediaType.Image,
        ip: ip,
    });
}
function getCopyMoment(moment, newRoleId) {
    const m = {
        RoleId: newRoleId,
        Text: moment.Text,
        ImgList: moment.ImgList,
        ImgAudit: moment.ImgAudit,
        VideoList: moment.VideoList,
        VideoAudit: moment.VideoAudit,
        VideoCoverList: moment.VideoCoverList,
        VideoCoverAudit: moment.VideoCoverAudit,
        ZanList: "",
        HotState: "",
        CreateTime: moment.CreateTime,
        Status: moment.Status,
        VisualRange: moment.VisualRange,
        CanCommentByAll: moment.CanCommentByAll,
        CanForward: moment.CanForward,
        CanCommentByStranger: moment.CanForward,
    };
    return m;
}
async function getLastFinishCopyId(params) {
    const query = CopyMoment_1.CopyMomentModel.scope()
        .where({ toRoleId: params.roleid })
        .where({ FromRoleId: params.copyRoleId })
        .orderBy("FromMomentId", "desc")
        .limit(1);
    const rows = await CopyMoment_1.CopyMomentModel.executeByQuery(query);
    if (rows && rows.length > 0) {
        return rows[0].FromMomentId;
    }
    else {
        return 0;
    }
}
async function copyMoments(lastId, fromRoleId, toRoleId) {
    let fromMoments = await scanCopyMoment(fromRoleId, lastId);
    while (fromMoments.length > 0) {
        for (const m of fromMoments) {
            const newInsertProps = getCopyMoment(m, toRoleId);
            const newMomentId = await PyqMoments_1.MomentModel.insert(newInsertProps);
            await bluebird.delay(config_1.copyMomentCfg.delayTime);
            await CopyMoment_1.CopyMomentModel.insert({
                ToRoleId: toRoleId,
                FromRoleId: fromRoleId,
                FromMomentId: m.ID,
                CreateTime: Date.now(),
                ToMomentId: newMomentId,
            });
            const forwardRecord = await PyqMomentForward_1.MomentForwardModel.findOne({ MomentId: m.ID });
            if (forwardRecord && forwardRecord.ID) {
                await PyqMomentForward_1.MomentForwardModel.insert({
                    RoleId: toRoleId,
                    MomentId: newMomentId,
                    ForwardId: forwardRecord.ForwardId,
                    OriginId: forwardRecord.OriginId,
                });
            }
            lastId = m.ID;
        }
        fromMoments = await scanCopyMoment(fromRoleId, lastId);
    }
    // 复制后清除目标的动态缓存计数
    await (0, momentCounter_1.refreshPlayerMomentsCount)(toRoleId);
}
async function scanCopyMoment(fromRoleId, lastId) {
    const copyQuery = PyqMoments_1.MomentModel.scope()
        .where({ RoleId: fromRoleId, Status: 0 /* Statues.Normal */ })
        .where("ID", ">", lastId)
        .limit(config_1.copyMomentCfg.batchSize);
    const fromMoments = await PyqMoments_1.MomentModel.executeByQuery(copyQuery);
    return fromMoments;
}
async function copyPlayerMoments(params) {
    const fromRoleId = params.copyRoleId;
    const toRoleId = params.roleid;
    const lastId = await getLastFinishCopyId(params);
    if (fromRoleId === toRoleId) {
        await Bluebird.reject(errorCodes_1.PyqErrors.CopyMomentSameRoleId);
    }
    const copyQuery = PyqMoments_1.MomentModel.scope().where({ RoleId: fromRoleId, Status: 0 /* Statues.Normal */ }).where("ID", ">", lastId);
    const fromMoments = await PyqMoments_1.MomentModel.executeByQuery(copyQuery);
    (0, helper_1.asyncRun)(copyMoments(lastId, fromRoleId, toRoleId));
    return { count: fromMoments.length };
}
function publicNormalMomentScope() {
    const query = PyqMoments_1.MomentModel.normalScope().where("VisualRange", PyqMoments_1.EMomentVisualRange.all);
    return query;
}
//# sourceMappingURL=moment.js.map