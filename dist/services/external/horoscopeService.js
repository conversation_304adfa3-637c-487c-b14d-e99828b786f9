"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HoroscopeService = void 0;
const httpLib = require("../../common/request");
const logger_1 = require("../../logger");
const config_1 = require("../../common/config");
const fs = require("fs");
const path = require("path");
var CommandType;
(function (CommandType) {
    CommandType[CommandType["USER_HOROSCOPE"] = 1] = "USER_HOROSCOPE";
    CommandType[CommandType["PLANETARY_ASPECTS"] = 2] = "PLANETARY_ASPECTS";
    CommandType[CommandType["DICE"] = 3] = "DICE"; // 占星骰子
})(CommandType || (CommandType = {}));
class HoroscopeService {
    constructor() {
        this.logger = (0, logger_1.clazzLogger)("service/horoscopeApi");
        this.fallbackConfigs = [];
        this.loadFallbackConfigs();
    }
    static getInstance() {
        if (!HoroscopeService.instance) {
            HoroscopeService.instance = new HoroscopeService();
        }
        return HoroscopeService.instance;
    }
    loadFallbackConfigs() {
        try {
            const configPath = path.join(__dirname, "../../../asserts/json/astrology_ai_default_fallback.json");
            const configData = fs.readFileSync(configPath, "utf-8");
            this.fallbackConfigs = JSON.parse(configData);
            this.logger.info({ configCount: this.fallbackConfigs.length }, "FallbackConfigsLoaded");
        }
        catch (error) {
            this.logger.error({ error }, "LoadFallbackConfigsError");
            this.fallbackConfigs = [];
        }
    }
    getFallbackByCommandType(commandType) {
        return this.fallbackConfigs.find(config => config.condition === `req.command_type = ${commandType}`) || null;
    }
    createHoroscopeFallback(fallbackConfig) {
        const fallbackData = fallbackConfig.default_response;
        return {
            fortune: "",
            time: "",
            user_constellation: "",
            in_water_reversal: false,
            title: fallbackData.title || "运势解读",
            content: Array.isArray(fallbackData.content) && fallbackData.content.length > 0 && typeof fallbackData.content[0] === 'object'
                ? fallbackData.content
                : [{ title: "运势分析", content: "暂时无法获取运势信息，请稍后再试" }],
            score: fallbackData.score || 0,
            lucky_color: fallbackData.lucky_color || "",
            lucky_color_code: ""
        };
    }
    createPlanetaryAspectsFallback(fallbackConfig) {
        const fallbackData = fallbackConfig.default_response;
        return {
            title: fallbackData.title || "今日星象",
            content: Array.isArray(fallbackData.content) && typeof fallbackData.content[0] === 'string'
                ? fallbackData.content
                : ["暂时无法获取星象信息，请稍后再试"]
        };
    }
    createDiceResultFallback(fallbackConfig) {
        const fallbackData = fallbackConfig.default_response;
        return {
            title: fallbackData.title || "占星骰子",
            content: Array.isArray(fallbackData.content) && fallbackData.content.length > 0 && typeof fallbackData.content[0] === 'object'
                ? fallbackData.content
                : [{ title: "占卜结果", content: "暂时无法获取占卜信息，请稍后再试" }]
        };
    }
    async withFallback(ctx, commandType, apiCall, fallbackCreator, apiName) {
        try {
            return await apiCall();
        }
        catch (err) {
            this.logger.error({ ctx, err }, `${apiName}Error`);
            const fallbackConfig = this.getFallbackByCommandType(commandType);
            if (fallbackConfig) {
                this.logger.info({ ctx, fallbackConfigId: fallbackConfig.id }, `Using${apiName}Fallback`);
                return fallbackCreator(fallbackConfig);
            }
            throw err;
        }
    }
    /**
     * 星盘测算
     * @param userInfo 用户信息
     * @param timeInterval 时间区间：TODAY-今日，THIS_WEEK-本周
     * @param fortune 测运类型：基本运势/财运/事业与学业/情感
     */
    async getUserHoroscope(ctx, userInfo, timestamp, timeInterval, fortune) {
        return this.withFallback(ctx, CommandType.USER_HOROSCOPE, async () => {
            const url = config_1.astrologyCfg.horoscopeApiEndPoint;
            const body = {
                command_type: CommandType.USER_HOROSCOPE,
                timestamp: timestamp,
                user_list: [userInfo],
                time_interval: timeInterval,
                fortune: fortune
            };
            const res = await httpLib.request({
                method: "POST",
                url: url,
                timeout: config_1.astrologyCfg.horoscopeApiTimeout,
                body: body
            });
            this.logger.info({ ctx, url, body, res }, "GetUserHoroscopeApiRes");
            if (res && res.code === 200) {
                return res.data;
            }
            else {
                throw new Error(res?.message || "GetUserHoroscopeApiError");
            }
        }, this.createHoroscopeFallback.bind(this), "UserHoroscope");
    }
    /**
     * 星象提醒
     */
    async getPlanetaryAspects(ctx, timestamp) {
        return this.withFallback(ctx, CommandType.PLANETARY_ASPECTS, async () => {
            const url = config_1.astrologyCfg.horoscopeApiEndPoint;
            const body = {
                command_type: CommandType.PLANETARY_ASPECTS,
                timestamp: timestamp
            };
            const res = await httpLib.request({
                method: "POST",
                url: url,
                timeout: config_1.astrologyCfg.horoscopeApiTimeout,
                body: body
            });
            this.logger.info({ ctx, url, body, res }, "GetPlanetaryAspectsApiRes");
            if (res && res.code === 200) {
                return res.data;
            }
            else {
                throw new Error(res?.message || "Get planetary aspects failed");
            }
        }, this.createPlanetaryAspectsFallback.bind(this), "PlanetaryAspects");
    }
    /**
     * 占星骰子
     * @param fortuneQuery 占卜问题
     * @param diceResult 骰子结果，如：["水星", "白羊座", "11宫"]
     */
    async getDiceResult(ctx, fortuneQuery, diceResult) {
        return this.withFallback(ctx, CommandType.DICE, async () => {
            const url = config_1.astrologyCfg.horoscopeApiEndPoint;
            let duration = 0;
            let startTime = Date.now();
            const body = {
                command_type: CommandType.DICE,
                timestamp: Date.now(),
                fortune_query: fortuneQuery,
                dice_result: diceResult
            };
            const res = await httpLib.request({
                method: "POST",
                url: url,
                timeout: config_1.astrologyCfg.horoscopeApiTimeout,
                body: body
            });
            let endTime = Date.now();
            duration = endTime - startTime;
            this.logger.info({ ctx, url, body, res, duration }, "GetDiceResultApiRes");
            if (res && res.code === 200) {
                return res.data;
            }
            else {
                throw new Error(res?.message || "Get dice result failed");
            }
        }, this.createDiceResultFallback.bind(this), "DiceResult");
    }
}
exports.HoroscopeService = HoroscopeService;
//# sourceMappingURL=horoscopeService.js.map