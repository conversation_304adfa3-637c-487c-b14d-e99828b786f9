"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HoroscopeService = void 0;
const httpLib = require("../../common/request");
const LRU = require("lru-cache");
const logger_1 = require("../../logger");
const config_1 = require("../../common/config");
const constants_1 = require("../../common/constants");
class HoroscopeService {
    constructor() {
        this.logger = (0, logger_1.clazzLogger)("service/horoscopeApi");
        this.horoscopeCache = new LRU({
            max: 10000,
            maxAge: constants_1.DEFAULT_EXPIRE,
        });
    }
    /**
     * 星盘测算
     * @param userInfo 用户信息
     * @param timeInterval 时间区间：1-今日，2-本周
     * @param fortune 测运类型：基本运势/财运/事业与学业/情感
     */
    async calculateHoroscope(userInfo, timeInterval, fortune) {
        const url = config_1.horoscopeApiCfg.host + "/api/skill/horoscope";
        const cacheKey = [JSON.stringify(userInfo), timeInterval, fortune].join("|");
        const cacheResult = this.horoscopeCache.get(cacheKey);
        if (cacheResult) {
            this.logger.info({ url, result: cacheResult }, "CalculateHoroscopeHitCache");
            return cacheResult;
        }
        try {
            const res = await httpLib.request({
                method: "POST",
                url: url,
                timeout: config_1.horoscopeApiCfg.timeout,
                body: {
                    command_type: 1,
                    timestamp: Date.now(),
                    user_list: [userInfo],
                    time_interval: timeInterval,
                    fortune: fortune
                }
            });
            this.logger.info({ url, res }, "CalculateHoroscopeApiRes");
            if (res && res.code === 200) {
                this.horoscopeCache.set(cacheKey, res.data);
                return res.data;
            }
            else {
                throw new Error(res?.message || "Calculate horoscope failed");
            }
        }
        catch (err) {
            this.logger.error({ err, url }, "CalculateHoroscopeApiError");
            throw err;
        }
    }
    /**
     * 星象提醒
     */
    async getStarSign() {
        const url = config_1.horoscopeApiCfg.host + "/api/skill/horoscope";
        const cacheKey = "star_sign_" + Math.floor(Date.now() / (24 * 60 * 60 * 1000)); // 按天缓存
        const cacheResult = this.horoscopeCache.get(cacheKey);
        if (cacheResult) {
            this.logger.info({ url, result: cacheResult }, "GetStarSignHitCache");
            return cacheResult;
        }
        try {
            const res = await httpLib.request({
                method: "POST",
                url: url,
                timeout: config_1.horoscopeApiCfg.timeout,
                body: {
                    command_type: 2,
                    timestamp: Date.now()
                }
            });
            this.logger.info({ url, res }, "GetStarSignApiRes");
            if (res && res.code === 200) {
                this.horoscopeCache.set(cacheKey, res.data);
                return res.data;
            }
            else {
                throw new Error(res?.message || "Get star sign failed");
            }
        }
        catch (err) {
            this.logger.error({ err, url }, "GetStarSignApiError");
            throw err;
        }
    }
    /**
     * 占星骰子
     * @param fortuneQuery 占卜问题
     * @param diceResult 骰子结果，如：["水星", "白羊座", "11宫"]
     */
    async getDiceResult(fortuneQuery, diceResult) {
        const url = config_1.horoscopeApiCfg.host + "/api/skill/horoscope";
        const cacheKey = [fortuneQuery, diceResult.join("|")].join("|");
        const cacheResult = this.horoscopeCache.get(cacheKey);
        if (cacheResult) {
            this.logger.info({ url, result: cacheResult }, "GetDiceResultHitCache");
            return cacheResult;
        }
        try {
            const res = await httpLib.request({
                method: "POST",
                url: url,
                timeout: config_1.horoscopeApiCfg.timeout,
                body: {
                    command_type: 3,
                    timestamp: Date.now(),
                    fortune_query: fortuneQuery,
                    dice_result: diceResult
                }
            });
            this.logger.info({ url, res }, "GetDiceResultApiRes");
            if (res && res.code === 200) {
                this.horoscopeCache.set(cacheKey, res.data);
                return res.data;
            }
            else {
                throw new Error(res?.message || "Get dice result failed");
            }
        }
        catch (err) {
            this.logger.error({ err, url }, "GetDiceResultApiError");
            throw err;
        }
    }
}
exports.HoroscopeService = HoroscopeService;
exports.default = new HoroscopeService();
//# sourceMappingURL=horoScopeAiService.js.map