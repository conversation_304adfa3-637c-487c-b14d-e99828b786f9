"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarRatingService = void 0;
const errorCodes_1 = require("../../errorCodes");
const errorCode_1 = require("../../errors/errorCode");
const logger_1 = require("../../logger");
const bazaarRatingModel_1 = require("../../models/astrology/bazaarRatingModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarCommentService");
class BazaarRatingService {
    constructor() {
        this.bazaarRatingModel = bazaarRatingModel_1.BazaarRatingModel.getInstance();
    }
    static getInstance() {
        if (!BazaarRatingService.instance) {
            BazaarRatingService.instance = new BazaarRatingService();
        }
        return BazaarRatingService.instance;
    }
    async addBazaarRating(ctx, bazaarRating) {
        try {
            const id = await this.bazaarRatingModel.insert(bazaarRating);
            logger.info({ ctx, id, bazaarRating }, "addBazaarRatingOk");
            return id;
        }
        catch (err) {
            if (err && err.code === errorCode_1.DBErrorCodes.DuplicatedEntry) {
                throw errorCode_1.errorCodesV2.BazaarRatingAlreadyExists;
            }
            logger.error({ err, bazaarRating }, "addBazaarRatingError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async isUserRated(ctx, roleId, commentId) {
        const r = await this.bazaarRatingModel.findOne({
            FromRoleId: roleId,
            CommentId: commentId,
            Status: 0 /* Statues.Normal */
        }, ['ID']);
        return r && r.ID > 0;
    }
    async checkUserRated(ctx, roleId, commentId) {
        const isRated = await this.isUserRated(ctx, roleId, commentId);
        if (isRated) {
            throw errorCode_1.errorCodesV2.BazaarRatingAlreadyExists;
        }
    }
    convertToBazaarRatingShowItemList(bazaarRatingList, roleInfoMap, userInfoMap) {
        const itemList = [];
        for (const bazaarRating of bazaarRatingList) {
            const roleInfo = roleInfoMap[bazaarRating.FromRoleId];
            const userInfo = userInfoMap[bazaarRating.FromRoleId];
            const item = {
                id: bazaarRating.ID,
                roleId: bazaarRating.FromRoleId,
                postId: bazaarRating.PostId,
                commentId: bazaarRating.CommentId,
                star: bazaarRating.Star,
                roleInfo,
                userInfo,
                text: bazaarRating.Text,
                createTime: bazaarRating.CreateTime,
            };
            itemList.push(item);
        }
        return itemList;
    }
    /**
     * 获取用户接收的评价列表
     * @param ctx
     * @param roleId
     * @param pagination
     * @returns
     */
    async listReceiveBazaarRatingList(ctx, roleId, pagination) {
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    ToRoleId: roleId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, commentListLen: list.length, roleId, pagination }, "listReceiveBazaarRatingListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, roleId, pagination }, "listReceiveBazaarRatingListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async countPlayerReceiveRating(ctx, roleId) {
        try {
            const count = await this.bazaarRatingModel.count({
                ToRoleId: roleId,
                Status: 0 /* Statues.Normal */
            });
            logger.debug({ ctx, count, roleId }, "countPlayerReceiveRatingOk");
            return count;
        }
        catch (err) {
            logger.error({ ctx, err, roleId }, "countPlayerReceiveRatingError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    /**
     * 获取用户发送的评价列表
     * @param ctx
     * @param roleId
     * @param pagination
     * @returns
     */
    async listSendBazaarRatingList(ctx, roleId, pagination) {
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    RoleId: roleId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, commentListLen: list.length, roleId, pagination }, "listSendBazaarRatingListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, roleId, pagination }, "listSendBazaarRatingListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getRatingByCommentIds(ctx, commentIds) {
        if (commentIds.length === 0) {
            return [];
        }
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    CommentId: commentIds,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination: {
                    page: 1,
                    pageSize: commentIds.length
                }
            });
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, commentIds }, "getRatingByCommentIdsError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getRatingCoreMap(ctx, commentIds) {
        const list = await this.getRatingByCommentIds(ctx, commentIds);
        const map = {};
        for (const item of list) {
            map[item.CommentId] = {
                commentId: item.CommentId,
                star: item.Star,
                text: item.Text,
                createTime: item.CreateTime
            };
        }
        return map;
    }
    async getCommentBeRatingRoleGteMinNum(ctx, roleId, minNum) {
        try {
            const query = this.bazaarRatingModel.raw(`
                SELECT COUNT(*) >= ? AS isGteMinNum
                FROM (
                    SELECT DISTINCT FromRoleId
                    FROM nsh_astrology_bazaar_rating
                    WHERE ToRoleId = ? AND Status = 0
                    LIMIT ?
                ) AS distinct_roles;
            `, [minNum, roleId, minNum]);
            const result = await this.bazaarRatingModel.executeByQuery(query);
            const isGteMinNum = Boolean(result[0]?.isGteMinNum ?? 0);
            logger.debug({ ctx, roleId, minNum, isGteMinNum }, "getCommentBeRatingRoleGteMinNumOK");
            return isGteMinNum;
        }
        catch (err) {
            logger.error({ ctx, err, roleId, minNum }, "getCommentBeRatingRoleGteMinNumError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarRatingService = BazaarRatingService;
//# sourceMappingURL=bazaarRatingService.js.map