"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarCommentService = void 0;
const errorCodes_1 = require("../../errorCodes");
const errorCode_1 = require("../../errors/errorCode");
const logger_1 = require("../../logger");
const bazaarCommentModel_1 = require("../../models/astrology/bazaarCommentModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarCommentService");
class BazaarCommentService {
    constructor() {
        this.bazaarCommentModel = bazaarCommentModel_1.BazaarCommentModel.getInstance();
    }
    static getInstance() {
        if (!BazaarCommentService.instance) {
            BazaarCommentService.instance = new BazaarCommentService();
        }
        return BazaarCommentService.instance;
    }
    async addBazaarComment(ctx, bazaarComment) {
        try {
            const id = await this.bazaarCommentModel.insert(bazaarComment);
            logger.info({ ctx, id, bazaarComment }, "addBazaarCommentOk");
            return id;
        }
        catch (err) {
            if (err && err.code === errorCode_1.DBErrorCodes.DuplicatedEntry) {
                throw errorCode_1.errorCodesV2.BazaarCommentAlreadyExists;
            }
            logger.error({ err, bazaarComment }, "addBazaarCommentError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async isUserCommented(ctx, roleId, postId) {
        const r = await this.bazaarCommentModel.findOne({
            RoleId: roleId,
            PostId: postId,
            Status: 0 /* Statues.Normal */
        }, ['ID']);
        const isCommented = r && r.ID > 0;
        return isCommented;
    }
    async checkUserCommented(ctx, roleId, postId) {
        const isCommented = await this.isUserCommented(ctx, roleId, postId);
        if (isCommented) {
            logger.warn({ ctx, roleId, postId }, "checkUserCommentedOk");
            throw errorCode_1.errorCodesV2.BazaarCommentAlreadyExists;
        }
    }
    async getBazaarUserComment(ctx, roleId, commentId) {
        const bazaarComment = await this.bazaarCommentModel.findOne({ ID: commentId, RoleId: roleId, Status: 0 /* Statues.Normal */ });
        if (!bazaarComment) {
            logger.warn({ ctx, roleId, commentId }, "getBazaarCommentNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarComment;
    }
    async verifyBazaarComment(ctx, commentId) {
        const bazaarComment = await this.bazaarCommentModel.findOne({ ID: commentId, Status: 0 /* Statues.Normal */ });
        if (!bazaarComment) {
            logger.warn({ ctx, commentId }, "getBazaarCommentNotFound");
            throw errorCode_1.errorCodesV2.BazaarCommentNotFound;
        }
        return bazaarComment;
    }
    convertToBazaarCommentShowItemList(bazaarCommentList, showRoleInfoMap, userInfoMap, ratingCoreMap) {
        const itemList = [];
        for (const bazaarComment of bazaarCommentList) {
            const roleInfo = showRoleInfoMap[bazaarComment.RoleId];
            const userInfo = userInfoMap[bazaarComment.RoleId];
            const ratingCore = ratingCoreMap[bazaarComment.ID];
            const item = {
                id: bazaarComment.ID,
                roleId: bazaarComment.RoleId,
                postId: bazaarComment.PostId,
                roleInfo: roleInfo,
                userInfo: userInfo,
                text: bazaarComment.Text,
                createTime: bazaarComment.CreateTime,
                rating: {
                    commentId: bazaarComment.ID,
                    star: ratingCore?.star || 0,
                    text: ratingCore?.text || "",
                    createTime: ratingCore?.createTime || 0
                }
            };
            itemList.push(item);
        }
        return itemList;
    }
    async listBazaarCommentList(ctx, postId, pagination) {
        try {
            const list = await this.bazaarCommentModel.findMany({
                where: {
                    PostId: postId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, commentListLen: list.length, postId, pagination }, "listBazaarCommentListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, postId, pagination }, "listBazaarCommentListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async countBazaarCommentByPostId(ctx, postId) {
        try {
            const count = await this.bazaarCommentModel.count({
                PostId: postId,
                Status: 0 /* Statues.Normal */
            });
            logger.debug({ ctx, count, postId }, "countBazaarCommentListOk");
            return count;
        }
        catch (err) {
            logger.error({ ctx, err, postId }, "countBazaarCommentListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    /**
     * 过滤当前用户已评论的帖子id
     * @param ctx
     * @param roleId
     * @param postIds
     * @returns
     */
    async filterCommentedPostIds(ctx, roleId, postIds) {
        try {
            const rows = await this.bazaarCommentModel.findMany({
                where: {
                    RoleId: roleId,
                    PostId: postIds
                },
                select: ['PostId'],
                pagination: {
                    page: 1,
                    pageSize: postIds.length
                }
            });
            const commentedPostIds = rows.map(item => item.PostId);
            logger.info({ ctx, commentedPostIds, roleId, postIds }, "filterCommentedPostIdsOk");
            return commentedPostIds;
        }
        catch (err) {
            logger.error({ ctx, err, roleId, postIds }, "filterCommentedPostIdsError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarCommentService = BazaarCommentService;
//# sourceMappingURL=bazaarCommentService.js.map