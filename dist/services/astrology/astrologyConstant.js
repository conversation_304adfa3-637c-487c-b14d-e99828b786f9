"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyHouseMap = exports.AstrologyConstellationMap = exports.AstrologyPlanetMap = void 0;
exports.fromAstrologyBazaarDiceTuple = fromAstrologyBazaarDiceTuple;
exports.toAstrologyBazaarDiceTuple = toAstrologyBazaarDiceTuple;
exports.AstrologyPlanetMap = {
    ["sun" /* AstrologyPlanet.SUN */]: "太阳",
    ["moon" /* AstrologyPlanet.MOON */]: "月亮",
    ["mercury" /* AstrologyPlanet.MERCURY */]: "水星",
    ["venus" /* AstrologyPlanet.VENUS */]: "金星",
    ["mars" /* AstrologyPlanet.MARS */]: "火星",
    ["jupiter" /* AstrologyPlanet.JUPITER */]: "木星",
    ["saturn" /* AstrologyPlanet.SATURN */]: "土星",
    ["uranus" /* AstrologyPlanet.URANUS */]: "天王星",
    ["neptune" /* AstrologyPlanet.NEPTUNE */]: "海王星",
    ["pluto" /* AstrologyPlanet.PLUTO */]: "冥王星",
    ["southNode" /* AstrologyPlanet.SOUTH_NODE */]: "南郊点",
    ["northNode" /* AstrologyPlanet.NORTH_NODE */]: "北郊点"
};
exports.AstrologyConstellationMap = {
    ["aries" /* AstrologyConstellation.ARIES */]: "白羊座",
    ["taurus" /* AstrologyConstellation.TAURUS */]: "金牛座",
    ["gemini" /* AstrologyConstellation.GEMINI */]: "双子座",
    ["cancer" /* AstrologyConstellation.CANCER */]: "巨蟹座",
    ["leo" /* AstrologyConstellation.LEO */]: "狮子座",
    ["virgo" /* AstrologyConstellation.VIRGO */]: "处女座",
    ["libra" /* AstrologyConstellation.LIBRA */]: "天秤座",
    ["scorpio" /* AstrologyConstellation.SCORPIO */]: "天蝎座",
    ["sagittarius" /* AstrologyConstellation.SAGITTARIUS */]: "射手座",
    ["capricorn" /* AstrologyConstellation.CAPRICORN */]: "摩羯座",
    ["aquarius" /* AstrologyConstellation.AQUARIUS */]: "水瓶座",
    ["pisces" /* AstrologyConstellation.PISCES */]: "双鱼座"
};
exports.AstrologyHouseMap = {
    [1 /* AstrologyHouse.HOUSE_1 */]: "第一宫命宫",
    [2 /* AstrologyHouse.HOUSE_2 */]: "第二宫财运宫",
    [3 /* AstrologyHouse.HOUSE_3 */]: "第三宫兄弟宫",
    [4 /* AstrologyHouse.HOUSE_4 */]: "第四宫家庭宫",
    [5 /* AstrologyHouse.HOUSE_5 */]: "第五宫子女宫",
    [6 /* AstrologyHouse.HOUSE_6 */]: "第六宫健康宫",
    [7 /* AstrologyHouse.HOUSE_7 */]: "第七宫夫妻宫",
    [8 /* AstrologyHouse.HOUSE_8 */]: "第八宫疾厄宫",
    [9 /* AstrologyHouse.HOUSE_9 */]: "第九宫迁移宫",
    [10 /* AstrologyHouse.HOUSE_10 */]: "第十宫事业宫",
    [11 /* AstrologyHouse.HOUSE_11 */]: "第十一宫人际宫",
    [12 /* AstrologyHouse.HOUSE_12 */]: "第十二宫玄秘宫"
};
function fromAstrologyBazaarDiceTuple(diceResultTuple) {
    return {
        planet: exports.AstrologyPlanetMap[diceResultTuple[0]],
        constellation: exports.AstrologyConstellationMap[diceResultTuple[1]],
        house: exports.AstrologyHouseMap[diceResultTuple[2]]
    };
}
function toAstrologyBazaarDiceTuple(diceResult) {
    return [exports.AstrologyPlanetMap[diceResult.planet], exports.AstrologyConstellationMap[diceResult.constellation], exports.AstrologyHouseMap[diceResult.house]];
}
//# sourceMappingURL=astrologyConstant.js.map