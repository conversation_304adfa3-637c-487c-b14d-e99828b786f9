"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarTopicService = void 0;
const util_1 = require("../../common/util");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const bazaarTopicDailyHotModel_1 = require("../../models/astrology/bazaarTopicDailyHotModel");
const bazaarTopicModel_1 = require("../../models/astrology/bazaarTopicModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarTopicService");
class BazaarTopicService {
    constructor() {
        this.bazaarTopicModel = bazaarTopicModel_1.BazaarTopicModel.getInstance();
        this.bazaarTopicDailyHotModel = bazaarTopicDailyHotModel_1.BazaarTopicDailyHotModel.getInstance();
    }
    static getInstance() {
        if (!BazaarTopicService.instance) {
            BazaarTopicService.instance = new BazaarTopicService();
        }
        return BazaarTopicService.instance;
    }
    async saveTopic(ctx, topicId, topicText) {
        const now = Date.now();
        try {
            const id = await this.bazaarTopicModel.createOrUpdate({
                ID: topicId,
                Text: topicText,
                CreateTime: now,
                UpdateTime: now
            }, {
                UpdateTime: now,
                Text: topicText
            });
            logger.info({ ctx, id, topicId, topicText }, "saveTopicOk");
            return topicId;
        }
        catch (error) {
            logger.error({ ctx, error }, "saveTopicError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async incrDailyTopicHot(ctx, topicId, now) {
        const ds = (0, util_1.getDayStr)(now);
        try {
            const rawQuery = this.bazaarTopicDailyHotModel.raw(`
            INSERT INTO nsh_astrology_post_topic_daily_hot (DS, TopicId, Hot, CreateTime, UpdateTime)
            VALUES (?, ?, 1, ?, ?)
            ON DUPLICATE KEY UPDATE
            Hot = Hot + 1,
            UpdateTime = ?
        `, [ds, topicId, now, now, now]);
            const upRet = await this.bazaarTopicDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, topicId }, "incrDailyTopicHotOk");
        }
        catch (err) {
            logger.error({ ctx, err, ds, topicId }, "incrDailyTopicHotError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async decrDailyTopicHot(ctx, topicId, now) {
        const ds = (0, util_1.getDayStr)(now);
        try {
            const rawQuery = this.bazaarTopicDailyHotModel.raw(`
            UPDATE nsh_astrology_post_topic_daily_hot
            SET Hot = Hot - 1,
            UpdateTime = ?
            WHERE DS = ? AND TopicId = ? AND Hot > 0
        `, [now, ds, topicId]);
            const upRet = await this.bazaarTopicDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, topicId }, "decrDailyTopicHotOk");
        }
        catch (err) {
            logger.error({ ctx, err, ds, topicId }, "decrDailyTopicHotError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getTopicIdToTextMap(ctx, topicIds) {
        try {
            const ret = {};
            if (topicIds.length === 0) {
                return ret;
            }
            const list = await this.bazaarTopicModel.findMany({
                where: {
                    ID: topicIds
                },
                select: ['ID', 'Text'],
                pagination: {
                    page: 1,
                    pageSize: topicIds.length
                }
            });
            for (const item of list) {
                ret[item.ID] = item.Text;
            }
            return ret;
        }
        catch (err) {
            logger.error({ ctx, err }, "getTopicIdToTextMapError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getDailyTopicHotList(ctx, ds, fetchSize) {
        try {
            const list = await this.bazaarTopicDailyHotModel.findMany({
                where: { DS: ds },
                orderBy: [['Hot', 'desc'], ['UpdateTime', 'desc']],
                select: ['TopicId', 'Hot'],
                pagination: {
                    page: 1,
                    pageSize: fetchSize
                }
            });
            logger.info({ ctx, list, ds, fetchSize }, "getDailyTopicHotListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, ds, fetchSize }, "getDailyTopicHotListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    convertToTopicShowList(list, topicIdToTextMap) {
        const listRet = [];
        for (const item of list) {
            const text = topicIdToTextMap[item.TopicId] || "";
            listRet.push({
                id: item.TopicId,
                text,
                hot: item.Hot
            });
        }
        return listRet;
    }
    async getMostFocusedTopicId(ctx, ds) {
        let resp = null;
        try {
            const list = await this.getDailyTopicHotList(ctx, ds, 1);
            if (list.length === 0) {
                return resp;
            }
            const topicIds = list.map(item => item.TopicId);
            const topicIdToTextMap = await this.getTopicIdToTextMap(ctx, topicIds);
            resp = {
                id: list[0].TopicId,
                text: topicIdToTextMap[list[0].TopicId]
            };
            return resp;
        }
        catch (err) {
            logger.error({ ctx, err }, "getMostFocusedTopicIdError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarTopicService = BazaarTopicService;
//# sourceMappingURL=bazaarTopicService.js.map