"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarCommentService = void 0;
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const bazaarCommentModel_1 = require("../../models/astrology/bazaarCommentModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarCommentService");
class BazaarCommentService {
    constructor() {
        this.bazaarCommentModel = bazaarCommentModel_1.BazaarCommentModel.getInstance();
    }
    static getInstance() {
        if (!BazaarCommentService.instance) {
            BazaarCommentService.instance = new BazaarCommentService();
        }
        return BazaarCommentService.instance;
    }
    async addBazaarComment(ctx, bazaarComment) {
        try {
            const id = await this.bazaarCommentModel.insert(bazaarComment);
            logger.info({ ctx, id, bazaarComment }, "addBazaarCommentOk");
            return id;
        }
        catch (error) {
            logger.error({ error, bazaarComment }, "addBazaarCommentError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getBazaarUserComment(ctx, roleId, commentId) {
        const bazaarComment = await this.bazaarCommentModel.findOne({ ID: commentId, RoleId: roleId, Status: 0 /* Statues.Normal */ });
        if (!bazaarComment) {
            logger.warn({ ctx, roleId, commentId }, "getBazaarCommentNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarComment;
    }
    async verifyBazaarComment(ctx, commentId) {
        const bazaarComment = await this.bazaarCommentModel.findOne({ ID: commentId, Status: 0 /* Statues.Normal */ });
        if (!bazaarComment) {
            logger.warn({ ctx, commentId }, "getBazaarCommentNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarComment;
    }
    convertToBazaarCommentShowItemList(bazaarCommentList) {
        const itemList = [];
        for (const bazaarComment of bazaarCommentList) {
            const item = {
                id: bazaarComment.ID,
                roleId: bazaarComment.RoleId,
                postId: bazaarComment.PostId,
                //TODO: fill roleinfo
                roleInfo: {
                    roleId: bazaarComment.RoleId,
                    roleName: "",
                    jobId: 0,
                    gender: 0
                },
                text: bazaarComment.Text,
                createTime: bazaarComment.CreateTime,
                rating: {
                    commentId: bazaarComment.ID,
                    star: 0,
                    text: "",
                    createTime: 0
                }
            };
            itemList.push(item);
        }
        return itemList;
    }
    async listBazaarCommentList(ctx, postId, pagination) {
        try {
            const list = await this.bazaarCommentModel.findMany({
                where: {
                    PostId: postId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, commentListLen: list.length, postId, pagination }, "listBazaarCommentListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, postId, pagination }, "listBazaarCommentListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarCommentService = BazaarCommentService;
//# sourceMappingURL=bazaarCommentService%20copy.js.map