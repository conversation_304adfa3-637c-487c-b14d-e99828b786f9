"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarPostService = void 0;
const operation_1 = require("../../components/astrology/operation");
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const bazaarPostDailyHotModel_1 = require("../../models/astrology/bazaarPostDailyHotModel");
const bazaarPostModel_1 = require("../../models/astrology/bazaarPostModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarPostService");
class BazaarPostService {
    constructor() {
        this.bazaarPostModel = bazaarPostModel_1.BazaarPostModel.getInstance();
        this.bazaarPostDailyHotModel = bazaarPostDailyHotModel_1.BazaarPostDailyHotModel.getInstance();
    }
    static getInstance() {
        if (!BazaarPostService.instance) {
            BazaarPostService.instance = new BazaarPostService();
        }
        return BazaarPostService.instance;
    }
    async addBazaarPost(ctx, bazaarPost) {
        try {
            const id = await this.bazaarPostModel.insert(bazaarPost);
            logger.info({ ctx, id, bazaarPost }, "addBazaarPostOk");
            return id;
        }
        catch (error) {
            logger.error({ error, bazaarPost }, "addBazaarPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getBazaarPost(ctx, postId) {
        const bazaarPost = await this.bazaarPostModel.findOne({ ID: postId, Status: 0 /* Statues.Normal */ });
        if (!bazaarPost) {
            logger.warn({ ctx, postId }, "getBazaarPostNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarPost;
    }
    async verifyBazaarPost(ctx, postId) {
        const bazaarPost = await this.bazaarPostModel.findOne({ ID: postId, Status: 0 /* Statues.Normal */ });
        if (!bazaarPost) {
            logger.warn({ ctx, postId }, "getBazaarPostNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarPost;
    }
    async incrCommentCount(ctx, postId) {
        try {
            let query = this.bazaarPostModel.scope().where({ ID: postId, Status: 0 /* Statues.Normal */ }).increment('CommentCount', 1);
            const ret = await this.bazaarPostModel.executeByQuery(query);
            logger.info({ ctx, ret, postId }, "incrPostCommentCountOk");
        }
        catch (error) {
            logger.error({ ctx, error, postId }, "incrPostCommentCountError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async incrPostDailyHot(ctx, postId, now) {
        const ds = (0, operation_1.getDayStrForAstrology)(new Date(now));
        try {
            const rawQuery = this.bazaarPostDailyHotModel.raw(`
            INSERT INTO nsh_astrology_post_daily_hot (DS, PostId, Hot, CreateTime, UpdateTime)
            VALUES (?, ?, 1, ?, ?)
            ON DUPLICATE KEY UPDATE
            Hot = Hot + 1,
            UpdateTime = ?
        `, [ds, postId, now, now, now]);
            const upRet = await this.bazaarPostDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, postId }, "incrPostDailyHotOk");
        }
        catch (err) {
            logger.error({ ctx, err, ds, postId }, "incrPostDailyHotError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async decrPostDailyHot(ctx, postId, now) {
        const ds = (0, operation_1.getDayStrForAstrology)(new Date(now));
        try {
            const rawQuery = this.bazaarPostDailyHotModel.raw(`
            UPDATE nsh_astrology_post_daily_hot
            SET Hot = Hot - 1,
            UpdateTime = ?
            WHERE DS = ? AND PostId = ? AND Hot > 0
        `, [now, ds, postId]);
            const upRet = await this.bazaarPostDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, postId }, "decrPostDailyHotOk");
        }
        catch (err) {
            logger.error({ ctx, err, ds, postId }, "decrPostDailyHotError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async decrCommentCount(ctx, postId) {
        try {
            let query = this.bazaarPostModel.scope().where({ ID: postId, Status: 0 /* Statues.Normal */ }).where('CommentCount', '>', 0).decrement('CommentCount', 1);
            const ret = await this.bazaarPostModel.executeByQuery(query);
            logger.info({ ctx, ret, postId }, "decrPostCommentCountOk");
        }
        catch (err) {
            logger.error({ ctx, err, postId }, "decrPostCommentCountError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    convertToBazaarPostShowItemList(bazaarPostList, roleInfoMap, userInfoMap, commentedPostIds) {
        const itemList = [];
        const commentedPostIdsSet = new Set(commentedPostIds);
        for (const bazaarPost of bazaarPostList) {
            const roleInfo = roleInfoMap[bazaarPost.RoleId];
            const userInfo = userInfoMap[bazaarPost.RoleId];
            // 如果评论过该帖子，则不能评论,一个帖子用户只能评论一次
            const canComment = !commentedPostIdsSet.has(bazaarPost.ID);
            const item = {
                id: bazaarPost.ID,
                roleId: bazaarPost.RoleId,
                topicId: bazaarPost.TopicId,
                roleInfo,
                userInfo,
                question: bazaarPost.Question,
                dice: {
                    planet: bazaarPost.DicePlanet,
                    constellation: bazaarPost.DiceConstellation,
                    house: bazaarPost.DiceHouse
                },
                commentCount: bazaarPost.CommentCount,
                canComment,
                createTime: bazaarPost.CreateTime
            };
            itemList.push(item);
        }
        return itemList;
    }
    async listBazaarUserSelfPost(ctx, roleId, topicIds, pagination) {
        try {
            let initQuery = this.bazaarPostModel.normalScope().where({ RoleId: roleId });
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds);
            }
            const list = await this.bazaarPostModel.findMany({
                initQuery,
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, listLen: list.length }, "listUserSelfBazaarPostOk");
            return list;
        }
        catch (error) {
            logger.error({ ctx, error, roleId }, "listUserSelfBazaarPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async countBazaarUserSelfPost(ctx, roleId, topicIds) {
        try {
            let initQuery = this.bazaarPostModel.normalScope().where({ RoleId: roleId });
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds);
            }
            const count = await this.bazaarPostModel.countByQuery(initQuery);
            logger.debug({ ctx, count, roleId, topicIds }, "countBazaarUserSelfPostOk");
            return count;
        }
        catch (error) {
            logger.error({ ctx, error, roleId }, "countBazaarUserSelfPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async listBazaarPublicPost(ctx, roleId, topicIds, pagination) {
        try {
            let initQuery = this.bazaarPostModel.normalScope();
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds);
            }
            const list = await this.bazaarPostModel.findMany({
                initQuery,
                orderBy: [['CommentCount', 'desc']],
                pagination
            });
            logger.info({ ctx, listLen: list.length, roleId, topicIds }, "listUserSelfBazaarPostOk");
            return list;
        }
        catch (error) {
            logger.error({ ctx, error, roleId }, "listUserSelfBazaarPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async countBazaarPublicPost(ctx, roleId, topicIds) {
        try {
            let initQuery = this.bazaarPostModel.normalScope();
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds);
            }
            const count = await this.bazaarPostModel.countByQuery(initQuery);
            logger.debug({ ctx, count, roleId, topicIds }, "countBazaarPublicPostOk");
            return count;
        }
        catch (error) {
            logger.error({ ctx, error, roleId }, "countBazaarPublicPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getMostCommentedPostQuestion(ctx, ds) {
        try {
            const rows = await this.bazaarPostDailyHotModel.findMany({
                where: {
                    DS: ds
                },
                orderBy: [['Hot', 'desc'], ['ID', 'asc']],
                select: ['PostId'],
                pagination: {
                    page: 1,
                    pageSize: 1
                }
            });
            if (rows.length === 0) {
                logger.warn({ ctx, ds }, "getMostCommentedPostQuestionNotFound");
                return "";
            }
            const postId = rows[0].PostId;
            const post = await this.bazaarPostModel.findOne({ ID: postId, Status: 0 /* Statues.Normal */ }, ['Question']);
            if (!post) {
                logger.warn({ ctx, postId }, "getMostCommentedPostQuestionNotFound");
                return "";
            }
            return post.Question;
        }
        catch (err) {
            logger.error({ ctx, err, ds }, "getMostCommentedPostQuestionError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarPostService = BazaarPostService;
//# sourceMappingURL=bazaarPostService.js.map