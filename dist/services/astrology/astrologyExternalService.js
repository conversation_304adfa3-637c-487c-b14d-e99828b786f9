"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyExternalService = void 0;
const logger_1 = require("../../logger");
const MomentService = require("../../services/moment");
const logger = (0, logger_1.clazzLogger)("astrology.astrologyExternalService");
class AstrologyExternalService {
    constructor() {
    }
    static getInstance() {
        if (!AstrologyExternalService.instance) {
            AstrologyExternalService.instance = new AstrologyExternalService();
        }
        return AstrologyExternalService.instance;
    }
    async addMoment(ctx, params) {
        try {
            const ip = ctx.getIp();
            let momentText = params.question;
            // Add topicText with #topicText <originText> syntax if topicText exists
            if (params.topicText && params.topicText.trim()) {
                momentText = `#${params.topicText} ${params.question}`;
            }
            // Generate shareImage URL from dice results
            const shareImageUrl = this.generateShareImageUrl(params.dice);
            const addProps = {
                roleid: params.roleid,
                text: momentText,
                ip: ip,
                ...(shareImageUrl && {
                    imgs: shareImageUrl,
                    preApprovedImgs: [shareImageUrl]
                }),
            };
            const addRet = await MomentService.addMomentWithPreApprovedImages(addProps);
            logger.info({ ctx, addRet, params, addProps }, "astrologyAddMomentOk");
        }
        catch (err) {
            logger.error({ ctx, err, params }, "astrologyAddMomentFail");
            return false;
        }
        return true;
    }
    generateShareImageUrl(dice) {
        if (!dice) {
            return "";
        }
        const { planet, constellation, house } = dice;
        return `https://hi-163-nsh.nosdn.127.net/assets/icons/astrology/planet_${planet}_zodiac_${constellation}_house_${house}.png`;
    }
}
exports.AstrologyExternalService = AstrologyExternalService;
//# sourceMappingURL=astrologyExternalService.js.map