"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarPostService = void 0;
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const bazaarPostModel_1 = require("../../models/astrology/bazaarPostModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarPostService");
class BazaarPostService {
    constructor() {
        this.bazaarPostModel = bazaarPostModel_1.BazaarPostModel.getInstance();
    }
    static getInstance() {
        if (!BazaarPostService.instance) {
            BazaarPostService.instance = new BazaarPostService();
        }
        return BazaarPostService.instance;
    }
    async addBazaarPost(ctx, bazaarPost) {
        try {
            const id = await this.bazaarPostModel.insert(bazaarPost);
            logger.info({ ctx, id, bazaarPost }, "addBazaarPostOk");
            return id;
        }
        catch (error) {
            logger.error({ error, bazaarPost }, "addBazaarPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async getBazaarUserPost(ctx, roleId, postId) {
        const bazaarPost = await this.bazaarPostModel.findOne({ ID: postId, RoleId: roleId, Status: 0 /* Statues.Normal */ });
        if (!bazaarPost) {
            logger.warn({ ctx, roleId, postId }, "getBazaarPostNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarPost;
    }
    async verifyBazaarPost(ctx, postId) {
        const bazaarPost = await this.bazaarPostModel.findOne({ ID: postId, Status: 0 /* Statues.Normal */ });
        if (!bazaarPost) {
            logger.warn({ ctx, postId }, "getBazaarPostNotFound");
            throw errorCodes_1.errorCode.DataNotFound;
        }
        return bazaarPost;
    }
    async convertToBazaarPostShowItemList(ctx, bazaarPostList) {
        const itemList = [];
        for (const bazaarPost of bazaarPostList) {
            const item = {
                id: bazaarPost.ID,
                roleId: bazaarPost.RoleId,
                topicId: bazaarPost.TopicId,
                //TODO: fill roleinfo
                roleInfo: {
                    roleId: bazaarPost.RoleId,
                    roleName: "",
                    jobId: 0,
                    gender: 0
                },
                question: bazaarPost.Question,
                dice: {
                    planet: bazaarPost.DicePlanet,
                    constellation: bazaarPost.DiceConstellation,
                    house: bazaarPost.DiceHouse
                },
                commentCount: bazaarPost.CommentCount,
                //TODO: 需要根据用户是否解析过该帖子来判断是否可以评论
                canComment: true,
                createTime: bazaarPost.CreateTime
            };
            itemList.push(item);
        }
        return itemList;
    }
    async listBazaarUserSelfPost(ctx, roleId, pagination) {
        try {
            const list = await this.bazaarPostModel.findMany({
                where: {
                    RoleId: roleId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, listLen: list.length }, "listUserSelfBazaarPostOk");
            return list;
        }
        catch (error) {
            logger.error({ ctx, error, roleId }, "listUserSelfBazaarPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    async listBazaarPublicPost(ctx, roleId, pagination) {
        try {
            const list = await this.bazaarPostModel.findMany({
                where: {
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, listLen: list.length, roleId }, "listUserSelfBazaarPostOk");
            return list;
        }
        catch (error) {
            logger.error({ ctx, error, roleId }, "listUserSelfBazaarPostError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarPostService = BazaarPostService;
//# sourceMappingURL=bazaarPostService%20copy.js.map