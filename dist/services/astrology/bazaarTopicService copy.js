"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarRatingService = void 0;
const errorCodes_1 = require("../../errorCodes");
const logger_1 = require("../../logger");
const bazaarRatingModel_1 = require("../../models/astrology/bazaarRatingModel");
const logger = (0, logger_1.clazzLogger)("astrology.bazaarCommentService");
class BazaarRatingService {
    constructor() {
        this.bazaarRatingModel = bazaarRatingModel_1.BazaarRatingModel.getInstance();
    }
    static getInstance() {
        if (!BazaarRatingService.instance) {
            BazaarRatingService.instance = new BazaarRatingService();
        }
        return BazaarRatingService.instance;
    }
    async addBazaarRating(ctx, bazaarRating) {
        try {
            const id = await this.bazaarRatingModel.insert(bazaarRating);
            logger.info({ ctx, id, bazaarRating }, "addBazaarRatingOk");
            return id;
        }
        catch (error) {
            logger.error({ error, bazaarRating }, "addBazaarRatingError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    convertToBazaarRatingShowItemList(bazaarRatingList) {
        const itemList = [];
        for (const bazaarRating of bazaarRatingList) {
            const item = {
                id: bazaarRating.ID,
                roleId: bazaarRating.RoleId,
                postId: bazaarRating.PostId,
                commentId: bazaarRating.CommentId,
                star: bazaarRating.Star,
                roleInfo: {
                    roleId: bazaarRating.RoleId,
                    roleName: "",
                    jobId: 0,
                    gender: 0
                },
                text: bazaarRating.Text,
                createTime: bazaarRating.CreateTime,
            };
            itemList.push(item);
        }
        return itemList;
    }
    /**
     * 获取用户接收的评价列表
     * @param ctx
     * @param roleId
     * @param pagination
     * @returns
     */
    async listReceiveBazaarRatingList(ctx, roleId, pagination) {
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    CommentRoleId: roleId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, commentListLen: list.length, roleId, pagination }, "listReceiveBazaarRatingListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, roleId, pagination }, "listReceiveBazaarRatingListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
    /**
     * 获取用户发送的评价列表
     * @param ctx
     * @param roleId
     * @param pagination
     * @returns
     */
    async listSendBazaarRatingList(ctx, roleId, pagination) {
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    RoleId: roleId,
                    Status: 0 /* Statues.Normal */
                },
                orderBy: [['ID', 'desc']],
                pagination
            });
            logger.info({ ctx, commentListLen: list.length, roleId, pagination }, "listSendBazaarRatingListOk");
            return list;
        }
        catch (err) {
            logger.error({ ctx, err, roleId, pagination }, "listSendBazaarRatingListError");
            throw errorCodes_1.errorCode.DatabaseError;
        }
    }
}
exports.BazaarRatingService = BazaarRatingService;
//# sourceMappingURL=bazaarTopicService%20copy.js.map