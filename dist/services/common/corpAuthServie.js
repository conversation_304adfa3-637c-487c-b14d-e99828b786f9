"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateToken = generateToken;
exports.skipCorpAuth = skipCorpAuth;
exports.corpAuthCookieAuth = corpAuthCookieAuth;
const config_1 = require("../../common/config");
const constants_1 = require("../../common/constants");
const util_1 = require("../../common/util");
const context_1 = require("../../context");
function generateToken(mail, time, secret) {
    const payload = [mail, time, secret].join('');
    return (0, util_1.hexMd5)(payload);
}
function skipCorpAuth(req, res, next) {
    req.skipCorpAuth = true;
    next();
}
function corpAuthCookieAuth(req, res, next) {
    if (req.skipCorpAuth) {
        next();
        return;
    }
    const cookies = req.cookies;
    let info = cookies?.[config_1.corpAuthCfg.infoKey];
    let token = cookies?.[config_1.corpAuthCfg.tokenKey];
    if (!info || !token) {
        res.send(401, { code: 401, msg: "当前用户未登录" });
        return;
    }
    let [mailBuff, time, payloadEncoded] = info.split('.');
    let mail = Buffer.from(mailBuff, 'base64').toString();
    let payload = JSON.parse(Buffer.from(decodeURIComponent(payloadEncoded), "base64").toString());
    if (time) {
        if (Date.now() / 1000 - time > constants_1.ONE_DAY_SECONDS) {
            res.send(401, { code: 401, msg: "登录状态过期,请重新登录" });
            return;
        }
    }
    let calToken = generateToken(mail, time, config_1.corpAuthCfg.secret);
    if (calToken !== token) {
        return { validate: false, msg: "登录token错误" };
    }
    let ctx = context_1.Context.createWithRequest(req);
    const corpAuthInfo = {
        mail,
        fullName: payload.fullname,
        nickname: payload.nickname
    };
    ctx.set(constants_1.ctxCorpAuthInfoKey, corpAuthInfo);
    next();
}
//# sourceMappingURL=corpAuthServie.js.map