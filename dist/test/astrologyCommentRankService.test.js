"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const astrologyCommentRankService_1 = require("../services/astrology/astrologyCommentRankService");
const redis_1 = require("../common/redis");
describe("AstrologyCommentRankService", () => {
    let service;
    let ctx;
    beforeEach(() => {
        service = astrologyCommentRankService_1.AstrologyCommentRankService.getInstance();
        ctx = { requestId: "test-request-id" };
    });
    afterEach(async () => {
        // 清理测试缓存
        try {
            await (0, redis_1.getRedis)().delAsync("astrology_comment_rank:");
        }
        catch (err) {
            // 忽略清理错误
        }
    });
    describe("getCommentRank", () => {
        it("should return empty list when no users have comments", async () => {
            // 清除缓存确保从数据库查询
            await (0, redis_1.getRedis)().delAsync("astrology_comment_rank:");
            const result = await service.getCommentRank(ctx);
            expect(result).toHaveProperty("list");
            expect(result).toHaveProperty("updateTime");
            expect(Array.isArray(result.list)).toBe(true);
            expect(typeof result.updateTime).toBe("number");
        });
        it("should use cached data when available", async () => {
            const mockCachedData = {
                list: [
                    {
                        roleId: 123,
                        roleName: "TestUser",
                        commentCount: 5,
                        rank: 1,
                        jobId: 1,
                        gender: 0,
                        subGender: 0,
                        headPaintId: 0,
                        bodyPaintId: 0
                    }
                ],
                updateTime: Date.now()
            };
            // 设置缓存
            await (0, redis_1.getRedis)().setAsync("astrology_comment_rank:", JSON.stringify(mockCachedData), "EX", 600);
            const result = await service.getCommentRank(ctx);
            expect(result.list).toHaveLength(1);
            expect(result.list[0].roleId).toBe(123);
            expect(result.list[0].commentCount).toBe(5);
            expect(result.list[0].rank).toBe(1);
        });
    });
    describe("incrementUserCommentCount", () => {
        it("should handle non-existent user gracefully", async () => {
            const nonExistentRoleId = 999999999;
            // 应该不抛出错误
            await expect(service.incrementUserCommentCount(ctx, nonExistentRoleId)).resolves.not.toThrow();
        });
    });
    describe("refreshRank", () => {
        it("should generate rank correctly", async () => {
            const result = await service.refreshRank(ctx);
            expect(result).toHaveProperty("list");
            expect(result).toHaveProperty("updateTime");
            expect(Array.isArray(result.list)).toBe(true);
            // 验证排名逻辑
            for (let i = 0; i < result.list.length - 1; i++) {
                const current = result.list[i];
                const next = result.list[i + 1];
                // 解惑次数应该是降序排列
                expect(current.commentCount).toBeGreaterThanOrEqual(next.commentCount);
                // 排名应该是正确的
                expect(current.rank).toBeGreaterThan(0);
                expect(next.rank).toBeGreaterThanOrEqual(current.rank);
            }
        });
    });
});
//# sourceMappingURL=astrologyCommentRankService.test.js.map