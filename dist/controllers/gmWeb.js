"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cloudGameDurationAuthMiddleware = void 0;
exports.gmWebLoginInfoHandlerGen = gmWebLoginInfoHandlerGen;
exports.gmWebLoginInfoHandler = gmWebLoginInfoHandler;
exports.gmWebLogoutHandler = gmWebLogoutHandler;
const config_1 = require("../common/config");
const constants_1 = require("../common/constants");
const context_1 = require("../context");
const helper_1 = require("../helper");
const OpenIdRoleModel_1 = require("../models/OpenIdRoleModel");
exports.cloudGameDurationAuthMiddleware = gmWebLoginInfoHandlerGen(OpenIdRoleModel_1.ScopeType.CLOUD_GAME_DURATION);
function gmWebLoginInfoHandlerGen(scope) {
    async function middleware(req, res, next) {
        try {
            let ctx = context_1.Context.createWithRequest(req);
            const corpAuthInfo = ctx.get(constants_1.ctxCorpAuthInfoKey);
            const mail = corpAuthInfo.mail;
            const permission = await OpenIdRoleModel_1.default.findRoleByOpenIdAndScope(mail, scope);
            if (!permission) {
                res.send(403, { code: 403, message: "Permission denied" });
                return;
            }
            next();
        }
        catch (err) {
            (0, helper_1.errorHandler)(err, req, res, next);
        }
    }
    return middleware;
}
async function gmWebLoginInfoHandler(req, res, next) {
    try {
        const ctx = context_1.Context.createWithRequest(req);
        const corpAuthInfo = ctx.get(constants_1.ctxCorpAuthInfoKey);
        res.send({ code: 0, data: corpAuthInfo });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
async function gmWebLogoutHandler(req, res, next) {
    try {
        res.clearCookie(config_1.corpAuthCfg.infoKey, config_1.corpAuthCfg.cookieCfg);
        res.clearCookie(config_1.corpAuthCfg.tokenKey, config_1.corpAuthCfg.cookieCfg);
        res.send({ code: 0, data: null });
    }
    catch (err) {
        (0, helper_1.errorHandler)(err, req, res, next);
    }
}
//# sourceMappingURL=gmWeb.js.map