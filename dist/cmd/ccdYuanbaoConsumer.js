"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config = require("../common/config");
const logConsumer_1 = require("../components/cloudGameDuration/logConsumer/logConsumer");
const restify = require("restify");
const logger_1 = require("../logger");
async function main() {
    const app = restify.createServer();
    app.get("/health", async (req, res) => {
        const data = {
            uptime: process.uptime(),
            message: "Ok",
            date: new Date(),
        };
        res.send(data);
    });
    app.listen(config.server.port, () => {
        logger_1.logger.info({ name: app.name, url: app.url }, "Start listening");
    });
    if (config.cloudGameDurationCfg.disableYuanbaoChargeByLog) {
        logger_1.logger.warn("disableYuanbaoChargeByLog is true, will not listen kafka topic");
        return;
    }
    const consumer = await (0, logConsumer_1.getConsumer)();
    await (0, logConsumer_1.listenKafkaTopic)(consumer);
    const errorTypes = ["unhandledRejection", "uncaughtException"];
    const signalTraps = ["SIGTERM", "SIGINT", "SIGUSR2"];
    errorTypes.forEach((type) => {
        process.on(type, async (e) => {
            try {
                console.log(`process listen on ${type} occur,`, { e });
                await consumer.disconnect();
                process.exit(0);
            }
            catch (_) {
                process.exit(1);
            }
        });
    });
    signalTraps.forEach((type) => {
        //@ts-ignore
        process.once(type, async () => {
            try {
                console.log(`listen signal traps on ${type} occur`);
                await consumer.disconnect();
            }
            finally {
                process.kill(process.pid, type);
            }
        });
    });
}
main()
    .then(() => {
    console.log("cloud game duration consumer start");
})
    .catch((err) => {
    console.error(err);
});
//# sourceMappingURL=ccdYuanbaoConsumer.js.map