"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const delayTasks_1 = require("../cron-jobs/delayTasks");
const Bluebird = require("bluebird");
const logger_1 = require("../logger");
const logger = (0, logger_1.clazzLogger)("cmd/delayTaskRunner");
async function main() {
    logger.info("delayTaskRunner start");
    // eslint-disable-next-line no-constant-condition
    while (true) {
        await (0, delayTasks_1.processReadyTask)();
        await (0, delayTasks_1.triggerDelayTask)();
        await Bluebird.delay(1000);
    }
}
main()
    .then(() => {
    console.log("Done!");
    process.exit(0);
})
    .catch((err) => {
    console.error(err);
    process.exit(-1);
});
//# sourceMappingURL=delayTaskRunner.js.map