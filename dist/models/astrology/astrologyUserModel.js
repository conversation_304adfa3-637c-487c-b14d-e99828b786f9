"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyUserModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class AstrologyUserModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_user");
    }
    static getInstance() {
        if (!AstrologyUserModel.instance) {
            AstrologyUserModel.instance = new AstrologyUserModel();
        }
        return AstrologyUserModel.instance;
    }
}
exports.AstrologyUserModel = AstrologyUserModel;
//# sourceMappingURL=astrologyUserModel.js.map