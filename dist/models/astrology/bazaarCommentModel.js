"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarCommentModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class BazaarCommentModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_bazaar_comment");
    }
    static getInstance() {
        if (!BazaarCommentModel.instance) {
            BazaarCommentModel.instance = new BazaarCommentModel();
        }
        return BazaarCommentModel.instance;
    }
}
exports.BazaarCommentModel = BazaarCommentModel;
//# sourceMappingURL=bazaarCommentModel.js.map