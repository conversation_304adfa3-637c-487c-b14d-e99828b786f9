"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyCommentRankModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class AstrologyCommentRankModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_comment_rank");
    }
    static getInstance() {
        if (!AstrologyCommentRankModel.instance) {
            AstrologyCommentRankModel.instance = new AstrologyCommentRankModel();
        }
        return AstrologyCommentRankModel.instance;
    }
}
exports.AstrologyCommentRankModel = AstrologyCommentRankModel;
//# sourceMappingURL=astrologyCommentRankModel.js.map