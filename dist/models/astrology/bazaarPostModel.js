"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarPostModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class BazaarPostModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_bazaar_post");
    }
    static getInstance() {
        if (!BazaarPostModel.instance) {
            BazaarPostModel.instance = new BazaarPostModel();
        }
        return BazaarPostModel.instance;
    }
}
exports.BazaarPostModel = BazaarPostModel;
//# sourceMappingURL=bazaarPostModel.js.map