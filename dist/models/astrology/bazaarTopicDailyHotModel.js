"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarTopicDailyHotModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class BazaarTopicDailyHotModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_post_topic_daily_hot");
    }
    static getInstance() {
        if (!BazaarTopicDailyHotModel.instance) {
            BazaarTopicDailyHotModel.instance = new BazaarTopicDailyHotModel();
        }
        return BazaarTopicDailyHotModel.instance;
    }
}
exports.BazaarTopicDailyHotModel = BazaarTopicDailyHotModel;
//# sourceMappingURL=bazaarTopicDailyHotModel.js.map