"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarTopicModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class BazaarTopicModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_post_topic");
    }
    static getInstance() {
        if (!BazaarTopicModel.instance) {
            BazaarTopicModel.instance = new BazaarTopicModel();
        }
        return BazaarTopicModel.instance;
    }
}
exports.BazaarTopicModel = BazaarTopicModel;
//# sourceMappingURL=bazaarTopicModel.js.map