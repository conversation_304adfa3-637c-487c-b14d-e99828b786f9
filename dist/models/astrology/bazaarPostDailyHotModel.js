"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarPostDailyHotModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class BazaarPostDailyHotModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_post_daily_hot");
    }
    static getInstance() {
        if (!BazaarPostDailyHotModel.instance) {
            BazaarPostDailyHotModel.instance = new BazaarPostDailyHotModel();
        }
        return BazaarPostDailyHotModel.instance;
    }
}
exports.BazaarPostDailyHotModel = BazaarPostDailyHotModel;
//# sourceMappingURL=bazaarPostDailyHotModel.js.map