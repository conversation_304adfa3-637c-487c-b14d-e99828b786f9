"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyRoleInfoModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class AstrologyRoleInfoModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_roleinfo", "RoleId");
    }
    static getInstance() {
        if (!AstrologyRoleInfoModel.instance) {
            AstrologyRoleInfoModel.instance = new AstrologyRoleInfoModel();
        }
        return AstrologyRoleInfoModel.instance;
    }
}
exports.AstrologyRoleInfoModel = AstrologyRoleInfoModel;
//# sourceMappingURL=astrologyRoleInfoModel.js.map