"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AstrologyUserDailyForecastModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class AstrologyUserDailyForecastModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_user_daily_forecast");
    }
    static getInstance() {
        if (!AstrologyUserDailyForecastModel.instance) {
            AstrologyUserDailyForecastModel.instance = new AstrologyUserDailyForecastModel();
        }
        return AstrologyUserDailyForecastModel.instance;
    }
}
exports.AstrologyUserDailyForecastModel = AstrologyUserDailyForecastModel;
//# sourceMappingURL=astrologyUserDailyForecastModel.js.map