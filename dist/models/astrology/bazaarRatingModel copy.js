"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BazaarRatingModel = void 0;
const BaseModel2_1 = require("../BaseModel2");
class BazaarRatingModel extends BaseModel2_1.BaseModelClass {
    constructor() {
        super("nsh_astrology_bazaar_rating");
    }
    static getInstance() {
        if (!BazaarRatingModel.instance) {
            BazaarRatingModel.instance = new BazaarRatingModel();
        }
        return BazaarRatingModel.instance;
    }
}
exports.BazaarRatingModel = BazaarRatingModel;
//# sourceMappingURL=bazaarRatingModel%20copy.js.map