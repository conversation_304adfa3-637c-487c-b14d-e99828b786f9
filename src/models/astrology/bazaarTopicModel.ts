import { BaseModelClass } from "../BaseModel2";

export interface BazaarTopicRecord {
    /** 主键 */
    ID: number;
    /** 话题文本 */
    Text: string;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
}

export class BazaarTopicModel extends BaseModelClass<BazaarTopicRecord> {
    private static instance: BazaarTopicModel;
    constructor() {
        super("nsh_astrology_post_topic");
    }

    static getInstance() {
        if (!BazaarTopicModel.instance) {
            BazaarTopicModel.instance = new BazaarTopicModel();
        }
        return BazaarTopicModel.instance;
    }
}