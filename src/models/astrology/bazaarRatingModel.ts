import { BaseModelClass } from "../BaseModel2";

export interface BazaarRatingRecord {
    /** 主键 */
    ID: number;
    /** 帖子ID */
    PostId: number;
    /** 评论ID, 如果来自im的，为0 */
    CommentId: number;
    /** 被评分角色ID */
    ToRoleId: number;
    /** 评分的角色ID */
    FromRoleId: number;
    /** 评分 */
    Star: number;
    /** 评分内容 */
    Text: string;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
    /** 状态 * 0: 正常 * -1: 删除 */
    Status: number;
}

export class BazaarRatingModel extends BaseModelClass<BazaarRatingRecord> {
    private static instance: BazaarRatingModel;
    constructor() {
        super("nsh_astrology_bazaar_rating");
    }

    static getInstance() {
        if (!BazaarRatingModel.instance) {
            BazaarRatingModel.instance = new BazaarRatingModel();
        }
        return BazaarRatingModel.instance;
    }
}