import { AstrologyConstellation, AstrologyHouse, AstrologyPlanet } from "../../services/astrology/astrologyConstant";
import { BaseModelClass } from "../BaseModel2";

export interface BazaarPostRecord {
  /** 主键 */
  ID: number;
  /** 角色ID */
  RoleId: number;
  /** 话题ID */
  TopicId: number;
  /** 问题 */
  Question: string;
  /** 行星 */
  DicePlanet: AstrologyPlanet;
  /** 星座 */
  DiceConstellation: AstrologyConstellation;
  /** 星座宫位 */
  DiceHouse: AstrologyHouse;
  /** 评论数 */
  CommentCount: number;
  /** 创建时间 */
  CreateTime: number;
  /** 更新时间 */
  UpdateTime: number;
  /** 状态 */
  Status: number;
}

export class BazaarPostModel extends BaseModelClass<BazaarPostRecord> {
  private static instance: BazaarPostModel;
  constructor() {
    super("nsh_astrology_bazaar_post");
  }

  static getInstance() {
    if (!BazaarPostModel.instance) {
      BazaarPostModel.instance = new BazaarPostModel();
    }
    return BazaarPostModel.instance;
  }
}
