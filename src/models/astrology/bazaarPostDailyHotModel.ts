import { BaseModelClass } from "../BaseModel2";

export interface BazaarPostDailyHotRecord {
    /** 主键 */
    ID: number;
    /** 日期, 格式: yyyyMMdd */
    DS: string;
    /** 帖子ID */
    PostId: number;
    /** 热度 */
    Hot: number;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
}

export class BazaarPostDailyHotModel extends BaseModelClass<BazaarPostDailyHotRecord> {
    private static instance: BazaarPostDailyHotModel;
    constructor() {
        super("nsh_astrology_post_daily_hot");
    }

    static getInstance() {
        if (!BazaarPostDailyHotModel.instance) {
            BazaarPostDailyHotModel.instance = new BazaarPostDailyHotModel();
        }
        return BazaarPostDailyHotModel.instance;
    }
}