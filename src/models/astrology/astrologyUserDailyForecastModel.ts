import { BaseModelClass } from "../BaseModel2";

export interface AstrologyUserDailyForecastRecord {
    /** 主键 */
    ID: number;
    /** 日期, 格式: yyyyMMdd */
    DS: string;
    /** 用户ID */
    RoleId: number;
    /** 运势分数 */
    FortuneScore: number;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
}

export class AstrologyUserDailyForecastModel extends BaseModelClass<AstrologyUserDailyForecastRecord> {
    private static instance: AstrologyUserDailyForecastModel;
    constructor() {
        super("nsh_astrology_user_daily_forecast");
    }

    static getInstance() {
        if (!AstrologyUserDailyForecastModel.instance) {
            AstrologyUserDailyForecastModel.instance = new AstrologyUserDailyForecastModel();
        }
        return AstrologyUserDailyForecastModel.instance;
    }
}