import { BaseModelClass } from "../BaseModel2";

export interface BazaarCommentRecord {
    /** 主键 */
    ID: number;
    /** 帖子ID */
    PostId: number;
    /** 帖子角色ID */
    PostRoleId: number;
    /** 评论的角色ID */
    RoleId: number;
    /** 评论内容 */
    Text: string;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
    /** 状态 * 0: 正常 * -1: 删除 */
    Status: number;
}

export class BazaarCommentModel extends BaseModelClass<BazaarCommentRecord> {
    private static instance: BazaarCommentModel;
    constructor() {
        super("nsh_astrology_bazaar_comment");
    }

    static getInstance() {
        if (!BazaarCommentModel.instance) {
            BazaarCommentModel.instance = new BazaarCommentModel();
        }
        return BazaarCommentModel.instance;
    }
}