import { BaseModelClass } from "../BaseModel2";

export interface AstrologyUserRecord {
    /** 角色ID */
    RoleId: number;
    /** 性别 */
    Gender: number;
    /** 出生时间戳 */
    BirthTime: number;
    /** 出生地 */
    BirthPlace: string;
    /** 星巫等级 */
    AstroLevel: number;
    /** 星巫类型 */
    AstroType: number;
    /** 当前地 */
    CurrentPlace: string;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
    /** 解惑评分总和 */
    RatingSum: number;
    /** 解惑评分次数 */
    RatingCount: number;
    /** 解惑总次数 */
    CommentCount: number;
    /** 最后一次解惑时间戳 */
    LastCommentTime: number;
}

export class AstrologyUserModel extends BaseModelClass<AstrologyUserRecord> {
    private static instance: AstrologyUserModel;
    constructor() {
        super("nsh_astrology_user");
    }

    static getInstance() {
        if (!AstrologyUserModel.instance) {
            AstrologyUserModel.instance = new AstrologyUserModel();
        }
        return AstrologyUserModel.instance;
    }
}