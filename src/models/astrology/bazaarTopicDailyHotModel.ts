import { BaseModelClass } from "../BaseModel2";

export interface BazaarTopicDailyHotRecord {
    /** 主键 */
    ID: number;
    /** 日期, 格式: yyyyMMdd */
    DS: string;
    /** 话题ID */
    TopicId: number;
    /** 热度 */
    Hot: number;
    /** 创建时间 */
    CreateTime: number;
    /** 更新时间 */
    UpdateTime: number;
}

export class BazaarTopicDailyHotModel extends BaseModelClass<BazaarTopicDailyHotRecord> {
    private static instance: BazaarTopicDailyHotModel;
    constructor() {
        super("nsh_astrology_post_topic_daily_hot");
    }

    static getInstance() {
        if (!BazaarTopicDailyHotModel.instance) {
            BazaarTopicDailyHotModel.instance = new BazaarTopicDailyHotModel();
        }
        return BazaarTopicDailyHotModel.instance;
    }
}