import { BaseModelClass } from "../BaseModel2";

export interface AstrologyRoleInfoRecord {
    /** 角色ID */
    RoleId: number;
    /** 角色名称 */
    RoleName: string;
    /** 性别 */
    Gender: number;
    /** 子性别 */
    SubGender: number;
    /** 职业 */
    JobId: number;
    /** 头像 */
    HeadPaintId: number;
    /** 立绘 */
    BodyPaintId: number;
}

export class AstrologyRoleInfoModel extends BaseModelClass<AstrologyRoleInfoRecord> {
    private static instance: AstrologyRoleInfoModel;
    constructor() {
        super("nsh_roleinfo", "RoleId");
    }

    static getInstance() {
        if (!AstrologyRoleInfoModel.instance) {
            AstrologyRoleInfoModel.instance = new AstrologyRoleInfoModel();
        }
        return AstrologyRoleInfoModel.instance;
    }
}