-- nodejs_yxb.nsh_astrology_bazaar_comment definition

CREATE TABLE `nsh_astrology_bazaar_comment` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PostId` bigint(20) NOT NULL COMMENT '帖子ID',
  `PostRoleId` bigint(20) NOT NULL COMMENT '帖子角色ID',
  `RoleId` bigint(20) NOT NULL COMMENT '评论的角色ID',
  `Text` text NOT NULL COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0: 正常, -1: 删除',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_uniq_post_role` (`PostId`,`RoleId`),
  KEY `idx_role_id` (`RoleId`),
  KEY `idx_post_status_id` (`PostId`,`Status`,`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫-问惑-评论';


-- nodejs_yxb.nsh_astrology_bazaar_post definition

CREATE TABLE `nsh_astrology_bazaar_post` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `TopicId` int(11) NOT NULL COMMENT '话题ID',
  `Question` text NOT NULL COMMENT '问题',
  `DicePlanet` varchar(32) NOT NULL COMMENT '行星',
  `DiceConstellation` varchar(32) NOT NULL COMMENT '星座',
  `DiceHouse` tinyint(4) NOT NULL COMMENT '星座宫位',
  `CommentCount` int(11) NOT NULL DEFAULT '0' COMMENT '评论数',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0: 正常, -1: 删除',
  PRIMARY KEY (`ID`),
  KEY `idx_status_comment_count` (`Status`,`CommentCount`),
  KEY `idx_role_status_id` (`RoleId`,`Status`,`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫-问惑-帖子';


-- nodejs_yxb.nsh_astrology_bazaar_rating definition

CREATE TABLE `nsh_astrology_bazaar_rating` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `PostId` bigint(20) NOT NULL COMMENT '问题ID',
  `CommentId` bigint(20) NOT NULL COMMENT '评论ID',
  `ToRoleId` bigint(20) NOT NULL COMMENT '被评分的角色ID',
  `FromRoleId` bigint(20) NOT NULL COMMENT '评分的角色ID',
  `Star` tinyint(4) NOT NULL COMMENT '解惑评分',
  `Text` text NOT NULL COMMENT '评论内容',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  `Status` int(11) NOT NULL DEFAULT '0' COMMENT '0: 正常, -1: 删除',
  PRIMARY KEY (`ID`),
  KEY `idx_comment_id` (`CommentId`),
  KEY `idx_post_id` (`PostId`),
  KEY `idx_to_role_status` (`ToRoleId`,`Status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫-问惑-评价';


-- nodejs_yxb.nsh_astrology_post_daily_hot definition

CREATE TABLE `nsh_astrology_post_daily_hot` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
  `PostId` bigint(20) NOT NULL COMMENT '帖子ID',
  `Hot` int(11) NOT NULL COMMENT '热度',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_post_id_ds` (`PostId`,`DS`),
  KEY `idx_ds_hot_id` (`DS`,`Hot`,`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星座帖子每日热门表';


-- nodejs_yxb.nsh_astrology_post_topic definition

CREATE TABLE `nsh_astrology_post_topic` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `Text` varchar(255) NOT NULL COMMENT '话题文本',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星巫话题表';


-- nodejs_yxb.nsh_astrology_post_topic_daily_hot definition

CREATE TABLE `nsh_astrology_post_topic_daily_hot` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
  `TopicId` bigint(20) NOT NULL COMMENT '话题ID',
  `Hot` int(11) NOT NULL COMMENT '热度',
  `CreateTime` bigint(20) NOT NULL COMMENT '建时间',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_topic_id_ds` (`TopicId`,`DS`),
  KEY `idx_ds_hot_update` (`DS`,`Hot`,`UpdateTime`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='星座帖子每日热门表';


-- nodejs_yxb.nsh_astrology_user definition

CREATE TABLE `nsh_astrology_user` (
  `RoleId` bigint(20) NOT NULL COMMENT '角色ID',
  `Gender` tinyint(1) NOT NULL COMMENT '性别 0 男 1 女',
  `BirthTime` bigint(20) NOT NULL COMMENT '出生时间戳',
  `BirthPlace` varchar(255) NOT NULL COMMENT '出生地',
  `CurrentPlace` varchar(255) NOT NULL COMMENT '当前地',
  `AstroLevel` int(10) NOT NULL DEFAULT '0' COMMENT '星巫等级',
  `AstroType` int(10) NOT NULL DEFAULT '0' COMMENT '星巫类型',
  `CreateTime` bigint(20) NOT NULL COMMENT '创建时间戳',
  `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间戳',
  `RatingSum` bigint(20) NOT NULL DEFAULT '0' COMMENT '解惑评分总和',
  `RatingCount` int(10) NOT NULL DEFAULT '0' COMMENT '解惑评分次数',
  `CommentCount` int(10) NOT NULL DEFAULT '0' COMMENT '解惑总次数',
  `LastCommentTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '最后一次解惑时间戳，用于相同解惑次数时的排序',
  PRIMARY KEY (`RoleId`),
  KEY `idx_comment_count_time` (`CommentCount`,`LastCommentTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='星巫用户资料';


-- nodejs_yxb.nsh_astrology_user_daily_forecast definition

CREATE TABLE `nsh_astrology_user_daily_forecast` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
  `RoleId` bigint(20) NOT NULL COMMENT '用户ID',
  `FortuneScore` int(11) NOT NULL COMMENT '运势分数',
  `CreateTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `UpdateTime` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uk_role_id_ds` (`RoleId`,`DS`),
  KEY `idx_ds_fortune` (`DS`,`FortuneScore`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='用户每日运势表';