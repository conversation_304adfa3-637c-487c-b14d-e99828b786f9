import * as config from "../common/config";
import * as HotMomentsCache from "../services/HotMomentsCache";
import { CronPattern, TaskManager } from "./taskManager";
import { processReadyTask, triggerDelayTask } from "./delayTasks";
import { updatePyqMomentsHot } from "./recalMomentHotValue";
import { autoMigrateWeekRenQi } from "../services/weekRenQiMigrator";
import { refreshRecommendCache } from "../services/follow";
import { ServerList } from "../services/nshServerList";
import { triggerPublicMoment } from "../services/official_accounts/moments";
import { autoCorrectHonorBindData, fixUnTransfer } from "../services/playerTransfer";
import { FcmWlcCfg } from "../common/config";
import { consumeBehaviorQueue, fcmStatAlarmCheck } from "../services/fcm";
import { cronLogger } from "../logger";
import * as MarriageTransferService from "../services/marriageTransfer";
import { fixPhotoWallDuplicate } from "../components/marriagePhotoWall/service";
import { refreshHoliday } from "../components/fcm/holidayModel";
import { HolidayCNCacheClass } from "../components/fcm/holiday";
import { healthAppListen } from "../healthApp";
import { AstrologyCommentRankService } from "../services/astrology/astrologyCommentRankService";
import { Context } from "../context";
import { AstrologyWeeklyCommentRankService } from "../services/astrology/astrologyWeeklyCommentRankService";

let TaskSwitch = config.CronTaskSwitch;
healthAppListen(config.cronPort.pyqCrons);

let manager = TaskManager.create("pyqCrons", TaskSwitch);

// 热度值随时间衰减
manager.add({
  name: "RefreshMomentHotValue",
  cronTime: "35 */2 * * *",
  execute: async function () {
    return updatePyqMomentsHot();
  },
  checkUrl: config.cronTaskCheckUrl.RefreshMomentHotValue
});

// 重建热门列表缓存
manager.add({
  name: "RebuildHotMomentCache",
  cronTime: "*/10 * * * *", // At every 10th minute
  execute: async function () {
    return HotMomentsCache.refreshAll();
  },
  checkUrl: config.cronTaskCheckUrl.RebuildHotMomentCache
});

manager.add({
  name: "PublicOfficialMoment",
  cronTime: CronPattern.EVERY_MINUTE,
  execute: async function () {
    return triggerPublicMoment();
  },
});

manager.add({
  name: "RefreshServerList",
  cronTime: CronPattern.EVERY_THIRTY_MINUTE,
  execute: async function () {
    const serverList = await ServerList.refresh();
    cronLogger.info({ serverList }, "RefreshServerListFinish")
  },
  checkUrl: config.cronTaskCheckUrl.RefreshServerList
});

manager.add({
  name: "WeekRenQiMigrator",
  cronTime: CronPattern.EVERY_TEN_MINUTE,
  execute: async function () {
    return autoMigrateWeekRenQi();
  },
});

manager.add({
  name: "RefreshRecommendList",
  cronTime: CronPattern.EVERY_MINUTE,
  execute: async function () {
    return refreshRecommendCache();
  },
  checkUrl: config.cronTaskCheckUrl.RefreshRecommendList
});

manager.add({
  name: "TriggerDelayTask",
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    return triggerDelayTask();
  },
});

manager.add({
  name: "ProcessReadyTask",
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function () {
    return processReadyTask();
  },
});

manager.add({
  name: "FixUnTransfer",
  cronTime: CronPattern.EVERY_TEN_MINUTE,
  execute: async function () {
    return fixUnTransfer();
  },
});


manager.add({
  name: "FcmStatAlarmCheck",
  cronTime: '59 23 * * *',
  execute: async function () {
    return fcmStatAlarmCheck();
  },
});

manager.add({
  name: "AutoCorrectHonorBindData",
  cronTime: '30 4 * * *',
  execute: async function () {
    return autoCorrectHonorBindData();
  }
})


manager.add({
  // 防沉迷数据上传, 1s处罚10次上传，每次尝试去最大获取128条合并在一次上传
  name: 'BehaviorCollectionUpload',
  cronTime: CronPattern.EVERY_SECOND,
  execute: async function name() {
    for (let i = 0; i < FcmWlcCfg.behaviorCollectQPS; i++) {
      await consumeBehaviorQueue(FcmWlcCfg.maxBatchBehaviorUpload)
    }
  }
})


manager.add({
  name: "FixMarriageUnTransfer",
  cronTime: CronPattern.EVERY_TEN_MINUTE,
  execute: async function () {
    return MarriageTransferService.fixUnTransfer();
  },
});


manager.add({
  name: "FixMarriagePhotoWallDuplicate",
  cronTime: CronPattern.EVERY_TEN_MINUTE,
  execute: async function () {
    return fixPhotoWallDuplicate()
  },
  runOnInit: true
});

manager.add({
  name: "HolidayCNDataSync",
  cronTime: config.holidayCnCfg.cronPattern,
  execute: async function () {
    const month = new Date().getMonth();
    const year = new Date().getFullYear();
    if (month >= 10) {
      // 国务院在10月以后可能公布下一年假期, 所以10月以后可以刷新下一年
      await refreshHoliday(year + 1);
    } else {
      await refreshHoliday(year);
    }
    await HolidayCNCacheClass.getInstance().refresh(year);
  },
  runOnInit: true,
});

manager.add({
  name: "AstrologyCommentRankRefresh",
  cronTime: config.astrologyCfg.commentRankRefreshCronTime,
  execute: async function () {
    const ctx = Context.emptyContext()
    Promise.all([
      AstrologyCommentRankService.getInstance().warmupCache(ctx),
      AstrologyWeeklyCommentRankService.getInstance().warmupCache(ctx),
    ])
  },
});

if (require.main === module) {
  manager.runAll();
} else {
  exports.manager = manager;
}