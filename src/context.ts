import type { Request } from "restify";
import { getIp } from "./common/util";

type ExtendedRequest = Request & {
    /** 用于存储梦岛上下文数据 */
    __md_container?: Map<string, any>
}

export class Context {
    private values: Map<string, any>;
    private request: ExtendedRequest;

    constructor() {
        this.values = new Map();
    }

    set(key: string, value: any): void {
        if (this.req) {
            if (!this.req.__md_container) {
                this.req.__md_container = new Map();
            }
            this.req.__md_container.set(key, value);
        } else {
            this.values.set(key, value);
        }
    }

    setReq(req: Request): void {
        this.request = req;
    }

    get<T>(key: string): T | undefined {
        if (this.req) {
            if (!this.req.__md_container) {
                return undefined;
            }
            return this.req.__md_container.get(key);
        } else {
            return this.values.get(key);
        }
    }

    get req(): ExtendedRequest {
        return this.request;
    }

    toJSON() {
        if (this.request) {
            const info: Record<string, any> = {
                requestId: this.request.id(),
                url: this.request.url,
            };
            return info
        } else {
            return null
        }
    }

    getIp(): string {
        const req = this.req
        if (req && req.headers) {
            return getIp(req)
        }
        return ""
    }

    static createWithRequest(req: Request): Context {
        const ctx = new Context()
        ctx.setReq(req)
        return ctx
    }

    static emptyContext(): Context {
        const ctx = new Context()
        return ctx
    }
}