/* eslint-disable prefer-const */
import { BunyanPoPoAlert } from "./bunyanPoPoAlert";

export let testCfg = {
  req_log: false,
  res_log: false,
  test_env: false,
  debug: false,
  show_doc: false,
  skip_audit: false,
  skip_level: false,
  skip_ip_auth: false,
  skip_skey_check: false,
  cheat_skey_enable: false,
  skip_token: false,
  db_debug: false,
  db_debug_show_result: false,
  redis_debug: false,
  request_debug: false,
  skip_neDun_verify: false,
  skip_openId_auth: false,
  no_forward_proxy: false,
  source_map_support: false,
  skip_photo_wall_audit: false,
};

export let db = {
  connectionLimit: 20,
  host: "",
  user: "",
  port: 3306,
  password: "",
  database: "",
  charset: "utf8mb4",
};

export let fakeAdultDB = {
  connectionLimit: 2,
  host: "",
  user: "media",
  port: 3306,
  password: "mR6O30qZm1TL",
  database: "fakeAdult",
  charset: "utf8",
};

export let slaveDb = db;

export let testDb = {
  connectionLimit: 20,
  port: 3306,
  host: "",
  user: "",
  password: "",
  database: "",
};

export let dbTraffic = {
  enable: false,
  select: { master: 1, slave: 1 },
  debug: false,
};

export let redis = {
  hosts: [""],
  host: "",
  port: 6379,
  password: "",
  db: 0,
  no_ready_check: true,
  prefix: "nsh_md:",
};

export let redisSkeyCfg = {
  hosts: [""],
  host: "",
  port: 6379,
  password: "",
  db: 0,
  no_ready_check: true,
  prefix: "nsh:",
};

export let geoRedis = {
  hosts: [""],
  host: "",
  port: 6379,
  db: 0,
  password: "",
  no_ready_check: true,
  prefix: "nsh_md:",
};

export let log = {
  dir: "",
  level: "info",
  prefix: "nsh_md",
  printInConsole: false,
  env: "prod",
  schema: "com.netease.leihuo.ccc.base.model.tables.v1.NshMdLog",
  yunyingdir: "",
};

export let server = {
  port: "4001",
  apiPrefix: "/nsh/md",
  deployEnv: "original" as "original" | "huaijiu",
};

export let JWT_TOKEN_SECRET = "jwt_token_secret";
export let ONE_DAY_SECONDS = 24 * 3600;

export let NOS_CFG = {
  buckets: {
    nsh: { name: "hi-163-nsh", region: "JD" },
  },
  accessKey: "",
  secretKey: "",
};

export let AUTH_TOKEN_SALT = "8GthrWun7J";

export let MomentWeekRenqi = {
  maxAddDaily: 100,
  like: 1,
  comment: 1,
  forward: 1,
};

export let MessageBoardWeekRenqi = {
  maxAddDaily: 300,
  signIn: 1,
};

export let yinyunLog = {
  tag: "nsh_md",
  level: "info",
  facility: "local0",
  host: "127.0.0.1",
  port: "514",
  sendByAddon: true,
  printInConsole: false,
};

export let mdCfg = {
  openMinLevel: 40,
};

export let WeekRankCfg = {
  mockTime: false,
  mockDate: "",
};

export let MessageBoardGift = {
  receiveLimitPerDay: 10,
};

export let TopicAdminUrs = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

export let TopicAdminOpenIds = [
  "hzwangzhenhua",
  "hzyangchenlu1",
  "yufeifan01",
  "hzlimin1",
  "liqianyun",
  "gaomengdi",
  "huangwenzhe",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

export let IdentityAdminOpenIds = [
  "hzwangzhenhua",
  "hzyangchenlu1",
  "yufeifan01",
  "hzlimin1",
  "liqianyun",
  "gaomengdi",
  "huangwenzhe",
  "zhuyue01",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

export let Features = {
  official_accounts: true,
  activity_topic: true,
  identity_tag: true,
  user_avatar: true,
  fake_audit: true,
  guild_polemic: true,
  game_club: true,
  lianghao: false,
  fcmBlackAuth: false,
  gm: true,
  gmWeb: false,
  transfer: false,
  wishlist: true,
  clubMatchRank: true,
  copyMoments: true,
  fcm: true,
  guild_expression: true,
  server_annal: true,
  note_mail: true,
  guild_photo_wall: true,
  admin_moment_pick: true,
  fcm_ban_account: true,
  player_annal: true,
  cloud_game_duration: true,
  // 提供给活动业务的接口
  activity_api: true,
  parentControl: true,
  expansion_trial_scene: true,
  ngc_score_check: true,
  enableMetric: true,
  multi_garden_photo_wall: true,
  marriageInfo: true,
  marriagePhotoWall: true,
  activityTakePhoto: true,
  garden: false,

  /** 势力大事记*/
  forceEvent: true,
  /**头像半身像上传下载维护功能 */
  bustPhoto: true,
  /** 伤害统计记录功能 */
  damageStat: true,
  /**技能组合存储 */
  skillCombo: true,
  /** 庄园特定玩法图片存储 */
  gardenPhotoUnit: true,
  /** 头像立绘功能 */
  appearancePaint: true,
  /** 时代广场图片 */
  timesSquarePhoto: true,
  /** 自定义表情包 */
  meme: true,
  /** kafka 消息队列 */
  kafka: true,
  /** 服务器同步比赛结果接口 */
  serverCompetitionComponent: true,
  /** 星巫 */
  astrology: true,
  /** 开放接口 */
  open: true,
};

export let officialAccount = {
  skipUrsCheck: false,
};

export let HotMoment = {
  serverLimit: 50,
  allServerLimit: 200,
  maxTopDay: 3, // 最多置顶三天
  maxHotFactor: 2,
  minHotFactor: 0,
};

export let weekStat = {
  isMockDate: false,
  mockDate: "",
};

export let WEEK_RENQI_REWARD_CONFIG = {
  isMockDate: false,
  mockDate: "",
  beginDate: "2019-07-13",
};

export let RECOMMENDED_FOLLOW_CONFIG = {
  size: 50,
};

export let RED_DOT_CFG = {
  maxViewDurationDay: 3,
  newMomentExpireSeconds: 5 * 60, // 新动态计数缓存失效时间
  followIdExpireSeconds: 5 * 60, // 好友列表计数缓存失效时间
  redDotExpireSeconds: 90, // 红点总缓存失效时间
};

export let CronTaskSwitch = {
  RefreshMomentHotValue: true,
  RebuildHotMomentCache: true,
  PublicOfficialMoment: true,
  RefreshServerList: true,
  WeekRenQiMigrator: true,
  RefreshRecommendList: true,
  TriggerDelayTask: true,
  ProcessReadyTask: true,
  FixUnTransfer: true,
  BehaviorCollectionUpload: true,
  FcmStatAlarmCheck: true,
  AutoCorrectHonorBindData: true,
  FixMarriageUnTransfer: true,
  FixMarriagePhotoWallDuplicate: true,
  PlayerHonorCacheWarmUp: true,
  HolidayCNDataSync: true,
  /** 表情包图片迁移 */
  MemeDataMigrate: true,
};

export let activityServerIps = [
  "************",
  "*************",
  "************",
  "***********",
  "**************",
  "*************",
  "************",
  "*************",
  "**************",
  "***********",
  "*************",
  "************",
  "************",
  "************",
  "**************",
  "*************",
  "**************",
  "************",
  "*************",
  "*************",
  "************",
  "**************",
  "**************",
];

export let corsOrigins = [/^https?:\/\/.*\.163\.com(:\d+)?/, /^https?:\/\/.*\.netease\.com(:\d+)?/];

export let fakeAuditCfg = {
  auditTime: 5, // 5 seconds
  passRate: 1, // 100% 会通过
  returnPicUrl: "",
};

export let guildPolemicCfg = {
  tokenExpire: 5 * 60 * 1000, // 5分钟
  skipPubToken: false,
  hotSize: 200,
  hotExpireDays: 30,
  loseHotSeconds: 14400,
  hotListExpireSeconds: 10 * 60,
};

export let clubWebCfg = {
  cookieName: "nsh_md_club_web_cookie",
  loginCookieCheck: true,
  recommendQualityCheck: true,
  smsCodeCheck: true,
  cookieCfg: {
    path: "/",
    domain: ".163.com",
    httpOnly: true,
  },
};

export let cookieCfg = {
  path: "/",
  domain: ".163.com",
  httpOnly: true,
};

export let openIdCfg = {
  mode: "easy",
  secret: "x2g&^JtBd@l2`1",
  infoKey: "common_auth_corp_info",
  tokenKey: "common_auth_corp_token",
  cookieCfg: {
    path: "/",
    domain: ".netease.com",
    httpOnly: true,
    sameSite: "none",
    secure: true,
  },
};

export const maxExportTopicMomentSize = 10000;

export let transferSignUpCfg = {
  syncQueueEnable: true,
  syncQueueKey: "syncQueueKey",
  syncPerTime: 200,
  transferMark: "2020-08-19",
};

export let wishListCfg = {
  maxPageSize: 200,
  maxWishSize: 50,
};

export let gmCfg = {
  superAdmins: ["hzwangzhenhua"],
};

export let honorPlayerCfg = {
  newSheetNames: ["跨服比武大会11", "跨服明星邀请赛5"],
};

export let copyMomentCfg = {
  batchSize: 50,
  delayTime: 50, //50ms
};

export let ClubHonorCfg = {
  gplsSeason: 2,
  kfmxyqsSeason: 4,
  nhzfSeason: 1,
};

export let FcmSecureCfg = {
  appId: "76d4114af4b0409d98aad6164cd74758",
  bizId: "2201003814",
  secretKey: "",
};

export let AES_URS_GAME_SECRETKEY = "83Gi&UA1v^y!u%oz"; // urs 检测游戏 密钥

export const FcmWlcCfg = {
  enable: true, // 中宣开启
  collectEnable: false, // 中宣上报行为开启
  authCheckTimeout: 2000,
  /** ai字段固定长度 */
  aiFieldLength: 32,
  behaviorQueueName: "fcm_login_out",
  maxBatchBehaviorUpload: 128,
  // 接口限流：10 QPS（超出后会被限流1分钟）
  behaviorCollectQPS: 10,
  /** 查询pi任务间隔 24小时 */
  piQueryStepInterval: 24 * 3600 * 1000,
};

export let GAME_IPS = [
  "*************",
  "*************", // 数据组访问fcm接口ip
  "*************",
  "*************",
  "**************",

  // 天谕接入fcm需要配置白名单
  "***********",
  "***********",
];

export let FCM_WLC_HTTPS_HOST = "https://api.wlc.nppa.gov.cn";
export let FCM_WLC_HTTP_HOST = "http://api2.wlc.nppa.gov.cn";
export let URS_API_HOST = "http://reg.163.com";

export let GmMatrixCfg = {
  URS_FAKE_AUTH_LOGIN_CHANNEL: "netease", //https://docs.matrix.netease.com/octopus_doc/cmd_aas/black_adult_as_minor.html 默认login_channel
  HOST: "https://gmtest.matrix.netease.com", // 测试环境  https://gm.matrix.netease.com 正式环境
};

export const delayTaskCfg = {
  name: "share_delay_task",
};

export const requestCfg = {
  timeout: 5000, // 5s
};

export let CBGGuildActivityCfg = {
  apiHost: "https://nshssl.hi.163.com/nsh/cbg/guild", //逆水寒藏宝阁活动正式接口
  transferKey: "m7*Qx#a1YQm06kT#",
};

export const noteMailCfg = {
  redDot: false,
};

export const fcmCfg = {
  mobileUrsMock: {
    enable: false,
    mockRet: {
      result: "201",
      msg: "",
      realnameVerify: "1",
      realnameSet: true,
      isAdult: true,
      pi: "pi_test",
    },
  },
  mailUrsMock: {
    enable: false,
    mockRet:
      "201\nrealname_flag=1&realname_status=127&id=120104200301010398&reg_time=1615963314&update_time=1615963314",
  },
  mockQueryRealNameAndAntiIndulgenceText: {
    enable: false,
    data: null,
  },
};

//#region  和出口代理ip相关的放这里
export let FORWARD_PROXY_HOST = "**************"; // 丹炉配置到原来机器的代理

export let auditCfg = {
  product: "nsh",
  sendPicApi: `http://${FORWARD_PROXY_HOST}:10011/send_pic_v2`,
  batchSendSize: 50,
  ipWhiteList: ["**************", "************", "************", "************", "************", "**************"],
  auditEnv: "proc",
  auditEnvDBMap: {
    proc: "db",
    rc: "rcDb",
  },
};

export let ursCookie = {
  checkByLocal: false,
  hosts: [`http://${FORWARD_PROXY_HOST}:10018/validate`, `http://${FORWARD_PROXY_HOST}:10019/validate`],
  productId: "f77072fb1b1a4664856363589c4127af",
};

export let GameItem = {
  qps: 10,
  appSecret: "SIHvqYxm5a",
  url: `http://${FORWARD_PROXY_HOST}:10037/nsh_reward_http_service/claim_rewards`,
};

export let CHATBOTCFG = {
  jingling: {
    url: "http://nsh.chatbot.nie.163.com/cgi-bin/bot.cgi",
    default_qs: {
      user: "nshgw",
      format: "json",
      encode: "utf8",
    },
  },
  fuxi: {
    url: `http://${FORWARD_PROXY_HOST}:10047/virtualhuman`,
  },
  textCheckLevel: 1, // maybe 1    rejected 2
};
//#endregion

export let OAMExportLimit = {
  comment: 2000,
  forward: 500,
  like: 30000,
};

export let parentControlCfg = {
  gameId: "d30",
  secret: "",
  redirectUri: "https://n.163.com/",
  devMode: false,
  apiTimeout: 2000,
};

export let ngcCheckCfg = {
  ngcBulkRegisterCheck: "",
};

export let smartMemorizeCfg = {
  disableCache: false,
};

export let dbSlowLogCfg = {
  enable: true,
  threshold: 1000,
};

export let fatalAlarmCfg = {
  interval: 10 * 60 * 1000,
  ignoreRegArr: [],
};

export let alarmPaoPao3Cfg = {
  url: "http://proxy.kong.svc/leihuo_web_gw_322/redmine/popomsgccc.php", //文档 https://confluence.leihuo.netease.com/pages/viewpage.action?pageId=130806194
  alarmOpenId: 1599911,
};

export let playerHonorCfg = {
  pageSize: 500,
  delayInterval: 10,
};

export let ActivityTakePhotoCfg = {
  locationNum: 12,
  locationImageNum: 6,
  locationDefaultImg: {
    1: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/1.png",
    2: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/2.png",
    3: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/3.png",
    4: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/4.png",
    5: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/5.png",
    6: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/6.png",
    7: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/7.png",
    8: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/8.png",
    9: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/9.png",
    10: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/10.png",
    11: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/11.png",
    12: "https://hi-163-nsh.nos-jd.163yun.com/nsh/activity_take_photo/default/12.png",
  },
};

/** 数据中心查询接口配置 */
export let dcCfg = {
  getPlayerInfoApi: "http://nsh-datacenter.leihuo.netease.com:5000",
  listUrsRoleInfosApi: "http://nsh-datacenter.leihuo.netease.com:5001",
};

/** 日志报警渠道, 会推送到报警群 */
export let bunyanPoPoAlertCfg = {
  enable: true,
  level: "error",
  webhookUrl: "https://lhpp-popo-server.apps-hp.danlu.netease.com/popo/popo/msg/send",
  secretKey: "lOMvNtTgjrT7WJrmXOknYA==",
  timeout: 2000,
  project: "nsh",
  biz: "md",
  env: "release" as BunyanPoPoAlert.Env,
  /** 最小推送间隔, 默认1分钟允许1次 */
  minNotifyInterval: 60,
  atUids: ["<EMAIL>"],
};

/** 势力大事模块 */
export let forceEventCfg = {};

export let neDunTextCheckCfg = {
  enable: true,
  apiUrl: "http://as.dun.163yun.com/v4/text/check",
  version: "v4.2",
  secretId: "",
  secretKey: "",
  businessId: "",
  timeout: 2000,
  /** 审核嫌疑的是否处理成通过*/
  suspicionAsPass: false,
  /** 调用接口出错是是否处理成通过 */
  apiErrorAsPass: true,
};

export let bustPhotoCfg = {
  maxSizePerRoleId: 20,
  // 批量查询的时候最大允许数量
  maxBatchSize: 10,
};

export let gardenPhotoUnitCfg = {
  maxSizePerRoleId: 20,
};

export let damageStatCfg = {
  /**每个副本每个boss保存数据条数 */
  keepRecentSize: 20,
};

export let skillComboCfg = {
  /** 每个玩家套路每个职业最大收藏数量 */
  maxCollectPerPlayerPerCategoryPerJob: 3,
  /** 每个玩家套路最大收藏数量*/
  maxCollectPerPlayer: 100,
  /**删除上传的套路10分钟内不能再次上传， */
  waitSecondsAfterDel: 10 * 60,
};

/** 家长护航相关配置 */
export let parentEscortCfg = {
  /** 是否开启 */
  enable: true,
  /** 计费接口 */
  apiHost: "https://gm.matrix.netease.com",
  /** 计费接口支持类秘钥 */
  secretKey: "",
  timeout: 5000,
  /** 是否记录游玩日志到数据库 */
  playDurationLogToDb: false,
  /** 最小合法游玩时间, 单位ms */
  minimalPlayDuration: 5000,

  /** urs产品号和计费gameId的映射 */
  productGameIdMap: {
    nsh: "d30",
    qn: "d10",
    pm02: "d21",
    ty: "d21",
    d41: "d41",
  },
  /** 在线时长再游戏redis数据保留几天 */
  dailyPlayDurationKeepDays: 3,
  /** 记录在线状态最多保留多少天 */
  onlineStatusExpireDays: 30,
  defaultGameId: "d30",
  /**
   * mock家长守护规则计费接口
   *
  config.parentEscortCfg.mockSuperAssLimit = {
    enable: true,
    data: {
      code: 0,
      data: {
        create_order_limit: 0,
        month_sum_limit: 0,
        online_time_limit: 60,
        holiday_online_time_limit: 0,
        curfew_end_time: "",
        curfew_start_time: "",
        gameId: "all",
        aas_msg: "",
        source: 0,
        expired_time: "",
      },
    },
  };
   *
  */
  mockSuperAssLimit: {
    enable: false,
    data: null,
  },

  kickoff: {
    taskQueueName: "fcm_{{gameId}}_kickoff_queue",
    concurrency: 1,
    /**
     * 测试环境用 ***************:23950
     * 正式环境用
     */
    nshKickApiPrefix: "http://***************:23950",
    /**
     * 测试环境用 *************:8181
     * 正式环境用 *************:8181
     */
    tyKickApiPrefix: `http://${FORWARD_PROXY_HOST}:10255`,
    /** 启用踢人逻辑的游戏id*/
    enableGameIds: ["d21"],
  },
};

/** 中国节假日抓取配置*/
export let holidayCnCfg = {
  scrapeUrls: [
    "https://cdn.jsdelivr.net/gh/NateScarlet/holiday-cn@master/{year}.json",
    "https://raw.githubusercontent.com/NateScarlet/holiday-cn/master/{year}.json",
  ],
  timeout: 15000,
  cronPattern: "0 3 * * *",
};

export let timesSquarePhotoCfg = {
  maxSizePerRoleId: 12,
};

export let memeCfg = {
  // 申请审核的产品号
  product: "D30_Emoji",
  // 发送审核对的地址
  sendPicUrl: "http://niv-api.gameyw.netease.com:8080/send_pic_v2",
  // 每个角色最大允许存储的自定义表情包数量
  maxSizePerRoleId: 50,
  // 默认缓存5分钟
  cacheTime: 5 * 60,

  // 表情包的fp配置
  fpCfg: {
    project: "d30-emoji",
    secretKey: "qCRhuC1YhGzCa3RwLNth8FEWAjYj4pA3",
    devMode: false,
  },
};

// 防护易盾被刷
export let neDunProtectorCfg = {
  rule: {
    ruleName: "half_hour",
    windowSeconds: 30 * 60, // 半小时
    threshold: 5,
  },
  kp: "neDun_check_protector",
  banTimeMs: 24 * 3600 * 1000, // 检测恶意后封禁24小时后
};

export let docCfg = {
  title: "逆水寒梦岛接口文档",
  docUrl: "http://localhost:4001/nsh/md/doc/swagger/swagger.yaml",
  mouthPath: "/docs/scalar",
};

export let cloudGameDurationCfg = {
  /**
   * 元宝充值业务是否通过日志同步, 通过日志同步的方式需要关闭http接口
   * 如果关闭http接口, 则只能通过日志消费方式同步数据
   * 默认不关闭，这里双重消费是安全的
   */
  disableYuanbaoChargeByHttp: false,
  disableYuanbaoChargeByLog: false,
  kafkaCfg: {
    clientId: "",
    brokers: [""],
    groupId: "",
    topic: "ccc_d30_cloud_game_duration",
    partitionsConsumedConcurrently: 3,
    taskConcurrency: 5,
    logLevel: "INFO",
    fromBeginning: false,
    sasl: {
      mechanism: "plain" as const, // scram-sha-256 or scram-sha-512
      username: "",
      password: "",
    },
  },
};

/** 给通用留言模块的配置 */
export let commonMessageCfg = {
  enable: true,
  /** 留言最大长度 */
  maxMessageLenth: 200,
  /** 留言最大数量 */
  maxMessageCount: 100,

  /** 管理员留言搜索最大数量 */
  adminMessageSearchMaxCount: 100000,

  /** 推荐Self留言池大小 */
  reccSelfPoolSize: 100,
  /** 推荐Self选择 */
  reccSelfPickSize: 1,

  /** 推荐留言池大小 */
  reccFakePoolSize: 100,
  /** 推荐留言池大小 */
  reccPlayerPoolSize: 100,
  /** 推荐留言池大小 */
  reccPickFakeSize: 4,
  /** 推荐留言池大小 */
  reccPickTotalSize: 8,
};


export let envSdkApiCfg = {
  host: "http://qnm-envsdk-service-release.apps-hp.danlu.netease.com",
  /** 文本审核时间 */
  timeout: 2000,
};


/**
 * 跨服通信配置, 需要跨服的模块读取数据用这里的配置
 * 跨服是指经典服，怀旧服和黄金服需要共享同一份数据配置
 */
export let crossCommCfg = {
  db: {
    connectionLimit: 20,
    host: "",
    user: "",
    port: 3306,
    password: "",
    database: "",
    charset: "utf8mb4",
  }
}

export let cronPort = {
  pyqCrons: 3000,
}

export let cronTaskCheckUrl = {
  RefreshMomentHotValue: "",
  RebuildHotMomentCache: "",
  RefreshServerList: "",
  RefreshRecommendList: "",
}


/**
 * 星巫功能相关配置
 */
export let astrologyCfg = {
  /** 热门话题刷新时间间隔, 单位:秒 */
  hotTopicRefreshInterval: 2 * 3600,
  /** 热门话题数量 */
  hotTopicSize: 3,
  /** 星盘API接口地址 */
  horoscopeApiEndPoint: "https://links-d30-skill-pro-14406-80.apps-cae.danlu.netease.com/api/skill/horoscope",
  /** 星盘API请求超时时间, 30s */
  horoscopeApiTimeout: 30000,

  /** 计算解惑评分平均值最小角色数 */
  avgRatingMinRoleIdCount: 10,
  /** 解惑评分平均值计算保留小数位数*/
  avgRatingDecimalPlaces: 1,

  /** 问惑最大长度 */
  questionMaxLen: 30,
  /** 解惑最大长度 */
  commentMaxLen: 200,
  /** 评价文本最大长度 */
  ratingTextMaxLen: 30,

  /** 解惑排行榜缓存时间, 单位:分钟 */
  commentRankCacheTime: 30,
  /** 解惑排行榜刷新定时任务, 每10分钟刷新一次 */
  commentRankRefreshCronTime: "*/10 * * * *",
  /** 解惑排行榜大小 */
  commentRankSize: 100,

  /** 周解惑排行榜缓存时间, 单位:分钟 */
  weeklyCommentRankCacheTime: 30,
  /** 周解惑排行榜大小 */
  weeklyCommentRankSize: 100,

}

/** corp管理员web端配置 */
export let corpAuthCfg = {
  mode: "easy",
  secret: "x2g&^JtBd@l2`1",
  infoKey: "common_auth_corp_info",
  tokenKey: "common_auth_corp_token",
  cookieCfg: {
    path: "/",
    domain: ".netease.com",
    httpOnly: true,
    sameSite: "none",
    secure: true,
  },
};
