import { getRedis, ExpireType, SET_OPTION } from "./redis";
import { clazzLogger } from "../logger";

const logger = clazzLogger("common/redisDistributedLock");

export interface DistributedLockOptions {
    /** 锁的超时时间（秒），默认30秒 */
    timeoutSeconds?: number;
    /** 获取锁失败时的重试次数，默认1次 */
    retryCount?: number;
    /** 重试间隔（毫秒），默认100ms */
    retryIntervalMs?: number;
}

export interface LockResult {
    /** 是否成功获取锁 */
    acquired: boolean;
    /** 锁的值，用于释放锁时验证 */
    lockValue?: string;
    /** 错误信息 */
    error?: string;
}

/**
 * Redis分布式锁工具类
 * 
 * 特性：
 * - 使用SET NX EX命令原子性获取锁
 * - 使用Lua脚本原子性释放锁，防止误删
 * - 支持锁超时自动释放，防止死锁
 * - 支持重试机制
 * - 完善的日志记录
 * 
 * 使用示例：
 * ```typescript
 * const lock = new RedisDistributedLock("my_lock_key");
 * const result = await lock.acquire({ timeoutSeconds: 30 });
 * if (result.acquired) {
 *   try {
 *     // 执行需要加锁的业务逻辑
 *   } finally {
 *     await lock.release(result.lockValue!);
 *   }
 * }
 * ```
 */
export class RedisDistributedLock {
    private readonly lockKey: string;

    constructor(lockKey: string) {
        this.lockKey = lockKey;
    }

    /**
     * 获取分布式锁
     */
    async acquire(options: DistributedLockOptions = {}): Promise<LockResult> {
        const {
            timeoutSeconds = 30,
            retryCount = 1,
            retryIntervalMs = 100
        } = options;

        const lockValue = this.generateLockValue();

        for (let attempt = 0; attempt <= retryCount; attempt++) {
            try {
                // 使用SET NX EX命令原子性获取锁
                const result = await getRedis().setAsync(
                    this.lockKey,
                    lockValue,
                    ExpireType.EX,
                    timeoutSeconds,
                    SET_OPTION.NX
                );

                if (result === 'OK') {
                    logger.debug({ 
                        lockKey: this.lockKey, 
                        lockValue, 
                        timeoutSeconds,
                        attempt 
                    }, "distributedLockAcquired");
                    
                    return {
                        acquired: true,
                        lockValue
                    };
                }

                // 获取锁失败，如果还有重试次数则等待后重试
                if (attempt < retryCount) {
                    logger.debug({ 
                        lockKey: this.lockKey, 
                        attempt, 
                        retryCount 
                    }, "distributedLockAcquireRetry");
                    
                    await this.sleep(retryIntervalMs);
                }
            } catch (err) {
                logger.error({ 
                    err, 
                    lockKey: this.lockKey, 
                    lockValue, 
                    attempt 
                }, "distributedLockAcquireError");
                
                return {
                    acquired: false,
                    error: err instanceof Error ? err.message : String(err)
                };
            }
        }

        logger.warn({ 
            lockKey: this.lockKey, 
            retryCount 
        }, "distributedLockAcquireFailed");
        
        return {
            acquired: false,
            error: "Failed to acquire lock after retries"
        };
    }

    /**
     * 释放分布式锁
     * 使用Lua脚本确保只有锁的持有者才能释放锁
     */
    async release(lockValue: string): Promise<boolean> {
        try {
            // Lua脚本：检查锁的值是否匹配，匹配则删除
            const luaScript = `
                if redis.call("GET", KEYS[1]) == ARGV[1] then
                    return redis.call("DEL", KEYS[1])
                else
                    return 0
                end
            `;

            const result = await getRedis().evalAsync(luaScript, 1, this.lockKey, lockValue);

            if (result === 1) {
                logger.debug({ 
                    lockKey: this.lockKey, 
                    lockValue 
                }, "distributedLockReleased");
                return true;
            } else {
                logger.warn({ 
                    lockKey: this.lockKey, 
                    lockValue 
                }, "distributedLockReleaseNotOwner");
                return false;
            }
        } catch (err) {
            logger.error({ 
                err, 
                lockKey: this.lockKey, 
                lockValue 
            }, "distributedLockReleaseError");
            return false;
        }
    }

    /**
     * 尝试获取锁并执行回调函数
     * 自动处理锁的获取和释放
     */
    async withLock<T>(
        callback: () => Promise<T>,
        options: DistributedLockOptions = {}
    ): Promise<T> {
        const lockResult = await this.acquire(options);
        
        if (!lockResult.acquired) {
            throw new Error(`Failed to acquire lock: ${lockResult.error || 'Unknown error'}`);
        }

        try {
            return await callback();
        } finally {
            await this.release(lockResult.lockValue!);
        }
    }

    /**
     * 生成唯一的锁值
     */
    private generateLockValue(): string {
        return `${process.pid}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 睡眠指定毫秒数
     */
    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取锁的键名
     */
    getLockKey(): string {
        return this.lockKey;
    }
}

/**
 * 创建分布式锁实例的便捷函数
 */
export function createDistributedLock(lockKey: string): RedisDistributedLock {
    return new RedisDistributedLock(lockKey);
}
