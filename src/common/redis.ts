import * as redis from "redis";
import * as _ from "lodash";
import * as bluebird from "bluebird";
import * as config from "../common/config";
import { logger } from "../logger";

bluebird.promisifyAll(redis.RedisClient.prototype, {
  promisifier: promisifier,
});

bluebird.promisifyAll(redis.Multi.prototype, {
  promisifier: promisifier,
});

function promisifier(originalMethod) {
  return function promisified() {
    // eslint-disable-next-line prefer-rest-params
    const args = [].slice.call(arguments);
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    return new Promise(function (resolve, reject) {
      args.push(function (err, data) {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
      originalMethod.apply(self, args);
    }).catch((err) => {
      logger.error("RedisCommendError", { method: originalMethod.name, args: args });
      throw err;
    });
  };
}

type IRedisCfg = typeof config.redis;

function getRedisClient(config: IRedisCfg) {
  if (!config.password) {
    delete config.password;
  }
  const client = redis.createClient(config) as ExtendRedisClient;
  return client;
}

class RedisClass {
  private instance: ExtendRedisClient;
  private redisConfig: IRedisCfg;

  constructor(config: IRedisCfg) {
    this.instance = null;
    this.redisConfig = config;
  }

  retry_strategy() {
    this.instance = null;
  }

  getInstance() {
    const redisConfig = _.assign({}, this.redisConfig, { retry_strategy: this.retry_strategy.bind(this) });
    if (this.instance === null) {
      this.instance = getRedisClient(redisConfig);
    }
    return this.instance;
  }
}

class RedisPool {
  private pool: RedisClass[];

  constructor(config: IRedisCfg) {
    this.pool = [];
    for (const h of config.hosts) {
      const cfg: IRedisCfg = Object.assign({}, config, { host: h });
      const rc = new RedisClass(cfg);
      this.pool.push(rc);
    }
  }

  getInstance() {
    const idx = _.random(0, this.pool.length - 1);
    return this.pool[idx].getInstance();
  }
}

const redisPool = new RedisPool(config.redis);

export function getRedis() {
  return redisPool.getInstance();
}

const GLOBAL_REDIS_CLIENT = getRedis();

const GeoRedisPool = new RedisPool(config.geoRedis);

export function getGeoRedis() {
  return GeoRedisPool.getInstance();
}

const SkeyRedisPool = new RedisPool(config.redisSkeyCfg);

export function getRedisForSkey() {
  return SkeyRedisPool.getInstance();
}

GLOBAL_REDIS_CLIENT.on("error", function (err) {
  const str = JSON.stringify({ type: "reborn", err: err });
  logger.error("redis_error", str);
});

if (config.testCfg.redis_debug) {
  const monitorClient = GLOBAL_REDIS_CLIENT.duplicate();
  monitorClient.monitor(function (err) {
    if (err) {
      logger.error(err);
    } else {
      logger.info("RedisClient: Entering monitoring mode.");
    }
  });

  monitorClient.on("monitor", function (time, args) {
    const commend = _.upperCase(args[0]);
    const commendArgs = _.tail(args)
      .filter((r) => {
        return !_.isFunction(r);
      })
      .join(" ");
    logger.info("RedisClient" + ": " + commend + " " + commendArgs);
  });
}

export enum GeoUnit {
  ml = "ml",
  km = "km",
  ft = "ft",
  mi = "mi",
}

export enum ExpireType {
  /** seconds */
  EX = "EX",
  /** mille seconds */
  PX = "PX",
}

type RedisBound = "+inf" | "-inf";

export interface ExtendRedisClient extends redis.RedisClient {
  zaddAsync: (key: string, score: number, member: string | number) => Promise<string>;
  zcardAsync: (key: string) => Promise<number>;
  zremAsync: (key: string, member: string | number) => Promise<string>;
  zremrangebyscoreAsync: (key: string, start: number | RedisBound, end: number | RedisBound) => Promise<string>;
  zrevrangeAsync: (key: string, start: number, end: number, withScore?: string) => Promise<string[]>;
  zrevrangebyscoreAsync: (
    key: string,
    start: number | RedisBound,
    end: number | RedisBound,
    withScore?: string,
    limit?: string,
    offset?: number,
    count?: number,
  ) => Promise<string[]>;
  zincrbyAsync: (key: string, increment: number, member: string) => Promise<string>;
  zscoreAsync: (key: string, member: string) => Promise<string>;
  zrevrankAsync: (key: string, member: string) => Promise<number | null>;
  zcountAsync: (key: string, min: number | RedisBound, max: number | RedisBound) => Promise<number>;
  ttlAsync: (key: string) => Promise<number>;

  expireAsync: (key: string, seconds: number) => Promise<number>;
  expireatAsync: (key: string, time: number) => Promise<number>;
  geoaddAsync: (key: string, lng: number, lat: number, member: string | number) => Promise<string>;
  georadiusAsync: (key: string, lng: number, lat: number, radious: number, unit: GeoUnit, ...rest) => Promise<unknown>;
  hgetAsync: (key: string, k: string) => Promise<string>;
  hdelAsync: (key: string, k: string) => Promise<string>;
  hmsetAsync: (key: string, kvs: object) => Promise<string>;
  hsetAsync: (key: string, k1: string, v1: string | number, k2?: string, v2?: string) => Promise<string>;
  hgetallAsync: (key: string) => Promise<{ [key: string]: string }>;
  hincrbyAsync: (key: string, m: string, incr: number) => Promise<number>;
  hexistsAsync: (key: string, l: string | number) => Promise<IExistResult>;
  hsetnxAsync: (key: string, k: string, v: string | number) => Promise<number>;

  lpopAsync: (key: string) => Promise<string>;
  lpushAsync: (key: string, ele: string) => Promise<string>;
  lrangeAsync: (key: string, start: number, end: number) => Promise<string[]>;
  ltrimAsync: (key: string, start: number, end: number) => Promise<number>;
  rpushAsync: (key: string, ele: string) => Promise<string>;
  llenAsync: (key: string) => Promise<number>;

  getAsync: (key: string) => Promise<string>;
  setAsync: (
    key: string,
    content: string | number,
    expireType?: ExpireType,
    expire?: number,
    setOption?: SET_OPTION,
  ) => Promise<string>;
  setexAsync: (key: string, seconds: number, value: string) => Promise<string>;
  delAsync: (key: string) => Promise<number>;
  quitAsync: () => Promise<string>;

  incrAsync: (key: string) => Promise<number>;
  incrbyAsync: (key: string, increment: number) => Promise<number>;
  decrAsync: (key: string) => Promise<number>;
  existsAsync: (key: string) => Promise<IExistResult>;

  saddAsync: (key: string, ...members: string[]) => Promise<number>;
  sremAsync: (key: string, member: string) => Promise<number>;
  sismemberAsync: (key: string, member: string) => Promise<IExistResult>;
  zremrangebyrankAsync: (key: string, start: number, stop: number) => Promise<number>;
  flushallAsync: () => Promise<number>;

  // Lua脚本执行相关方法
  evalAsync: (script: string, numKeys: number, ...args: (string | number)[]) => Promise<any>;
  evalshaAsync: (sha: string, numKeys: number, ...args: (string | number)[]) => Promise<any>;
  scriptAsync: (command: string, ...args: string[]) => Promise<any>;
}

export enum IExistResult {
  Exist = 1,
  NotExist = 0,
}

export const enum SET_OPTION {
  NX = "NX", // Only set the key if it does not already exist.
  XX = "XX", // Only set the key if it already exist.
}
