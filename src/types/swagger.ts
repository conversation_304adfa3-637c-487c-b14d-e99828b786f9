/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */


export interface paths {
  "/astrology/bazaar/post/add": {
    /**
     * 星巫-问惑-发布
     * @description 星巫-问惑-发布
     */
    post: operations["bazaarPostAdd"];
  };
  "/astrology/bazaar/post/list": {
    /**
     * 星巫-问惑-公共列表
     * @description 星巫-问惑-公共列表
     */
    get: operations["bazaarPostList"];
  };
  "/astrology/bazaar/post/self_list": {
    /**
     * 星巫-问惑-我的列表
     * @description 星巫-问惑-我的列表
     */
    get: operations["bazaarPostSelfList"];
  };
  "/astrology/bazaar/post/interpret": {
    /**
     * 星巫-问惑-AI占星骰子
     * @description 发起解读，把对应问题和占卜结果发送AI, 并返回解读结果
     * [伏羲提供的上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794)
     */
    get: operations["bazaarPostInterpret"];
  };
  "/astrology/bazaar/comment/add": {
    /**
     * 星巫-问疑解答-新增
     * @description 星巫-问疑解答-新增
     */
    post: operations["bazaarCommentAdd"];
  };
  "/astrology/bazaar/comment/list": {
    /**
     * 星巫-问疑解答-列表
     * @description 星巫-问疑解答-列表
     */
    get: operations["bazaarPostCommentList"];
  };
  "/astrology/bazaar/rating/add": {
    /**
     * 星巫-解惑评分-添加
     * @description 星巫-解惑评分-添加
     */
    post: operations["bazaarRatingAdd"];
  };
  "/astrology/rating/add": {
    /**
     * 星巫-评分-添加 (服务端调用)
     * @description 直接给用户评分, 不走巫师集会逻辑
     */
    post: operations["ratingAdd"];
  };
  "/astrology/bazaar/hot_topic": {
    /**
     * 星巫-热门话题
     * @description 星巫-热门话题
     */
    get: operations["bazaarHotTopic"];
  };
  "/astrology/bazaar/group_divination_report": {
    /**
     * 星巫-群体占卜报告
     * @description 星巫-群体占卜报告
     */
    get: operations["bazaarGroupDivinationReport"];
  };
  "/astrology/bazaar/user/register": {
    /**
     * 星巫-用户-注册 (服务端调用)
     * @description 星巫-用户-注册 (服务端调用)
     */
    post: operations["bazaarUserRegister"];
  };
  "/astrology/bazaar/user/profile": {
    /**
     * 星巫-用户资料-展示
     * @description 星巫-用户资料-展示
     */
    get: operations["bazaarUserProfile"];
  };
  "/astrology/server/bazaar/user/profile": {
    /**
     * 星巫-用户资料-展示 (服务器调用)
     * @description 星巫-用户资料-展示 (服务器调用)
     */
    get: operations["bazaarUserProfileForServer"];
  };
  "/astrology/bazaar/user/profile/update": {
    /**
     * 星巫-用户资料-更新 (服务端调用)
     * @description 更新星巫用户资料，更新字段可选，不传对应字段不更新
     */
    post: operations["bazaarUserProfileUpdate"];
  };
  "/astrology/bazaar/user/rating/receive_list": {
    /**
     * 星巫-用户-解惑收到的评分列表
     * @description 星巫-用户-解惑收到的评分列表
     */
    get: operations["bazaarUserRatingReceiveList"];
  };
  "/astrology/dice_result/interpret": {
    /**
     * 星巫-占星骰子-AI解读
     * @description 不走巫师集会逻辑, 根据问题和骰子结果，直接调用伏羲AI接口
     */
    post: operations["diceResultInterpret"];
  };
  "/astrology/horoscope/planetary_aspects": {
    /**
     * 星巫-星盘-AI今日星象
     * @description 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
     */
    get: operations["horoscopePlanetaryAspects"];
  };
  "/astrology/horoscope/daily_forecast": {
    /**
     * 星巫-星盘-AI今日运势
     * @description 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
     */
    get: operations["horoscopeDailyForecast"];
  };
  "/astrology/comment/rank": {
    /**
     * 星巫-解惑次数排行榜 (服务端调用)
     * @description 获取解惑次数排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
     */
    get: operations["commentRank"];
  };
  "/astrology/comment/rank_weekly": {
    /**
     * 星巫-解惑次数周排行榜 (服务端调用)
     * @description 获取解惑次数周排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
     */
    get: operations["commentRankWeekly"];
  };
  "/common_message/add": {
    /**
     * 通用留言 - 添加留言
     * @description 通用留言 - 添加留言
     */
    post: operations["commonMessageAdd"];
  };
  "/common_message/recc_list": {
    /**
     * 通用留言 - 推荐列表
     * @description 获取推荐的通用留言列表
     */
    get: operations["commonMessageReccList"];
  };
  "/common_message/admin/message/add": {
    /**
     * 后台-通用留言-添加
     * @description 后台-通用留言-添加
     */
    post: operations["commonMessageAdminMessageAdd"];
  };
  "/common_message/admin/message/del": {
    /**
     * 后台-通用留言-删除
     * @description 后台-通用留言-删除
     */
    post: operations["commonMessageAdminMessageDel"];
  };
  "/common_message/admin/message/list": {
    /**
     * 后台-通用留言-列表
     * @description 后台-通用留言-列表
     */
    get: operations["commonMessageAdminMessageList"];
  };
  "/common_message/admin/message/update_status": {
    /**
     * 后台-通用留言-更新显示状态
     * @description 后台-通用留言-更新显示状态
     */
    post: operations["commonMessageAdminMessageUpdateStatus"];
  };
  "/common_message/admin/operator/add": {
    /**
     * 后台-管理员-新增
     * @description 后台-管理员-新增
     */
    post: operations["commonMessageAdminOperatorAdd"];
  };
  "/common_message/admin/operator/del": {
    /**
     * 后台-管理员-删除
     * @description 后台-管理员-删除
     */
    post: operations["commonMessageAdminOperatorDel"];
  };
  "/common_message/admin/operator/list": {
    /**
     * 后台-管理员-列表
     * @description 后台-管理员-列表
     */
    get: operations["commonMessageAdminOperatorList"];
  };
  "/common_message/admin/operator/update": {
    /**
     * 后台-管理员-更新
     * @description 后台-管理员-更新
     */
    post: operations["commonMessageAdminOperatorUpdate"];
  };
  "/common_message/admin/modules/list": {
    /**
     * 后台-模块-列表
     * @description 后台-模块-列表
     */
    get: operations["commonMessageAdminModulesList"];
  };
  "/kafka/cloud_game_duration/on_buy_month_card_log": {
    /**
     * 模拟日志消费-云游戏购买月卡
     * @description 同步云游戏购买月卡的日志，处理消费逻辑
     */
    post: operations["kafkaCloudGameDurationOnBuyMonthCardLog"];
  };
  "/meme/list": {
    /**
     * 自定义表情包列表
     * @description 自定义表情包列表
     */
    get: operations["memeList"];
  };
  "/meme/add": {
    /**
     * 添加到自定义表情
     * @description 添加到自定义表情
     */
    post: operations["memeAdd"];
  };
  "/meme/del": {
    /**
     * 删除自定义表情
     * @description 删除自定义表情
     */
    post: operations["memeDel"];
  };
  "/meme/return_pic": {
    /**
     * 运营审核系统表情包图片审核地址回调接收地址
     * @description 运营审核系统表情包图片审核地址回调接收地址
     */
    post: operations["memeReturnPic"];
  };
  "/audit/send_pic_v2": {
    /**
     * 发送图片到审核系统中
     * @description 发送图片到审核系统中
     */
    post: operations["auditSendPicV2"];
  };
  "/audit/return_pic": {
    /**
     * 运营审核系统图片审核地址回调接收地址
     * @description 运营审核系统图片审核地址回调接收地址
     */
    post: operations["auditReturnPic"];
  };
  "/times_square_photo/list": {
    /** @description 列出玩家时代广场图片 */
    get: operations["timesSquarePhotoList"];
  };
  "/times_square_photo/add": {
    /**
     * 保存或更新玩家时代广场图片
     * @description 同一个index上传url会覆盖， 游戏自己控制允许的数量，通过限制index的最大值
     */
    post: operations["timesSquarePhotoAdd"];
  };
  "/times_square_photo/del": {
    /**
     * 删除玩家时代广场个人图片
     * @description 删除玩家时代广场个人图片
     */
    post: operations["timesSquarePhotoDel"];
  };
  "/appearance_paint/update": {
    /** @description 更新立绘 */
    post: operations["appearancePaintUpdate"];
  };
  "/appearance_paint/get": {
    get: operations["appearancePaintGet"];
  };
  "/skill_combo/add": {
    /**
     * 上传技能组合
     * @description 上传技能组合
     */
    post: operations["skillComboAdd"];
  };
  "/skill_combo/del": {
    /**
     * 删除技能组合
     * @description 删除技能组合
     */
    post: operations["skillComboDel"];
  };
  "/skill_combo/get": {
    /**
     * 获取技能组合
     * @description 获取技能组合
     */
    get: operations["skillComboGet"];
  };
  "/skill_combo/collect": {
    /**
     * 收藏技能组合
     * @description 收藏技能组合
     */
    post: operations["skillComboCollect"];
  };
  "/skill_combo/cancel_collect": {
    /**
     * 取消收藏技能组合
     * @description 取消收藏技能组合
     */
    post: operations["skillComboCancelCollect"];
  };
  "/skill_combo/like": {
    /**
     * 点赞技能组合
     * @description 点赞技能组合
     */
    post: operations["skillComboLike"];
  };
  "/skill_combo/sync": {
    /**
     * 同步技能组合
     * @description 同步技能组合
     */
    post: operations["skillComboSync"];
  };
  "/skill_combo/cancel_like": {
    /**
     * 取消点赞技能组合
     * @description 取消点赞技能组合
     */
    post: operations["skillComboCancelLike"];
  };
  "/skill_combo/update": {
    /**
     * 修改技能组合
     * @description 修改技能组合
     */
    post: operations["skillComboUpdate"];
  };
  "/skill_combo/list": {
    /**
     * 技能组合列表
     * @description 技能组合列表
     */
    get: operations["skillComboList"];
  };
  "/damage_stat/add": {
    /**
     * 新增伤害统计数据
     * @description 新增伤害统计数据
     */
    post: operations["damageStatAdd"];
  };
  "/damage_stat/list": {
    /**
     * 玩家某一副本下某个boss下的伤害统计数据列表
     * @description 1. 列表保留最新20条
     * 2. 计算历史最高秒伤和最高治疗
     */
    get: operations["damageStatList"];
  };
  "/damage_stat/share_info": {
    /**
     * 通过分享id获取某一个伤害统计数据
     * @description 通过分享id获取某一个伤害统计数据
     */
    get: operations["damageStatShareInfo"];
  };
  "/bust_photo/add": {
    /**
     * 半身像上传
     * @description 半身像上传
     */
    post: operations["bustPhotoAdd"];
  };
  "/bust_photo/get": {
    /**
     * 角色半身像获取指定位置
     * @description 角色半身像获取指定位置
     */
    get: operations["bustPhotoGet"];
  };
  "/bust_photo/get_batch": {
    /**
     * 批量获取角色半身像
     * @description 批量获取角色半身像
     */
    get: operations["bustPhotoGetBatch"];
  };
  "/bust_photo/server/get_batch": {
    /**
     * 批量获取角色半身像(服务器调用)
     * @description 批量获取角色半身像(服务器调用)
     */
    get: operations["bustPhotoServerGetBatch"];
  };
  "/bust_photo/list": {
    /**
     * 角色半身像列表
     * @description 角色半身像列表
     */
    get: operations["bustPhotoList"];
  };
  "/garden_photo_unit/add": {
    /**
     * 庄园特点玩法图片上传
     * @description 庄园特点玩法图片上传
     */
    post: operations["gardenPhotoUnitAdd"];
  };
  "/garden_photo_unit/get": {
    /**
     * 庄园特点玩法图片获取
     * @description 庄园特点玩法图片获取
     */
    get: operations["gardenPhotoUnitGet"];
  };
  "/garden_photo_unit/list": {
    /**
     * 庄园特点玩法图片列表
     * @description 庄园特点玩法图片列表
     */
    get: operations["gardenPhotoUnitList"];
  };
  "/force_event/add": {
    /**
     * 新增势力大事 (游戏服务器调用)
     * @description 新增势力大事 (游戏服务器调用)
     */
    post: operations["forceEventAdd"];
  };
  "/force_event/remark": {
    /**
     * 史官评论势力大事 (游戏服务器调用)
     * @description 史官评论势力大事 (游戏服务器调用)
     */
    post: operations["forceEventRemark"];
  };
  "/force_event/list": {
    /**
     * 势力大事列表
     * @description 势力大事列表
     */
    get: operations["forceEventList"];
  };
  "/force_event/del": {
    /**
     * 删除势力大事 (游戏服务器调用)
     * @description 删除势力大事 (游戏服务器调用)
     */
    post: operations["forceEventDel"];
  };
  "/force_event/like": {
    /**
     * 点赞势力大事
     * @description 点赞势力大事
     */
    post: operations["forceEventLike"];
  };
  "/force_event/cancel_like": {
    /**
     * 取消点赞势力大事
     * @description 取消点赞势力大事
     */
    post: operations["forceEventCancelLike"];
  };
  "/health_check": {
    /**
     * 健康检查
     * @description 健康检查
     */
    get: operations["healthCheck"];
  };
  "/marriage_info/update": {
    /**
     * 更新情缘信息 (CallByGameServer)
     * @description 更新情缘信息 (CallByGameServer)
     */
    post: operations["marriageInfoUpdate"];
  };
  "/marriage_info/transfer": {
    /**
     * 通知情缘id转服后变更 (CallByGameServer)
     * @description 通知情缘id转服后变更 (CallByGameServer)
     */
    post: operations["marriageInfoTransfer"];
  };
  "/marriage_info/show": {
    /**
     * 获取情缘信息
     * @description 获取情缘信息
     */
    get: operations["marriageInfoShow"];
  };
  "/official_accounts/moments/export_detail": {
    /**
     * 导出官方号动态下详情(点赞评论转发数据)
     * @description 导出官方号动态下详情(点赞评论转发数据)
     */
    post: operations["officialAccountsMomentsExportDetail"];
  };
  "/gm/meme/list": {
    /**
     * 查看某一个玩家的列表
     * @description 查看某一个玩家的列表
     */
    get: operations["gmMemeList"];
  };
  "/gm/meme/audit": {
    /**
     * 审核玩家的一张表情包图片
     * @description 审核玩家的一张表情包图片
     */
    get: operations["gmMemeAudit"];
  };
  "/gm/cloud_game_duration/expire_daily_duration": {
    /**
     * 将非参数指定的日期的当日时长都清空
     * @description 用来模拟当日时长第二天凌晨会过期的规则
     */
    post: operations["gmCloudGameDurationExpireDailyDuration"];
  };
  "/gm/cloud_game_duration/month_card/merge_server": {
    /**
     * 云游戏月卡时长需要合并
     * @description 云游戏月卡时长需要合并
     */
    post: operations["gmCloudGameDurationMonthCardMergeServer"];
  };
  "/kafka/cloud_game_duration/on_add_yuanbao_log": {
    /**
     * 模拟日志消费-云游戏充值元宝
     * @description 同步云游戏充值元宝的日志，处理消费逻辑
     */
    post: operations["kafkaCloudGameDurationOnAddYuanbaoLog"];
  };
  "/cloud_game_duration/show": {
    /**
     * 展示当前urs账号云游戏时长信息以及月卡信息
     * @description 展示当前urs账号云游戏时长信息以及月卡信息
     */
    get: operations["cloudGameDurationShow"];
  };
  "/cloud_game_duration/month_card/show": {
    /**
     * 展示当前urs账号服务器下月卡信息 (ip白名单授权)
     * @description 展示当前urs账号服务器下月卡信息 (ip白名单授权)
     */
    get: operations["cloudGameDurationMonthCardShow"];
  };
  "/cloud_game_duration/notify_charge_yuanbao": {
    /**
     * 游戏通知充值元宝数量，服务负责折算充值比例
     * @description 游戏通知充值元宝数量，服务负责折算充值比例
     */
    post: operations["cloudGameDurationNotifyChargeYuanbao"];
  };
  "/cloud_game_duration/notify_buy_month_card": {
    /**
     * 游戏通知购买月卡信息
     * @description 游戏通知购买月卡信息
     */
    post: operations["cloudGameDurationNotifyBuyMonthCard"];
  };
  "/cloud_game_duration/receive_daily_award": {
    /**
     * 领取每日登录奖励时长
     * @description 领取每日登录奖励时长
     */
    post: operations["cloudGameDurationReceiveDailyAward"];
  };
  "/cloud_game_duration/incr": {
    /**
     * 新增云游戏时长
     * @description 新增云游戏时长
     */
    post: operations["cloudGameDurationIncr"];
  };
  "/cloud_game_duration/decr": {
    /**
     * 扣除云游戏时长
     * @description 扣除云游戏时长
     */
    post: operations["cloudGameDurationDecr"];
  };
  "/admin/moment_pick/list": {
    /**
     * 查看动态列表
     * @description 查看动态列表
     */
    get: operations["adminMomentPickList"];
  };
  "/admin/moment_pick/update_status": {
    /**
     * 批量更新精选状态
     * @description 批量更新精选状态
     */
    post: operations["adminMomentPickUpdateStatus"];
  };
  "/admin/moment_pick/toggle_top": {
    /**
     * 移动该精选状态到最前
     * @description 移动该精选状态到最前
     */
    post: operations["adminMomentPickToggleTop"];
  };
  "/marriage/photo_wall/list": {
    /**
     * 查看情缘所有照片墙列表
     * @description 查看情缘所有照片墙列表
     */
    get: operations["marriagePhotoWallList"];
  };
  "/marriage/photo_wall/photo/{id}/show": {
    /**
     * 查看照片墙中具体图片
     * @description 查看照片墙中具体图片
     */
    get: operations["marriagePhotoWallPhotoIdShow"];
  };
  "/marriage/photo_wall/photo/{id}/move": {
    /**
     * 移动照片墙中图片的槽位
     * @description 移动照片墙中图片的槽位
     */
    post: operations["marriagePhotoWallPhotoIdMove"];
  };
  "/marriage/photo_wall/handpick_wall/up": {
    /**
     * 情缘当家从精选墙上架图片 (CallByGameServer)
     * @description 情缘当家从精选墙上架图片 (CallByGameServer)
     */
    post: operations["marriagePhotoWallHandpickWallUp"];
  };
  "/marriage/photo_wall/handpick_wall/down": {
    /**
     * 情缘当家从精选墙下架图片(CallByGameServer)
     * @description 情缘当家从精选墙下架图片(CallByGameServer)
     */
    post: operations["marriagePhotoWallHandpickWallDown"];
  };
  "/marriage/photo_wall/photo/{id}/del": {
    /**
     * 删除照片墙的这张图片 (CallByGameServer)
     * @description 删除照片墙的这张图片 (CallByGameServer)
     */
    post: operations["marriagePhotoWallPhotoIdDel"];
  };
  "/marriage/photo_wall/photo/{id}/like": {
    /**
     * 点赞照片墙中具体图片
     * @description 点赞照片墙中具体图片
     */
    post: operations["marriagePhotoWallPhotoIdLike"];
  };
  "/marriage/photo_wall/photo/{id}/cancel_like": {
    /**
     * 取消点赞照片墙中具体图片
     * @description 取消点赞照片墙中具体图片
     */
    post: operations["marriagePhotoWallPhotoIdCancelLike"];
  };
  "/marriage/photo_wall/comment/{id}/like": {
    /**
     * 点赞照片的评论
     * @deprecated
     * @description 点赞照片的评论
     */
    post: operations["marriagePhotoWallCommentIdLike"];
  };
  "/marriage/photo_wall/comment/{id}/cancel_like": {
    /**
     * 取消点赞照片的评论
     * @deprecated
     * @description 取消点赞照片的评论
     */
    post: operations["marriagePhotoWallCommentIdCancelLike"];
  };
  "/marriage/photo_wall/photo/{id}/comment/list": {
    /**
     * 查看照片墙图片的评论列表
     * @description 查看照片墙图片的评论列表
     */
    get: operations["marriagePhotoWallPhotoIdCommentList"];
  };
  "/marriage/photo_wall/photo/{id}/comment/add": {
    /**
     * 添加照片墙图片的评论
     * @description 添加照片墙图片的评论
     */
    post: operations["marriagePhotoWallPhotoIdCommentAdd"];
  };
  "/marriage/photo_wall/photo/{photo_id}/comment/del": {
    /**
     * 删除照片墙图片的评论 (CallByGameServer)
     * @description 删除照片墙图片的评论 (CallByGameServer)
     */
    post: operations["marriagePhotoWallPhotoPhotoIdCommentDel"];
  };
  "/marriage/photo_wall/{id}/photo/add": {
    /**
     * 上传照片墙里的图片
     * @description 上传照片墙里的图片
     */
    post: operations["marriagePhotoWallIdPhotoAdd"];
  };
  "/guild/photo_wall/{id}/show": {
    /**
     * 查看照片墙
     * @description 查看照片墙
     */
    get: operations["guildPhotoWallIdShow"];
  };
  "/guild/photo_wall/{id}/update": {
    /**
     * 更新照片墙信息 (CallByGameServer)
     * @description 更新照片墙信息 (CallByGameServer)
     */
    post: operations["guildPhotoWallIdUpdate"];
  };
  "/guild/photo_wall/add": {
    /**
     * 开启新的照片墙 (CallByGameServer)
     * @description 开启新的照片墙 (CallByGameServer)
     */
    post: operations["guildPhotoWallAdd"];
  };
  "/guild/photo_wall/notifications/list": {
    /**
     * 通知列表
     * @description 通知列表
     */
    get: operations["guildPhotoWallNotificationsList"];
  };
  "/guild/photo_wall/notifications/read": {
    /**
     * 单个通知设为已读
     * @description 单个通知设为已读
     */
    post: operations["guildPhotoWallNotificationsRead"];
  };
  "/guild/photo_wall/notifications/read_all": {
    /**
     * 通知列表全部设为已读
     * @description 通知列表全部设为已读
     */
    post: operations["guildPhotoWallNotificationsReadAll"];
  };
  "/guild/photo_wall/list": {
    /**
     * 查看帮会所有照片墙列表
     * @description 查看帮会所有照片墙列表
     */
    get: operations["guildPhotoWallList"];
  };
  "/guild/photo_wall/photo/{id}/show": {
    /**
     * 查看照片墙中具体图片
     * @description 查看照片墙中具体图片
     */
    get: operations["guildPhotoWallPhotoIdShow"];
  };
  "/guild/photo_wall/photo/{id}/move": {
    /**
     * 移动照片墙中图片的槽位
     * @description 移动照片墙中图片的槽位
     */
    post: operations["guildPhotoWallPhotoIdMove"];
  };
  "/guild/photo_wall/handpick_wall/up": {
    /**
     * 帮会当家从精选墙上架图片 (CallByGameServer)
     * @description 帮会当家从精选墙上架图片 (CallByGameServer)
     */
    post: operations["guildPhotoWallHandpickWallUp"];
  };
  "/guild/photo_wall/handpick_wall/down": {
    /**
     * 帮会当家从精选墙下架图片(CallByGameServer)
     * @description 帮会当家从精选墙下架图片(CallByGameServer)
     */
    post: operations["guildPhotoWallHandpickWallDown"];
  };
  "/guild/photo_wall/photo/{id}/del": {
    /**
     * 删除照片墙的这张图片 (CallByGameServer)
     * @description 删除照片墙的这张图片 (CallByGameServer)
     */
    post: operations["guildPhotoWallPhotoIdDel"];
  };
  "/guild/photo_wall/photo/{id}/like": {
    /**
     * 点赞照片墙中具体图片
     * @description 点赞照片墙中具体图片
     */
    post: operations["guildPhotoWallPhotoIdLike"];
  };
  "/guild/photo_wall/photo/{id}/cancel_like": {
    /**
     * 取消点赞照片墙中具体图片
     * @description 取消点赞照片墙中具体图片
     */
    post: operations["guildPhotoWallPhotoIdCancelLike"];
  };
  "/guild/photo_wall/comment/{id}/like": {
    /**
     * 点赞照片的评论
     * @deprecated
     * @description 点赞照片的评论
     */
    post: operations["guildPhotoWallCommentIdLike"];
  };
  "/guild/photo_wall/comment/{id}/cancel_like": {
    /**
     * 取消点赞照片的评论
     * @deprecated
     * @description 取消点赞照片的评论
     */
    post: operations["guildPhotoWallCommentIdCancelLike"];
  };
  "/guild/photo_wall/photo/{id}/comment/list": {
    /**
     * 查看照片墙图片的评论列表
     * @description 查看照片墙图片的评论列表
     */
    get: operations["guildPhotoWallPhotoIdCommentList"];
  };
  "/guild/photo_wall/photo/{id}/comment/add": {
    /**
     * 添加照片墙图片的评论
     * @description 添加照片墙图片的评论
     */
    post: operations["guildPhotoWallPhotoIdCommentAdd"];
  };
  "/guild/photo_wall/photo/{photo_id}/comment/del": {
    /**
     * 删除照片墙图片的评论 (CallByGameServer)
     * @description 删除照片墙图片的评论 (CallByGameServer)
     */
    post: operations["guildPhotoWallPhotoPhotoIdCommentDel"];
  };
  "/guild/photo_wall/{id}/photo/add": {
    /**
     * 上传照片墙里的图片
     * @description 上传照片墙里的图片
     */
    post: operations["guildPhotoWallIdPhotoAdd"];
  };
  "/marriage/photo_wall/{id}/show": {
    /**
     * 查看照片墙
     * @description 查看照片墙
     */
    get: operations["marriagePhotoWallIdShow"];
  };
  "/marriage/photo_wall/{id}/update": {
    /**
     * 更新照片墙信息 (CallByGameServer)
     * @description 更新照片墙信息 (CallByGameServer)
     */
    post: operations["marriagePhotoWallIdUpdate"];
  };
  "/marriage/photo_wall/add": {
    /**
     * 开启新的照片墙 (CallByGameServer)
     * @description 开启新的照片墙 (CallByGameServer)
     */
    post: operations["marriagePhotoWallAdd"];
  };
  "/marriage/photo_wall/notifications/list": {
    /**
     * 通知列表
     * @description 通知列表
     */
    get: operations["marriagePhotoWallNotificationsList"];
  };
  "/marriage/photo_wall/notifications/read": {
    /**
     * 单个通知设为已读
     * @description 单个通知设为已读
     */
    post: operations["marriagePhotoWallNotificationsRead"];
  };
  "/marriage/photo_wall/notifications/read_all": {
    /**
     * 通知列表全部设为已读
     * @description 通知列表全部设为已读
     */
    post: operations["marriagePhotoWallNotificationsReadAll"];
  };
  "/multi_garden/photo_wall/list": {
    /**
     * 查看联居所有照片墙列表
     * @description 查看联居所有照片墙列表
     */
    get: operations["multiGardenPhotoWallList"];
  };
  "/multi_garden/photo_wall/photo/{id}/show": {
    /**
     * 查看照片墙中具体图片
     * @description 查看照片墙中具体图片
     */
    get: operations["multiGardenPhotoWallPhotoIdShow"];
  };
  "/multi_garden/photo_wall/photo/{id}/move": {
    /**
     * 移动照片墙中图片的槽位
     * @description 移动照片墙中图片的槽位
     */
    post: operations["multiGardenPhotoWallPhotoIdMove"];
  };
  "/multi_garden/photo_wall/handpick_wall/up": {
    /**
     * 联居当家从精选墙上架图片 (CallByGameServer)
     * @description 联居当家从精选墙上架图片 (CallByGameServer)
     */
    post: operations["multiGardenPhotoWallHandpickWallUp"];
  };
  "/multi_garden/photo_wall/handpick_wall/down": {
    /**
     * 帮会当家从精选墙下架图片(CallByGameServer)
     * @description 帮会当家从精选墙下架图片(CallByGameServer)
     */
    post: operations["multiGardenPhotoWallHandpickWallDown"];
  };
  "/multi_garden/photo_wall/photo/{id}/del": {
    /**
     * 删除照片墙的这张图片 (CallByGameServer)
     * @description 删除照片墙的这张图片 (CallByGameServer)
     */
    post: operations["multiGardenPhotoWallPhotoIdDel"];
  };
  "/multi_garden/photo_wall/photo/{id}/like": {
    /**
     * 点赞照片墙中具体图片
     * @description 点赞照片墙中具体图片
     */
    post: operations["multiGardenPhotoWallPhotoIdLike"];
  };
  "/multi_garden/photo_wall/photo/{id}/cancel_like": {
    /**
     * 取消点赞照片墙中具体图片
     * @description 取消点赞照片墙中具体图片
     */
    post: operations["multiGardenPhotoWallPhotoIdCancelLike"];
  };
  "/multi_garden/photo_wall/comment/{id}/like": {
    /**
     * 点赞照片的评论
     * @deprecated
     * @description 点赞照片的评论
     */
    post: operations["multiGardenPhotoWallCommentIdLike"];
  };
  "/multi_garden/photo_wall/comment/{id}/cancel_like": {
    /**
     * 取消点赞照片的评论
     * @deprecated
     * @description 取消点赞照片的评论
     */
    post: operations["multiGardenPhotoWallCommentIdCancelLike"];
  };
  "/multi_garden/photo_wall/photo/{id}/comment/list": {
    /**
     * 查看照片墙图片的评论列表
     * @description 查看照片墙图片的评论列表
     */
    get: operations["multiGardenPhotoWallPhotoIdCommentList"];
  };
  "/multi_garden/photo_wall/photo/{id}/comment/add": {
    /**
     * 添加照片墙图片的评论
     * @description 添加照片墙图片的评论
     */
    post: operations["multiGardenPhotoWallPhotoIdCommentAdd"];
  };
  "/multi_garden/photo_wall/photo/{photo_id}/comment/del": {
    /**
     * 删除照片墙图片的评论 (CallByGameServer)
     * @description 删除照片墙图片的评论 (CallByGameServer)
     */
    post: operations["multiGardenPhotoWallPhotoPhotoIdCommentDel"];
  };
  "/multi_garden/photo_wall/{id}/photo/add": {
    /**
     * 上传照片墙里的图片
     * @description 上传照片墙里的图片
     */
    post: operations["multiGardenPhotoWallIdPhotoAdd"];
  };
  "/multi_garden/photo_wall/{id}/show": {
    /**
     * 查看照片墙
     * @description 查看照片墙
     */
    get: operations["multiGardenPhotoWallIdShow"];
  };
  "/multi_garden/photo_wall/{id}/update": {
    /**
     * 更新照片墙信息 (CallByGameServer)
     * @description 更新照片墙信息 (CallByGameServer)
     */
    post: operations["multiGardenPhotoWallIdUpdate"];
  };
  "/multi_garden/photo_wall/add": {
    /**
     * 开启新的照片墙 (CallByGameServer)
     * @description 开启新的照片墙 (CallByGameServer)
     */
    post: operations["multiGardenPhotoWallAdd"];
  };
  "/multi_garden/photo_wall/notifications/list": {
    /**
     * 通知列表
     * @description 通知列表
     */
    get: operations["multiGardenPhotoWallNotificationsList"];
  };
  "/multi_garden/photo_wall/notifications/read": {
    /**
     * 单个通知设为已读
     * @description 单个通知设为已读
     */
    post: operations["multiGardenPhotoWallNotificationsRead"];
  };
  "/multi_garden/photo_wall/notifications/read_all": {
    /**
     * 通知列表全部设为已读
     * @description 通知列表全部设为已读
     */
    post: operations["multiGardenPhotoWallNotificationsReadAll"];
  };
  "/note_mail/{mail_box_type}/show": {
    /**
     * 查看收件箱便利贴详情
     * @description 查看收件箱便利贴详情
     */
    get: operations["noteMailMailBoxTypeShow"];
  };
  "/note_mail/{mail_box_type}/del": {
    /**
     * 删除便利贴
     * @description 删除便利贴
     */
    post: operations["noteMailMailBoxTypeDel"];
  };
  "/note_mail/inbox/list": {
    /**
     * 收件箱邮件列表
     * @description 收件箱邮件列表
     */
    get: operations["noteMailInboxList"];
  };
  "/note_mail/outbox/list": {
    /**
     * 发件箱邮件列表
     * @description 发件箱邮件列表
     */
    get: operations["noteMailOutboxList"];
  };
  "/note_mail/{mail_box_type}/star": {
    /**
     * 收藏便利贴邮件
     * @description 收藏便利贴邮件
     */
    post: operations["noteMailMailBoxTypeStar"];
  };
  "/note_mail/{mail_box_type}/unstar": {
    /**
     * 取消收藏便利贴邮件
     * @description 取消收藏便利贴邮件
     */
    post: operations["noteMailMailBoxTypeUnstar"];
  };
  "/note_mail/send": {
    /**
     * 发送便利贴邮件 (只支持游戏服务器通过ip白名单访问)
     * @description 发送便利贴邮件 (只支持游戏服务器通过ip白名单访问)
     */
    post: operations["noteMailSend"];
  };
  "/note_mail/clean_all": {
    /**
     * 清理玩家的便利贴信息，包括收和发, 比如藏宝阁交易时 (CallByGameServer)
     * @description 清理玩家的便利贴信息，包括收和发, 比如藏宝阁交易时 (CallByGameServer)
     */
    post: operations["noteMailCleanAll"];
  };
  "/gm/week_renqi/get": {
    /**
     * 读取周人气
     * @description 读取周人气
     */
    get: operations["getWeekRenQiGm"];
  };
  "/gm/gb_super_aas_limit/urs/{account}": {
    /**
     * 批量查询家长守护防沉迷规则(不使用gameId)
     * @description 批量查询家长守护防沉迷规则(不使用gameId)
     */
    get: operations["gmGbSuperAasLimitUrsAccount"];
  };
  "/gm/gb_super_aas_limit/{gameid}/urs/{account}": {
    /**
     * 查询家长守护防沉迷规则
     * @description [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#5.-%E6%9F%A5%E8%AF%A2%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
     */
    get: operations["getGmGbSuperAasLimitGameidUrsAccount"];
    /**
     * 设置或者更新家长守护防沉迷规则
     * @description [计费对应上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#3.-%E6%B7%BB%E5%8A%A0(%E6%88%96%E6%9B%B4%E6%96%B0)%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
     */
    post: operations["postGmGbSuperAasLimitGameidUrsAccount"];
    /**
     * 删除家长守护防沉迷规则
     * @description [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#4-%E5%88%A0%E9%99%A4%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
     */
    delete: operations["deleteGmGbSuperAasLimitGameidUrsAccount"];
  };
  "/gm/fcm/ty/add_kickoff_task": {
    /**
     * 添加天谕踢人任务
     * @description 添加天谕踢人任务
     */
    post: operations["gmFcmTyAddKickoffTask"];
  };
  "/gm/fcm/{gameid}/get_daily_online_time": {
    /**
     * 查询当日在线时长
     * @description 查询当日在线时长
     */
    get: operations["gmFcmGameidGetDailyOnlineTime"];
  };
  "/gm/fcm/{gameid}/set_daily_online_time": {
    /**
     * 设置当日在线时长
     * @description 设置当日在线时长
     */
    post: operations["gmFcmGameidSetDailyOnlineTime"];
  };
  "/gm/week_renqi/set": {
    /**
     * 设置周人气
     * @description 设置周人气
     */
    post: operations["setWeekRenqiGm"];
  };
  "/gm/audit/update_status": {
    /**
     * 设置图片视频审核状态
     * @description 设置图片视频审核状态
     */
    get: operations["gmAuditUpdateStatus"];
  };
  "/gm/server_list/merge_server/list": {
    /**
     * 查看设置的合服关系列表
     * @description 查看设置的合服关系列表
     */
    get: operations["gmServerListMergeServerList"];
  };
  "/gm/server_list/merge_server/add": {
    /**
     * 添加合服关系
     * @description 添加合服关系
     */
    post: operations["gmServerListMergeServerAdd"];
  };
  "/gm/server_list/merge_server/clean": {
    /**
     * 清理所有设置的合服关系
     * @description 清理所有设置的合服关系
     */
    post: operations["gmServerListMergeServerClean"];
  };
  "/server_annal/event/upvote": {
    /**
     * 点赞编年史事件
     * @description 点赞编年史事件
     */
    post: operations["serverAnnalEventUpvote"];
  };
  "/server_annal/event/downvote": {
    /**
     * 点踩编年史事件
     * @description 点踩编年史事件
     */
    post: operations["serverAnnalEventDownvote"];
  };
  "/server_annal/list": {
    /**
     * 获取编年史事件列表
     * @description 获取编年史事件列表
     */
    get: operations["getServerAnnalEventList"];
  };
  "/server_annal/score": {
    /**
     * 获取个人累计积分数据
     * @description 获取个人累计积分数据
     */
    get: operations["serverAnnalScore"];
  };
  "/server_annal/fengyun_players": {
    /**
     * 风云人物
     * @description 风云人物
     */
    get: operations["getServerAnnalFengyunPlayers"];
  };
  "/server_annal/sync": {
    /**
     * 游戏服务器同步编年史事件 (IP白名单授权)
     * @description 游戏服务器同步编年史事件 (IP白名单授权)
     */
    post: operations["syncServerAnnal"];
  };
  "/fcm/ban_account/show": {
    /**
     * 查询账号封禁信息
     * @description 查询账号封禁信息
     */
    get: operations["fcmBanAccountShow"];
  };
  "/fcm/ban_account/add": {
    /**
     * 添加账号封禁
     * @description 添加账号封禁
     */
    post: operations["fcmBanAccountAdd"];
  };
  "/fcm/ban_account/remove": {
    /**
     * 解除账号封禁
     * @description 解除账号封禁
     */
    post: operations["fcmBanAccountRemove"];
  };
  "/player_annal/list": {
    /**
     * 获取玩家编年史事件列表
     * @description 获取玩家编年史事件列表
     */
    get: operations["playerAnnalList"];
  };
  "/player_annal/sync": {
    /**
     * 游戏服务器同步编年史事件 (CallByGameServer)
     * @description 游戏服务器同步编年史事件 (CallByGameServer)
     */
    post: operations["playerAnnalSync"];
  };
  "/fcm/interfaces/realname/sync_realname.do": {
    /**
     * 实名同步（游戏专用）
     * @description 游戏服务器同步给梦岛，梦岛负责查询中宣部认证信息后，之后同步给urs [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=459#/)
     */
    get: operations["syncRealName"];
  };
  "/fcm/interfaces/yd/web/queryRealnameStatus.do": {
    /**
     * 手机帐号查询实名接口
     * @description 接口参数格式保持和上游一致 [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=105#/)
     */
    get: operations["queryRealNameStatusByYd"];
  };
  "/fcm/services/queryRealnameAndAntiIndulgenceServlet": {
    /**
     * 查询实名认证和防沉迷
     * @description 接口参数格式保持和上游一致 [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=170#/)
     */
    get: operations["queryRealNameAndAntiIndulgence"];
  };
  "/fcm/firefly/gm/query_gameid_by_urs": {
    /**
     * urs中实名无法区分来自哪个游戏，但是中宣实名上报时，需要区分游戏，并用到游戏对应的参数（中宣分配）， 因此，由计费搜集urs-gameid的映射关系，并提供给urs查询
     * @description urs中实名无法区分来自哪个游戏，但是中宣实名上报时，需要区分游戏，并用到游戏对应的参数（中宣分配）， 因此，由计费搜集urs-gameid的映射关系，并提供给urs查询
     */
    get: operations["queryGameIdByUrs"];
  };
  "/fcm/behavior/login": {
    /**
     * 上报登录行为
     * @description 上报登录行为
     */
    get: operations["loginBehavior"];
  };
  "/fcm/behavior/logout": {
    /**
     * 上报登出行为
     * @description 上报登出行为
     */
    get: operations["logoutBehavior"];
  };
  "/player/clean_history": {
    /**
     * 清理梦岛历史(删除状态， 留言板， 通知)
     * @description 用于提供个买号玩家清理之前账号历史记录的接口
     */
    post: operations["playerCleanHistory"];
  };
  "/changesign": {
    /**
     * 编辑签名
     * @description 编辑签名
     */
    post: operations["changesign"];
  };
  "/player/copy_moments": {
    /**
     * 复制旧号朋友圈动态
     * @description 换号时支持把旧号上的朋友圈状态复制到新号上
     * 全部由旧号发出的朋友圈状态，不包括状态下的转发、评论、点赞，不包括留言板，不包括左侧个人资料
     * [ReadMine](http://cloud.pm.netease.com/v6/issues/60699)
     */
    post: operations["copyMoments"];
  };
  "/likemoment": {
    /**
     * 点赞心情
     * @description 点赞心情
     */
    post: operations["likemoment"];
  };
  "/guild_polemic/apply_add_token": {
    /**
     * 获取发布檄文的token(游戏服务器申请)
     * @description 获取发布檄文的token(游戏服务器申请)
     */
    get: operations["guildPolemicApplyAddToken"];
  };
  "/guild_polemic/add": {
    /**
     * 添加帮会檄文
     * @description 添加帮会檄文
     */
    post: operations["guildPolemicAdd"];
  };
  "/guild_polemic/like": {
    /**
     * 点赞檄文
     * @description 点赞檄文
     */
    post: operations["guildPolemicLike"];
  };
  "/guild_polemic/cancel_like": {
    /**
     * 取消点赞檄文
     * @description 取消点赞檄文
     */
    post: operations["guildPolemicCancelLike"];
  };
  "/guild_polemic/get_detail": {
    /**
     * 获取檄文详情
     * @description 获取檄文详情
     */
    get: operations["guildPolemicGetDetail"];
  };
  "/guild_polemic/list": {
    /**
     * 获取檄文列表
     * @description 获取檄文列表
     */
    get: operations["guildPolemicList"];
  };
  "/guild_polemic/list_hot": {
    /**
     * 获取热门檄文列表
     * @description 获取热门檄文列表
     */
    get: operations["guildPolemicListHot"];
  };
  "/guild_expression/add": {
    /**
     * 添加帮会表情
     * @description 添加帮会表情
     */
    post: operations["guildExpressionAdd"];
  };
  "/guild_expression/byGuildId": {
    /**
     * 查看帮会表情
     * @description 查看帮会表情
     */
    get: operations["guildExpressionByGuildId"];
  };
  "/club/sync_data/{name}": {
    /**
     * 同步俱乐部相关数据(游戏服务器调用)
     * @description 同步俱乐部相关数据(游戏服务器调用)
     */
    post: operations["clubSyncDataName"];
  };
  "/club/sync_data/YZClubStat": {
    /**
     * 同步俱乐部约战统计数据
     * @description [比赛数据查询接口](/swagger/nsh/#/club/GetClubMatchStatistic)
     */
    post: operations["clubSyncDataYzClubStat"];
  };
  "/club/get_extra_infos": {
    /**
     * 获取俱乐部额外信息 (俱乐部荣誉值， 成员列表荣誉值以及认证选手标记)
     * @description 获取俱乐部额外信息 (俱乐部荣誉值， 成员列表荣誉值以及认证选手标记)
     */
    get: operations["clubGetExtraInfos"];
  };
  "/club/get_icons": {
    /**
     * 批量获取俱乐部图标
     * @description 批量获取俱乐部图标
     */
    get: operations["clubGetIcons"];
  };
  "/club/list": {
    /**
     * 获取俱乐部列表
     * @description 获取俱乐部列表
     */
    get: operations["clubList"];
  };
  "/club/match_statistics": {
    /**
     * 俱乐部赛事统计
     * @description 俱乐部赛事统计
     */
    get: operations["getClubMatchStatistic"];
  };
  "/club/player_match_statistics": {
    /**
     * 个人赛事统计
     * @description 个人赛事统计
     */
    get: operations["clubPlayerMatchStatistics"];
  };
  "/club/player/info": {
    /**
     * 查询玩家俱乐部相关信息
     * @description 查询玩家俱乐部相关信息
     */
    get: operations["clubPlayerInfo"];
  };
  "/club/player/clean": {
    /**
     * 清理玩家俱乐部相关荣誉
     * @description 清理玩家俱乐部相关荣誉
     */
    post: operations["clubPlayerClean"];
  };
  "/club/certified_players/list": {
    /**
     * 列出认证选手列表
     * @description 列出认证选手列表
     */
    get: operations["clubCertifiedPlayersList"];
  };
  "/club/certified_players/detail": {
    /**
     * 获取认证选手详情
     * @description 获取认证选手详情
     */
    get: operations["clubCertifiedPlayersDetail"];
  };
  "/club/detail": {
    /**
     * 获取俱乐部详情
     * @description 获取俱乐部详情
     */
    get: operations["clubDetail"];
  };
  "/club/match/rank/list": {
    /**
     * 俱乐部比赛排行
     * @description 俱乐部比赛排行
     */
    get: operations["clubMatchRankList"];
  };
  "/club/honors/location/swap": {
    /**
     * 俱乐部荣誉交换位置
     * @description 俱乐部荣誉交换位置
     */
    post: operations["clubHonorsLocationSwap"];
  };
  "/club/honors/location/restore": {
    /**
     * 俱乐部荣誉恢复位置
     * @description 俱乐部荣誉恢复位置
     */
    post: operations["clubHonorsLocationRestore"];
  };
  "/open/competition/options": {
    /**
     * 获取赛事选项
     * @description 获取赛事选项
     */
    get: operations["openCompetitionOptions"];
  };
  "/open/competition/list": {
    /**
     * 获取赛事结果
     * @description 获取赛事结果
     */
    get: operations["openCompetitionList"];
  };
  "/club/web/auth/login": {
    /**
     * 俱乐部赛事中心登录
     * @description 俱乐部赛事中心登录
     */
    post: operations["clubWebAuthLogin"];
  };
  "/club/web/roles/list": {
    /**
     * 列出角色列表
     * @description 列出角色列表
     */
    get: operations["clubWebRolesList"];
  };
  "/club/web/roles/bind": {
    /**
     * 切换urs账号绑定角色
     * @description 切换urs账号绑定角色
     */
    post: operations["clubWebRolesBind"];
  };
  "/club/web/loginInfo": {
    /**
     * 获取登录信息
     * @description 获取登录信息
     */
    get: operations["clubWebLoginInfo"];
  };
  "/club/web/commanders/apply/sms_code": {
    /**
     * 发送认证指挥的短信验证码
     * @description 发送认证指挥的短信验证码
     */
    post: operations["clubWebCommandersApplySmsCode"];
  };
  "/club/web/commanders/apply": {
    /**
     * 申请认证指挥
     * @description 申请认证指挥
     */
    post: operations["clubWebCommandersApply"];
  };
  "/club/web/commanders/recommend": {
    /**
     * 推荐指挥人选
     * @description 推荐指挥人选
     */
    post: operations["clubWebCommandersRecommend"];
  };
  "/club/web/operators/apply": {
    /**
     * 申请认证操作手
     * @description 申请认证操作手
     */
    post: operations["clubWebOperatorsApply"];
  };
  "/club/web/certified_players/list": {
    /**
     * 认证玩家列表
     * @description 认证玩家列表
     */
    get: operations["clubWebCertifiedPlayersList"];
  };
  "/club/web/certified_players/detail": {
    /**
     * 认证玩家详情
     * @description 认证玩家详情
     */
    get: operations["clubWebCertifiedPlayersDetail"];
  };
  "/club/web/list": {
    /**
     * 俱乐部列表
     * @description 俱乐部列表
     */
    get: operations["clubWebList"];
  };
  "/club/web/detail": {
    /**
     * 俱乐部详情页
     * @description 俱乐部详情页
     */
    get: operations["clubWebDetail"];
  };
  "/club/web/match_statistics": {
    /**
     * 俱乐部赛事统计
     * @description 俱乐部赛事统计
     */
    get: operations["clubWebMatchStatistics"];
  };
  "/club/web/player_match_statistics": {
    /**
     * 个人赛事统计
     * @description 个人赛事统计
     */
    get: operations["clubWebPlayerMatchStatistics"];
  };
  "/club/web/edit": {
    /**
     * 编辑俱乐部信息
     * @description 编辑俱乐部信息
     */
    post: operations["clubWebEdit"];
  };
  "/club/web/rank": {
    /**
     * 俱乐部排名列表
     * @description 俱乐部排名列表
     */
    get: operations["clubWebRank"];
  };
  "/club/web/user/edit": {
    /**
     * 编辑玩家认证信息
     * @description 编辑玩家认证信息
     */
    post: operations["clubWebUserEdit"];
  };
  "/club/web/user/home": {
    /**
     * 玩家主页信息
     * @description 玩家主页信息
     */
    get: operations["clubWebUserHome"];
  };
  "/club/web/certified_info/videos/add": {
    /**
     * 添加认证视频
     * @description 添加认证视频
     */
    post: operations["clubWebCertifiedInfoVideosAdd"];
  };
  "/club/web/certified_info/videos/del": {
    /**
     * 删除认证视频
     * @description 删除认证视频
     */
    post: operations["clubWebCertifiedInfoVideosDel"];
  };
  "/club/web/certified_info/videos/update": {
    /**
     * 更新认证视频
     * @description 更新认证视频
     */
    post: operations["clubWebCertifiedInfoVideosUpdate"];
  };
  "/club/web/competition/list": {
    /**
     * 获取赛事列表
     * @description 获取赛事列表
     */
    get: operations["clubWebCompetitionList"];
  };
  "/club/web/common/get_nos_token/{type}/{extName}": {
    /**
     * 获取nosToken上传凭证
     * @description 获取nosToken上传凭证
     */
    get: operations["clubWebCommonGetNosTokenTypeExtName"];
  };
  "/club/admin/loginInfo": {
    /**
     * 获取登录信息
     * @description 跳转地址 https://ssl.hi.163.com/file_mg/public/share/common_auth/corpauth/login
     */
    get: operations["clubAdminLoginInfo"];
  };
  "/club/admin/logout": {
    /**
     * 登出
     * @description 登出
     */
    get: operations["clubAdminLogout"];
  };
  "/club/admin/videos/list": {
    /**
     * 列出视频列表
     * @description 列出视频列表
     */
    get: operations["clubAdminVideosList"];
  };
  "/club/admin/videos/audit": {
    /**
     * 审核视频
     * @description 审核视频
     */
    post: operations["clubAdminVideosAudit"];
  };
  "/club/admin/videos/audit_batch": {
    /**
     * 批量审核视频
     * @description 批量审核视频
     */
    post: operations["clubAdminVideosAuditBatch"];
  };
  "/club/admin/images/list": {
    /**
     * 列出图片列表
     * @description 列出图片列表
     */
    get: operations["clubAdminImagesList"];
  };
  "/club/admin/images/audit": {
    /**
     * 审核图片
     * @description 审核图片
     */
    post: operations["clubAdminImagesAudit"];
  };
  "/club/admin/images/audit_batch": {
    /**
     * 批量审核图片
     * @description 批量审核图片
     */
    post: operations["clubAdminImagesAuditBatch"];
  };
  "/club/admin/certified_info/list": {
    /**
     * 列出认证信息列表
     * @description 列出认证信息列表
     */
    get: operations["clubAdminCertifiedInfoList"];
  };
  "/club/admin/certified_info/audit": {
    /**
     * 审核认证信息
     * @description 审核认证信息
     */
    post: operations["clubAdminCertifiedInfoAudit"];
  };
  "/club/admin/certified_info/audit_batch": {
    /**
     * 批量审核认证信息
     * @description 批量审核认证信息
     */
    post: operations["clubAdminCertifiedInfoAuditBatch"];
  };
  "/club/admin/player/tag/list": {
    /**
     * 列出玩家标签列表
     * @description 列出玩家标签列表
     */
    get: operations["clubAdminPlayerTagList"];
  };
  "/club/admin/player/tag/add_batch": {
    /**
     * 批量添加标签
     * @description 批量添加标签
     */
    post: operations["clubAdminPlayerTagAddBatch"];
  };
  "/club/admin/player/tag/update": {
    /**
     * 修改玩家标签列表
     * @description 修改玩家标签列表
     */
    post: operations["clubAdminPlayerTagUpdate"];
  };
  "/club/ue/stat": {
    /**
     * 获取俱乐部统计信息
     * @description 获取俱乐部统计信息
     */
    get: operations["clubUeStat"];
  };
  "/club/competition/admin/loginInfo": {
    /**
     * 获取登录信息
     * @description 跳转地址 https://ssl.hi.163.com/file_mg/public/share/common_auth/corpauth/login
     */
    get: operations["clubCompetitionAdminLoginInfo"];
  };
  "/club/competition/admin/logout": {
    /**
     * 登出
     * @description 登出
     */
    get: operations["clubCompetitionAdminLogout"];
  };
  "/club/competition/admin/add": {
    /**
     * 添加战绩
     * @description ```
     * 赛事类型及其数据范围定义
     * https://git-wz.nie.netease.com/hi-api/nsh_md_api/blob/develop/src/models/Competition.ts
     *
     * 接口参数定义 -namespace CompetitionReq
     * https://git-wz.nie.netease.com/hi-api/nsh_md_api/blob/develop/src/type/req.d.ts
     * ```
     */
    post: operations["clubCompetitionAdminAdd"];
  };
  "/club/competition/admin/edit": {
    /**
     * 修改战绩
     * @description 修改战绩
     */
    post: operations["clubCompetitionAdminEdit"];
  };
  "/club/competition/admin/del": {
    /**
     * 删除战绩
     * @description 删除战绩
     */
    post: operations["clubCompetitionAdminDel"];
  };
  "/club/competition/admin/list": {
    /**
     * 战绩列表
     * @description 战绩列表
     */
    get: operations["clubCompetitionAdminList"];
  };
  "/getinforms": {
    /**
     * 获取消息列表
     * @description 获取消息列表
     */
    get: operations["getinforms"];
  };
  "/informs/clean_all": {
    /**
     * 清空所有消息
     * @description 清空所有消息
     */
    post: operations["informsCleanAll"];
  };
  "/informs/red_dot": {
    /**
     * 消息红点，是否有新消息(是否有好友新动态， 是否新便利贴消息etc)
     * @description 消息红点，是否有新消息(是否有好友新动态， 是否新便利贴消息etc)
     */
    get: operations["informsRedDot"];
  };
  "/comment/list": {
    /**
     * 评论列表
     * @description 评论列表
     */
    get: operations["commentList"];
  };
  "/addcomment": {
    /**
     * 添加评论或回复
     * @description 添加评论或回复
     */
    post: operations["addcomment"];
  };
  "/delcomment": {
    /**
     * 删除评论
     * @description 删除评论
     */
    post: operations["delcomment"];
  };
  "/likecomment": {
    /**
     * 点赞评论
     * @description 点赞评论
     */
    post: operations["likecomment"];
  };
  "/gethotmoments": {
    /**
     * 获取本服热门心情列表
     * @description 获取本服热门心情列表
     */
    get: operations["gethotmoments"];
  };
  "/getallhotmoments": {
    /**
     * 获取全服热门心情列表
     * @description 获取全服热门心情列表
     */
    get: operations["getallhotmoments"];
  };
  "/getmoments": {
    /**
     * 获取用户朋友圈或者指定用户的朋友圈
     * @description 获取用户朋友圈或者指定用户的朋友圈
     */
    get: operations["getmoments"];
  };
  "/moment/picked_list": {
    /**
     * 获取编辑精选的朋友圈列表
     * @description 获取编辑精选的朋友圈列表
     */
    get: operations["momentPickedList"];
  };
  "/getmomentbyid": {
    /**
     * 获取单条动态
     * @description 获取单条动态
     */
    get: operations["getMomentById"];
  };
  "/moment/forward": {
    /**
     * 转发动态
     * @description 转发动态
     */
    post: operations["momentForward"];
  };
  "/getmessages": {
    /**
     * 留言列表
     * @description 留言列表
     */
    get: operations["getmessages"];
  };
  "/addmessage": {
    /**
     * 添加留言
     * @description 添加留言
     */
    post: operations["addmessage"];
  };
  "/delmessage": {
    /**
     * 删除留言
     * @description 删除留言
     */
    post: operations["delmessage"];
  };
  "/getprofile": {
    /**
     * 获取玩家详情
     * @description 获取玩家详情
     */
    get: operations["getPlayerProfile"];
  };
  "/players/job_avatars": {
    /**
     * 获取职业头像
     * @description 获取职业头像
     */
    get: operations["playersJobAvatars"];
  };
  "/setprivacy": {
    /**
     * 更新玩家隐私设置
     * @description 更新玩家隐私设置
     */
    post: operations["setprivacy"];
  };
  "/player/avatar/update": {
    /**
     * 上传用户头像
     * @description 上传用户头像
     */
    post: operations["playerAvatarUpdate"];
  };
  "/player/avatar/delete": {
    /**
     * 删除用户头像(恢复职业默认头像)
     * @description 删除用户头像(恢复职业默认头像)
     */
    post: operations["playerAvatarDelete"];
  };
  "/recent_visitors": {
    /**
     * 最近访客
     * @description 最近访客
     */
    get: operations["recentVisitors"];
  };
  "/server_competition/sync_result": {
    /**
     * 同步赛事结果数据(有队员)
     * @description 同步赛事结果数据(有队员)
     */
    post: operations["serverCompetitionSyncResult"];
  };
  "/server_competition/sync_table": {
    /**
     * 同步赛事结果数据(表格,无队员)
     * @description 同步赛事结果数据(表格,无队员)
     */
    post: operations["serverCompetitionSyncTable"];
  };
  "/server/transfer/add": {
    /**
     * 添加转服记录
     * @description 添加转服记录
     */
    post: operations["serverTransferAdd"];
  };
  "/transfer/sync_data/{name}": {
    /**
     * 转服信息同步
     * @description 转服信息同步
     */
    post: operations["transferSyncDataName"];
  };
  "/transfer/get_signup_num": {
    /**
     * 获取各个服务器报名数量
     * @description 获取各个服务器报名数量
     */
    get: operations["transferGetSignupNum"];
  };
  "/transfer/get_signup_list": {
    /**
     * 获取某个服务器报名列表
     * @description 获取某个服务器报名列表
     */
    get: operations["transferGetSignupList"];
  };
  "/server/moment/add": {
    /**
     * 服务器添加动态
     * @description 服务器添加动态
     */
    post: operations["serverMomentAdd"];
  };
  "/follow/getRecommendedList": {
    /**
     * 获取推荐关注列表
     * @description 获取推荐关注列表
     */
    get: operations["followGetRecommendedList"];
  };
  "/follow/add": {
    /**
     * 添加关注
     * @description 添加关注
     */
    post: operations["followAdd"];
  };
  "/follow/cancel": {
    /**
     * 取消关注
     * @description 取消关注
     */
    post: operations["followCancel"];
  };
  "/follow/friends": {
    /**
     * 一键同步跨服好友以及官方号
     * @description ⚠️  该接口会删除不在好友列表里的关注
     */
    post: operations["followFriends"];
  };
  "/follow_list": {
    /**
     * 关注列表
     * @description 关注列表
     */
    post: operations["followList"];
  };
  "/fan_list": {
    /**
     * 粉丝列表
     * @description 粉丝列表
     */
    post: operations["fanList"];
  };
  "/addmoment": {
    /**
     * 添加动态
     * @description 添加动态
     */
    post: operations["addMoment"];
  };
  "/delmoment": {
    /**
     * 删除动态
     * @description 删除动态
     */
    post: operations["delMoment"];
  };
  "/topic/list": {
    /**
     * 活动话题列表
     * @description 活动话题列表
     */
    get: operations["topicList"];
  };
  "/get_honors": {
    /**
     * 获取荣誉墙荣誉
     * @description 获取荣誉墙荣誉
     */
    get: operations["getHonors"];
  };
  "/get_honor_num": {
    /**
     * 获取荣誉墙荣誉数量
     * @description 获取荣誉墙荣誉数量
     */
    get: operations["getHonorNum"];
  };
  "/activity/friend_ids": {
    /**
     * 查看玩家好友id列表
     * @description 查看玩家好友id列表
     */
    get: operations["activityFriendIds"];
  };
  "/activity/get_couple_info": {
    /**
     * 获取玩家侠侣信息
     * @description 获取玩家侠侣信息
     */
    get: operations["activityGetCoupleInfo"];
  };
  "/activity/add_moment": {
    /**
     * 活动分享到梦岛
     * @description 话题link特殊格式
     * text1<link action={Color="ff8c00",Text="＃我要上花火节＃",Name="OnDoCircleOfFriendsTopicAction",Params={TopicId=39}}>text2
     */
    post: operations["activityAddMoment"];
  };
  "/gm/ping": {
    get: operations["gmPing"];
  };
  "/gm/club/honor_excel/{action}": {
    /**
     * 更新俱乐部和玩家荣誉
     * @description 更新俱乐部和玩家荣誉
     */
    post: operations["gmClubHonorExcelAction"];
  };
  "/gm/club/match_table/{action}": {
    /**
     * 更新俱乐部比赛定义表
     * @description 更新俱乐部比赛定义表
     */
    post: operations["gmClubMatchTableAction"];
  };
  "/gm/club/clean_honors": {
    /**
     * 清理俱乐部荣誉
     * @description 清理俱乐部荣誉
     */
    post: operations["gmClubCleanHonors"];
  };
  "/gm/identity/add_admin": {
    /**
     * 添加标签认证后台管理员
     * @description 添加标签认证后台管理员
     */
    post: operations["gmIdentityAddAdmin"];
  };
  "/gm/hot_moments/refresh": {
    /**
     * 刷新朋友圈热门动态
     * @description 刷新朋友圈热门动态
     */
    post: operations["gmHotMomentsRefresh"];
  };
  "/gm/copy_moments/clean": {
    /**
     * 清除拷贝动态的记录
     * @description 清除拷贝动态的记录
     */
    post: operations["gmCopyMomentsClean"];
  };
  "/gm/moment/get_hot": {
    /**
     * 获取动态的热度
     * @description 获取动态的热度
     */
    get: operations["gmMomentGetHot"];
  };
  "/gm/player/set_couple_info": {
    /**
     * 设置侠侣信息
     * @description 设置侠侣信息
     */
    post: operations["gmPlayerSetCoupleInfo"];
  };
  "/gm/player/get_couple_info": {
    /**
     * 获取玩家侠侣信息
     * @description 获取玩家侠侣信息
     */
    get: operations["gmPlayerGetCoupleInfo"];
  };
  "/gm/club/clean_by_server": {
    /**
     * 根据服务器id批量清理俱乐部相关数据
     * @description 根据服务器id批量清理俱乐部相关数据
     */
    get: operations["gmClubCleanByServer"];
  };
  "/wishlist/wishes/remove": {
    /**
     * 删除一个心愿
     * @deprecated
     * @description 删除一个心愿
     */
    post: operations["wishlistWishesRemove"];
  };
  "/wishlist/wishes/remove_all": {
    /**
     * 删除全部心愿
     * @deprecated
     * @description 删除全部心愿
     */
    post: operations["wishlistWishesRemoveAll"];
  };
  "/wishlist/add": {
    /**
     * 添加商品到心愿单
     * @description 添加商品到心愿单
     */
    post: operations["wishlistAdd"];
  };
  "/wishlist/add_by_fashion": {
    /**
     * 添加时装到心愿单
     * @description 添加时装到心愿单
     */
    post: operations["wishlistAddByFashion"];
  };
  "/wishlist/remove": {
    /**
     * 从心愿单移除商品
     * @description 从心愿单移除商品
     */
    post: operations["wishlistRemove"];
  };
  "/wishlist/remove_by_fashion": {
    /**
     * 从心愿单移除时装
     * @description 从心愿单移除时装
     */
    post: operations["wishlistRemoveByFashion"];
  };
  "/wishlist/notify_buy_action": {
    /**
     * 通知购买行为, 检查商品ID并处理心愿单
     * @description 通知购买行为, 检查商品ID并处理心愿单
     */
    post: operations["wishlistNotifyBuyAction"];
  };
  "/wishlist/notify_fashion_get_action": {
    /**
     * 通知获取时装事件，检查时装id并处理心愿单
     * @description 通知获取时装事件，检查时装id并处理心愿单
     */
    post: operations["wishlistNotifyFashionGetAction"];
  };
  "/wishlist/show": {
    /**
     * 查看玩家心愿单
     * @description 查看玩家心愿单
     */
    get: operations["wishlistShow"];
  };
  "/wishlist/full_filled": {
    /**
     * 列出已经实现的心愿
     * @description 列出已经实现的心愿
     */
    get: operations["wishlistFullFilled"];
  };
  "/wishlist/full_filled/remove": {
    /**
     * 删除一条心愿实现记录
     * @description 删除一条心愿实现记录
     */
    post: operations["wishlistFullFilledRemove"];
  };
  "/wishlist/full_filled/remove_all": {
    /**
     * 删除全部心愿实现记录
     * @description 删除全部心愿实现记录
     */
    post: operations["wishlistFullFilledRemoveAll"];
  };
  "/expansion_trial_scene/set_reward": {
    /**
     * 服务器设置奖励
     * @description 服务器设置奖励
     */
    post: operations["expansionTrialSceneSetReward"];
  };
  "/expansion_trial_scene/get_reward": {
    /**
     * 服务器领取奖励
     * @description 服务器领取奖励
     */
    post: operations["expansionTrialSceneGetReward"];
  };
  "/expansion_trial_scene/query_reward": {
    /**
     * 客户端查询奖励
     * @description 客户端查询奖励
     */
    get: operations["expansionTrialSceneQueryReward"];
  };
  "/activity/take_photo/add_photo": {
    /**
     * 上传照片
     * @description 上传照片
     */
    post: operations["activityTakePhotoAddPhoto"];
  };
  "/activity/take_photo/get_my_photos": {
    /**
     * 查询自己的照片
     * @description 查询自己的照片
     */
    get: operations["activityTakePhotoGetMyPhotos"];
  };
  "/activity/take_photo/select_photo": {
    /**
     * 选中照片
     * @description 选中照片
     */
    post: operations["activityTakePhotoSelectPhoto"];
  };
  "/activity/take_photo/lock_photo": {
    /**
     * 锁定照片
     * @description 锁定照片
     */
    post: operations["activityTakePhotoLockPhoto"];
  };
  "/activity/take_photo/unlock_photo": {
    /**
     * 解锁照片
     * @description 解锁照片
     */
    post: operations["activityTakePhotoUnlockPhoto"];
  };
  "/activity/take_photo/get_location_selected_photo": {
    /**
     * 查玩家某地点选中的照片
     * @description 查玩家某地点选中的照片
     */
    get: operations["activityTakePhotoGetLocationSelectedPhoto"];
  };
  "/garden/sync_data/{name}": {
    /**
     * 同步庄园数据
     * @description 同步庄园数据
     */
    post: operations["gardenSyncDataName"];
  };
  "/garden/detail": {
    /**
     * 庄园详情
     * @description 庄园详情
     */
    get: operations["gardenDetail"];
  };
  "/garden/rank": {
    /**
     * 庄园排行
     * @description 庄园排行
     */
    get: operations["gardenRank"];
  };
  "/garden/evaluation_add": {
    /**
     * 庄园评价
     * @description 庄园评价
     */
    post: operations["gardenEvaluationAdd"];
  };
  "/garden/evaluation_del": {
    /**
     * 庄园删除评价
     * @description 庄园删除评价
     */
    post: operations["gardenEvaluationDel"];
  };
  "/garden/evaluation_like": {
    /**
     * 评价点赞
     * @description 评价点赞
     */
    post: operations["gardenEvaluationLike"];
  };
  "/garden/evaluation_cancel_like": {
    /**
     * 评价取消点赞
     * @description 评价取消点赞
     */
    post: operations["gardenEvaluationCancelLike"];
  };
  "/garden/evaluation_list": {
    /**
     * 庄园评价列表
     * @description 庄园评价列表
     */
    get: operations["gardenEvaluationList"];
  };
  "/garden/evaluation/comment": {
    /**
     * 评价评论
     * @description 评价评论
     */
    post: operations["gardenEvaluationComment"];
  };
  "/garden/evaluation/comment_del": {
    /**
     * 评价评论删除
     * @description 评价评论删除
     */
    post: operations["gardenEvaluationCommentDel"];
  };
  "/garden/evaluation/comment_list": {
    /**
     * 评论列表-一级评论
     * @description 评论列表-一级评论
     */
    get: operations["gardenEvaluationCommentList"];
  };
  "/garden/evaluation/comment_sub_list": {
    /**
     * 评论列表-二级评论
     * @description 评论列表-二级评论
     */
    get: operations["gardenEvaluationCommentSubList"];
  };
  "/garden/evaluation/comment_like": {
    /**
     * 评论点赞
     * @description 评论点赞
     */
    post: operations["gardenEvaluationCommentLike"];
  };
  "/garden/evaluation/comment_cancel_like": {
    /**
     * 评论取消点赞
     * @description 评论取消点赞
     */
    post: operations["gardenEvaluationCommentCancelLike"];
  };
  "/garden/inform/list": {
    /**
     * 庄园消息列表
     * @description ```
     * 返回字段中type为消息的类型
     * 1 - 新的打卡
     * 2 - 打卡点赞
     * 3 - 打卡评论
     * 4 - 评论点赞
     * 5 - 评论回复
     *
     * 返回字段中status为读取状态
     * 0 - 未读
     * 1 - 已读
     *
     * 查看该接口后,玩家的所有消息的读取状态都会变为已读
     * ```
     */
    get: operations["gardenInformList"];
  };
  "/garden/inform/unread": {
    /**
     * 庄园消息红点
     * @description 庄园消息红点
     */
    get: operations["gardenInformUnread"];
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    CompetitionOptions: {
      /**
       * @example 1
       * @enum {number}
       */
      serverType?: 1 | 2 | 3;
      /** @example 正式服 */
      serverTypeName?: string;
      competitionIds?: {
          /** @example 1 */
          competitionId?: number;
          /** @example 天下第一 */
          competitionName?: string;
          whichTimes?: {
              /**
               * @description 届数
               * @example 1
               */
              whichTimes?: number;
              /** @example 第一届 */
              whichTimesName?: string;
            }[];
        }[];
    };
    WebCompetitionList: {
      /** @example 1 */
      competitionId?: number;
      /** @example 1 */
      serverType?: number;
      /** @example 1 */
      whichTimes?: number;
      /** @description 赛事结果,有队员的 */
      result?: ({
          /**
           * @description 排名
           * @example 1
           */
          rank?: number;
          /**
           * @description 服务器id
           * @example 1
           */
          server?: number;
          /**
           * @description 服务器名称
           * @example 烟雨江南
           */
          serverName?: string;
          /**
           * @description 队伍名称
           * @example 队伍名称
           */
          teamName?: string;
          roleList?: ({
              /**
               * @description 角色名称
               * @example 角色名称
               */
              roleName?: string;
              /**
               * @description 职业
               * @example 1
               */
              career?: number;
              /**
               * @description 性别
               * @example 1
               */
              gender?: number;
              /**
               * @description 角色类型 1队长 2队员 3替补 4军师
               * @example 1
               * @enum {number}
               */
              roleType?: 1 | 2 | 3 | 4;
              /**
               * @description 头像
               * @example https://hi-163-nsh.nos-jd.163yun.com/club/4297f44b13955235245b2497399d7a93/202007/14/39c8d8a0c5c311ea98ab7b68c63692c8.png
               */
              avatarUrl?: string;
            })[];
        })[];
      /** @description 赛事表格,有队员的,表格形式的 */
      table?: {
          /**
           * @description 排名
           * @example 1
           */
          rank?: number;
          /**
           * @description 服务器名称
           * @example 烟雨江南
           */
          serverName?: string;
          /**
           * @description 帮会名称
           * @example 帮会名称
           */
          guildName?: string;
          /**
           * @description 帮主名称
           * @example 帮主名称
           */
          guildMaster?: string;
        }[];
    };
    /**
     * @description 具体哪一周(用周日作为日期开始)
     * @example 2021-05-31
     */
    SunWeekStr: string;
    PlayerGetProfile: {
      /** @example 24017600001 */
      roleId: number;
      /** @example 我最帅比 */
      roleName: string;
      /**
       * @description 是否关注
       * @example false
       */
      isFollowing: boolean;
      /**
       * @description 玩家等级
       * @example 109
       */
      level: number;
      /**
       * @description 称号
       * @example
       */
      title: string;
      /**
       * @description 称号id
       * @example 52000167
       */
      titleId: number;
      /**
       * @description 玩家职业id
       * @example 4
       */
      jobId: number;
      /**
       * @description 玩家性别
       * @example 0
       */
      gender: number;
      /**
       * @description 玩家子性别
       * @example 0
       */
      subGender: number;
      /**
       * @description 玩家语音识别性别
       * @example 0
       */
      voiceGender: string;
      /** @example 44710001 */
      frameId: number;
      /** @example */
      gang: string;
      /** @example 0 */
      horoscopeTag: number;
      /**
       * @description 头像
       * @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2021/03/03/0F1F9217F0F1F1614743838.png?imageView&crop=233_48_531_531
       */
      photo: string;
      /**
       * @description 头像审核状态
       * @example 1
       */
      photoAudit: number;
      /** @example 0 */
      photoAuditMsg: number;
      /** @example 20 */
      gift: number;
      /** @example 0 */
      flower: number;
      /** @example 测试12获取地理位置 */
      location: string;
      /**
       * @description 本周周人气
       * @example 0
       */
      renqi: number;
      /**
       * @description 玩家发送动态附带的图片数量限制
       * @example 4
       */
      momentImageLimit: number;
      /**
       * @description 玩家签名
       * @example 一二三四五六七八九十一二三四五六七二三四五六七八九十
       */
      signature: string;
      /**
       * @description 姻缘信息
       * @example
       */
      yinyuan: string;
      /** @example 297 */
      momentCount: number;
      /**
       * @description 粉丝数
       * @example 21
       */
      fansNum: number;
      privacy: components["schemas"]["PrivacyOptions"];
      /** @example 0 */
      bRenQiFull: number;
      /** @example 5 */
      honorNum: number;
      /** @example false */
      hasUnReceiveRenQiReward: boolean;
      messageBoard: {
        /** @example false */
        signUpReachLimit: boolean;
        /** @example false */
        signUpForTargetToday: boolean;
      };
      /**
       * @description 是否官方账号
       * @example false
       */
      bOfficialAccount: boolean;
      /** @example true */
      enableNotConcernOfficial: boolean;
      /** @example true */
      isPublic: boolean;
      /** @example 0 */
      lianghaoId: number;
      /** @example 0 */
      lianghaoOutTimeStamp: number;
      /** @example [] */
      identityList: unknown[];
      /** @example {} */
      currentIdentity: Record<string, never>;
      /**
       * @description 玩家明星认证过期时间
       * @example *************
       */
      starPlayerType: number;
      /**
       * @description 玩家明星认证类型
       * @example 10
       */
      starPlayerExpTime: number;
    };
    /** @description 玩家隐私设置 */
    PrivacyOptions: {
      /**
       * @description 隐藏lbs位置
       * @example false
       */
      hideLocation: boolean;
      /**
       * @description 隐藏语音性别
       * @example false
       */
      hideVoiceGender: boolean;
      /**
       * @description 限制非好友评论
       * @example false
       */
      limitComment: boolean;
      /**
       * @description 禁止新动态提醒
       * @example false
       */
      muteNewMoment: boolean;
      /**
       * @description 限制跨服交友
       * @example false
       */
      limitCrossFriend: boolean;
      /**
       * @description 隐藏心愿单
       * @example true
       */
      hideWishList: boolean;
    };
    /** @description 事件参数字典, 用来实例化事件文本显示模板, 内容根据实际自定义 */
    MayBeForceEventArgs: {
      /**
       * @description 势力id
       * @example 1001
       */
      forceId?: number;
      [key: string]: unknown;
    };
    /** @description 事件参数字典, 用来实例化事件文本显示模板 */
    MayBeServerAnnalEventArgs: {
      /**
       * @description 和本事件关联的玩家id
       * @example 1001
       */
      playerId?: number;
      /** @description 和本事件关联的玩家id列表 */
      playerIdList?: number[];
      /**
       * @description 和本事件关联的玩家名字
       * @example 绝代双骄
       */
      playerName?: string;
      /** @description 和本事件关联的玩家名字列表 */
      playerNameList?: string[];
      /**
       * @description 玩家战力
       * @example 6488
       */
      playerScore?: number;
      /** @description 玩家战力列表 */
      playerScoreList?: number[];
      /**
       * @description 帮会id
       * @example 3001
       */
      guildId?: number;
      /**
       * @description 帮会名字
       * @example 斧头帮
       */
      guildName?: string;
      /**
       * @description 敌对/联盟帮会id
       * @example 3002
       */
      targetGuildId?: number;
      /**
       * @description 敌对/联盟帮会名字
       * @example 青龙帮
       */
      targetGuildName?: string;
      /**
       * @description 标记是否首次事件 需要高亮和置顶
       * @enum {number}
       */
      first?: 0 | 1;
      /**
       * @description 事件积分映射位置(用于处理相同事件不同参数具有不等积分的情况)
       * @example 3
       */
      condIndex?: number;
    };
    /** @description 事件参数字典, 用来实例化事件文本显示模板 */
    MayBePlayerAnnalEventArgs: {
      /**
       * @description 和本事件关联的玩家id
       * @example 1001
       */
      playerId?: number;
      /** @description 和本事件关联的玩家id列表 */
      playerIdList?: number[];
      /**
       * @description 和本事件关联的玩家名字
       * @example 绝代双骄
       */
      playerName?: string;
      /** @description 和本事件关联的玩家名字列表 */
      playerNameList?: string[];
      /**
       * @description 帮会id
       * @example 3001
       */
      guildId?: number;
      /**
       * @description 帮会名字
       * @example 斧头帮
       */
      guildName?: string;
      /**
       * @description 敌对/联盟帮会id
       * @example 3002
       */
      targetGuildId?: number;
      /**
       * @description 敌对/联盟帮会名字
       * @example 青龙帮
       */
      targetGuildName?: string;
      /**
       * @description 标记是否首次事件 需要高亮和置顶
       * @enum {number}
       */
      first?: 0 | 1;
      /**
       * @description 事件积分映射位置(用于处理相同事件不同参数具有不等积分的情况)
       * @example 3
       */
      condIndex?: number;
    };
    /** @description 编年史数据定义 */
    BaseServerAnnalEvent: {
      /**
       * @description 服务器Id
       * @example 12
       */
      serverId: number;
      /**
       * @description 事件分类下的具体类型id, 用来映射策划表的事件文本
       * @example 10
       */
      eventType: number;
      /**
       * @description 事件发生的事件(单位ms)
       * @example 1620628236384
       */
      eventTime: number;
      eventArgs: components["schemas"]["MayBeServerAnnalEventArgs"];
    };
    /** @description 编年史数据定义 */
    BasePlayerAnnalEvent: {
      /**
       * @description 和本事件关联的玩家id
       * @example 1001
       */
      roleId: number;
      /**
       * @description 服务器Id
       * @example 12
       */
      serverId: number;
      /**
       * @description 事件分类下的具体类型id, 用来映射策划表的事件文本
       * @example 10
       */
      eventType: number;
      /**
       * @description 事件发生的事件(单位ms)
       * @example 1620628236384
       */
      eventTime: number;
      eventArgs: components["schemas"]["MayBePlayerAnnalEventArgs"];
    };
    /**
     * @description 事件高亮标记(控制是否进总览, 1 => 普通事件 2 => 高光事件)
     * @example 1
     * @enum {number}
     */
    EventHighlight: 1 | 2;
    ServerAnnalEvent: {
      /** @example 1 */
      id: number;
      category: components["schemas"]["ServerAnnalCategory"];
      /**
       * @description 点赞数
       * @example 10
       */
      upvote: number;
      /**
       * @description 是否点赞
       * @example true
       */
      isUpVote: boolean;
    } & components["schemas"]["BaseServerAnnalEvent"];
    PicSendPicItem: {
      /** @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png */
      url?: string;
      /**
       * @description string
       * @example times_square_photo:1
       */
      pic_id?: number;
      /**
       * @description 角色id
       * @example 1001
       */
      role_id?: number;
      /**
       * @description 资源类型 0=>图片 1=> 视频
       * @example 0
       */
      media?: number;
      /**
       * @description 备注
       * @example
       */
      note?: string;
    };
    PicReturnPicItem: {
      /** @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png */
      url?: string;
      /**
       * @description 状态 0 => 审核中 1 => 通过 -1 => 拒绝
       * @example PASS
       * @enum {number}
       */
      status?: -1 | 0 | 1;
      /**
       * @description string
       * @example times_square_photo:1
       */
      pic_id?: number;
      /**
       * @description 角色id
       * @example 1001
       */
      role_id?: number;
      /**
       * @description 资源类型 0=>图片 1=> 视频
       * @example 0
       */
      media?: number;
      /**
       * @description 备注
       * @example
       */
      note?: string;
    };
    AuditSendPic: {
      /**
       * @description 产品
       * @example nsh
       */
      product?: string;
      /**
       * @example [
       *   {
       *     "url": "https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg",
       *     "role_id": 24017600001,
       *     "pic_id": "times_square_photo:1",
       *     "media": 0,
       *     "note": ""
       *   }
       * ]
       */
      pic_list: components["schemas"]["PicSendPicItem"][];
      /**
       * @description 审核回调url
       * @example http://your-app.com/audit/return_pic
       */
      callback_url?: string;
    };
    AuditReturnPic: {
      /**
       * @description 产品
       * @example nsh
       */
      product?: string;
      /**
       * @example [
       *   {
       *     "url": "https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg",
       *     "role_id": 24017600001,
       *     "pic_id": "times_square_photo:1",
       *     "media": 0,
       *     "status": 1,
       *     "note": ""
       *   }
       * ]
       */
      pic_list: components["schemas"]["PicReturnPicItem"][];
    };
    ActivityTakePhotoMyListItem: {
      /** @description 位置ID */
      locationId?: number;
      /** @description 图片ID */
      imgId?: number;
      imgUrl?: string;
      /** @description 是否被锁定 */
      isLocked?: boolean;
      /** @description 是否被选中 */
      isSelected?: boolean;
    };
    RoleInfo: {
      roleId: number;
      /**
       * @description 角色名字
       * @example zhangsan
       */
      roleName: string;
      /**
       * @description 服务器id
       * @example 12
       */
      server?: number;
      /**
       * @description 职业id
       * @example 100100001
       */
      jobId: number;
      /**
       * @description 性别
       * @example 1
       */
      gender: number;
      /**
       * @description 等级
       * @example 20
       */
      level: number;
    };
    GardenBasicInfo: components["schemas"]["RoleInfo"] & {
      /** @description 庄园等级 */
      gardenGrade: number;
      /** @description 评分 */
      score: number;
      /** @description 打卡人数 */
      evaluationCount: number;
      /** @description 人气值 */
      popularity: number;
      /** @description 庄园id */
      gardenId?: string;
      fengShui?: number;
      shiYong?: number;
      meiGuan?: number;
      photo?: string;
    };
    idData: {
      /** @example 1 */
      id: number;
    };
    TimesSquarePhotoAddRet: {
      /**
       * @description 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
       * @example 1
       */
      auditStatus?: number;
      /**
       * @description 创建时间
       * @example 1620628236384
       */
      createTime?: number;
    };
    TimesSquarePhotoListRet: {
      list?: (components["schemas"]["TimesSquarePhoto"] & components["schemas"]["TimesSquarePhotoAddRet"])[];
      /** @example 1 */
      count?: number;
    };
    MemeAddRet: {
      /**
       * @description 表情包id
       * @example 10
       */
      id?: number;
      /**
       * @description 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
       * @example 1
       */
      auditStatus?: number;
      /**
       * @description 创建时间
       * @example 1620628236384
       */
      createTime?: number;
    };
    GardenEvalutionListItem: {
      id?: number;
      text?: string;
      score?: number;
      imgList?: string[];
      /** @description 点赞数 */
      likeCount?: number;
      /** @description 评论数 */
      commentCount?: number;
      /** @description 创建时间，单位：ms */
      createTime?: number;
      /** @description 是否点赞 */
      isLiked?: boolean;
    } & components["schemas"]["RoleInfo"];
    GardenEvalutionCommentListItem: components["schemas"]["RoleInfo"] & {
      id: number;
      text: string;
      /** @description 点赞数 */
      likeCount: number;
      /** @description 回复评论数 */
      replyCount: number;
      /** @description 创建时间，单位：ms */
      createTime: number;
      /** @description 是否点赞 */
      isLiked: boolean;
      /** @description 被回复的角色信息 */
      replyRoleInfo?: components["schemas"]["RoleInfo"];
    };
    GardenInformListItem: components["schemas"]["RoleInfo"] & ({
      id: number;
      text: string;
      /** @description 消息类型 */
      type?: number;
      /**
       * @description 已读状态 0=>未读 1=>已读
       * @enum {number}
       */
      status?: 0 | 1;
    });
    PlayerAnnalEvent: {
      /** @example 1 */
      id: number;
    } & components["schemas"]["BasePlayerAnnalEvent"];
    QueryRealNameStatus: {
      /** @example 201 */
      result: string;
      /** @example */
      msg: string;
      /** @example 1 */
      realnameVerify: number;
      /** @example true */
      realnameSet: boolean;
      /** @example true */
      isAdult: boolean;
      /** @example pi_test */
      pi: string;
      /** @example true */
      fake_adult?: boolean;
      /**
       * @description 是否被封禁
       * @example true
       */
      isBanned?: boolean;
      /**
       * @description 封禁截止时间(单位ms)
       * @example *************
       */
      banTime?: number;
      /**
       * @description 未满14岁是否通过家长授权允许
       * @example true
       */
      parentsAuth?: boolean;
      /**
       * @description 未满14岁家长认证表单页面
       * @example https://mpay-personal-privacy-protection.g.mkey.163.com/parents_auth/authorize?ticket=XxXaAonXPnZLEqVbZSGFqfDFzvLaStzP&redirect_uri=https%3A%2F%2Fn.163.com%2F
       */
      parentsAuthRedirectUrl?: string;
      super_aas_limit_wrap?: components["schemas"]["SuperAssLimitWrap"];
      /** @description 信用分结果 */
      ngc_result_wrap?: {
        /**
         * @description 用户是否正常，正常/风险
         * @example 正常
         */
        check_res?: string;
        /**
         * @description -1代表接口调用异常，0代表结果正常，1代表用户在批注库内就结束判断了，2代表用户为urs批注用户就判断结束了，3代表用户没有安全手机
         * @example 0
         */
        risk_source?: number;
        /** @description NGC信用分相关 */
        ngc_result?: {
          /**
           * @description 游戏账号分
           * @example 500
           */
          credit_score?: number;
          /**
           * @description 账号正向分
           * @example 0
           */
          all_score_positive?: number;
          /** @description 最大扣分项目 */
          largest_reduced_item?: string;
          /** @description 最大扣分，若为满分500，则为None */
          largest_reduced_score?: number;
          /**
           * @description 信用分
           * @example 500
           */
          ngc_score?: number;
        };
      };
    };
    /** @description 家长守护平台规则获取以及是否触发限制 */
    SuperAssLimitWrap: {
      /**
       * @description 是否由于家长守护规则限制登录, 判断是否被限制用该字段即可
       * @example false
       */
      restrict_login: boolean;
      /**
       * @description 校验规则具体状态码
       * -1: 访问上游接口失败，此时不限制登录
       * 0: 上游接口正常, 未触发限制规则
       * 1: 触发宵禁规则被限制
       * 2: 触发工作日时长上限被限制
       * 3: 触发节假日时长上限被限制
       *
       * @example 0
       */
      check_status: number;
      /**
       * @description 当日统计游玩时长(秒)
       * @example 600
       */
      daily_play_time: number;
      /** @description 当访问上游接口失败时，该字段为null */
      super_aas_limit: {
        /**
         * @description 单次消费限额，单位元；限额范围：[0, 999999999]  不传对成年账号默认不限消费额度，对未成年账号默认采用游戏防沉迷标准限额
         * @example 45
         */
        create_order_limit: number;
        /**
         * @description 月消费限额，单位元；限额范围：[0, 999999999] * 不传对成年账号默认不限消费额度，对未成年账号默认采用游戏防沉迷标准限额
         * @example 390
         */
        month_sum_limit: number;
        /**
         * Format: float
         * @description 工作日可在线时长，单位精确到秒，0表示工作日禁止登录 不传默认使用中宣部限制，未成年账号和未实名账号不允许传除0以外的其他值
         * @example 3600
         */
        online_time_limit: number;
        /**
         * @description 节假日可在线时长，单位精确到秒，0表示节假日禁止登录 不传默认使用中宣部限制，未成年账号和未实名账号不允许传除0以外的其他值
         * @example 1200
         */
        holiday_online_time_limit: number;
        /**
         * Format: time
         * @description 宵禁开启时间，格式为"hh:mm:ss"；如19:00:00，表示从19:00:00到当天23:59:59为宵禁时间 不传默认不限制宵禁
         * @example 19:00:00
         */
        curfew_start_time: string;
        /**
         * Format: time
         * @description 宵禁结束时间，格式为"hh:mm:ss"；如06:00:00，表示从00:00:00到当天06:00:00为宵禁时间 不传默认不限制宵禁
         * @example 06:00:00
         */
        curfew_end_time: string;
        /**
         * Format: date
         * @description 设置规则过期时间，格式为"YYYY-mm-dd"；如2021-05-05，表示过期时间为2021-05-05 00:00:00；不传则默认不过期
         * @example 2021-05-04
         */
        expired_time: string;
        /**
         * @description 被限制时的提示语，提示语应提及所有被限制的参数项，提示信息长度不超过130个中文字符
         * @example 当前账号触发防沉迷限制：单笔订单限额为10元，工作日登录时长为3600秒，宵禁时间为每日19:00:00至次日06:00:00。
         */
        aas_msg: string;
        /**
         * @description 家长守护平台：不传/传0 运营限制：传1
         * @example 1
         */
        source: number;
      };
    };
    GuildExpressionRes: {
      /** @example 1 */
      id: number;
      /** @example 1 */
      serverId: number;
      /** @example 1 */
      guildId: number;
      /** @example 61468700232 */
      roleId: number;
      /** @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png */
      url: string;
      /**
       * @description 0表示圆形 1表示正方形
       * @example 1
       */
      style: number;
      /**
       * @description 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过
       * @example 1
       */
      auditStatus: number;
      /**
       * @description 0未使用 1使用中
       * @example 1
       */
      status: number;
      /** @example 1617334320059 */
      createTime: number;
    };
    Topic: {
      ID?: number;
      Name?: string;
      Banner?: string;
      Desc?: string;
      CreateTime?: number;
    };
    SuccRes: {
      code?: number;
      data?: Record<string, never>;
    };
    RedDotRes: {
      /**
       * @description 新动态数量
       * @example 1
       */
      dynaNum: number;
      /**
       * @description 新消息数量
       * @example 2
       */
      msgNum: number;
      /**
       * @description 便签消息数量
       * @example 2
       */
      noteMailNum: number;
      /**
       * @description 帮会照片墙是否有红点标记  0=>无红点 1 => 有红点
       * @example 1
       * @enum {number}
       */
      guildPhotoWallNum: 0 | 1;
      /**
       * @description 联居照片墙是否有红点标记  0=>无红点 1 => 有红点
       * @example 1
       * @enum {number}
       */
      multiGardenPhotoWallNum?: 0 | 1;
      /**
       * @description 个人大事记有否有红点
       * @example true
       */
      newPlayerAnnal: boolean;
    };
    /**
     * @description | value |    desc    |
     * |-------|:----------:|
     * | 0     |  未归类事件  |
     * | 1     |  高光事件  |
     * | 2     | 个人类世界 |
     * | 3     | 帮会类事件 |
     *
     * @example 1
     * @enum {number}
     */
    ServerAnnalCategory: 0 | 1 | 2 | 3;
    /**
     * @description | value |    desc    |
     * |-------|:----------:|
     * |1      |论武套路     |
     * |2      |帮战套路     |
     * |3      |副本套路     |
     *
     * @example 1
     * @enum {number}
     */
    SkillComboCategory: 1 | 2 | 3;
    ClubPlayerTag: {
      /**
       * @description | Rare | index | Color|
       * |--|:---:|:---:|
       * | SSR| 3   | 黄 |
       * | SR | 2   | 蓝 |
       * | F  | 4  | 棕 |
       * | N  | 1  | 红 |
       *
       * @example N
       * @enum {string}
       */
      rare?: "SSR" | "SR" | "F" | "N";
      /**
       * @description 标签文字
       * @example 官方认证选手
       */
      name?: string;
    };
    ApplyPersonInfo: {
      /**
       * @description urs
       * @example <EMAIL>
       */
      urs?: string;
      /** @example 24017600001 */
      roleid?: number;
      /** @example nickname */
      nickname?: string;
      /** @example 1 */
      expertClazz?: number;
      /** @example avatar.png */
      avatar?: string;
      /** @example realName */
      realName?: string;
      /** @example 33050122121024 */
      idCard?: string;
      /** @example 1 */
      gender?: number;
      /** @example <EMAIL> */
      email?: string;
      /** @example 13751239012 */
      phone?: number;
      /**
       * @description 短信验证码
       * @example 0513
       */
      smsCode?: string;
    };
    ApplyOperator: components["schemas"]["ApplyPersonInfo"] & {
      images?: string[];
      videos?: string[];
    };
    MarriageInfo: {
      /** @example 1234567890 */
      marriageInfoId: number;
      /** @example marrageInfo */
      marriageInfo?: string;
      /** @example marrageInfo */
      marriageMemory?: string;
      /**
       * @description 时间戳(ms)
       * @example 1661918550231
       */
      updateTime: number;
    };
    ApplyCommander: components["schemas"]["ApplyPersonInfo"] & {
      videos?: string[];
    };
    QueryRealNameAndAntiIndulgence: {
      /**
       * @description urs接口返回码
       * @example 0
       */
      code?: number;
      data?: {
        /**
         * @description 标记用户是否填写证件号码、真实姓名和联系电话；位顺序右起，从0开始：
         * - 0 填写证件号码标记：1为填写，0为未填写；
         * - 1 填写真实姓名标记：1为填写，0为未填写；
         * - 2 填写联系电话标记：1为填写，0为未填写；
         *
         * @example 1
         * @enum {number}
         */
        realname_flag?: 0 | 1 | 2;
        /**
         * @description 按位表示，每一位的含义如下(右起)
         * - 0 是否登记了实名制认证信息，如果为0则不返回id和reg_time
         * - 1 是否未成年
         * - 2 是否已经过公安机关验证
         * - 3 已经过公安机关验证的，是否通过了验证
         * - 4 公安认证不通过的原因，1: 号码、姓名不匹配 0: 号码不存在
         *     - 5 1: 用户提交了证件复印件 0：没有提交
         *     - 6 1: 复印件审核通过 0: 没有通过
         *     - 7 1: 已经审核完成 0: 还未审核
         *
         * @example 127
         */
        realname_status?: number;
        /**
         * @description 身份证号码，用于游戏中控制使用同一个身份证的不同帐号的游戏时间
         * @example 120104200301010398
         */
        id?: string;
        /**
         * @description 登记实名制信息的时间，精确到秒的长整型时间戳
         * @example 1615963314
         */
        reg_time?: number;
        /**
         * @description 修改实名登记状态时间，精确到秒的长整型时间戳（经过公安机关验证才返回此字段）
         * @example 1615963314
         */
        update_time?: number;
      };
    };
    QueryGameIdByUrs: {
      /** @example 200 */
      code: number;
      /** @example 0 */
      subcode: number;
      /** @example ok */
      status: string;
      /** @example HV9vzAnmtRPOstA//Pop0gq19Od1LLNODfrnkJM+ZYllbqHEnIwmefzVj6t1Alr593fBC/jCLmLqrjB0YkHlg6JyesY9oZtLZnkB6WQ6H9bDr8wGi/NQaoar6oi7m7Gbz028Gln7bdb3YfCGpchVGQ== */
      data: string;
    };
    /**
     * @example {
     *   "isOk": true
     * }
     */
    ApiAction: {
      /**
       * @description 操作是否正常
       * @example true
       */
      isOk: boolean;
      [key: string]: unknown;
    };
    BanAccountShow: {
      /**
       * @description 返回值,0正常, 其他皆为异常
       * @example <EMAIL>
       */
      urs: string;
      /**
       * @description 是否被封禁
       * @example true
       */
      isBanned: boolean;
      /**
       * @description 封禁截止时间(单位ms)
       * @example *************
       */
      banTime: number;
    };
    ForceEventAddRet: {
      /** @example 3 */
      id?: number;
      /**
       * @description 大事件保存时间
       * @example *************
       */
      createTime?: number;
    };
    ApiOk: {
      /** @description 返回值,0正常, 其他皆为异常 */
      code: number;
      /**
       * @description 具体返回的数据对象
       * @example {}
       */
      data: {
        [key: string]: unknown;
      };
    };
    UrsApiRet: {
      /**
       * @description 返回码
       * @example 200
       */
      retCode?: number;
      /**
       * @description 返回说明
       * @example Ok and more information returned.
       */
      retDesc?: string;
      /**
       * @description 具体返回的数据对象
       * @example null
       */
      result?: Record<string, never>;
    };
    ClubCpDetail: {
      /** @description 角色Id */
      roleId?: number;
      /** @description 头像 */
      avatar?: string;
      /** @description 荣誉点 */
      honor?: number;
      /** @description 昵称 */
      nickname?: string;
      /** @description 名次 */
      rank?: number;
      /** @description 擅长职业 */
      expertClazz?: number;
      /** @description 标签 */
      tags?: components["schemas"]["ClubPlayerTag"][];
      /** @description 俱乐部相关个人荣誉 */
      clubHonors?: string[];
      /**
       * @description 是否认证指挥
       * @example false
       */
      isCommander?: boolean;
      /**
       * @description 是否认证高手
       * @example false
       */
      isOperator?: boolean;
    };
    ShowWishListItem: {
      id: number;
      roleId: number;
      type: string;
      payload: {
        commodityId: number;
        commodityIndex: number;
        itemId: number;
        shopId: number;
      };
      createTime: number;
    };
    /** CopyMomentsRes */
    CopyMomentsRes: {
      /**
       * @description 复制的动态数量
       * @example 10
       */
      count: number;
    };
    /**
     * @description 邮箱类型
     * @example inbox
     * @enum {string}
     */
    NoteMailBoxType: "inbox" | "outbox";
    NoteMailSendAppend: {
      /**
       * @description 邮件id
       * @example 1
       */
      id: number;
    };
    NoteMailInboxExtra: {
      /**
       * @description 邮件id
       * @example 1
       */
      id: number;
      /**
       * @description 发件人角色名
       * @example 法外狂徒张三
       */
      senderName: string;
      /**
       * @description 收件人角色名
       * @example 王小美
       */
      receiverRoleName: string;
      /**
       * @description 是否收藏
       * @example true
       */
      star: boolean;
      /**
       * @description 收藏时间(ms)
       * @example 1625812450785
       */
      starTime: number;
      /**
       * @description 发送时间(ms)
       * @example 1625812450785
       */
      sendTime?: number;
      /**
       * @description 是否已读
       * @example true
       */
      isRead: boolean;
    };
    NoteMailSend: {
      /**
       * @description 写信人Id
       * @example 1001
       */
      sender: number;
      /**
       * @description 收信人Id
       * @example 1002
       */
      receiver: number;
      /**
       * @description 收件人昵称
       * @example 收件人昵称
       */
      receiverName: string;
      /**
       * @description 文本
       * @example 便签内容
       */
      content: string;
      /**
       * @description 邮箱落款签名
       * @example 邮箱落款签名
       */
      signature: string;
      /**
       * @description 发送时间（单位ms)
       * @example 1625807170708
       */
      sendTime: number;
      /**
       * @description 便签样式
       * @example 1
       */
      style: number;
    };
    ServerAnnalFengyunPlayer: {
      /** @example 1 */
      id: number;
      /**
       * @description 排名
       * @example 1
       */
      rank: number;
      /**
       * @description 玩家id
       * @example 38104100237
       */
      roleId: number;
      /**
       * @description 玩家名字
       * @example 一叶知秋
       */
      roleName: string;
      /**
       * @description 服务器id
       * @example 1001
       */
      serverId: number;
      guild: components["schemas"]["GuildInfo"];
      /** @example 100 */
      score: number;
    };
    GuildInfo: {
      /**
       * @description 帮会id
       * @example 1
       */
      id: number;
      /**
       * @description 帮会名字
       * @example 四海为家
       */
      name: string;
    };
    /**
     * SyncServerAnnalEvent
     * @description 游戏服务器同步的编年史事件
     */
    SyncServerAnnalEvent: {
      /**
       * @description 建议生成一个uuid追踪作为每次的事件唯一标记
       * @example 14f8071127d644d3a8eceae7c638fe94
       */
      uuid: string;
    } & components["schemas"]["BaseServerAnnalEvent"];
    /** @description 游戏服务器同步的个人编年史事件 */
    SyncPlayerAnnalEvent: {
      /**
       * @description 建议生成一个uuid追踪作为每次的事件唯一标记
       * @example 14f8071127d644d3a8eceae7c638fe94
       */
      uuid: string;
    } & components["schemas"]["BasePlayerAnnalEvent"];
    ServerAnnalScore: {
      /**
       * @description 积分
       * @example 80
       */
      score: number;
    };
    WeekRenQi: {
      /**
       * @description 人气值
       * @example 10
       */
      renqi: number;
    };
    GbSuperAssLimitFcmRuleReq: {
      /**
       * Format: float
       * @description 单次消费限额，单位元；限额范围：[0, 999999999]
       */
      create_order_limit?: number;
      /**
       * Format: float
       * @description 月消费限额，单位元；限额范围：[0, 999999999]
       */
      month_sum_limit?: number;
      /**
       * @description 币种，暂时只支持CNY，不传默认使用CNY
       * @enum {string}
       */
      currency?: "CNY";
      /**
       * Format: float
       * @description 工作日可在线时长，单位精确到秒
       */
      online_time_limit?: number;
      /**
       * Format: float
       * @description 节假日可在线时长，单位精确到秒
       */
      holiday_online_time_limit?: number;
      /**
       * Format: time
       * @description 宵禁开启时间，格式为"hh:mm:ss"；如19:00:00，表示从19:00:00到当天23:59:59为宵禁时间
       */
      curfew_start_time?: string;
      /**
       * Format: time
       * @description 宵禁结束时间，格式为"hh:mm:ss"；如06:00:00，表示从00:00:00到当天06:00:00为宵禁时间
       */
      curfew_end_time?: string;
      /**
       * Format: date
       * @description 设置规则生效时间，格式为"YYYY-mm-dd"；如2021-04-04，表示生效时间为2021-04-04 00:00:00
       */
      effective_time: string;
      /**
       * Format: date
       * @description 设置规则过期时间，格式为"YYYY-mm-dd"；如2021-05-05，表示过期时间为2021-05-05 00:00:00；不传则默认不过期
       */
      expired_time?: string;
      /** @description 被限制时的提示语，提示语应提及所有被限制的参数项，提示信息长度不超过130个中文字符 */
      aas_msg: string;
      /**
       * @description 家长守护平台：不传/传0 运营限制：传1
       * @enum {integer}
       */
      source: 0 | 1;
    };
    GmServerListMergeServerItem: {
      /**
       * @description 最终合服id
       * @example 123
       */
      to: number;
      /** @description 被合服的服务器id列表 */
      from_list: number[];
    };
    PaginationMeta: {
      /** @example 1 */
      curPage: number;
      /** @example 2 */
      totalPage: number;
      /** @example 20 */
      totalCount: number;
    };
    NoteMailInboxShow: components["schemas"]["NoteMailInboxExtra"] & components["schemas"]["NoteMailSend"];
    NoteMailInBoxList: {
      list?: components["schemas"]["NoteMailInboxShow"][];
      meta?: components["schemas"]["PaginationMeta"];
    };
    /**
     * @description | id | desc                                 |
     * | -- | --                                   |
     * | 1  | 我发布的照片被点赞                   |
     * | 2  | 我发布的照片被评论                   |
     * | 3  | 别人发布照片时，把我设置为“标记用户” |
     * | 4  | 我的评论被回复                       |
     * | 5  | 我的照片被管理员删除                 |
     * | 6  | 我的照片上了“精选墙”。
     *
     * @example 1
     * @enum {number}
     */
    GuildPhotoWallNotificationType: 1 | 2 | 3 | 4 | 5 | 6;
    GuildPhotoWallCommentItem: {
      /**
       * @description 评论id
       * @example 1
       */
      id: number;
      /**
       * @description 评论的照片id
       * @example 2
       */
      photoId: number;
      /**
       * @description 评论人的角色id
       * @example 1004
       */
      roleId: number;
      /**
       * @description 角色名
       * @example 李四
       */
      roleName: string;
      /**
       * @description 通知文本 评论和回复类通知会附带评论文本
       * @example 照片真好看
       */
      text: string;
      /**
       * @description 回复的人的角色id
       * @example 1004
       */
      replyId?: number;
      /**
       * @description 回复的人角色名
       * @example 李四
       */
      replyName?: string;
      /**
       * @description 点赞数
       * @example 10
       */
      likeCount: number;
      /**
       * @description 是否点赞过
       * @example false
       */
      isLiked: boolean;
      /**
       * @description 评论时间
       * @example 1630033216732
       */
      createTime: number;
      /**
       * @description 是否可以评论
       * @example 1
       */
      canComment?: number;
    };
    ShowRoleInfo: {
      /**
       * @description 角色名字
       * @example zhangsan
       */
      roleName: string;
      /**
       * @description 服务器id
       * @example 12
       */
      server: number;
      /**
       * @description 职业id
       * @example 100100001
       */
      jobId: number;
      /**
       * @description 性别
       * @example 1
       */
      gender: number;
      /**
       * @description 等级
       * @example 20
       */
      level: number;
      /**
       * @description 头像框
       * @example 10
       */
      frameId: number;
    };
    GuildPhotoWallNotificationItem: {
      /**
       * @description 通知id
       * @example 1
       */
      id: number;
      /**
       * @description 通知发起人, 比如点赞就是点赞的人,评论就是评论的人(无发起人此时为0, 比如入选精选墙)
       * @example 1004
       */
      roleId: number;
      /**
       * @description 接受通知的人
       * @example 100100001
       */
      targetId: number;
      type: components["schemas"]["GuildPhotoWallNotificationType"];
      /**
       * @description 通知文本 评论和回复类通知会附带评论文本
       * @example 照片真好看
       */
      text?: string;
      /**
       * @description 和照片相关的通知输出照片id
       * @example 1
       */
      photoId?: number;
      /**
       * @description 和照片相关的通知输出照片url(用于通知处预览照片)
       * @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2021/08/26/0F1F9217F0F1F1629944290.jpg
       */
      photoUrl?: string;
      /**
       * @description 和照片墙相关的通知会输出照片墙id
       * @example 10
       */
      wallId?: number;
      /**
       * @description 通知状态 0=>未读, 1=>已读
       * @example 1
       */
      status: number;
      /**
       * @description 通知产生的时间
       * @example 1630033216732
       */
      createTime: number;
    } & components["schemas"]["ShowRoleInfo"];
    GuildPhotoWallNotificationList: {
      list: components["schemas"]["GuildPhotoWallNotificationItem"][];
      meta: components["schemas"]["PaginationMeta"];
    };
    GuildPhotoWallCommentList: {
      list: components["schemas"]["GuildPhotoWallCommentItem"][];
      meta: components["schemas"]["PaginationMeta"];
    };
    /**
     * @description | id | desc                                 |
     * | -- | --                                   |
     * | 1  | 我发布的照片被点赞                   |
     * | 2  | 我发布的照片被评论                   |
     * | 3  | 别人发布照片时，把我设置为“标记用户” |
     * | 4  | 我的评论被回复                       |
     * | 5  | 我的照片被管理员删除                 |
     * | 6  | 我的照片上了“精选墙”。
     *
     * @example 1
     * @enum {number}
     */
    MultiGardenPhotoWallNotificationType: 1 | 2 | 3 | 4 | 5 | 6;
    MultiGardenPhotoWallNotificationItem: {
      /**
       * @description 通知id
       * @example 1
       */
      id: number;
      /**
       * @description 通知发起人, 比如点赞就是点赞的人,评论就是评论的人(无发起人此时为0, 比如入选精选墙)
       * @example 1004
       */
      roleId: number;
      /**
       * @description 接受通知的人
       * @example 100100001
       */
      targetId: number;
      type: components["schemas"]["MultiGardenPhotoWallNotificationType"];
      /**
       * @description 通知文本 评论和回复类通知会附带评论文本
       * @example 照片真好看
       */
      text?: string;
      /**
       * @description 和照片相关的通知输出照片id
       * @example 1
       */
      photoId?: number;
      /**
       * @description 和照片相关的通知输出照片url(用于通知处预览照片)
       * @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2021/08/26/0F1F9217F0F1F1629944290.jpg
       */
      photoUrl?: string;
      /**
       * @description 和照片墙相关的通知会输出照片墙id
       * @example 10
       */
      wallId?: number;
      /**
       * @description 通知状态 0=>未读, 1=>已读
       * @example 1
       */
      status: number;
      /**
       * @description 通知产生的时间
       * @example 1630033216732
       */
      createTime: number;
    } & components["schemas"]["ShowRoleInfo"];
    MultiGardenPhotoWallNotificationList: {
      list: components["schemas"]["MultiGardenPhotoWallNotificationItem"][];
      meta: components["schemas"]["PaginationMeta"];
    };
    MultiGardenPhotoWallCommentList: {
      list: components["schemas"]["GuildPhotoWallCommentItem"][];
      meta: components["schemas"]["PaginationMeta"];
    };
    /** @description 批量操作精选状态 */
    MomentPickBatchOp: {
      /** @description 动态ids */
      ids: number[];
      /** @example true */
      isPick: boolean;
    };
    /** @description 上传图片相关信息 */
    PhotoWallPhotoAdd: {
      /**
       * @description 作者的一句话描述
       * @example 作者发照片想说的话， 作者发照片想说的话
       */
      text?: string;
      /**
       * @description 图片url
       * @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
       */
      url: string;
      /**
       * @description 图片在墙的槽位
       * @example 2
       */
      slot: number;
      /** @description 文本中at的玩家id列表 */
      atRoleIds?: number[];
    };
    /** @description 上传图片相关信息 */
    GuildPhotoWallPhotoAdd: {
      /**
       * @description 作者的一句话描述
       * @example 作者发照片想说的话， 作者发照片想说的话
       */
      text?: string;
      /**
       * @description 图片url
       * @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
       */
      url: string;
      /**
       * @description 图片在墙的槽位
       * @example 2
       */
      slot: number;
      /** @description 文本中at的玩家id列表 */
      atRoleIds?: number[];
    };
    PhotoWallAdd: {
      /**
       * @description 每一面照片墙有自己的名字，管理员可进行修改。
       * @example 帮会团战回忆
       */
      name?: string;
      /**
       * @description 模板id
       * @example 1002
       */
      templateId?: number;
      /**
       * @description 照片墙相框id
       * @example 3
       */
      frameId?: number;
      /**
       * @description 照片墙类型 0 => 普通 1 => 精选
       * @example 0
       * @enum {number}
       */
      type?: 0 | 1;
      /**
       * @description 照片墙下标
       * @example 1
       */
      idx?: number;
    };
    PhotoWallShow: {
      /** @example 1 */
      id: number;
      photos: components["schemas"]["GuildPhotoWallPhotoListItem"][];
      /** @example guildName */
      guildName?: string;
    } & components["schemas"]["PhotoWallAdd"];
    GuildPhotoWallShow: {
      /** @example 1 */
      id: number;
      photos: components["schemas"]["GuildPhotoWallPhotoListItem"][];
      /** @example 帮会名字 */
      guildName: string;
    } & components["schemas"]["PhotoWallAdd"];
    GuildPhotoWallBaseItem: {
      /** @example 1 */
      id: number;
      /**
       * @description 0 普通照片墙 1 => 精选照片墙
       * @example 0
       */
      type: number;
      /**
       * @description 照片墙开启时间
       * @example *************
       */
      createTime: number;
    };
    GuildPhotoWallList: ({
        /** @example 1 */
        id: number;
      } & components["schemas"]["GuildPhotoWallBaseItem"] & components["schemas"]["PhotoWallAdd"])[];
    GuildPhotoWallPhoto: components["schemas"]["GuildPhotoWallPhotoListItem"] & components["schemas"]["GuildPhotoWallPhotoActionData"] & {
      /**
       * @description 是否可以评论
       * @example 1
       */
      canComment?: number;
    };
    /** @description 上传图片相关信息 */
    MultiGardenPhotoWallPhotoAdd: {
      /**
       * @description 作者的一句话描述
       * @example 作者发照片想说的话， 作者发照片想说的话
       */
      text?: string;
      /**
       * @description 图片url
       * @example http://hi-163-nsh.nosdn.127.net/dynamicPicture/2018/12/13/0F201F7177F0F1F1544667574.png
       */
      url: string;
      /**
       * @description 图片在墙的槽位
       * @example 2
       */
      slot: number;
      /** @description 文本中at的玩家id列表 */
      atRoleIds?: number[];
    };
    MultiGardenPhotoWallAdd: {
      /**
       * @description 每一面照片墙有自己的名字，管理员可进行修改。
       * @example 联居回忆
       */
      name?: string;
      /**
       * @description 模板id
       * @example 1002
       */
      templateId?: number;
      /**
       * @description 照片墙相框id
       * @example 3
       */
      frameId?: number;
      /**
       * @description 照片墙类型 0 => 普通 1 => 精选
       * @example 0
       * @enum {number}
       */
      type?: 0 | 1;
      /**
       * @description 照片墙下标
       * @example 1
       */
      idx?: number;
    };
    MultiGardenPhotoWallShow: {
      /** @example 1 */
      id: number;
      photos: components["schemas"]["MultiGardenPhotoWallPhotoListItem"][];
      /** @example 联居名字 */
      multiGardenName: string;
    } & components["schemas"]["MultiGardenPhotoWallAdd"];
    MultiGardenPhotoWallBaseItem: {
      /** @example 1 */
      id: number;
      /**
       * @description 0 普通照片墙 1 => 精选照片墙
       * @example 0
       */
      type: number;
      /**
       * @description 照片墙开启时间
       * @example *************
       */
      createTime: number;
    };
    MultiGardenPhotoWallList: ({
        /** @example 1 */
        id: number;
      } & components["schemas"]["MultiGardenPhotoWallBaseItem"] & components["schemas"]["MultiGardenPhotoWallAdd"])[];
    MultiGardenPhotoWallPhoto: components["schemas"]["MultiGardenPhotoWallPhotoListItem"] & components["schemas"]["MultiGardenPhotoWallPhotoActionData"];
    MomentPickItem: {
      /**
       * @description 动态id
       * @example 1
       */
      id: number;
      /** @example 2339902297 */
      roleId: number;
      /** @example 稀饭 */
      roleName: string;
      /** @example <link button=寻找每日小锦鲤,PSHotTalk,105> */
      text: string;
      imgList: string[];
      /** @example 0 */
      likeCount: number;
      /** @example 0 */
      commentCount: number;
      /**
       * @description 是否入选
       * @example false
       */
      isPicked: boolean;
      /**
       * @description 入选时间
       * @example *************
       */
      pickTime: number;
    };
    MomentPickList: components["schemas"]["MomentPickItem"][];
    /**
     * @description | id | desc     |
     * | -- | ----     |
     * | -1 | 审核拒绝 |
     * | 0  | 审核中   |
     * | 1  | 审核通过 |
     *
     * @example 1
     * @enum {number}
     */
    EAuditStatus: -1 | 0 | 1;
    GuildPhotoWallPhotoListItem: {
      /**
       * @description id
       * @example 21
       */
      id: number;
      /**
       * @description 照片id
       * @example 21
       */
      photoId: number;
      /**
       * @description 墙id
       * @example 11
       */
      wallId: number;
      /**
       * @description 照片墙的帮会id
       * @example 11
       */
      guildId: number;
      /**
       * @description 上传图片的角色id
       * @example 100100001
       */
      roleId: number;
      /**
       * @description 作者昵称
       * @example 闫瑜
       */
      roleName: string;
      /**
       * @description 是否点赞过
       * @example false
       */
      isLiked: boolean;
      auditStatus: components["schemas"]["EAuditStatus"];
      /**
       * @description 图片发布时间
       * @example *************
       */
      createTime: number;
      /**
       * @description 是否入选了精选墙
       * @example false
       */
      isPicked: boolean;
      /**
       * @description 是否可以评论
       * @example 1
       */
      canComment?: number;
    } & components["schemas"]["GuildPhotoWallPhotoAdd"];
    GuildPhotoWallPhotoActionData: {
      /**
       * @description 点赞数
       * @example 8
       */
      likeCount: number;
      /**
       * @description 评论数
       * @example 10
       */
      commentCount: number;
    };
    MultiGardenPhotoWallPhotoListItem: {
      /**
       * @description id
       * @example 21
       */
      id: number;
      /**
       * @description 照片id
       * @example 21
       */
      photoId: number;
      /**
       * @description 墙id
       * @example 11
       */
      wallId: number;
      /**
       * @description 照片墙的联居id
       * @example 060000000001162400020000C6EF0A5D
       */
      multiGardenId: string;
      /**
       * @description 上传图片的角色id
       * @example 100100001
       */
      roleId: number;
      /**
       * @description 作者昵称
       * @example 闫瑜
       */
      roleName: string;
      /**
       * @description 是否点赞过
       * @example false
       */
      isLiked: boolean;
      auditStatus: components["schemas"]["EAuditStatus"];
      /**
       * @description 图片发布时间
       * @example *************
       */
      createTime: number;
      /**
       * @description 是否入选了精选墙
       * @example false
       */
      isPicked: boolean;
    } & components["schemas"]["GuildPhotoWallPhotoAdd"];
    MultiGardenPhotoWallPhotoActionData: {
      /**
       * @description 点赞数
       * @example 8
       */
      likeCount: number;
      /**
       * @description 评论数
       * @example 10
       */
      commentCount: number;
    };
    GetCoupleInfoData: {
      /**
       * @description 存在侠侣关系
       * @example true
       */
      has: boolean;
      /**
       * @description 侠侣id
       * @example 100245
       */
      coupleId?: number;
      /**
       * @description 侠侣名字
       * @example 过儿
       */
      coupleName?: string;
    };
    CloudGameDurationShowData: components["schemas"]["CloudGameDurationData"] & {
      yuanbao?: components["schemas"]["CloudGameMonthYuanBao"];
    } & {
      monthCard?: components["schemas"]["CloudGameMonthCardData"];
    };
    CloudGameDurationChangeData: components["schemas"]["CloudGameDurationData"] & components["schemas"]["CloudGameDurationDeltaData"];
    CloudGameNotifyYuanbaoChangeData: components["schemas"]["CloudGameDurationChangeData"] & {
      yuanbao?: components["schemas"]["CloudGameMonthYuanBao"];
    };
    CloudGameDurationDeltaData: {
      /**
       * @description 是否新增/扣除成功
       * @example true
       */
      isChange: boolean;
      /**
       * @description 当日时长变化量(单位s)
       * @example 60
       */
      dailyDurationDelta: number;
      /**
       * @description 永久时长变化量(单位s)
       * @example -60
       */
      permanentDurationDelta: number;
    };
    CloudGameMonthCardData: {
      /**
       * @description 月卡状态 (0=>从未开通, 1 => 开通正常, 2 => 开通已过期)
       * @example 0
       * @enum {number}
       */
      status: 0 | 1 | 2;
      /**
       * @description 月卡过期时间
       * @example 1662249600
       */
      expireAt: number;
    };
    CloudGameDurationData: {
      /**
       * @description 通行证账号
       * @example <EMAIL>
       */
      urs: string;
      /**
       * @description 今日字符串
       * @example 2022-01-05
       */
      ds?: string;
      /**
       * @description 剩余当日时长(单位s)
       * @example 300
       */
      dailyDuration: number;
      /**
       * @description 剩余永久时长(单位s)
       * @example 600
       */
      permanentDuration: number;
    };
    /** @description 本月元宝充值以及奖励时长 */
    CloudGameMonthYuanBao: {
      /**
       * @description 月度标记
       * @example 2022-01
       */
      month: string;
      /**
       * @description 充值元宝数量
       * @example 30
       */
      chargeNum: number;
      /**
       * @description 奖励永久时长(单位s)
       * @example 300
       */
      awardDuration: number;
    };
    GetHonorList: {
      code: number;
      data: {
        list: {
            /** @description 策划配置的荣誉id, 如 10000060, 可以通过这个id在策划表里所有该赛事信息 */
            ID: number;
            /** @description 玩家绑定荣誉后生成的id, 存在梦岛后db里的id */
            HonorId: number;
            /** @description 第几届 */
            WhichTimes: number;
            /** @description 显示届数的名字(是用策划表名字) */
            ShowWhichTimes: string;
            /** @description 是否使用设置的届数名字(0和1, 1的时候需要使用ShowWhichTimes字段) */
            UseName2: number;
            /**
             * @description 荣誉名次类型 (是指策划HonorPlayers.xlsx 这个配表的名次那一列的值)
             * @example 32
             */
            HonorType: number;
            /** @description 比赛时间戳(ms) */
            Date: number;
            /** @description 赛事类型id, 在 Mengdao.xlsm策划表的 CompetitionType的这个表格 */
            CompetitionId: number;
            /** @description 组别(比如风云可待组, 有些比赛类型是比赛) */
            GroupName: string;
          }[];
      };
    };
    SkillComboStat: {
      /** @description 点赞数 */
      likeCnt: number;
      /** @description 热度 */
      hot: number;
    };
    SkillComboUserAction: {
      /**
       * @description 是否点赞
       * @example false
       */
      isLiked: boolean;
      /**
       * @description 是否收藏
       * @example false
       */
      isCollected: boolean;
    };
    SkillComboPlayerExtend: components["schemas"]["AppearancePaint"] & {
      /**
       * @description 角色名字
       * @example roleName
       */
      roleName: string;
      /**
       * @description 角色职业
       * @example 1
       */
      jobId: number;
      /**
       * @description 角色性别
       * @example 0
       */
      gender: number;
      /**
       * @description 角色性别
       * @example 0
       */
      subGender: number;
    };
    GardenPhotoUnitAddBody: {
      /**
       * @description 图片地址
       * @example https://hi-163-nsh.nos-jd.163yun.com/nsh/skill_combo/skill_combo_1.meta
       */
      url: string;
      /**
       * @description 物品模板id
       * @example 10
       */
      itemTemplateId: number;
    };
    AppearancePaint: {
      /** @example 44731002 */
      headPaintId?: number;
      /** @example 44732002 */
      bodyPaintId?: number;
    };
    SkillComboRecord: {
      /**
       * @description 上传玩家id
       * @example 24017600001
       */
      roleId: number;
      /**
       * @description 技能组合数据nos地址
       * @example https://hi-163-nsh.nos-jd.163yun.com/nsh/skill_combo/skill_combo_1.meta
       */
      url: string;
      /**
       * @description 套路名字
       * @example 套路名字7个字
       */
      name: string;
      /**
       * @description 策划配表的标签id列表
       * @example [
       *   1,
       *   2
       * ]
       */
      tagIds: number[];
      /**
       * @description 技能描述
       * @example 对具体技能组合的特点介绍和描述
       */
      desc: string;
      /**
       * @description |val|desc|
       * |--|--|
       * |1|论武套路|
       * |2|帮战套路|
       * |3|副本套路|
       *
       * @example 1
       * @enum {number}
       */
      category: components["schemas"]["SkillComboCategory"];
      /**
       * @description 职业id
       * @example 10
       */
      jobId: number;
      /**
       * @description 技能推荐要按照服务器分组分开, 同属一个推荐区域使用同一个region
       * @example 1
       */
      region: number;
    };
    SkillComboAddRet: {
      /**
       * @description 技能推荐组合id
       * @example 1
       */
      id: number;
    };
    SkillComboListRet: {
      /** @description 技能推荐组合列表 */
      list: components["schemas"]["SkillComboGetRet"][];
      /**
       * @description 技能组合数量
       * @example 100
       */
      count: number;
    };
    SkillComboGetRet: components["schemas"]["SkillComboAddRet"] & components["schemas"]["SkillComboRecord"] & components["schemas"]["SkillComboPlayerExtend"] & components["schemas"]["SkillComboStat"] & components["schemas"]["SkillComboUserAction"];
    DamageStatRecord: {
      /**
       * @description 玩家id
       * @example 24017600001
       */
      playerId: number;
      /**
       * @description 游戏副本id
       * @example 51001777
       */
      gamePlayId: number;
      /**
       * @description 因为这边需求是副本里的每个boss单独统计，所以这里的参数应该要加一个bossId
       * @example 100
       */
      bossId: number;
      /**
       * @description 记录时间(ms)
       * @example 1620628236384
       */
      recordTime: number;
      /**
       * @description 游戏序列化的二进制数据(base64编码)
       * @example ZW5jb2RlR2FtZURhdGFTdHI=
       */
      recordStr: string;
      /**
       * @description 每秒伤害
       * @example 1234
       */
      hurtPerSec: number;
      /**
       * @description 每秒治疗
       * @example 1234
       */
      curePerSec: number;
    };
    ForceEventAdd: {
      /**
       * @description 需要生成一个uuid追踪作为每次的事件唯一标记
       * @example 14f8071127d644d3a8eceae7c638fe94
       */
      uuid: string;
      /**
       * @description 服务器id
       * @example 12
       */
      serverId: number;
      /**
       * @description 势力id
       * @example 13
       */
      forceId: number;
      /**
       * @description 事件分类下的具体类型id, 用来映射策划表的事件文本
       * @example 10
       */
      eventType: number;
      /**
       * @description 事件发生的事件(单位ms)
       * @example 1620628236384
       */
      eventTime: number;
      eventArgs?: components["schemas"]["MayBeForceEventArgs"];
    };
    BustPhotoAddRet: {
      /**
       * @description 记录id
       * @example 1
       */
      id: number;
    };
    GardenPhotoUnitAddRet: {
      /**
       * @description 记录id
       * @example 1
       */
      id: number;
    };
    DamageStatAddRet: {
      /**
       * @description 数据id
       * @example 1
       */
      id: number;
      /**
       * @description 分享的时候我要给别人拼一个能唯一定位到某条记录的方式
       * @example 1TYRZY6oFUI8syatJ877E
       */
      shareId?: string;
    };
    DamageStatItem: components["schemas"]["DamageStatAddRet"] & components["schemas"]["DamageStatRecord"];
    GardenPhotoUnitItem: components["schemas"]["GardenPhotoUnitAddRet"] & {
      /** @example 24017600001 */
      roleId?: number;
    } & components["schemas"]["GardenPhotoUnitAddBody"];
    BustPhotoItem: components["schemas"]["BustPhotoAddRet"] & {
      /**
       * @description 玩家角色id
       * @example 24017600001
       */
      roleId: number;
      /**
       * @description 半身像位置索引(1-20， 1个玩家最多20张位置)
       * @example 10
       */
      index: number;
      /**
       * @description 半身像图片链接
       * @example https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
       */
      url: string;
    };
    AppearancePaintGetRet: components["schemas"]["AppearancePaint"] & {
      /** @description 角色id */
      roleId?: number;
    };
    ForceEventRemark: {
      /**
       * @description 史官评注文本
       * @example 史官评注文本
       */
      remark: string;
      /**
       * @description 史官评注时间
       * @example *************
       */
      remarkTime: number;
      /**
       * @description 评论的史官角色id
       * @example 1001
       */
      remarkRoleId: number;
      /**
       * @description 评论的史官名字
       * @example 评论的史官名字
       */
      remarkRoleName: string;
    };
    ForceEventtat: {
      /**
       * @description 点赞数
       * @example 1
       */
      likeCount: number;
    };
    ForceEventAction: {
      /**
       * @description 是否点赞
       * @example false
       */
      isLiked: boolean;
    };
    TimesSquarePhoto: {
      /**
       * @description 图片链接
       * @example https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
       */
      url: string;
      /**
       * @description 图片位置索引
       * @example 1
       */
      index: number;
    };
    MemeAdd: {
      /** @example 24017600001 */
      roleid?: number;
      /** @example https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg */
      url?: string;
    };
    /**
     * @description 行星ID（英文枚举值）。ID与行星的对应关系如下：
     * | ID | 行星 |
     * |----|------|
     * | sun | 太阳 |
     * | moon | 月亮 |
     * | mercury | 水星 |
     * | venus | 金星 |
     * | mars | 火星 |
     * | jupiter | 木星 |
     * | saturn | 土星 |
     * | uranus | 天王星 |
     * | neptune | 海王星 |
     * | pluto | 冥王星 |
     * | southNode | 南郊点 |
     * | northNode | 北郊点 |
     * @enum {string}
     */
    planet: "sun" | "moon" | "mercury" | "venus" | "mars" | "jupiter" | "saturn" | "uranus" | "neptune" | "pluto" | "southNode" | "northNode";
    /**
     * @description 星座ID（英文枚举值）。ID与星座的对应关系如下：
     * | ID | 星座 |
     * |----|------|
     * | aries | 白羊座 |
     * | taurus | 金牛座 |
     * | gemini | 双子座 |
     * | cancer | 巨蟹座 |
     * | leo | 狮子座 |
     * | virgo | 处女座 |
     * | libra | 天秤座 |
     * | scorpio | 天蝎座 |
     * | sagittarius | 射手座 |
     * | capricorn | 摩羯座 |
     * | aquarius | 水瓶座 |
     * | pisces | 双鱼座 |
     * @enum {string}
     */
    constellation: "aries" | "taurus" | "gemini" | "cancer" | "leo" | "virgo" | "libra" | "scorpio" | "sagittarius" | "capricorn" | "aquarius" | "pisces";
    /**
     * @description 宫位ID。ID与宫位的对应关系如下：
     * | ID | 宫位 |
     * |----|------|
     * | 1  | 第一宫命宫 |
     * | 2  | 第二宫财运宫 |
     * | 3  | 第三宫兄弟宫 |
     * | 4  | 第四宫家庭宫 |
     * | 5  | 第五宫子女宫 |
     * | 6  | 第六宫健康宫 |
     * | 7  | 第七宫夫妻宫 |
     * | 8  | 第八宫疾厄宫 |
     * | 9  | 第九宫迁移宫 |
     * | 10 | 第十宫事业宫 |
     * | 11 | 第十一宫人际宫 |
     * | 12 | 第十二宫精神宫 |
     */
    house: number;
    bazaar_dice: {
      planet: components["schemas"]["planet"];
      constellation: components["schemas"]["constellation"];
      house: components["schemas"]["house"];
    };
    bazaar_post_add: {
      /**
       * Format: int64
       * @description 话题ID
       * @example 1
       */
      topicId: number;
      /**
       * @description 话题文本
       * @example 爱情运势
       */
      topicText: string;
      /**
       * @description 用户选择或输入的占卜问题。
       * @example #今日困惑 考研初试分数怎么样?
       */
      question: string;
      dice: components["schemas"]["bazaar_dice"];
      /**
       * @description 是否同步到朋友圈
       * @example true
       */
      isSyncMoment: boolean;
    };
    bazaar_post_add_resp: {
      /**
       * Format: int64
       * @description 问惑ID
       * @example 1234567890
       */
      id: number;
    };
    basic_role_info: {
      /**
       * Format: int64
       * @description 角色ID
       * @example 1234567890
       */
      roleId: number;
      /**
       * @description 角色名称
       * @example 星巫
       */
      roleName: string;
      /**
       * Format: int64
       * @description 职业ID
       * @example 1
       */
      jobId: number;
      /**
       * @description 性别
       * @example 1
       */
      gender: number;
      /**
       * @description 子性别
       * @example 1
       */
      subGender: number;
      /**
       * @description 头像ID
       * @example 44731002
       */
      headPaintId: number;
      /**
       * @description 立绘ID
       * @example 44732002
       */
      bodyPaintId: number;
    };
    bazaar_core_user_info: {
      /**
       * Format: int64
       * @description 角色ID
       * @example 1234567890
       */
      roleId: number;
      /**
       * Format: int32
       * @description 性别
       * @example 1
       */
      gender: number;
      /**
       * Format: int32
       * @description 星巫等级
       * @example 1
       */
      astroLevel: number;
      /**
       * Format: int32
       * @description 星巫类型
       * @example 1
       */
      astroType: number;
    };
    bazaar_post_show_item: {
      /**
       * Format: int64
       * @description 问惑ID
       * @example 1
       */
      id: number;
      /**
       * Format: int64
       * @description 角色ID
       * @example 1
       */
      roleId: number;
      /**
       * Format: int64
       * @description 话题ID
       * @example 1
       */
      topicId: number;
      roleInfo: components["schemas"]["basic_role_info"];
      userInfo: components["schemas"]["bazaar_core_user_info"];
      /**
       * @description 用户选择或输入的占卜问题。
       * @example #今日困惑 考研初试分数怎么样?
       */
      question: string;
      dice: components["schemas"]["bazaar_dice"];
      /**
       * Format: int64
       * @description 评论数
       * @example 1
       */
      commentCount: number;
      /**
       * @description 是否可以评论, 当用户已经解析过该帖子并完成评论后时，该字段为false
       * @example true
       */
      canComment: boolean;
      /**
       * Format: int64
       * @description 创建时间
       * @example 1747294491610
       */
      createTime: number;
    };
    bazaar_post_list_resp: {
      list: components["schemas"]["bazaar_post_show_item"][];
      /**
       * Format: integer
       * @description 总数量
       * @example 100
       */
      total: number;
    };
    /**
     * @example {
     *   "title": "占星骰子",
     *   "content": [
     *     {
     *       "title": "骰子解读",
     *       "content": "骰子1：行星骰，冥王星。冥王星象征深度蜕变、隐秘力量与重生，暗示关系中存在潜在的控制欲或重大转折。\\n骰子2：星座骰，双子。双子座代表信息流动、双重性与灵活应变，关系可能呈现多变特质，涉及大量言语交流或心智博弈。\\n骰子3：宫位骰，11宫。11宫关联社群网络、共同理想，说明双方关系可能根植于社交圈层或共享的未来愿景。"
     *     },
     *     {
     *       "title": "总体解读",
     *       "content": "这段关系存在隐秘的双向影响，冥王星在11宫暗示双方可能通过社交活动建立深层羁绊，但双子座的变动性使信任基础易受流言干扰。表面看似轻松的友谊联结，实则暗含信息不对等或理念分歧的风险。"
     *     },
     *     {
     *       "title": "建议",
     *       "content": "主动澄清关键信息，避免群体舆论影响判断。用双子座的幽默感化解紧张，同时建立冥王星式的深度信任契约。关注共同目标而非琐碎争议。"
     *     }
     *   ]
     * }
     */
    bazaar_post_interpret_resp: {
      /**
       * @description 占卜標題
       * @example 占星骰子
       */
      title: string;
      content: {
          /**
           * @description 內容標題
           * @example 骰子解讀
           */
          title: string;
          /**
           * @description 內容詳情
           * @example 骰子1：行星骰，冥王星。冥王星象征深度蜕变、隐秘力量与重生，暗示关系中存在潜在的控制欲或重大转折。\n骰子2：星座骰，双子。双子座代表信息流动、双重性与灵活应变，关系可能呈现多变特质，涉及大量言语交流或心智博弈。\n骰子3：宫位骰，11宫。11宫关联社群网络、共同理想，说明双方关系可能根植于社交圈层或共享的未来愿景。
           */
          content: string;
        }[];
    };
    bazaar_comment_add: {
      /**
       * Format: int64
       * @description 问惑ID
       * @example 1
       */
      postId: number;
      /**
       * @description 作为评论的解析内容
       * @example 解析内容
       */
      text: string;
    };
    bazaar_comment_add_resp: {
      /**
       * Format: int64
       * @description 问惑评论ID
       * @example 1234567890
       */
      id: number;
    };
    /** @description 解析评价 */
    bazaar_rating_add: {
      /**
       * Format: int32
       * @description 打分的评论ID
       * @example 1
       */
      commentId: number;
      /**
       * Format: int32
       * @description 用户评分，1 到 5 之间的整数
       * @example 4
       */
      star: number;
      /** @example 谢谢您的解析，非常准确！ */
      text: string;
    };
    bazaar_rating: components["schemas"]["bazaar_rating_add"] & {
      /**
       * Format: int64
       * @description 评价时间
       * @example 1747294491610
       */
      createTime?: number;
    };
    bazaar_comment_show_item: {
      /**
       * Format: int64
       * @description 评论ID
       * @example 1
       */
      id: number;
      /**
       * Format: int64
       * @description 角色ID
       * @example 1
       */
      roleId: number;
      roleInfo: components["schemas"]["basic_role_info"];
      userInfo: components["schemas"]["bazaar_core_user_info"];
      /**
       * Format: int64
       * @description 问惑ID
       * @example 1
       */
      postId: number;
      /**
       * @description 评论内容
       * @example 评论内容
       */
      text: string;
      /**
       * Format: int64
       * @description 创建时间
       * @example 1747294491610
       */
      createTime: number;
      rating?: components["schemas"]["bazaar_rating"];
    };
    bazaar_comment_list_resp: {
      list: components["schemas"]["bazaar_comment_show_item"][];
      /**
       * Format: integer
       * @description 总数量
       * @example 100
       */
      total: number;
    };
    bazaar_rating_add_resp: {
      /**
       * Format: int64
       * @description 评价id
       * @example 1234567890
       */
      id: number;
      /**
       * Format: int64
       * @description 评价时间
       * @example 1747294491610
       */
      createTime: number;
    };
    /** @description 用户评价 */
    rating_add: {
      /**
       * Format: int64
       * @description 评价来源角色id
       * @example 1234567890
       */
      fromRoleId: number;
      /**
       * Format: int64
       * @description 被评价的角色id
       * @example 1234567890
       */
      toRoleId: number;
      /**
       * Format: int32
       * @description 用户评分，1 到 5 之间的整数
       * @example 4
       */
      star: number;
      /** @example 谢谢您的解析，非常准确！ */
      text: string;
    };
    bazaar_topic_show_item: {
      /**
       * Format: int64
       * @description 话题ID
       * @example 1234567890
       */
      id: number;
      /**
       * @description 话题文本
       * @example 占卜
       */
      text: string;
      /**
       * Format: int64
       * @description 热度
       * @example 100
       */
      hot?: number;
    };
    bazaar_hot_topic_resp: {
      /** @description 热门话题列表 */
      list: components["schemas"]["bazaar_topic_show_item"][];
      /**
       * Format: int64
       * @description 上一次刷新时间
       * @example 1716028800000
       */
      lastRefreshTime: number;
    };
    bazaar_group_divination_report_resp: {
      /**
       * Format: date-time
       * @description 报告对应的日期
       * @example 2023-05-24
       */
      reportDate: string;
      mostFocusedTopic: {
        /**
         * @description 话题ID
         * @example 1234567890
         */
        id: number;
        /**
         * @description 话题文本
         * @example 爱情运势
         */
        text: string;
      };
      /**
       * @description 平均运势分数
       * @example 75.5
       */
      averageFortuneScore: number;
      /**
       * @description 最多回答的问惑
       * @example 怎么才能让我的财运变好？
       */
      mostCommentedQuestion: string;
      /**
       * Format: date-time
       * @description 报告生成时间
       * @example 2023-05-24T20:00:00Z
       */
      generatedAt: string;
    };
    location: {
      /**
       * @description 出生地-省份
       * @example 浙江省
       */
      province: string;
      /**
       * @description 出生地-城市
       * @example 杭州市
       */
      city: string;
      /**
       * @description 出生地-区
       * @example 滨江区
       */
      district: string;
    };
    bazaar_user_register_req: {
      /**
       * @description 玩家性别，e.g. 0男  1女
       * @example 0
       * @enum {number}
       */
      gender: 0 | 1;
      /**
       * Format: date-time
       * @description 出生日期，e.g. 2025-01-01
       * @example 2025-01-01 00:00
       */
      birthTime: string;
      /**
       * Format: int32
       * @description 星巫等级
       * @example 1
       */
      astroLevel: number;
      /**
       * Format: int32
       * @description 星巫类型(游戏配表决定)
       * @example 1
       */
      astroType: number;
      birthPlace: components["schemas"]["location"];
      currentPlace: components["schemas"]["location"];
    };
    bazaar_user_register_res: {
      /**
       * Format: int64
       * @description 角色ID
       * @example 1234567890
       */
      roleId: number;
      /**
       * Format: int64
       * @description 创建时间
       * @example 1547516362459
       */
      createTime: number;
    };
    bazaar_user_info: {
      /**
       * @description 玩家性别，e.g. 0男  1女
       * @example 0
       * @enum {number}
       */
      gender: 0 | 1;
      /**
       * Format: date-time
       * @description 出生日期，e.g. 2025-01-01
       * @example 2025-01-01 00:00
       */
      birthTime: string;
      birthPlace: components["schemas"]["location"];
      currentPlace: components["schemas"]["location"];
      /**
       * Format: int32
       * @description 星巫等级
       * @example 1
       */
      astroLevel: number;
      /**
       * Format: int32
       * @description 星巫类型(游戏配表决定)
       * @example 1
       */
      astroType: number;
    };
    bazaar_user_profile: {
      /**
       * Format: int64
       * @description 角色ID
       * @example 1234567890
       */
      roleId: number;
      roleInfo: components["schemas"]["basic_role_info"];
      userInfo?: components["schemas"]["bazaar_user_info"];
      /**
       * Format: double
       * @description 平均评分
       * @example 4.5
       */
      averageRating: number;
      /**
       * Format: int64
       * @description 解惑被评分总次数
       * @example 100
       */
      totalReviews: number;
    };
    bazaar_user_profile_for_server: {
      /**
       * Format: int64
       * @description 角色ID
       * @example 1234567890
       */
      roleId: number;
      /**
       * Format: double
       * @description 平均评分
       * @example 4.5
       */
      averageRating: number;
      /**
       * Format: int64
       * @description 解惑被评分总次数
       * @example 100
       */
      totalReviews: number;
    };
    bazaar_user_update_req: {
      /**
       * @description 玩家性别，e.g. 0男  1女
       * @example 0
       * @enum {number}
       */
      gender?: 0 | 1;
      /**
       * Format: date-time
       * @description 出生日期，e.g. 2025-01-01
       * @example 2025-01-01 00:00
       */
      birthTime?: string;
      /**
       * Format: int32
       * @description 星巫等级
       * @example 1
       */
      astroLevel?: number;
      /**
       * Format: int32
       * @description 星巫类型(游戏配表决定)
       * @example 1
       */
      astroType?: number;
      birthPlace?: components["schemas"]["location"];
      currentPlace?: components["schemas"]["location"];
    };
    base_resp: {
      [key: string]: unknown;
    };
    bazaar_rating_show_item: components["schemas"]["bazaar_rating"] & {
      /**
       * Format: int64
       * @description 评分ID
       * @example 1234567890
       */
      id?: number;
      /**
       * Format: int64
       * @description 角色ID
       * @example 1234567890
       */
      roleId?: number;
      roleInfo?: components["schemas"]["basic_role_info"];
      userInfo?: components["schemas"]["bazaar_core_user_info"];
      /**
       * Format: int64
       * @description 解惑的评论ID
       * @example 1234567890
       */
      commentId?: number;
      /**
       * Format: int64
       * @description 问题ID
       * @example 1234567890
       */
      postId?: number;
    };
    bazaar_user_rating_list: {
      list: components["schemas"]["bazaar_rating_show_item"][];
      /**
       * Format: integer
       * @description 总数量
       * @example 100
       */
      total: number;
    };
    horoscope_planetary_aspects_resp: {
      /**
       * @description 标题
       * @example 今日星象
       */
      title: string;
      /**
       * @description 内容
       * @example [
       *   "00:15 木星进入巨蟹座3宫",
       *   "形成持续至6月4日的有利相位，带来认知升级，水星逆行促使你重新梳理知识体系，上午9 - 11点适合系统性知识整理；行动赋能，下午15 - 17点适合签署协议或启动新项目；情感共振，强化家庭与情感联结，晚间21 - 23点适合进行家庭对话。",
       *   "12:30 太阳与海王星形成135度相位",
       *   "巨蟹座迎来本命月关键转折日，情绪沉淀后迎来行动突破，在生活各方面完成蜕变循环。"
       * ]
       */
      content: string[];
    };
    horoscope_daily_forecast_resp: {
      /**
       * Format: string
       * @description 透传，玩家选择的测运类型
       * @example 事业与学业
       */
      fortune: string;
      /**
       * Format: string
       * @description 玩家选择的时间区间。如果是本周，返回 2025-05-11 ~ 2025-05-17
       * @example 2025-05-11 ~ 2025-05-17
       */
      time: string;
      /**
       * Format: string
       * @description 玩家的星座
       * @example 射手座
       */
      user_constellation: string;
      /**
       * Format: boolean
       * @description 是否在水逆期
       * @example true
       */
      in_water_reversal: boolean;
      /**
       * Format: int64
       * @description 在水逆期时，剩余天数
       * @example 0
       */
      day_for_water_reversal_remain: number;
      /**
       * Format: int64
       * @description 不在水逆期，玩家的星座距离下一次水逆的时间
       * @example 21
       */
      day_for_next_water_reversal: number;
      /**
       * Format: string
       * @description 标题
       * @example 事业与学业运势解读
       */
      title: string;
      /** @description 内容 */
      content: {
          /**
           * @description 标题
           * @example 2025年5月30日双子座事业趋势分析
           */
          title: string;
          /**
           * @description 内容
           * @example 6月1日火星进入天秤座六合水星，利于谈判、合作项目推进，适合建立新职场关系。6月4日月亮过境第10宫合木星，事业运上升，有望获得上级认可或新机会降临。6月8日金星对冲天王星，财务决策需谨慎，避免冒险投资影响事业发展。
           */
          content: string;
        }[];
      /**
       * Format: int64
       * @description 整体打分
       * @example 85
       */
      score: number;
      /**
       * Format: string
       * @description 今日幸运色
       * @example 深蓝色
       */
      lucky_color: string;
      /**
       * Format: string
       * @description 今日幸运色具体色值
       * @example #FF7D00
       */
      lucky_color_code?: string;
    };
    comment_rank_resp: {
      /** @description 排行榜列表 */
      list: {
          /**
           * @description 角色ID
           * @example 123456
           */
          roleId: number;
          /**
           * @description 角色名称
           * @example 星巫大师
           */
          roleName?: string;
          /**
           * @description 解惑次数
           * @example 100
           */
          commentCount: number;
          /**
           * @description 最后一次解惑时间戳
           * @example 1640995200000
           */
          lastCommentTime?: number;
          /**
           * @description 排名
           * @example 1
           */
          rank: number;
          /**
           * @description 职业ID
           * @example 1
           */
          jobId: number;
          /**
           * @description 性别
           * @example 1
           */
          gender: number;
          /**
           * @description 子性别
           * @example 0
           */
          subGender: number;
          /**
           * @description 头部涂装ID
           * @example 0
           */
          headPaintId: number;
          /**
           * @description 身体涂装ID
           * @example 0
           */
          bodyPaintId: number;
        }[];
      /**
       * @description 排行榜更新时间戳
       * @example 1640995200000
       */
      updateTime: number;
      /**
       * @description 周开始时间戳
       * @example 2025-07-14 00:00:00
       */
      weekStart?: string;
      /**
       * @description 周结束时间戳
       * @example 2025-07-20 23:59:59
       */
      weekEnd?: string;
    };
    /**
     * @description | 模块id|模块名字 |
     * | --- | ----|
     * | 10001 | 吃饭奇遇|
     *
     * @enum {integer}
     */
    commonMessageModuleId: 10001;
    cloudGameNotifyBuyMonthCardData: {
      /**
       * @description 通行证账号
       * @example <EMAIL>
       */
      urs: string;
      /**
       * @description 月卡状态 (0=>从未开通, 1 => 开通正常, 2 => 开通已过期)
       * @example 0
       * @enum {number}
       */
      status?: 0 | 1 | 2;
      /** @example 200 */
      serverId: number;
      /**
       * @description 月卡资格过期时间戳(单位s)
       * @example 1662249600
       */
      expireAt: number;
    };
    /** @description OK */
    cloudGameNotifyBuyMonthCardRes: unknown;
  };
  responses: {
    /** @description OK */
    CompetitionOptionsRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list?: components["schemas"]["CompetitionOptions"][];
          };
        };
      };
    };
    /** @description OK */
    WebCompetitionListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["WebCompetitionList"];
        };
      };
    };
    /** @description OK */
    GetCoupleInfoRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["GetCoupleInfoData"];
        };
      };
    };
    /** @description OK */
    ActivityFriendIdsRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list: number[];
          };
        };
      };
    };
    /** @description OK */
    CloudGameMonthCardShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CloudGameMonthCardData"];
        };
      };
    };
    /** @description OK */
    CloudGameDurationShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CloudGameDurationShowData"];
        };
      };
    };
    /** @description OK */
    CloudGameNotifyBuyMonthCard: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["cloudGameNotifyBuyMonthCardRes"];
        };
      };
    };
    /** @description OK */
    CloudGameNotifyYuanBaoChargeRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CloudGameNotifyYuanbaoChangeData"];
        };
      };
    };
    /** @description OK */
    CloudGameDurationChangeRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CloudGameDurationChangeData"];
        };
      };
    };
    /** @description OK */
    MomentPickListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list: components["schemas"]["MomentPickList"];
            meta: components["schemas"]["PaginationMeta"];
          };
        };
      };
    };
    /** @description OK */
    GuildPhotoWallListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list: components["schemas"]["GuildPhotoWallList"];
            /**
             * @description 帮会下图片总数量(aka 普通照片墙图片总数之和)
             * @example 10
             */
            photoCnt: number;
          };
        };
      };
    };
    /** @description OK */
    GuildPhotoWallPhotoShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["GuildPhotoWallPhoto"];
        };
      };
    };
    /** @description OK */
    PhotoWallShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["PhotoWallShow"];
        };
      };
    };
    /** @description OK */
    MultiGardenPhotoWallListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list: components["schemas"]["MultiGardenPhotoWallList"];
            /**
             * @description 帮会下图片总数量(aka 普通照片墙图片总数之和)
             * @example 10
             */
            photoCnt: number;
          };
        };
      };
    };
    /** @description OK */
    MultiGardenPhotoWallPhotoShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MultiGardenPhotoWallPhoto"];
        };
      };
    };
    /** @description OK */
    MultiGardenPhotoWallShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MultiGardenPhotoWallShow"];
        };
      };
    };
    /** @description OK */
    NoteMailSendRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["NoteMailSendAppend"];
        };
      };
    };
    /** @description OK */
    NoteMailInboxShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["NoteMailInboxShow"];
        };
      };
    };
    /** @description OK */
    NoteMailInBoxListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["NoteMailInBoxList"];
        };
      };
    };
    /** @description OK */
    GuildPhotoWallNotificationListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["GuildPhotoWallNotificationList"];
        };
      };
    };
    /** @description OK */
    GuildPhotoWallCommentListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["GuildPhotoWallCommentList"];
        };
      };
    };
    /** @description OK */
    PhotoWallAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            /**
             * @description 新增墙的id
             * @example 1
             */
            id: number;
          };
        };
      };
    };
    /** @description OK */
    MultiGardenPhotoWallNotificationListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MultiGardenPhotoWallNotificationList"];
        };
      };
    };
    /** @description OK */
    MultiGardenPhotoWallCommentListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MultiGardenPhotoWallCommentList"];
        };
      };
    };
    /** @description OK */
    MultiGardenPhotoWallAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            /**
             * @description 新增墙的id
             * @example 1
             */
            id: number;
          };
        };
      };
    };
    /** @description OK */
    ApiOkRes: {
      content: {
        "application/json": components["schemas"]["ApiOk"];
      };
    };
    /** @description OK */
    ForceEventRemarkRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["ForceEventRemark"];
        };
      };
    };
    /** @description OK */
    AppearancePaintGetRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["AppearancePaintGetRet"];
        };
      };
    };
    /** @description OK */
    SkillComboAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["SkillComboAddRet"];
        };
      };
    };
    /** @description OK */
    SkillComboListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["SkillComboListRet"];
        };
      };
    };
    /** @description OK */
    SkillComboGetRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["SkillComboGetRet"];
        };
      };
    };
    /** @description OK */
    DamageStatAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["DamageStatAddRet"];
        };
      };
    };
    /** @description OK */
    DamageStatShareInfoRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["DamageStatItem"];
        };
      };
    };
    /** @description OK */
    DamageStatListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            list?: components["schemas"]["DamageStatItem"][];
          };
        };
      };
    };
    /** @description OK */
    BustPhotoAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["BustPhotoAddRet"];
        };
      };
    };
    /** @description OK */
    GardenPhotoUnitAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["GardenPhotoUnitAddRet"];
        };
      };
    };
    /** @description OK */
    BustPhotoGetRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["BustPhotoItem"];
        };
      };
    };
    /** @description OK */
    GardenPhotoUnitGetRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["GardenPhotoUnitItem"];
        };
      };
    };
    /** @description OK */
    GardenPhotoUnitListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            list?: components["schemas"]["GardenPhotoUnitItem"][];
          };
        };
      };
    };
    /** @description OK */
    BustPhotoListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            list?: components["schemas"]["BustPhotoItem"][];
          };
        };
      };
    };
    /** @description OK */
    ForceEventAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["ForceEventAddRet"];
        };
      };
    };
    /** @description OK */
    ForceEventListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            list: (components["schemas"]["ForceEventAdd"] & components["schemas"]["ForceEventAddRet"] & components["schemas"]["ForceEventRemark"] & components["schemas"]["ForceEventtat"] & components["schemas"]["ForceEventAction"])[];
            /**
             * @description 总数, 周六23点59拿到道具，应该是本周六-下周五的结果的数量总和
             * @example 50
             */
            count: number;
          };
        };
      };
    };
    /** @description OK */
    BanAccountShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["BanAccountShow"];
        };
      };
    };
    /** @description OK */
    ApiActionRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["ApiAction"];
        };
      };
    };
    /** @description OK */
    GmWeekRenQiRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["WeekRenQi"];
        };
      };
    };
    /** @description OK */
    GmServerListMergeServerListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["GmServerListMergeServerItem"][];
        };
      };
    };
    /** @description OK */
    PlayerGetProfileRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["PlayerGetProfile"];
        };
      };
    };
    /** @description OK */
    ServerAnnalScoreRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["ServerAnnalScore"];
        };
      };
    };
    /** @description OK */
    GetServerAnnalEventListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            list: components["schemas"]["ServerAnnalEvent"][];
          };
        };
      };
    };
    /** @description OK */
    GetPlayerAnnalEventListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            list: components["schemas"]["PlayerAnnalEvent"][];
            /** @example 3 */
            count: number;
          };
        };
      };
    };
    /** @description Example response */
    PlayerCopyMoments: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["CopyMomentsRes"];
        };
      };
    };
    /** @description Example response */
    ShowWishList: {
      content: {
        "application/json": {
          code: number;
          data: {
            list?: components["schemas"]["ShowWishListItem"][];
          };
        };
      };
    };
    /** @description Example response */
    FullFillWishList: {
      content: {
        "application/json": {
          code: number;
          data: {
            list: {
                id: number;
                type: string;
                adderId: number;
                adderName: string;
                addTime: number;
                buyerId: number;
                buyerName: string;
                fullFillTime: number;
                payload?: {
                  commodityId: number;
                  itemId: number;
                  shopId: number;
                  commodityIndex: number;
                };
              }[];
            meta: {
              curPage: number;
              totalPage: number;
              totalCount: number;
            };
          };
        };
      };
    };
    /** @description ok */
    ActivityTakePhotoGetMyPhotosRes: {
      content: {
        "application/json": {
          code: number;
          /** @description 数组类型，数组顺序对应地点顺序，即：下标=地点ID-1 */
          data: components["schemas"]["ActivityTakePhotoMyListItem"][][];
        };
      };
    };
    /** @description ok */
    ActivityTakePhotoGetLocationSelectedPhotoRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            imgUrl: string;
          };
        };
      };
    };
    /** @description ok */
    GardenDetailRes: {
      content: {
        "application/json": {
          code: number;
          data: components["schemas"]["GardenBasicInfo"];
        };
      };
    };
    /** @description ok */
    GardenRankRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            list?: components["schemas"]["GardenBasicInfo"][];
          };
        };
      };
    };
    /** @description ok */
    GardenEvalutionListRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            /** @description 总数 */
            count: number;
            list: components["schemas"]["GardenEvalutionListItem"][];
          };
        };
      };
    };
    /** @description ok */
    GardenEvaluationCommentListRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            /** @description 总数 */
            count: number;
            list: components["schemas"]["GardenEvalutionCommentListItem"][];
          };
        };
      };
    };
    /** @description ok */
    GardenInformListRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            /** @description 总数 */
            count: number;
            list: components["schemas"]["GardenInformListItem"][];
          };
        };
      };
    };
    /** @description ok */
    GardenInformUnreadRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            /** @description 未读消息数 */
            count: number;
          };
        };
      };
    };
    /** @description OK */
    idRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["idData"];
        };
      };
    };
    /** @description OK */
    TimesSquarePhotoAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["TimesSquarePhotoAddRet"];
        };
      };
    };
    /** @description OK */
    MemeAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["MemeAddRet"];
        };
      };
    };
    /** @description OK */
    TimesSquarePhotoListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["TimesSquarePhotoListRet"];
        };
      };
    };
    /** @description ok */
    MemeListRes: {
      content: {
        "application/json": {
          code: number;
          data: {
            list: {
                /**
                 * @description 表情包id
                 * @example 342
                 */
                id?: number;
                /**
                 * @description 表情包url
                 * @example https://hi-163-nsh.nos-jd.163yun.com/avatar/201903/28/036250d0514c11e9a026a34f9bd10b05.jpg
                 */
                url?: string;
                /**
                 * @description 客户端id
                 * @example 10
                 */
                clientId?: number;
                auditStatus?: components["schemas"]["EAuditStatus"];
                /**
                 * @description 创建时间
                 * @example 1620628236384
                 */
                createTime?: number;
              }[];
          };
        };
      };
    };
    /** @description OK */
    cloudGameNotifyBuyMonthCardRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["cloudGameNotifyBuyMonthCardData"];
        };
      };
    };
  };
  parameters: {
    /** @description 势力id */
    forceId: number;
    /** @description 势力大事的id, 添加后生成 */
    forceEventId: number;
    /** @description 史官评注文本 */
    forceEventContent: string;
    /** @description 评论的史官角色id */
    forceEventRemarkRoleId: number;
    /** @description 评论的史官名字 */
    forceEventRemarkRoleName: string;
    /** @description 照片墙类型 0 => 普通 1 => 精选 */
    guildPhotoWallType: 0 | 1;
    /** @description 通知状态过滤 0=>未读 1=>已读 */
    notificationStatus?: 0 | 1;
    /** @description 回复评论的玩家id */
    replyId?: number;
    /** @description 评论你想说的话 */
    commentText: string;
    /** @description 覆盖模式(目标槽位有图片时直接替换) */
    gpwOverwrite?: boolean;
    /** @description 照片墙id */
    guildPhotoWallId: number;
    /** @description 照片墙id */
    photoWallId: number;
    /** @description 是否帮会当家 */
    isGuildLeader: boolean;
    /** @description 图片评论id */
    guildPhotoWallCommentId: number;
    /** @description 图片评论id */
    photoWallCommentId: number;
    /** @description 图片评论id */
    photoWallCommentIdInQuery: number;
    /** @description 图片槽位id */
    guildPhotoWallSlot: number;
    /** @description 图片槽位id */
    photoWallSlot: number;
    /** @description 精选墙id */
    guildPhotoWallWallId: number;
    /** @description 精选墙id */
    photoWallWallId: number;
    /** @description 图片id */
    guildPhotoWallPhotoId: number;
    /** @description 图片id */
    photoWallPhotoId: number;
    /** @description 图片id */
    photoWallPhotoIdFull: number;
    /** @description 图片id */
    guildPhotoWallPhotoIdInQuery: number;
    /** @description 图片id */
    photoWallPhotoIdInQuery: number;
    /** @description 照片墙id */
    multiGardenPhotoWallId: number;
    /** @description 图片评论id */
    multiGardenPhotoWallCommentId: number;
    /** @description 图片评论id */
    multiGardenPhotoWallCommentIdInQuery: number;
    /** @description 图片槽位id */
    multiGardenPhotoWallSlot: number;
    /** @description 精选墙id */
    multiGardenPhotoWallWallId: number;
    /** @description 图片id */
    multiGardenPhotoWallPhotoId: number;
    /** @description 图片id */
    multiGardenPhotoWallPhotoIdFull: number;
    /** @description 图片id */
    multiGardenPhotoWallPhotoIdInQuery: number;
    /** @description 是否联居当家 */
    isMultiGardenLeader: boolean;
    /** @description 编年史事件id */
    eventId: number;
    /** @description 撤销该行为 */
    undo?: boolean;
    /** @description 调用产品标识，该产品标识是接口配置允许调用的产品标识。（如：xyq、xy2、ff、xyw等等。） */
    product: string;
    /** @description 状态（1：待认证，13：认证通过；5：认证失败。 不传默认为1待认证） **游戏无需处理, 梦岛负责同步给urs** */
    syncStatus?: 1 | 13 | 5;
    /** @description 中宣部认证返回的pi值 **游戏无需处理, 梦岛负责同步给urs** */
    syncPi?: string;
    /** @description 实名认证登记时填写的Email地址，符合邮件地址格式。 */
    fcmEmail: string;
    /** @description 需要进行实名认证的证件号码，对于身份证号码，长度15字符或18字符，（对于18位的身份证前17位均为数字，只有最后一位可以为字母，必须符合身份证的校验规则）。 */
    idnum: string;
    /** @description 证件类型（0：身份证，1：军官证，2：护照，3：其他） */
    idtype: 0 | 1 | 2 | 3;
    /** @description 用户真实姓名，长度4到12位（2到6个汉字），需要进行UTF-8 encode。 */
    realname: string;
    /** @description 该参数表示用户的真实IP（如果真实IP获取不到，则记录下调用服务器IP，该参数尽量传用户的真实IP） */
    userip?: string;
    /** @description 是否需要返回pi（1需要返回）**游戏不需要关心，梦岛会处理这个参数** */
    needPi?: number;
    /** @description 手机帐号主帐号（yd.xxxxxxx） */
    ydUsername: string;
    /** @description 用户名，可以是目前通行证支持的所有帐号，非163帐号带上域信息（如***********, ************等等），163帐号可以不带域信息。 格式要求：帐号不能为空，必须是字符、下划线、减号、@或点组成，长度小于40个字符。 */
    username: string;
    /** @description 游戏服务器时间戳(单位s), 建议游戏添加，不传使用梦岛的服务器时间 */
    GameServerTimestamp?: number;
    /** @description 行为上报时间戳(单位s), 建议游戏添加，不传使用梦岛的服务器时间 */
    timestamp?: number;
    /** @description 商品 ID */
    commodityId?: number;
    /** @description 道具 ID */
    itemId?: number;
    /** @description 商店 ID */
    shopId?: number;
    /** @description 封禁的截止时间戳(单位ms), 注意不是封禁的时长，是截止时间 */
    banAccountTime?: number;
    /** @description 商品 Index */
    commodityIndex?: number;
    /** @description 时装 ID */
    fashionId?: number;
    /** @description kick date */
    kickDate: string;
    /** @description 通行证账号登录 */
    urs: string;
    /** @description 倩女端游 d10; 逆水寒端游 d30; 天谕端游 d21; 超激斗梦境 d41 */
    fcmGameId: string;
    /** @description 指定日期 (yyyy-MM-dd) */
    cloudGameDurationTodayStr: string;
    /** @description 充值订单id, 游戏需要保证唯一性来保障接口幂等 */
    cloudGameDurationChargeId: string;
    /** @description 充值元宝数量 */
    cloudGameDurationYuanbao: number;
    /** @description 时长类型 */
    cloudGameDurationType: "daily" | "permanent";
    /** @description 扣除类型 1 => 只允许扣除当日时长; 2 => 优先扣除当日时长，余额从永久时长扣除 */
    cloudGameCostType: 1 | 2;
    /** @description 用户类型 */
    cloudGameUserType?: "normal" | "cloud";
    /** @description 变化游戏时间(点数) */
    cloudGameDuration: number;
    /** @description 被合服的服务器id */
    fromServerId: number;
    /** @description 合服目的地服务器id */
    toServerId: number;
    /** @description 具体哪一周(用周一的日期表示) */
    weekStr: string;
    /** @description 周人气值 */
    weekRenqi: number;
    /** @description 邮件id */
    noteMailId: number;
    /** @description 邮箱类型 */
    mailBoxType: components["schemas"]["NoteMailBoxType"];
    /** @description 搜索关键字(支持发件人名字) */
    noteMailInboxListKw?: string;
    /** @description 搜索关键字(支持收件人名字) */
    noteMailOutboxListKw?: string;
    /** @description 心愿id */
    wishId: number;
    /** @description 表情包id */
    memeId: number;
    /** @description 角色id */
    roleid: number;
    /** @description 是否可以评论 */
    canComment?: 0 | 1;
    /** @description 是否可以评论 */
    canCommentByAll?: 0 | 1;
    /** @description 是否可以转发 */
    canForward?: 0 | 1;
    /** @description 是否可以被陌生人评论 */
    canCommentByStranger?: 0 | 1;
    /** @description 伤害数据上传后生成的分享id */
    damageStatShareId: string;
    /** @description 技能组合id */
    skillComboId: number;
    /**
     * @description | value |    desc    |
     * |-------|:----------:|
     * |1      |论武套路     |
     * |2      |帮战套路     |
     * |3      |副本套路     |
     */
    skillComboCategory?: components["schemas"]["SkillComboCategory"];
    /** @description 套路职业 */
    skillComboJobId?: number;
    /** @description 游戏副本id */
    gamePlayId: number;
    /** @description 副本里的bossId */
    bossId: number;
    /** @description 是否同一个服 */
    sameServer?: boolean;
    /** @description 通知 id */
    notificationId: number;
    /** @description 角色id */
    roleId?: number;
    /** @description 角色id */
    roleIdIn: number;
    /** @description 游戏当前时间时间戳 */
    timeStampOpt?: number;
    /** @description 角色id */
    roleIdRqd: number;
    /** @description 物品模板id */
    itemTemplateId: number;
    /** @description 半身像位置索引(1-20， 1个玩家最多20张位置) */
    bustPhotoIndex: number;
    /** @description 半身像图片链接 */
    bustPhotoUrl: string;
    /** @description 像图片链接 */
    commonPhotoUrl: string;
    /** @description 0=>普通身份 1=>史官身份 */
    roleType: 0 | 1;
    /** @description 庄园主角色id */
    ownerId: number;
    /** @description 目标玩家id */
    targetId: number;
    /** @description 目标玩家id列表, csv格式 */
    targetIds: string;
    /** @description 情缘id */
    marriageOldId: number;
    /** @description 情缘id */
    marriageNewId: number;
    /** @description 情缘id */
    marriageInfoId: number;
    /** @description 侠侣id */
    coupleId?: number;
    /** @description 侠侣名字 */
    coupleName?: string;
    /** @description 搜索关键字(玩家id或者有何玩家名) */
    momentPickKw?: string;
    /** @description -1 => 未精选 0 => 所有  1 => 精选 */
    momentPickType?: -1 | 0 | 1;
    /** @description 角色id列表(csv格式) */
    roleIds?: string;
    /** @description 角色名字 */
    roleName?: string;
    /** @description 报名转服的目标服务器id */
    signUpServerId?: number;
    /** @description 转服的唯一标记,可不传,之后多次转服需求用,最长20 */
    transferMark?: string;
    /** @description 标签 */
    tag?: string;
    /** @description 标签列表 */
    tags?: string;
    /** @description 标签稀有度(CSV_ARRAY) */
    rares?: string;
    /** @description 标签稀有度 */
    rare?: "SSR" | "SR" | "F";
    /** @description 目标玩家id */
    targetid?: number;
    /** @description 帮会等级 */
    guildLevel: number;
    /** @description 情缘等级 */
    marriageLevel: number;
    /** @description 联居等级 */
    multiGardenLevel: number;
    /** @description 情缘id */
    marriageId: number;
    /** @description 帮会id */
    guildId: number;
    /** @description 联居id */
    multiGardenId: string;
    EventCategory?: components["schemas"]["ServerAnnalCategory"];
    /** @description 请输入玩家名称或id */
    RoleIdAndNameKw?: string;
    /** @description 最小id */
    minId?: number;
    /** @description 最大id */
    maxId?: number;
    /** @description 月卡购买渠道 */
    monthCardChannel?: "game" | "cloud_game";
    /** @description 月卡购买订单id */
    monthCardOrderId?: string;
    /** @description 月卡购买时间戳(单位s) */
    monthCardBuyTime?: number;
    /** @description 月卡时长(单位s) */
    monthCardDuration?: number;
    /** @description 被合服的服务器Id */
    mergeFrom?: number;
    /** @description 合服到的服务器Id */
    mergeTo?: number;
    /** @description 服务器Id */
    serverId?: number;
    /** @description 回复的玩家id */
    replyid?: number;
    /** @description 时间戳单位(ms) */
    time?: number;
    /** @description 随机字符串(6位) */
    nonce?: string;
    /** @description token计算方式为 md5(ASCII升序参数的值 + salt) */
    authToken?: string;
    /** @description 登录后返回的校验凭证 */
    skey?: string;
    /** @description 文本 */
    text?: string;
    /** @description 热度系数,默认为1,数值范围[0,2] */
    hotFactor?: number;
    /** @description 页码 */
    page?: number;
    /** @description 以周日开始的周标记 */
    sunWeekStr?: components["schemas"]["SunWeekStr"];
    /** @description 数量 */
    size?: number;
    /** @description 每页大小 */
    pageSize?: number;
    /** @description 心情id */
    momentid: number;
    /** @description 心情id */
    moment_id: number;
    /** @description 心情id */
    momentId: number;
    /** @description 开关 */
    switch: "on" | "off";
    /** @description 服务器id */
    serverid: number;
    /** @description 评论id */
    id_as_comment: number;
    /** @description 留言id */
    id_as_message: number;
    likeAction?: "do" | "undo";
    context?: string;
    url: string;
    /** @description 客户端的clientId */
    memeClientId: number;
    /** @description 檄文id */
    polemic_id?: number;
    /** @description club id */
    clubId?: number;
    /** @description from location */
    from?: number;
    /** @description to location */
    to?: number;
    /** @description 势力 */
    gForce?: number;
    /** @description 搜索关键字 */
    kw?: string;
    /** @description honor id */
    honorId?: number;
    gender?: number;
    /** @description 邮箱地址 */
    email?: string;
    /** @description 认证头像 */
    avatar?: string;
    /** @description 手机号 */
    phone?: number;
    /** @description 导出时的偏移量,请从上一个导出的表格的sheetName复制出来 */
    offset?: string;
    /** @description 审核资源媒体类型 */
    mediaType?: "guild_photo_wall_photo" | "multi_garden_photo_wall_photo";
    /** @description 账号 */
    accountInPath: string;
    /** @description gameid */
    gameidInPath: string;
    /** @description 审核资源id(填对应资源类型的id, 比如帮会照片墙图片，这里就填图片id) */
    mediaResourceId?: number;
    /** @description 审核状态 0=>审核中 -1=>审核拒绝， 1 => 审核通过 */
    auditStatus?: 0 | 1 | -1;
    /** @description 理由 1=>敏感用词 2=>虚假信息， 3 => 身份信息格式错误， 4=> 其他 */
    reason?: number;
    /** @description 自定义的理由文本 */
    reasonMsg?: string;
    /** @description 素材ID */
    assertId?: number;
    /** @description 素材ID列表 */
    assertIds?: number[];
    /** @description 素材ID */
    certifiedInfoId?: number;
    /** @description 认证信息ID列表 */
    certifiedInfoIds?: number[];
    /** @description 允许发布檄文的凭证 */
    pubToken?: string;
    /** @description 届数 */
    whichTimes?: number;
    /** @description 服务器类型 1=> 正式服  2=>赛季服 3=>黄金服 */
    serverType?: 1 | 2 | 3;
    /** @description 赛事id 1=>天下第一 2=>诸神之战 3=>皇城之巅 4=>剑试苍穹 5=>跨服联赛 */
    competitionId?: 1 | 2 | 3 | 4 | 5;
    /**
     * @description 角色id, 指向当前登录roleid，和skey需要匹配
     * @example 24017600001
     */
    roleid_in_query: number;
    /** @description 页码 */
    common_page: number;
    /** @description 每页大小 */
    common_page_size: number;
    /**
     * @description 星巫话题id列表，以逗号分隔的数字列表, 如果为空，则不进行过滤
     * @example 1,2,3
     */
    astrology_topic_ids?: string;
    /**
     * @description 问惑id
     * @example 1
     */
    bazaar_post_id: number;
    /**
     * @description 游戏时间戳，单位毫秒
     * @example 1747689600000
     */
    timestamp_in_query: number;
    /**
     * @description | 模块id|模块名字 |
     * | --- | ----|
     * | 10001 | 吃饭奇遇|
     */
    commonMessageModuleId: components["schemas"]["commonMessageModuleId"];
  };
  requestBodies: never;
  headers: never;
  pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {

  /**
   * 星巫-问惑-发布
   * @description 星巫-问惑-发布
   */
  bazaarPostAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["bazaar_post_add"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_post_add_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-问惑-公共列表
   * @description 星巫-问惑-公共列表
   */
  bazaarPostList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        page: components["parameters"]["common_page"];
        pageSize: components["parameters"]["common_page_size"];
        topicIds?: components["parameters"]["astrology_topic_ids"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_post_list_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-问惑-我的列表
   * @description 星巫-问惑-我的列表
   */
  bazaarPostSelfList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        page: components["parameters"]["common_page"];
        pageSize: components["parameters"]["common_page_size"];
        topicIds?: components["parameters"]["astrology_topic_ids"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_post_list_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-问惑-AI占星骰子
   * @description 发起解读，把对应问题和占卜结果发送AI, 并返回解读结果
   * [伏羲提供的上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794)
   */
  bazaarPostInterpret: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        postId: components["parameters"]["bazaar_post_id"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_post_interpret_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-问疑解答-新增
   * @description 星巫-问疑解答-新增
   */
  bazaarCommentAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["bazaar_comment_add"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code?: number;
            data?: components["schemas"]["bazaar_comment_add_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-问疑解答-列表
   * @description 星巫-问疑解答-列表
   */
  bazaarPostCommentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        postId: components["parameters"]["bazaar_post_id"];
        page: components["parameters"]["common_page"];
        pageSize: components["parameters"]["common_page_size"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_comment_list_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-解惑评分-添加
   * @description 星巫-解惑评分-添加
   */
  bazaarRatingAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["bazaar_rating_add"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_rating_add_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-评分-添加 (服务端调用)
   * @description 直接给用户评分, 不走巫师集会逻辑
   */
  ratingAdd: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["rating_add"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_rating_add_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-热门话题
   * @description 星巫-热门话题
   */
  bazaarHotTopic: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_hot_topic_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-群体占卜报告
   * @description 星巫-群体占卜报告
   */
  bazaarGroupDivinationReport: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        timestamp: components["parameters"]["timestamp_in_query"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_group_divination_report_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-用户-注册 (服务端调用)
   * @description 星巫-用户-注册 (服务端调用)
   */
  bazaarUserRegister: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["bazaar_user_register_req"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_user_register_res"];
          };
        };
      };
    };
  };
  /**
   * 星巫-用户资料-展示
   * @description 星巫-用户资料-展示
   */
  bazaarUserProfile: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_user_profile"];
          };
        };
      };
    };
  };
  /**
   * 星巫-用户资料-展示 (服务器调用)
   * @description 星巫-用户资料-展示 (服务器调用)
   */
  bazaarUserProfileForServer: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_user_profile_for_server"];
          };
        };
      };
    };
  };
  /**
   * 星巫-用户资料-更新 (服务端调用)
   * @description 更新星巫用户资料，更新字段可选，不传对应字段不更新
   */
  bazaarUserProfileUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["bazaar_user_update_req"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["base_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-用户-解惑收到的评分列表
   * @description 星巫-用户-解惑收到的评分列表
   */
  bazaarUserRatingReceiveList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        page: components["parameters"]["common_page"];
        pageSize: components["parameters"]["common_page_size"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_user_rating_list"];
          };
        };
      };
    };
  };
  /**
   * 星巫-占星骰子-AI解读
   * @description 不走巫师集会逻辑, 根据问题和骰子结果，直接调用伏羲AI接口
   */
  diceResultInterpret: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
      };
    };
    requestBody: {
      content: {
        "application/json": {
          question: string;
          dice: components["schemas"]["bazaar_dice"];
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["bazaar_post_interpret_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-星盘-AI今日星象
   * @description 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
   */
  horoscopePlanetaryAspects: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        timestamp: components["parameters"]["timestamp_in_query"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["horoscope_planetary_aspects_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-星盘-AI今日运势
   * @description 获取用户今日运势, [上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794?popo_locale=zh&xyz=1747711947790#edit)
   */
  horoscopeDailyForecast: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid_in_query"];
        timestamp: components["parameters"]["timestamp_in_query"];
        /**
         * @description 测运类型，包括：basic:基本运势、wealth:财运、career:事业与学业、love:情感
         * @example career
         */
        fortune: "basic" | "wealth" | "career" | "love";
        /**
         * @description 时间区间。today:今日，week:本周（周日为起始）
         * @example today
         */
        timeInterval: "today" | "week";
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            data: components["schemas"]["horoscope_daily_forecast_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-解惑次数排行榜 (服务端调用)
   * @description 获取解惑次数排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
   */
  commentRank: {
    responses: {
      /** @description 成功获取排行榜 */
      200: {
        content: {
          "application/json": {
            /**
             * @description 业务响应Code
             * @example 0
             */
            code: number;
            data: components["schemas"]["comment_rank_resp"];
          };
        };
      };
    };
  };
  /**
   * 星巫-解惑次数周排行榜 (服务端调用)
   * @description 获取解惑次数周排行榜，按解惑次数降序排列，相同次数按首次达到时间升序排列
   */
  commentRankWeekly: {
    responses: {
      /** @description 成功获取排行榜 */
      200: {
        content: {
          "application/json": {
            /**
             * @description 业务响应Code
             * @example 0
             */
            code: number;
            data: components["schemas"]["comment_rank_resp"];
          };
        };
      };
    };
  };
  /**
   * 通用留言 - 添加留言
   * @description 通用留言 - 添加留言
   */
  commonMessageAdd: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          moduleId?: components["schemas"]["commonMessageModuleId"];
          /**
           * Format: int64
           * @description 角色id
           */
          roleid?: number;
          /**
           * Format: int64
           * @description 职业id
           * @example 1
           */
          jobId?: number;
          /**
           * @description 性别
           * @example 1
           */
          gender: number;
          /**
           * @description 子性别
           * @example 1
           * @enum {integer}
           */
          subGender: 0 | 1;
          /** @description 角色名 */
          rolename: string;
          /** @description 留言文本 */
          text: string;
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /**
               * Format: int64
               * @description [int64]留言id
               */
              id?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 通用留言 - 推荐列表
   * @description 获取推荐的通用留言列表
   */
  commonMessageReccList: {
    parameters: {
      query: {
        /** @description 角色id */
        roleid: number;
        moduleId: components["parameters"]["commonMessageModuleId"];
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /** @description 推荐留言列表 */
              list?: {
                  /**
                   * Format: int64
                   * @description 留言id
                   */
                  id: number;
                  /** @description 角色id */
                  roleid: number;
                  /**
                   * @description 性别
                   * @example 1
                   */
                  gender: number;
                  /**
                   * @description 子性别
                   * @example 1
                   */
                  subGender: number;
                  /**
                   * @description 职业
                   * @example 1
                   */
                  jobId: number;
                  /**
                   * @description 角色名
                   * @example 张三
                   */
                  rolename: string;
                  /** @description 留言内容 */
                  text: string;
                  /**
                   * Format: int64
                   * @description 创建时间
                   */
                  createTime: number;
                }[];
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-通用留言-添加
   * @description 后台-通用留言-添加
   */
  commonMessageAdminMessageAdd: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          moduleId: components["schemas"]["commonMessageModuleId"];
          /** @description [string]角色名 */
          rolename: string;
          /** @description [string]留言文本 */
          text: string;
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /**
               * Format: int64
               * @description [int64]留言id
               */
              id?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-通用留言-删除
   * @description 后台-通用留言-删除
   */
  commonMessageAdminMessageDel: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          moduleId: components["schemas"]["commonMessageModuleId"];
          /** @description [slice]留言id */
          ids: number[];
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /** @description [int]删除数量 */
              delCnt?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-通用留言-列表
   * @description 后台-通用留言-列表
   */
  commonMessageAdminMessageList: {
    parameters: {
      query: {
        moduleId: components["parameters"]["commonMessageModuleId"];
        /** @description [int64]角色id */
        roleid?: number;
        /** @description [string]内容搜索关键字 */
        kw?: string;
        /** @description [int]Page */
        page: number;
        /** @description [int]PageSize */
        page_size: number;
        /** @description [string]Status */
        status: "all" | "show" | "hide";
        /** @description [string]SortBy */
        sort_by?: "role_type" | "show_status" | "create_time";
        /** @description [string]SortOrder */
        sort_order?: "asc" | "desc";
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /** @description [slice]列表 */
              list?: {
                  /**
                   * Format: int64
                   * @description [int64]创建时间
                   */
                  createTime?: number;
                  /**
                   * Format: int64
                   * @description [int64]留言id
                   */
                  id?: number;
                  /** @description [int]角色类型 0: 真玩家, 1: 普通假玩家, 2: NPC假玩家 */
                  roleType: number;
                  /**
                   * Format: int64
                   * @description [int64]角色id
                   */
                  roleid: number;
                  /** @description [string]角色名 */
                  rolename: string;
                  /** @description [int]状态 0:正常 -1:隐藏 -2:删除 */
                  status?: number;
                  /** @description [string]留言文本 */
                  text?: string;
                }[];
              /**
               * Format: int64
               * @description [int64]总数
               */
              total?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-通用留言-更新显示状态
   * @description 后台-通用留言-更新显示状态
   */
  commonMessageAdminMessageUpdateStatus: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          moduleId: components["schemas"]["commonMessageModuleId"];
          /**
           * Format: int64
           * @description [int64]留言id
           */
          id: number;
          /**
           * @description [int]0:正常 -1:隐藏
           * @enum {integer}
           */
          status?: 0 | -1;
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /**
               * Format: int64
               * @description [int64]留言id
               */
              id?: number;
              /**
               * Format: int64
               * @description [int64]更新时间
               */
              updateTime?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-管理员-新增
   * @description 后台-管理员-新增
   */
  commonMessageAdminOperatorAdd: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          /** @description [string]名字 */
          fullName: string;
          /**
           * Format: email
           * @description [string]登录邮箱账号
           */
          openId: string;
          /**
           * @description [int]角色类型 0: 普通运营, 1: 管理员
           * @enum {integer}
           */
          roleType?: 0 | 1;
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /**
               * Format: int64
               * @description [int64]id
               */
              id?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-管理员-删除
   * @description 后台-管理员-删除
   */
  commonMessageAdminOperatorDel: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          /**
           * Format: email
           * @description [string]登录邮箱账号
           */
          openId: string;
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /**
               * Format: int64
               * @description [int64]id
               */
              id?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-管理员-列表
   * @description 后台-管理员-列表
   */
  commonMessageAdminOperatorList: {
    parameters: {
      query: {
        /** @description [int]Page */
        page: number;
        /** @description [int]PageSize */
        page_size: number;
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /** @description [slice]列表 */
              list?: {
                  /**
                   * Format: int64
                   * @description [int64]创建时间
                   */
                  createTime?: number;
                  /** @description [string]名字 */
                  fullName?: string;
                  /**
                   * Format: int64
                   * @description [int64]id
                   */
                  id?: number;
                  /** @description [string]登录邮箱账号 */
                  openId?: string;
                  /** @description [int]角色类型 */
                  roleType?: number;
                  /**
                   * Format: int64
                   * @description [int64]更新时间
                   */
                  updateTime?: number;
                }[];
              /**
               * Format: int64
               * @description [int64]总数
               */
              total?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-管理员-更新
   * @description 后台-管理员-更新
   */
  commonMessageAdminOperatorUpdate: {
    /** @description 请求body */
    requestBody: {
      content: {
        "application/json": {
          /** @description [string]名字 */
          fullName: string;
          /**
           * Format: email
           * @description [string]登录邮箱账号
           */
          openId: string;
          /**
           * @description [int]角色类型 0: 普通运营, 1: 管理员
           * @enum {integer}
           */
          roleType?: 0 | 1;
        };
      };
    };
    responses: {
      /** @description Http Status Code 200时的业务响应 */
      200: {
        content: {
          "application/json": {
            /** @description 业务响应Code */
            code: number;
            /** @description 业务响应Data */
            data: {
              /**
               * Format: int64
               * @description [int64]id
               */
              id?: number;
            };
            /** @description 业务响应Message */
            message: string;
          };
        };
      };
    };
  };
  /**
   * 后台-模块-列表
   * @description 后台-模块-列表
   */
  commonMessageAdminModulesList: {
    responses: {
      /** @description 成功 */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code?: number;
            data?: {
              list?: {
                  /** @example 10001 */
                  id?: number;
                  /** @example 吃饭奇遇 */
                  name?: string;
                }[];
            };
          };
        };
      };
    };
  };
  /**
   * 模拟日志消费-云游戏购买月卡
   * @description 同步云游戏购买月卡的日志，处理消费逻辑
   */
  kafkaCloudGameDurationOnBuyMonthCardLog: {
    requestBody?: {
      content: {
        "text/plain": string;
      };
    };
    responses: {
      200: components["responses"]["cloudGameNotifyBuyMonthCardRes"];
    };
  };
  /**
   * 自定义表情包列表
   * @description 自定义表情包列表
   */
  memeList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["MemeListRes"];
    };
  };
  /**
   * 添加到自定义表情
   * @description 添加到自定义表情
   */
  memeAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        url: components["parameters"]["url"];
        clientId: components["parameters"]["memeClientId"];
      };
    };
    responses: {
      200: components["responses"]["MemeAddRes"];
    };
  };
  /**
   * 删除自定义表情
   * @description 删除自定义表情
   */
  memeDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        clientId: components["parameters"]["memeClientId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 运营审核系统表情包图片审核地址回调接收地址
   * @description 运营审核系统表情包图片审核地址回调接收地址
   */
  memeReturnPic: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["AuditReturnPic"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 发送图片到审核系统中
   * @description 发送图片到审核系统中
   */
  auditSendPicV2: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["AuditSendPic"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 运营审核系统图片审核地址回调接收地址
   * @description 运营审核系统图片审核地址回调接收地址
   */
  auditReturnPic: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["AuditReturnPic"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /** @description 列出玩家时代广场图片 */
  timesSquarePhotoList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["TimesSquarePhotoListRes"];
    };
  };
  /**
   * 保存或更新玩家时代广场图片
   * @description 同一个index上传url会覆盖， 游戏自己控制允许的数量，通过限制index的最大值
   */
  timesSquarePhotoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["TimesSquarePhoto"];
      };
    };
    responses: {
      200: components["responses"]["TimesSquarePhotoAddRes"];
    };
  };
  /**
   * 删除玩家时代广场个人图片
   * @description 删除玩家时代广场个人图片
   */
  timesSquarePhotoDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 图片索引位置 */
        index?: number;
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** @description 更新立绘 */
  appearancePaintUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["AppearancePaint"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  appearancePaintGet: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["AppearancePaintGetRes"];
    };
  };
  /**
   * 上传技能组合
   * @description 上传技能组合
   */
  skillComboAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["SkillComboRecord"];
      };
    };
    responses: {
      200: components["responses"]["SkillComboAddRes"];
    };
  };
  /**
   * 删除技能组合
   * @description 删除技能组合
   */
  skillComboDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 获取技能组合
   * @description 获取技能组合
   */
  skillComboGet: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["SkillComboGetRes"];
    };
  };
  /**
   * 收藏技能组合
   * @description 收藏技能组合
   */
  skillComboCollect: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 取消收藏技能组合
   * @description 取消收藏技能组合
   */
  skillComboCancelCollect: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 点赞技能组合
   * @description 点赞技能组合
   */
  skillComboLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 同步技能组合
   * @description 同步技能组合
   */
  skillComboSync: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 取消点赞技能组合
   * @description 取消点赞技能组合
   */
  skillComboCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 修改技能组合
   * @description 修改技能组合
   */
  skillComboUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["skillComboId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["SkillComboRecord"];
      };
    };
    responses: {
      200: components["responses"]["SkillComboAddRes"];
    };
  };
  /**
   * 技能组合列表
   * @description 技能组合列表
   */
  skillComboList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        category?: components["parameters"]["skillComboCategory"];
        jobId?: components["parameters"]["skillComboJobId"];
        /** @description 排序字段 hot=>热度 like=>点赞 */
        sort_by: "hot" | "like";
        /**
         * @description 列表类别
         * |val|desc|
         * |--|--|
         * |1|推荐套路（所有的都入选)|
         * |2|我收藏的套路|
         * |3|我上传的套路|
         */
        type: 1 | 2 | 3;
        /** @description 搜索关键字 */
        kw?: string;
        /** @description 技能推荐要按照服务器分组分开, 同属一个推荐区域使用同一个region */
        region: number;
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["SkillComboListRes"];
    };
  };
  /**
   * 新增伤害统计数据
   * @description 新增伤害统计数据
   */
  damageStatAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["DamageStatRecord"];
      };
    };
    responses: {
      200: components["responses"]["DamageStatAddRes"];
    };
  };
  /**
   * 玩家某一副本下某个boss下的伤害统计数据列表
   * @description 1. 列表保留最新20条
   * 2. 计算历史最高秒伤和最高治疗
   */
  damageStatList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        gamePlayId: components["parameters"]["gamePlayId"];
        bossId: components["parameters"]["bossId"];
      };
    };
    responses: {
      200: components["responses"]["DamageStatListRes"];
    };
  };
  /**
   * 通过分享id获取某一个伤害统计数据
   * @description 通过分享id获取某一个伤害统计数据
   */
  damageStatShareInfo: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        shareId: components["parameters"]["damageStatShareId"];
      };
    };
    responses: {
      200: components["responses"]["DamageStatShareInfoRes"];
    };
  };
  /**
   * 半身像上传
   * @description 半身像上传
   */
  bustPhotoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        index: components["parameters"]["bustPhotoIndex"];
        url: components["parameters"]["bustPhotoUrl"];
      };
    };
    responses: {
      200: components["responses"]["BustPhotoAddRes"];
    };
  };
  /**
   * 角色半身像获取指定位置
   * @description 角色半身像获取指定位置
   */
  bustPhotoGet: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetId: components["parameters"]["targetId"];
        index: components["parameters"]["bustPhotoIndex"];
      };
    };
    responses: {
      200: components["responses"]["BustPhotoGetRes"];
    };
  };
  /**
   * 批量获取角色半身像
   * @description 批量获取角色半身像
   */
  bustPhotoGetBatch: {
    parameters: {
      query: {
        targetIds: components["parameters"]["targetIds"];
        index: components["parameters"]["bustPhotoIndex"];
      };
    };
    responses: {
      200: components["responses"]["BustPhotoListRes"];
    };
  };
  /**
   * 批量获取角色半身像(服务器调用)
   * @description 批量获取角色半身像(服务器调用)
   */
  bustPhotoServerGetBatch: {
    parameters: {
      query: {
        targetIds: components["parameters"]["targetIds"];
        index: components["parameters"]["bustPhotoIndex"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      200: components["responses"]["BustPhotoListRes"];
    };
  };
  /**
   * 角色半身像列表
   * @description 角色半身像列表
   */
  bustPhotoList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetId: components["parameters"]["targetId"];
      };
    };
    responses: {
      200: components["responses"]["BustPhotoListRes"];
    };
  };
  /**
   * 庄园特点玩法图片上传
   * @description 庄园特点玩法图片上传
   */
  gardenPhotoUnitAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GardenPhotoUnitAddBody"];
      };
    };
    responses: {
      200: components["responses"]["GardenPhotoUnitAddRes"];
    };
  };
  /**
   * 庄园特点玩法图片获取
   * @description 庄园特点玩法图片获取
   */
  gardenPhotoUnitGet: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetId: components["parameters"]["targetId"];
        itemTemplateId: components["parameters"]["itemTemplateId"];
      };
    };
    responses: {
      200: components["responses"]["GardenPhotoUnitGetRes"];
    };
  };
  /**
   * 庄园特点玩法图片列表
   * @description 庄园特点玩法图片列表
   */
  gardenPhotoUnitList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetId: components["parameters"]["targetId"];
      };
    };
    responses: {
      200: components["responses"]["GardenPhotoUnitListRes"];
    };
  };
  /**
   * 新增势力大事 (游戏服务器调用)
   * @description 新增势力大事 (游戏服务器调用)
   */
  forceEventAdd: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["ForceEventAdd"];
      };
    };
    responses: {
      200: components["responses"]["ForceEventAddRes"];
    };
  };
  /**
   * 史官评论势力大事 (游戏服务器调用)
   * @description 史官评论势力大事 (游戏服务器调用)
   */
  forceEventRemark: {
    parameters: {
      query: {
        id: components["parameters"]["forceEventId"];
        roleId: components["parameters"]["forceEventRemarkRoleId"];
        roleName: components["parameters"]["forceEventRemarkRoleName"];
        content: components["parameters"]["forceEventContent"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 势力大事列表
   * @description 势力大事列表
   */
  forceEventList: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdRqd"];
        forceId: components["parameters"]["forceId"];
        serverId?: components["parameters"]["serverId"];
        ts?: components["parameters"]["timeStampOpt"];
        roleType: components["parameters"]["roleType"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["ForceEventListRes"];
    };
  };
  /**
   * 删除势力大事 (游戏服务器调用)
   * @description 删除势力大事 (游戏服务器调用)
   */
  forceEventDel: {
    parameters: {
      query: {
        roleId?: components["parameters"]["roleId"];
        forceId: components["parameters"]["forceId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 点赞势力大事
   * @description 点赞势力大事
   */
  forceEventLike: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdRqd"];
        id: components["parameters"]["forceEventId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 取消点赞势力大事
   * @description 取消点赞势力大事
   */
  forceEventCancelLike: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdRqd"];
        id: components["parameters"]["forceEventId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 健康检查
   * @description 健康检查
   */
  healthCheck: {
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 更新情缘信息 (CallByGameServer)
   * @description 更新情缘信息 (CallByGameServer)
   */
  marriageInfoUpdate: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["MarriageInfo"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 通知情缘id转服后变更 (CallByGameServer)
   * @description 通知情缘id转服后变更 (CallByGameServer)
   */
  marriageInfoTransfer: {
    parameters: {
      query: {
        oldId: components["parameters"]["marriageOldId"];
        newId: components["parameters"]["marriageNewId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 获取情缘信息
   * @description 获取情缘信息
   */
  marriageInfoShow: {
    parameters: {
      query: {
        roleId?: components["parameters"]["roleId"];
        marriageInfoId: components["parameters"]["marriageInfoId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code?: number;
            data?: components["schemas"]["MarriageInfo"];
          };
        };
      };
    };
  };
  /**
   * 导出官方号动态下详情(点赞评论转发数据)
   * @description 导出官方号动态下详情(点赞评论转发数据)
   */
  officialAccountsMomentsExportDetail: {
    parameters: {
      query: {
        momentId: components["parameters"]["momentId"];
        offset?: components["parameters"]["offset"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看某一个玩家的列表
   * @description 查看某一个玩家的列表
   */
  gmMemeList: {
    parameters: {
      query?: {
        roleId?: components["parameters"]["roleId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 审核玩家的一张表情包图片
   * @description 审核玩家的一张表情包图片
   */
  gmMemeAudit: {
    parameters: {
      query: {
        roleId?: components["parameters"]["roleId"];
        url: components["parameters"]["url"];
        auditStatus?: components["parameters"]["auditStatus"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 将非参数指定的日期的当日时长都清空
   * @description 用来模拟当日时长第二天凌晨会过期的规则
   */
  gmCloudGameDurationExpireDailyDuration: {
    parameters: {
      query: {
        ds: components["parameters"]["cloudGameDurationTodayStr"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 云游戏月卡时长需要合并
   * @description 云游戏月卡时长需要合并
   */
  gmCloudGameDurationMonthCardMergeServer: {
    requestBody: {
      content: {
        "application/json": {
          /** @example 2023-04-07 09:00 */
          time: string;
          /** @description 合并服务器信息 */
          merges: {
              /**
               * @description 被合并的服务器id
               * @example 342
               */
              from: number;
              /**
               * @description 合并指向的服务器id
               * @example 341
               */
              to: number;
            }[];
        };
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 模拟日志消费-云游戏充值元宝
   * @description 同步云游戏充值元宝的日志，处理消费逻辑
   */
  kafkaCloudGameDurationOnAddYuanbaoLog: {
    requestBody?: {
      content: {
        "text/plain": string;
      };
    };
    responses: {
      200: components["responses"]["CloudGameNotifyYuanBaoChargeRes"];
    };
  };
  /**
   * 展示当前urs账号云游戏时长信息以及月卡信息
   * @description 展示当前urs账号云游戏时长信息以及月卡信息
   */
  cloudGameDurationShow: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        serverId?: components["parameters"]["serverId"];
        userType?: components["parameters"]["cloudGameUserType"];
        ts?: components["parameters"]["GameServerTimestamp"];
      };
    };
    responses: {
      200: components["responses"]["CloudGameDurationShowRes"];
    };
  };
  /**
   * 展示当前urs账号服务器下月卡信息 (ip白名单授权)
   * @description 展示当前urs账号服务器下月卡信息 (ip白名单授权)
   */
  cloudGameDurationMonthCardShow: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        serverId?: components["parameters"]["serverId"];
      };
    };
    responses: {
      200: components["responses"]["CloudGameMonthCardShowRes"];
    };
  };
  /**
   * 游戏通知充值元宝数量，服务负责折算充值比例
   * @description 游戏通知充值元宝数量，服务负责折算充值比例
   */
  cloudGameDurationNotifyChargeYuanbao: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        userType?: components["parameters"]["cloudGameUserType"];
        orderId: components["parameters"]["cloudGameDurationChargeId"];
        num: components["parameters"]["cloudGameDurationYuanbao"];
        ts?: components["parameters"]["GameServerTimestamp"];
      };
    };
    responses: {
      200: components["responses"]["CloudGameNotifyYuanBaoChargeRes"];
    };
  };
  /**
   * 游戏通知购买月卡信息
   * @description 游戏通知购买月卡信息
   */
  cloudGameDurationNotifyBuyMonthCard: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        serverId?: components["parameters"]["serverId"];
        channel?: components["parameters"]["monthCardChannel"];
        orderId?: components["parameters"]["monthCardOrderId"];
        buyTime?: components["parameters"]["monthCardBuyTime"];
        duration?: components["parameters"]["monthCardDuration"];
      };
    };
    responses: {
      200: components["responses"]["cloudGameNotifyBuyMonthCardRes"];
    };
  };
  /**
   * 领取每日登录奖励时长
   * @description 领取每日登录奖励时长
   */
  cloudGameDurationReceiveDailyAward: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        userType?: components["parameters"]["cloudGameUserType"];
        ts?: components["parameters"]["GameServerTimestamp"];
      };
    };
    responses: {
      200: components["responses"]["CloudGameDurationChangeRes"];
    };
  };
  /**
   * 新增云游戏时长
   * @description 新增云游戏时长
   */
  cloudGameDurationIncr: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        userType?: components["parameters"]["cloudGameUserType"];
        type: components["parameters"]["cloudGameDurationType"];
        duration: components["parameters"]["cloudGameDuration"];
        ts?: components["parameters"]["GameServerTimestamp"];
      };
    };
    responses: {
      200: components["responses"]["CloudGameDurationChangeRes"];
    };
  };
  /**
   * 扣除云游戏时长
   * @description 扣除云游戏时长
   */
  cloudGameDurationDecr: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        userType?: components["parameters"]["cloudGameUserType"];
        type: components["parameters"]["cloudGameCostType"];
        duration: components["parameters"]["cloudGameDuration"];
        ts?: components["parameters"]["GameServerTimestamp"];
      };
    };
    responses: {
      200: components["responses"]["CloudGameDurationChangeRes"];
    };
  };
  /**
   * 查看动态列表
   * @description 查看动态列表
   */
  adminMomentPickList: {
    parameters: {
      query?: {
        pickType?: components["parameters"]["momentPickType"];
        kw?: components["parameters"]["momentPickKw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["MomentPickListRes"];
    };
  };
  /**
   * 批量更新精选状态
   * @description 批量更新精选状态
   */
  adminMomentPickUpdateStatus: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["MomentPickBatchOp"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 移动该精选状态到最前
   * @description 移动该精选状态到最前
   */
  adminMomentPickToggleTop: {
    parameters: {
      query: {
        momentId: components["parameters"]["momentId"];
        switch: components["parameters"]["switch"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查看情缘所有照片墙列表
   * @description 查看情缘所有照片墙列表
   */
  marriagePhotoWallList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        marriageId: components["parameters"]["marriageId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallListRes"];
    };
  };
  /**
   * 查看照片墙中具体图片
   * @description 查看照片墙中具体图片
   */
  marriagePhotoWallPhotoIdShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallPhotoShowRes"];
    };
  };
  /**
   * 移动照片墙中图片的槽位
   * @description 移动照片墙中图片的槽位
   */
  marriagePhotoWallPhotoIdMove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        slot: components["parameters"]["photoWallSlot"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 情缘当家从精选墙上架图片 (CallByGameServer)
   * @description 情缘当家从精选墙上架图片 (CallByGameServer)
   */
  marriagePhotoWallHandpickWallUp: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        photoId: components["parameters"]["photoWallPhotoIdInQuery"];
        wallId: components["parameters"]["photoWallWallId"];
        slot: components["parameters"]["photoWallSlot"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 情缘当家从精选墙下架图片(CallByGameServer)
   * @description 情缘当家从精选墙下架图片(CallByGameServer)
   */
  marriagePhotoWallHandpickWallDown: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wallId: components["parameters"]["photoWallWallId"];
        slot: components["parameters"]["photoWallSlot"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除照片墙的这张图片 (CallByGameServer)
   * @description 删除照片墙的这张图片 (CallByGameServer)
   */
  marriagePhotoWallPhotoIdDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        marriageId: components["parameters"]["marriageId"];
        isLeader: components["parameters"]["isGuildLeader"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 点赞照片墙中具体图片
   * @description 点赞照片墙中具体图片
   */
  marriagePhotoWallPhotoIdLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 取消点赞照片墙中具体图片
   * @description 取消点赞照片墙中具体图片
   */
  marriagePhotoWallPhotoIdCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 点赞照片的评论
   * @deprecated
   * @description 点赞照片的评论
   */
  marriagePhotoWallCommentIdLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 取消点赞照片的评论
   * @deprecated
   * @description 取消点赞照片的评论
   */
  marriagePhotoWallCommentIdCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看照片墙图片的评论列表
   * @description 查看照片墙图片的评论列表
   */
  marriagePhotoWallPhotoIdCommentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallCommentListRes"];
    };
  };
  /**
   * 添加照片墙图片的评论
   * @description 添加照片墙图片的评论
   */
  marriagePhotoWallPhotoIdCommentAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        text: components["parameters"]["commentText"];
        replyId?: components["parameters"]["replyId"];
      };
      path: {
        id: components["parameters"]["photoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除照片墙图片的评论 (CallByGameServer)
   * @description 删除照片墙图片的评论 (CallByGameServer)
   */
  marriagePhotoWallPhotoPhotoIdCommentDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        isLeader: components["parameters"]["isGuildLeader"];
        marriageId: components["parameters"]["marriageId"];
        id: components["parameters"]["photoWallCommentIdInQuery"];
      };
      path: {
        photo_id: components["parameters"]["photoWallPhotoIdFull"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 上传照片墙里的图片
   * @description 上传照片墙里的图片
   */
  marriagePhotoWallIdPhotoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        overwrite?: components["parameters"]["gpwOverwrite"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PhotoWallPhotoAdd"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看照片墙
   * @description 查看照片墙
   */
  guildPhotoWallIdShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallId"];
      };
    };
    responses: {
      200: components["responses"]["PhotoWallShowRes"];
    };
  };
  /**
   * 更新照片墙信息 (CallByGameServer)
   * @description 更新照片墙信息 (CallByGameServer)
   */
  guildPhotoWallIdUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PhotoWallAdd"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 开启新的照片墙 (CallByGameServer)
   * @description 开启新的照片墙 (CallByGameServer)
   */
  guildPhotoWallAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        guildId: components["parameters"]["guildId"];
        guildLevel: components["parameters"]["guildLevel"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PhotoWallAdd"];
      };
    };
    responses: {
      200: components["responses"]["PhotoWallAddRes"];
    };
  };
  /**
   * 通知列表
   * @description 通知列表
   */
  guildPhotoWallNotificationsList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        status?: components["parameters"]["notificationStatus"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallNotificationListRes"];
    };
  };
  /**
   * 单个通知设为已读
   * @description 单个通知设为已读
   */
  guildPhotoWallNotificationsRead: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notificationId: components["parameters"]["notificationId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 通知列表全部设为已读
   * @description 通知列表全部设为已读
   */
  guildPhotoWallNotificationsReadAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查看帮会所有照片墙列表
   * @description 查看帮会所有照片墙列表
   */
  guildPhotoWallList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        guildId: components["parameters"]["guildId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallListRes"];
    };
  };
  /**
   * 查看照片墙中具体图片
   * @description 查看照片墙中具体图片
   */
  guildPhotoWallPhotoIdShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallPhotoShowRes"];
    };
  };
  /**
   * 移动照片墙中图片的槽位
   * @description 移动照片墙中图片的槽位
   */
  guildPhotoWallPhotoIdMove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        slot: components["parameters"]["guildPhotoWallSlot"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 帮会当家从精选墙上架图片 (CallByGameServer)
   * @description 帮会当家从精选墙上架图片 (CallByGameServer)
   */
  guildPhotoWallHandpickWallUp: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        photoId: components["parameters"]["guildPhotoWallPhotoIdInQuery"];
        wallId: components["parameters"]["guildPhotoWallWallId"];
        slot: components["parameters"]["guildPhotoWallSlot"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 帮会当家从精选墙下架图片(CallByGameServer)
   * @description 帮会当家从精选墙下架图片(CallByGameServer)
   */
  guildPhotoWallHandpickWallDown: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wallId: components["parameters"]["guildPhotoWallWallId"];
        slot: components["parameters"]["guildPhotoWallSlot"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除照片墙的这张图片 (CallByGameServer)
   * @description 删除照片墙的这张图片 (CallByGameServer)
   */
  guildPhotoWallPhotoIdDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        isLeader: components["parameters"]["isGuildLeader"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 点赞照片墙中具体图片
   * @description 点赞照片墙中具体图片
   */
  guildPhotoWallPhotoIdLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 取消点赞照片墙中具体图片
   * @description 取消点赞照片墙中具体图片
   */
  guildPhotoWallPhotoIdCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 点赞照片的评论
   * @deprecated
   * @description 点赞照片的评论
   */
  guildPhotoWallCommentIdLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 取消点赞照片的评论
   * @deprecated
   * @description 取消点赞照片的评论
   */
  guildPhotoWallCommentIdCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看照片墙图片的评论列表
   * @description 查看照片墙图片的评论列表
   */
  guildPhotoWallPhotoIdCommentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallCommentListRes"];
    };
  };
  /**
   * 添加照片墙图片的评论
   * @description 添加照片墙图片的评论
   */
  guildPhotoWallPhotoIdCommentAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        text: components["parameters"]["commentText"];
        replyId?: components["parameters"]["replyId"];
        canComment?: components["parameters"]["canComment"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除照片墙图片的评论 (CallByGameServer)
   * @description 删除照片墙图片的评论 (CallByGameServer)
   */
  guildPhotoWallPhotoPhotoIdCommentDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        isLeader: components["parameters"]["isGuildLeader"];
        id: components["parameters"]["photoWallCommentIdInQuery"];
      };
      path: {
        photo_id: components["parameters"]["photoWallPhotoIdFull"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 上传照片墙里的图片
   * @description 上传照片墙里的图片
   */
  guildPhotoWallIdPhotoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        overwrite?: components["parameters"]["gpwOverwrite"];
        canComment?: components["parameters"]["canComment"];
      };
      path: {
        id: components["parameters"]["guildPhotoWallId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GuildPhotoWallPhotoAdd"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看照片墙
   * @description 查看照片墙
   */
  marriagePhotoWallIdShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallId"];
      };
    };
    responses: {
      200: components["responses"]["PhotoWallShowRes"];
    };
  };
  /**
   * 更新照片墙信息 (CallByGameServer)
   * @description 更新照片墙信息 (CallByGameServer)
   */
  marriagePhotoWallIdUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["photoWallId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PhotoWallAdd"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 开启新的照片墙 (CallByGameServer)
   * @description 开启新的照片墙 (CallByGameServer)
   */
  marriagePhotoWallAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        marriageId: components["parameters"]["marriageId"];
        marriageLevel: components["parameters"]["marriageLevel"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PhotoWallAdd"];
      };
    };
    responses: {
      200: components["responses"]["PhotoWallAddRes"];
    };
  };
  /**
   * 通知列表
   * @description 通知列表
   */
  marriagePhotoWallNotificationsList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        status?: components["parameters"]["notificationStatus"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallNotificationListRes"];
    };
  };
  /**
   * 单个通知设为已读
   * @description 单个通知设为已读
   */
  marriagePhotoWallNotificationsRead: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notificationId: components["parameters"]["notificationId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 通知列表全部设为已读
   * @description 通知列表全部设为已读
   */
  marriagePhotoWallNotificationsReadAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查看联居所有照片墙列表
   * @description 查看联居所有照片墙列表
   */
  multiGardenPhotoWallList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        multiGardenId: components["parameters"]["multiGardenId"];
      };
    };
    responses: {
      200: components["responses"]["MultiGardenPhotoWallListRes"];
    };
  };
  /**
   * 查看照片墙中具体图片
   * @description 查看照片墙中具体图片
   */
  multiGardenPhotoWallPhotoIdShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["MultiGardenPhotoWallPhotoShowRes"];
    };
  };
  /**
   * 移动照片墙中图片的槽位
   * @description 移动照片墙中图片的槽位
   */
  multiGardenPhotoWallPhotoIdMove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        slot: components["parameters"]["multiGardenPhotoWallSlot"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 联居当家从精选墙上架图片 (CallByGameServer)
   * @description 联居当家从精选墙上架图片 (CallByGameServer)
   */
  multiGardenPhotoWallHandpickWallUp: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        photoId: components["parameters"]["multiGardenPhotoWallPhotoIdInQuery"];
        wallId: components["parameters"]["multiGardenPhotoWallWallId"];
        slot: components["parameters"]["multiGardenPhotoWallSlot"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 帮会当家从精选墙下架图片(CallByGameServer)
   * @description 帮会当家从精选墙下架图片(CallByGameServer)
   */
  multiGardenPhotoWallHandpickWallDown: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wallId: components["parameters"]["multiGardenPhotoWallWallId"];
        slot: components["parameters"]["multiGardenPhotoWallSlot"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除照片墙的这张图片 (CallByGameServer)
   * @description 删除照片墙的这张图片 (CallByGameServer)
   */
  multiGardenPhotoWallPhotoIdDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        isLeader: components["parameters"]["isMultiGardenLeader"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 点赞照片墙中具体图片
   * @description 点赞照片墙中具体图片
   */
  multiGardenPhotoWallPhotoIdLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 取消点赞照片墙中具体图片
   * @description 取消点赞照片墙中具体图片
   */
  multiGardenPhotoWallPhotoIdCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 点赞照片的评论
   * @deprecated
   * @description 点赞照片的评论
   */
  multiGardenPhotoWallCommentIdLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 取消点赞照片的评论
   * @deprecated
   * @description 取消点赞照片的评论
   */
  multiGardenPhotoWallCommentIdCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看照片墙图片的评论列表
   * @description 查看照片墙图片的评论列表
   */
  multiGardenPhotoWallPhotoIdCommentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["GuildPhotoWallCommentListRes"];
    };
  };
  /**
   * 添加照片墙图片的评论
   * @description 添加照片墙图片的评论
   */
  multiGardenPhotoWallPhotoIdCommentAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        text: components["parameters"]["commentText"];
        replyId?: components["parameters"]["replyId"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallPhotoId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除照片墙图片的评论 (CallByGameServer)
   * @description 删除照片墙图片的评论 (CallByGameServer)
   */
  multiGardenPhotoWallPhotoPhotoIdCommentDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        isLeader: components["parameters"]["isMultiGardenLeader"];
        id: components["parameters"]["multiGardenPhotoWallCommentIdInQuery"];
      };
      path: {
        photo_id: components["parameters"]["multiGardenPhotoWallPhotoIdFull"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 上传照片墙里的图片
   * @description 上传照片墙里的图片
   */
  multiGardenPhotoWallIdPhotoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        overwrite?: components["parameters"]["gpwOverwrite"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GuildPhotoWallPhotoAdd"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查看照片墙
   * @description 查看照片墙
   */
  multiGardenPhotoWallIdShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallId"];
      };
    };
    responses: {
      200: components["responses"]["MultiGardenPhotoWallShowRes"];
    };
  };
  /**
   * 更新照片墙信息 (CallByGameServer)
   * @description 更新照片墙信息 (CallByGameServer)
   */
  multiGardenPhotoWallIdUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
      path: {
        id: components["parameters"]["multiGardenPhotoWallId"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PhotoWallAdd"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 开启新的照片墙 (CallByGameServer)
   * @description 开启新的照片墙 (CallByGameServer)
   */
  multiGardenPhotoWallAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        multiGardenId: components["parameters"]["multiGardenId"];
        multiGardenLevel: components["parameters"]["multiGardenLevel"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["MultiGardenPhotoWallAdd"];
      };
    };
    responses: {
      200: components["responses"]["MultiGardenPhotoWallAddRes"];
    };
  };
  /**
   * 通知列表
   * @description 通知列表
   */
  multiGardenPhotoWallNotificationsList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        status?: components["parameters"]["notificationStatus"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["MultiGardenPhotoWallNotificationListRes"];
    };
  };
  /**
   * 单个通知设为已读
   * @description 单个通知设为已读
   */
  multiGardenPhotoWallNotificationsRead: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notificationId: components["parameters"]["notificationId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 通知列表全部设为已读
   * @description 通知列表全部设为已读
   */
  multiGardenPhotoWallNotificationsReadAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查看收件箱便利贴详情
   * @description 查看收件箱便利贴详情
   */
  noteMailMailBoxTypeShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["noteMailId"];
      };
      path: {
        mail_box_type: components["parameters"]["mailBoxType"];
      };
    };
    responses: {
      200: components["responses"]["NoteMailInboxShowRes"];
    };
  };
  /**
   * 删除便利贴
   * @description 删除便利贴
   */
  noteMailMailBoxTypeDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["noteMailId"];
      };
      path: {
        mail_box_type: components["parameters"]["mailBoxType"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 收件箱邮件列表
   * @description 收件箱邮件列表
   */
  noteMailInboxList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        kw?: components["parameters"]["noteMailInboxListKw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["NoteMailInBoxListRes"];
    };
  };
  /**
   * 发件箱邮件列表
   * @description 发件箱邮件列表
   */
  noteMailOutboxList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        kw?: components["parameters"]["noteMailOutboxListKw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["NoteMailInBoxListRes"];
    };
  };
  /**
   * 收藏便利贴邮件
   * @description 收藏便利贴邮件
   */
  noteMailMailBoxTypeStar: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["noteMailId"];
      };
      path: {
        mail_box_type: components["parameters"]["mailBoxType"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 取消收藏便利贴邮件
   * @description 取消收藏便利贴邮件
   */
  noteMailMailBoxTypeUnstar: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["noteMailId"];
      };
      path: {
        mail_box_type: components["parameters"]["mailBoxType"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 发送便利贴邮件 (只支持游戏服务器通过ip白名单访问)
   * @description 发送便利贴邮件 (只支持游戏服务器通过ip白名单访问)
   */
  noteMailSend: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["NoteMailSend"];
      };
    };
    responses: {
      200: components["responses"]["NoteMailSendRes"];
    };
  };
  /**
   * 清理玩家的便利贴信息，包括收和发, 比如藏宝阁交易时 (CallByGameServer)
   * @description 清理玩家的便利贴信息，包括收和发, 比如藏宝阁交易时 (CallByGameServer)
   */
  noteMailCleanAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 读取周人气
   * @description 读取周人气
   */
  getWeekRenQiGm: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        ds: components["parameters"]["weekStr"];
      };
    };
    responses: {
      200: components["responses"]["GmWeekRenQiRes"];
    };
  };
  /**
   * 批量查询家长守护防沉迷规则(不使用gameId)
   * @description 批量查询家长守护防沉迷规则(不使用gameId)
   */
  gmGbSuperAasLimitUrsAccount: {
    parameters: {
      path: {
        account: components["parameters"]["accountInPath"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 查询家长守护防沉迷规则
   * @description [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#5.-%E6%9F%A5%E8%AF%A2%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
   */
  getGmGbSuperAasLimitGameidUrsAccount: {
    parameters: {
      path: {
        gameid: components["parameters"]["gameidInPath"];
        account: components["parameters"]["accountInPath"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 设置或者更新家长守护防沉迷规则
   * @description [计费对应上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#3.-%E6%B7%BB%E5%8A%A0(%E6%88%96%E6%9B%B4%E6%96%B0)%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
   */
  postGmGbSuperAasLimitGameidUrsAccount: {
    parameters: {
      path: {
        gameid: components["parameters"]["gameidInPath"];
        account: components["parameters"]["accountInPath"];
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GbSuperAssLimitFcmRuleReq"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除家长守护防沉迷规则
   * @description [计费上游接口](https://billing.matrix.netease.com/docs/help-center?app=gas3&lang=zh&path=support-api%2Faas%2Fsuper_aas_limit#4-%E5%88%A0%E9%99%A4%E8%B4%A6%E5%8F%B7%E9%98%B2%E6%B2%89%E8%BF%B7%E8%A7%84%E5%88%99)
   */
  deleteGmGbSuperAasLimitGameidUrsAccount: {
    parameters: {
      path: {
        gameid: components["parameters"]["gameidInPath"];
        account: components["parameters"]["accountInPath"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加天谕踢人任务
   * @description 添加天谕踢人任务
   */
  gmFcmTyAddKickoffTask: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        kickDate: components["parameters"]["kickDate"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 查询当日在线时长
   * @description 查询当日在线时长
   */
  gmFcmGameidGetDailyOnlineTime: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
      };
      path: {
        gameid: components["parameters"]["gameidInPath"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 设置当日在线时长
   * @description 设置当日在线时长
   */
  gmFcmGameidSetDailyOnlineTime: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        duration?: number;
      };
      path: {
        gameid: components["parameters"]["gameidInPath"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 设置周人气
   * @description 设置周人气
   */
  setWeekRenqiGm: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        ds: components["parameters"]["weekStr"];
        renqi: components["parameters"]["weekRenqi"];
      };
    };
    responses: {
      200: components["responses"]["GmWeekRenQiRes"];
    };
  };
  /**
   * 设置图片视频审核状态
   * @description 设置图片视频审核状态
   */
  gmAuditUpdateStatus: {
    parameters: {
      query?: {
        mediaType?: components["parameters"]["mediaType"];
        resourceId?: components["parameters"]["mediaResourceId"];
        auditStatus?: components["parameters"]["auditStatus"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查看设置的合服关系列表
   * @description 查看设置的合服关系列表
   */
  gmServerListMergeServerList: {
    responses: {
      200: components["responses"]["GmServerListMergeServerListRes"];
    };
  };
  /**
   * 添加合服关系
   * @description 添加合服关系
   */
  gmServerListMergeServerAdd: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["GmServerListMergeServerItem"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 清理所有设置的合服关系
   * @description 清理所有设置的合服关系
   */
  gmServerListMergeServerClean: {
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 点赞编年史事件
   * @description 点赞编年史事件
   */
  serverAnnalEventUpvote: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["eventId"];
        undo?: components["parameters"]["undo"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 点踩编年史事件
   * @description 点踩编年史事件
   */
  serverAnnalEventDownvote: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["eventId"];
        undo?: components["parameters"]["undo"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 获取编年史事件列表
   * @description 获取编年史事件列表
   */
  getServerAnnalEventList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        serverId?: components["parameters"]["serverId"];
        category?: components["parameters"]["EventCategory"];
        kw?: components["parameters"]["RoleIdAndNameKw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GetServerAnnalEventListRes"];
    };
  };
  /**
   * 获取个人累计积分数据
   * @description 获取个人累计积分数据
   */
  serverAnnalScore: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ServerAnnalScoreRes"];
    };
  };
  /**
   * 风云人物
   * @description 风云人物
   */
  getServerAnnalFengyunPlayers: {
    parameters: {
      query?: {
        serverId?: components["parameters"]["serverId"];
        size?: components["parameters"]["size"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /** @description 排行的数组(前3) */
              list?: components["schemas"]["ServerAnnalFengyunPlayer"][];
            };
          };
        };
      };
    };
  };
  /**
   * 游戏服务器同步编年史事件 (IP白名单授权)
   * @description 游戏服务器同步编年史事件 (IP白名单授权)
   */
  syncServerAnnal: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["SyncServerAnnalEvent"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 查询账号封禁信息
   * @description 查询账号封禁信息
   */
  fcmBanAccountShow: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
      };
    };
    responses: {
      200: components["responses"]["BanAccountShowRes"];
    };
  };
  /**
   * 添加账号封禁
   * @description 添加账号封禁
   */
  fcmBanAccountAdd: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        banTime?: components["parameters"]["banAccountTime"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 解除账号封禁
   * @description 解除账号封禁
   */
  fcmBanAccountRemove: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 获取玩家编年史事件列表
   * @description 获取玩家编年史事件列表
   */
  playerAnnalList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GetPlayerAnnalEventListRes"];
    };
  };
  /**
   * 游戏服务器同步编年史事件 (CallByGameServer)
   * @description 游戏服务器同步编年史事件 (CallByGameServer)
   */
  playerAnnalSync: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["SyncPlayerAnnalEvent"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 实名同步（游戏专用）
   * @description 游戏服务器同步给梦岛，梦岛负责查询中宣部认证信息后，之后同步给urs [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=459#/)
   */
  syncRealName: {
    parameters: {
      query: {
        username: components["parameters"]["username"];
        product: components["parameters"]["product"];
        idnum: components["parameters"]["idnum"];
        idtype: components["parameters"]["idtype"];
        realname: components["parameters"]["realname"];
        userip?: components["parameters"]["userip"];
        status?: components["parameters"]["syncStatus"];
        pi?: components["parameters"]["syncPi"];
      };
    };
    responses: {
      /** @description 保持和原urs接口的一致性 */
      200: {
        content: {
          "application/json": components["schemas"]["UrsApiRet"];
        };
      };
    };
  };
  /**
   * 手机帐号查询实名接口
   * @description 接口参数格式保持和上游一致 [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=105#/)
   */
  queryRealNameStatusByYd: {
    parameters: {
      query: {
        username: components["parameters"]["ydUsername"];
        product: components["parameters"]["product"];
        userip?: components["parameters"]["userip"];
        needPi?: components["parameters"]["needPi"];
      };
    };
    responses: {
      /** @description 保持和原urs接口的一致性 */
      200: {
        content: {
          "application/json": components["schemas"]["QueryRealNameStatus"];
        };
      };
    };
  };
  /**
   * 查询实名认证和防沉迷
   * @description 接口参数格式保持和上游一致 [urs对应文档](https://urs.hz.netease.com/docDetail.html?pid=170#/)
   */
  queryRealNameAndAntiIndulgence: {
    parameters: {
      query: {
        username: components["parameters"]["username"];
        product: components["parameters"]["product"];
        accept?: "application/json" | "text/plain";
      };
    };
    responses: {
      /**
       * @description #### 保持和原urs接口的一致性
       * super_aas_limit_wrap会被 base64(JSON.stringify(super_aas_limit_wrap))拼接到返回中, 结构和含义参考手机账号接口
       */
      200: {
        content: {
          "text/plain": string;
        };
      };
    };
  };
  /**
   * urs中实名无法区分来自哪个游戏，但是中宣实名上报时，需要区分游戏，并用到游戏对应的参数（中宣分配）， 因此，由计费搜集urs-gameid的映射关系，并提供给urs查询
   * @description urs中实名无法区分来自哪个游戏，但是中宣实名上报时，需要区分游戏，并用到游戏对应的参数（中宣分配）， 因此，由计费搜集urs-gameid的映射关系，并提供给urs查询
   */
  queryGameIdByUrs: {
    parameters: {
      query: {
        username: components["parameters"]["username"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["QueryGameIdByUrs"];
        };
      };
    };
  };
  /**
   * 上报登录行为
   * @description 上报登录行为
   */
  loginBehavior: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        urs: components["parameters"]["urs"];
        gameid: components["parameters"]["fcmGameId"];
        ts?: components["parameters"]["timestamp"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["ApiOk"];
        };
      };
    };
  };
  /**
   * 上报登出行为
   * @description 上报登出行为
   */
  logoutBehavior: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        urs: components["parameters"]["urs"];
        gameid: components["parameters"]["fcmGameId"];
        ts?: components["parameters"]["timestamp"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["ApiOk"];
        };
      };
    };
  };
  /**
   * 清理梦岛历史(删除状态， 留言板， 通知)
   * @description 用于提供个买号玩家清理之前账号历史记录的接口
   */
  playerCleanHistory: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 编辑签名
   * @description 编辑签名
   */
  changesign: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 签名 */
        signature: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 复制旧号朋友圈动态
   * @description 换号时支持把旧号上的朋友圈状态复制到新号上
   * 全部由旧号发出的朋友圈状态，不包括状态下的转发、评论、点赞，不包括留言板，不包括左侧个人资料
   * [ReadMine](http://cloud.pm.netease.com/v6/issues/60699)
   */
  copyMoments: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 旧号RoleId */
        copyRoleId: number;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      200: components["responses"]["PlayerCopyMoments"];
    };
  };
  /**
   * 点赞心情
   * @description 点赞心情
   */
  likemoment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        action?: components["parameters"]["likeAction"];
        hotFactor?: components["parameters"]["hotFactor"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取发布檄文的token(游戏服务器申请)
   * @description 获取发布檄文的token(游戏服务器申请)
   */
  guildPolemicApplyAddToken: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        guildId: components["parameters"]["guildId"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(guildId + nonce + roleid +  time + auth_slat) */
        token?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加帮会檄文
   * @description 添加帮会檄文
   */
  guildPolemicAdd: {
    parameters: {
      query: {
        pubToken?: components["parameters"]["pubToken"];
        roleid: components["parameters"]["roleid"];
        roleName?: components["parameters"]["roleName"];
        /** @description 服务器ID */
        serverId?: number;
        /** @description 檄文标题 */
        title?: string;
        /** @description 檄文内容 */
        content?: string;
        /** @description 发布者帮会id */
        guildId?: number;
        /** @description 发布者帮会名字 */
        guildName?: string;
        /** @description 对手帮会id */
        toGuildId?: number;
        /** @description 对手帮会名字 */
        toGuildName?: string;
        /** @description 对手Serve名字 */
        toServerName?: string;
        /** @description 檄文可见 1 => 全部  2 => 帮会 */
        visibility?: 1 | 2;
        /** @description 檄文发布风格 1=>横排， 2=>竖排 */
        style?: 1 | 2;
      };
    };
    responses: {
      /** @description ok */
      200: {
        content: never;
      };
    };
  };
  /**
   * 点赞檄文
   * @description 点赞檄文
   */
  guildPolemicLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id?: components["parameters"]["polemic_id"];
      };
    };
    responses: {
      /** @description ok */
      200: {
        content: never;
      };
    };
  };
  /**
   * 取消点赞檄文
   * @description 取消点赞檄文
   */
  guildPolemicCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id?: components["parameters"]["polemic_id"];
      };
    };
    responses: {
      /** @description ok */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取檄文详情
   * @description 获取檄文详情
   */
  guildPolemicGetDetail: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id?: components["parameters"]["polemic_id"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取檄文列表
   * @description 获取檄文列表
   */
  guildPolemicList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        guildId: components["parameters"]["guildId"];
        serverId?: components["parameters"]["serverId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取热门檄文列表
   * @description 获取热门檄文列表
   */
  guildPolemicListHot: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 筛选类型(本服热点， 全服热门) */
        type?: "local" | "all";
        serverId?: components["parameters"]["serverId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加帮会表情
   * @description 添加帮会表情
   */
  guildExpressionAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        serverId?: components["parameters"]["serverId"];
        guildId: components["parameters"]["guildId"];
        url: components["parameters"]["url"];
        /** @description 发布风格 1=>方形， 2=>圆形 */
        style?: 1 | 2;
      };
    };
    responses: {
      /** @description ok */
      200: {
        content: never;
      };
    };
  };
  /**
   * 查看帮会表情
   * @description 查看帮会表情
   */
  guildExpressionByGuildId: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        serverId?: components["parameters"]["serverId"];
        guildId: components["parameters"]["guildId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["GuildExpressionRes"];
        };
      };
    };
  };
  /**
   * 同步俱乐部相关数据(游戏服务器调用)
   * @description 同步俱乐部相关数据(游戏服务器调用)
   */
  clubSyncDataName: {
    parameters: {
      query?: {
        /** @description 同步类型对应payload数据(json字符串) */
        payload?: string;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat) */
        token?: string;
      };
      path: {
        /**
         * @description | name           | description        |
         * |----------------|--------------------|
         * | clubList       | 俱乐部列表         |
         * | clubDismiss    | 俱乐部解散         |
         * | GPLSClubStat   | 公平联赛俱乐部数据 |
         * | GPLSPlayerStat | 公平联赛玩家数据   |
         * | YZClubStat     | 约战俱乐部数据     |
         */
        name: "clubList" | "clubDismiss" | "GPLSClubStat" | "GPLSPlayerStat" | "YZClubStat";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 同步俱乐部约战统计数据
   * @description [比赛数据查询接口](/swagger/nsh/#/club/GetClubMatchStatistic)
   */
  clubSyncDataYzClubStat: {
    parameters: {
      query?: {
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat) */
        token?: string;
      };
    };
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 约战数据(json字符串)
           * @example {"clubId":1593955470969,"attend":1,"win":0,"resource":24050,"destroyBase":0,"destroyTower":0,"kill":0,"winTime":0,"loseTime":2400,"playerNum":0,"useCard":0,"shiQi":1,"die":0}
           */
          payload?: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取俱乐部额外信息 (俱乐部荣誉值， 成员列表荣誉值以及认证选手标记)
   * @description 获取俱乐部额外信息 (俱乐部荣誉值， 成员列表荣誉值以及认证选手标记)
   */
  clubGetExtraInfos: {
    parameters: {
      query?: {
        /** @description 俱乐部id */
        club_id?: number;
        /** @description 成员玩家id列表(csv格式，逗号分开) */
        role_ids?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 批量获取俱乐部图标
   * @description 批量获取俱乐部图标
   */
  clubGetIcons: {
    parameters: {
      query?: {
        /** @description Club Ids (csv syntax, separate by comma) */
        club_ids?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取俱乐部列表
   * @description 获取俱乐部列表
   */
  clubList: {
    parameters: {
      query?: {
        /** @description 俱乐部列表，排序需要最前面 */
        apply_club_ids?: string;
        /** @description 所属俱乐部 */
        my_club_id?: string;
        /** @description 本帮帮会id */
        guild_id?: string;
        gForce?: components["parameters"]["gForce"];
        /** @description 服务器id */
        server_id?: number;
        /**
         * @description | Sort| Value |
         * |--|:---:|
         * | 荣誉点倒序   | 1 |
         * | 成员数量倒序 | 2 |
         * | 帮会排名 | 3 |
         */
        sort_type?: 1 | 2 | 3;
        /** @description 俱乐部名字(部分匹配) */
        kw?: string;
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部赛事统计
   * @description 俱乐部赛事统计
   */
  getClubMatchStatistic: {
    parameters: {
      query?: {
        club_id?: components["parameters"]["clubId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 个人赛事统计
   * @description 个人赛事统计
   */
  clubPlayerMatchStatistics: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 查询玩家俱乐部相关信息
   * @description 查询玩家俱乐部相关信息
   */
  clubPlayerInfo: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 清理玩家俱乐部相关荣誉
   * @description 清理玩家俱乐部相关荣誉
   */
  clubPlayerClean: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(nonce + roleid + time + auth_slat) */
        token?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 列出认证选手列表
   * @description 列出认证选手列表
   */
  clubCertifiedPlayersList: {
    parameters: {
      query?: {
        /** @description player join club status */
        join_status?: "all" | "join" | "free";
        /** @description 玩家认证角色类型   1=>所有 2=>操作手 3=>指挥官  4=>双重认证 */
        role_type?: 1 | 2 | 3 | 4;
        /** @description search keyword */
        kw?: string;
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取认证选手详情
   * @description 获取认证选手详情
   */
  clubCertifiedPlayersDetail: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["ApiOk"] & {
            data?: components["schemas"]["ClubCpDetail"];
          };
        };
      };
    };
  };
  /**
   * 获取俱乐部详情
   * @description 获取俱乐部详情
   */
  clubDetail: {
    parameters: {
      query?: {
        club_id?: components["parameters"]["clubId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部比赛排行
   * @description 俱乐部比赛排行
   */
  clubMatchRankList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /**
         * @description | name  | description |
         * | ---   | ---         |
         * | gpls  | 公平联赛    |
         * | mxyqs | 明星邀请赛  |
         * | nhzf  | 怒海争锋 |
         */
        matchType?: "gpls" | "mxyqs" | "nhzf";
        whichTimes?: components["parameters"]["whichTimes"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部荣誉交换位置
   * @description 俱乐部荣誉交换位置
   */
  clubHonorsLocationSwap: {
    parameters: {
      query?: {
        club_id?: components["parameters"]["clubId"];
        from?: components["parameters"]["from"];
        to?: components["parameters"]["to"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部荣誉恢复位置
   * @description 俱乐部荣誉恢复位置
   */
  clubHonorsLocationRestore: {
    parameters: {
      query?: {
        club_id?: components["parameters"]["clubId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取赛事选项
   * @description 获取赛事选项
   */
  openCompetitionOptions: {
    responses: {
      /** @description OK */
      200: components["responses"]["CompetitionOptionsRes"];
    };
  };
  /**
   * 获取赛事结果
   * @description 获取赛事结果
   */
  openCompetitionList: {
    parameters: {
      query?: {
        competitionId?: components["parameters"]["competitionId"];
        serverType?: components["parameters"]["serverType"];
        whichTimes?: components["parameters"]["whichTimes"];
      };
    };
    responses: {
      /** @description OK */
      200: components["responses"]["WebCompetitionListRes"];
    };
  };
  /**
   * 俱乐部赛事中心登录
   * @description 俱乐部赛事中心登录
   */
  clubWebAuthLogin: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 列出角色列表
   * @description 列出角色列表
   */
  clubWebRolesList: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        /** @description 易盾key */
        neDunKey?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 切换urs账号绑定角色
   * @description 切换urs账号绑定角色
   */
  clubWebRolesBind: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取登录信息
   * @description 获取登录信息
   */
  clubWebLoginInfo: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 发送认证指挥的短信验证码
   * @description 发送认证指挥的短信验证码
   */
  clubWebCommandersApplySmsCode: {
    parameters: {
      query?: {
        phone?: components["parameters"]["phone"];
        /** @description 易盾key */
        neDunKey?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 申请认证指挥
   * @description 申请认证指挥
   */
  clubWebCommandersApply: {
    /** @description 申请认证信息 */
    requestBody: {
      content: {
        "application/json": components["schemas"]["ApplyCommander"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 推荐指挥人选
   * @description 推荐指挥人选
   */
  clubWebCommandersRecommend: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 申请认证操作手
   * @description 申请认证操作手
   */
  clubWebOperatorsApply: {
    /** @description 申请认证信息 */
    requestBody: {
      content: {
        "application/json": components["schemas"]["ApplyOperator"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 认证玩家列表
   * @description 认证玩家列表
   */
  clubWebCertifiedPlayersList: {
    parameters: {
      query?: {
        /** @description 认证类型 */
        role_type?: "commander" | "operator";
        kw?: components["parameters"]["kw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 认证玩家详情
   * @description 认证玩家详情
   */
  clubWebCertifiedPlayersDetail: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部列表
   * @description 俱乐部列表
   */
  clubWebList: {
    parameters: {
      query?: {
        kw?: components["parameters"]["kw"];
        gForce?: components["parameters"]["gForce"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部详情页
   * @description 俱乐部详情页
   */
  clubWebDetail: {
    parameters: {
      query?: {
        club_id?: components["parameters"]["clubId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部赛事统计
   * @description 俱乐部赛事统计
   */
  clubWebMatchStatistics: {
    parameters: {
      query?: {
        club_id?: components["parameters"]["clubId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 个人赛事统计
   * @description 个人赛事统计
   */
  clubWebPlayerMatchStatistics: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 编辑俱乐部信息
   * @description 编辑俱乐部信息
   */
  clubWebEdit: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        club_id?: components["parameters"]["clubId"];
        /** @description 俱乐部图表 */
        icon?: string;
        /** @description 俱乐部简介 */
        intro?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 俱乐部排名列表
   * @description 俱乐部排名列表
   */
  clubWebRank: {
    parameters: {
      query?: {
        size?: components["parameters"]["size"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 编辑玩家认证信息
   * @description 编辑玩家认证信息
   */
  clubWebUserEdit: {
    parameters: {
      query: {
        urs: components["parameters"]["urs"];
        gender?: components["parameters"]["gender"];
        avatar?: components["parameters"]["avatar"];
        email?: components["parameters"]["email"];
        phone?: components["parameters"]["phone"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 玩家主页信息
   * @description 玩家主页信息
   */
  clubWebUserHome: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加认证视频
   * @description 添加认证视频
   */
  clubWebCertifiedInfoVideosAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 申请认证Id */
        applyId?: number;
        /** @description 视频url */
        url?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除认证视频
   * @description 删除认证视频
   */
  clubWebCertifiedInfoVideosDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 素材ID */
        id?: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 更新认证视频
   * @description 更新认证视频
   */
  clubWebCertifiedInfoVideosUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 素材ID */
        id?: number;
        /** @description 视频url */
        url?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取赛事列表
   * @description 获取赛事列表
   */
  clubWebCompetitionList: {
    parameters: {
      query: {
        /** @description 比赛类型 */
        type: 1 | 2 | 3;
        /** @description 诸神之战类型 */
        subType?: 1 | 2 | 3;
        /** @description 组别 */
        group?: 1 | 2;
        /** @description 年份 */
        year?: number;
        /** @description 届数 */
        whichTimes?: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取nosToken上传凭证
   * @description 获取nosToken上传凭证
   */
  clubWebCommonGetNosTokenTypeExtName: {
    parameters: {
      path: {
        /** @description 上传对象子类型 */
        type: string;
        /** @description 扩展名 */
        extName: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取登录信息
   * @description 跳转地址 https://ssl.hi.163.com/file_mg/public/share/common_auth/corpauth/login
   */
  clubAdminLoginInfo: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 登出
   * @description 登出
   */
  clubAdminLogout: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 列出视频列表
   * @description 列出视频列表
   */
  clubAdminVideosList: {
    parameters: {
      query?: {
        auditStatus?: components["parameters"]["auditStatus"];
        kw?: components["parameters"]["kw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 审核视频
   * @description 审核视频
   */
  clubAdminVideosAudit: {
    parameters: {
      query?: {
        id?: components["parameters"]["assertId"];
        auditStatus?: components["parameters"]["auditStatus"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 批量审核视频
   * @description 批量审核视频
   */
  clubAdminVideosAuditBatch: {
    parameters: {
      query?: {
        ids?: components["parameters"]["assertIds"];
        auditStatus?: components["parameters"]["auditStatus"];
        reason?: components["parameters"]["reason"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 列出图片列表
   * @description 列出图片列表
   */
  clubAdminImagesList: {
    parameters: {
      query?: {
        auditStatus?: components["parameters"]["auditStatus"];
        kw?: components["parameters"]["kw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 审核图片
   * @description 审核图片
   */
  clubAdminImagesAudit: {
    parameters: {
      query?: {
        id?: components["parameters"]["assertId"];
        auditStatus?: components["parameters"]["auditStatus"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 批量审核图片
   * @description 批量审核图片
   */
  clubAdminImagesAuditBatch: {
    parameters: {
      query?: {
        ids?: components["parameters"]["assertIds"];
        auditStatus?: components["parameters"]["auditStatus"];
        reason?: components["parameters"]["reason"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 列出认证信息列表
   * @description 列出认证信息列表
   */
  clubAdminCertifiedInfoList: {
    parameters: {
      query?: {
        auditStatus?: components["parameters"]["auditStatus"];
        /** @description 申请类型 | 类型 |  值 | | --- |  -- | | 指挥官|  1 | | 高手 |  2 | */
        applyType?: 1 | 2;
        reason?: components["parameters"]["reason"];
        kw?: components["parameters"]["kw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 审核认证信息
   * @description 审核认证信息
   */
  clubAdminCertifiedInfoAudit: {
    parameters: {
      query?: {
        id?: components["parameters"]["certifiedInfoId"];
        auditStatus?: components["parameters"]["auditStatus"];
        reason?: components["parameters"]["reason"];
        reasonMsg?: components["parameters"]["reasonMsg"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 批量审核认证信息
   * @description 批量审核认证信息
   */
  clubAdminCertifiedInfoAuditBatch: {
    parameters: {
      query?: {
        ids?: components["parameters"]["certifiedInfoIds"];
        auditStatus?: components["parameters"]["auditStatus"];
        reason?: components["parameters"]["reason"];
        reasonMsg?: components["parameters"]["reasonMsg"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 列出玩家标签列表
   * @description 列出玩家标签列表
   */
  clubAdminPlayerTagList: {
    parameters: {
      query?: {
        roleId?: components["parameters"]["roleId"];
        rares?: components["parameters"]["rares"];
        kw?: components["parameters"]["kw"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 批量添加标签
   * @description 批量添加标签
   */
  clubAdminPlayerTagAddBatch: {
    parameters: {
      query?: {
        roleIds?: components["parameters"]["roleIds"];
        rare?: components["parameters"]["rare"];
        tag?: components["parameters"]["tag"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 修改玩家标签列表
   * @description 修改玩家标签列表
   */
  clubAdminPlayerTagUpdate: {
    parameters: {
      query?: {
        roleId?: components["parameters"]["roleId"];
        rare?: components["parameters"]["rare"];
        tags?: components["parameters"]["tags"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取俱乐部统计信息
   * @description 获取俱乐部统计信息
   */
  clubUeStat: {
    parameters: {
      query?: {
        /** @description 成员数量大于的数值列表(CSV格式) */
        great_than?: string;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(great_than + nonce + time + auth_slat) */
        token?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取登录信息
   * @description 跳转地址 https://ssl.hi.163.com/file_mg/public/share/common_auth/corpauth/login
   */
  clubCompetitionAdminLoginInfo: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 登出
   * @description 登出
   */
  clubCompetitionAdminLogout: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加战绩
   * @description ```
   * 赛事类型及其数据范围定义
   * https://git-wz.nie.netease.com/hi-api/nsh_md_api/blob/develop/src/models/Competition.ts
   *
   * 接口参数定义 -namespace CompetitionReq
   * https://git-wz.nie.netease.com/hi-api/nsh_md_api/blob/develop/src/type/req.d.ts
   * ```
   */
  clubCompetitionAdminAdd: {
    requestBody?: {
      content: {
        "application/json": Record<string, never>;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 修改战绩
   * @description 修改战绩
   */
  clubCompetitionAdminEdit: {
    requestBody?: {
      content: {
        "application/json": Record<string, never>;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除战绩
   * @description 删除战绩
   */
  clubCompetitionAdminDel: {
    parameters: {
      query: {
        /** @description 战绩id */
        id: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 战绩列表
   * @description 战绩列表
   */
  clubCompetitionAdminList: {
    parameters: {
      query: {
        /** @description 比赛类型 */
        type: 1 | 2 | 3;
        /** @description 年份 */
        year?: number;
        /** @description 届数 */
        whichTimes?: number;
        /** @description 诸神之战类型 */
        subType?: 1 | 2;
        /** @description 组别 */
        group?: 1 | 2 | 3 | 4 | 5;
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取消息列表
   * @description 获取消息列表
   */
  getinforms: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 清空所有消息
   * @description 清空所有消息
   */
  informsCleanAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 消息红点，是否有新消息(是否有好友新动态， 是否新便利贴消息etc)
   * @description 消息红点，是否有新消息(是否有好友新动态， 是否新便利贴消息etc)
   */
  informsRedDot: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["SuccRes"] & {
            data?: components["schemas"]["RedDotRes"];
          };
        };
      };
    };
  };
  /**
   * 评论列表
   * @description 评论列表
   */
  commentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        momentId: components["parameters"]["momentId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
        /** @description 排序方式(点赞数或者混合排序) */
        orderBy?: "likes" | "mix";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加评论或回复
   * @description 添加评论或回复
   */
  addcomment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        text?: components["parameters"]["text"];
        replyid?: components["parameters"]["replyid"];
        hotFactor?: components["parameters"]["hotFactor"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除评论
   * @description 删除评论
   */
  delcomment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["id_as_comment"];
        hotFactor?: components["parameters"]["hotFactor"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 点赞评论
   * @description 点赞评论
   */
  likecomment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["id_as_comment"];
        action?: components["parameters"]["likeAction"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取本服热门心情列表
   * @description 获取本服热门心情列表
   */
  gethotmoments: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        serverid: components["parameters"]["serverid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取全服热门心情列表
   * @description 获取全服热门心情列表
   */
  getallhotmoments: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取用户朋友圈或者指定用户的朋友圈
   * @description 获取用户朋友圈或者指定用户的朋友圈
   */
  getmoments: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取编辑精选的朋友圈列表
   * @description 获取编辑精选的朋友圈列表
   */
  momentPickedList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取单条动态
   * @description 获取单条动态
   */
  getMomentById: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 转发动态
   * @description 转发动态
   */
  momentForward: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        moment_id: components["parameters"]["moment_id"];
        text?: components["parameters"]["text"];
        hotFactor?: components["parameters"]["hotFactor"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 留言列表
   * @description 留言列表
   */
  getmessages: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加留言
   * @description 添加留言
   */
  addmessage: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        replyid?: components["parameters"]["replyid"];
        text?: components["parameters"]["text"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除留言
   * @description 删除留言
   */
  delmessage: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["id_as_message"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取玩家详情
   * @description 获取玩家详情
   */
  getPlayerProfile: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      200: components["responses"]["PlayerGetProfileRes"];
    };
  };
  /**
   * 获取职业头像
   * @description 获取职业头像
   */
  playersJobAvatars: {
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              jobs: {
                  /** @example 1 */
                  id?: number;
                  /** @example 碎梦 */
                  name?: string;
                }[];
              gender: {
                  /** @example 0 */
                  id?: number;
                  /** @example male */
                  name?: string;
                }[];
              jobAvatar: {
                  /** @example 1 */
                  jobId?: number;
                  /** @example 0 */
                  gender?: number;
                  /** @example http://hi-163-nsh.nosdn.127.net/asserts/job_avatar/suimeng_0.png */
                  url?: string;
                }[];
            };
          };
        };
      };
    };
  };
  /**
   * 更新玩家隐私设置
   * @description 更新玩家隐私设置
   */
  setprivacy: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 隐藏位置 */
        hideLocation?: "false" | "true";
        /** @description 隐藏语音性别 */
        hideVoiceGender?: "false" | "true";
        /** @description 限制评论，只允许好友评论 */
        limitComment?: "false" | "true";
        /** @description 禁止新动态提醒 */
        muteNewMoment?: "false" | "true";
        /** @description 限制跨服交友 */
        limitCrossFriend?: "false" | "true";
        /** @description 隐藏心愿单 */
        hideWishList?: "false" | "true";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 上传用户头像
   * @description 上传用户头像
   */
  playerAvatarUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        url: components["parameters"]["url"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除用户头像(恢复职业默认头像)
   * @description 删除用户头像(恢复职业默认头像)
   */
  playerAvatarDelete: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 最近访客
   * @description 最近访客
   */
  recentVisitors: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 同步赛事结果数据(有队员)
   * @description 同步赛事结果数据(有队员)
   */
  serverCompetitionSyncResult: {
    requestBody?: {
      content: {
        "application/json": {
          /** @example 1716144000 */
          time?: number;
          /**
           * @description token计算方式为 md5(time + auth_slat)
           * @example 37ad3c65836e4154f22f890b57fe82f2
           */
          token?: string;
          data?: {
            /**
             * @description 服务器类型 1 正式服 2赛季服 3黄金服
             * @example 1
             */
            serverType?: number;
            /**
             * @description 赛事id 1天下第一 3皇城之巅 4剑试苍穹
             * @example 1
             */
            competitionId?: number;
            /**
             * @description 第几届
             * @example 1
             */
            whichTimes?: number;
            result?: ({
                /**
                 * @description 排名
                 * @example 1
                 */
                rank?: number;
                /**
                 * @description 服务器id
                 * @example 1
                 */
                server?: number;
                /**
                 * @description 服务器名称
                 * @example 烟雨江南
                 */
                serverName?: string;
                /**
                 * @description 队伍名称
                 * @example 队伍名称
                 */
                teamName?: string;
                roleList?: ({
                    /**
                     * @description 角色id
                     * @example 1
                     */
                    roleId?: number;
                    /**
                     * @description 账号(urs账号,可能用于大神跳转主页)
                     * @example 账号
                     */
                    account?: string;
                    /**
                     * @description 角色名称
                     * @example 角色名称
                     */
                    roleName?: string;
                    /**
                     * @description 职业
                     * @example 1
                     */
                    career?: number;
                    /**
                     * @description 性别
                     * @example 1
                     */
                    gender?: number;
                    /**
                     * @description 角色类型 1队长 2队员 3替补 4军师
                     * @example 1
                     * @enum {number}
                     */
                    roleType?: 1 | 2 | 3 | 4;
                  })[];
              })[];
          };
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 同步赛事结果数据(表格,无队员)
   * @description 同步赛事结果数据(表格,无队员)
   */
  serverCompetitionSyncTable: {
    requestBody?: {
      content: {
        "application/json": {
          /** @example 1716144000 */
          time?: number;
          /**
           * @description token计算方式为 md5(time + auth_slat)
           * @example 37ad3c65836e4154f22f890b57fe82f2
           */
          token?: string;
          data?: {
            /**
             * @description 服务器类型 1 正式服 2赛季服 3黄金服
             * @example 1
             */
            serverType?: number;
            /**
             * @description 赛事id  2诸神之战 5跨服联赛
             * @example 1
             */
            competitionId?: number;
            /**
             * @description 第几届
             * @example 1
             */
            whichTimes?: number;
            table?: {
                /**
                 * @description 排名
                 * @example 1
                 */
                rank?: number;
                /**
                 * @description 服务器id
                 * @example 1
                 */
                server?: number;
                /**
                 * @description 服务器名称
                 * @example 烟雨江南
                 */
                serverName?: string;
                /**
                 * @description 帮会名称
                 * @example 帮会名称
                 */
                guildName?: string;
                /**
                 * @description 帮主名称
                 * @example 帮主名称
                 */
                guildMaster?: string;
                /**
                 * @description 账号(urs账号,可能用于大神跳转主页)
                 * @example 账号
                 */
                account?: string;
                /**
                 * @description 角色id
                 * @example 1
                 */
                roleId?: number;
              }[];
          };
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加转服记录
   * @description 添加转服记录
   */
  serverTransferAdd: {
    parameters: {
      query?: {
        /** @description 转服前的ID */
        oldId?: number;
        /** @description 转服后的ID */
        newId?: number;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(newId + nonce + oldId + time + auth_slat) */
        token?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 转服信息同步
   * @description 转服信息同步
   */
  transferSyncDataName: {
    parameters: {
      query?: {
        /** @description 同步类型对应payload数据 */
        payload?: string;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat) */
        token?: string;
      };
      path: {
        /**
         * @description | name           | description        |
         * |----------------|--------------------|
         * | signUpSync       | 报名信息         |
         * | signUpCancel     | 取消报名         |
         */
        name: "signUpSync" | "signUpCancel";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取各个服务器报名数量
   * @description 获取各个服务器报名数量
   */
  transferGetSignupNum: {
    parameters: {
      query?: {
        /** @description 服务器列表,多个用英文逗号隔开 */
        serverIds?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取某个服务器报名列表
   * @description 获取某个服务器报名列表
   */
  transferGetSignupList: {
    parameters: {
      query: {
        roleId?: components["parameters"]["roleId"];
        /** @description 积分 */
        score: number;
        serverId?: components["parameters"]["serverId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 服务器添加动态
   * @description 服务器添加动态
   */
  serverMomentAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        text?: components["parameters"]["text"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(nonce + roleid + text + time + auth_slat) */
        token?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取推荐关注列表
   * @description 获取推荐关注列表
   */
  followGetRecommendedList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加关注
   * @description 添加关注
   */
  followAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 取消关注
   * @description 取消关注
   */
  followCancel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 一键同步跨服好友以及官方号
   * @description ⚠️  该接口会删除不在好友列表里的关注
   */
  followFriends: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 好友id(CSV格式) */
        friend_ids?: string;
        include_official?: "true" | "false";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 关注列表
   * @description 关注列表
   */
  followList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 粉丝列表
   * @description 粉丝列表
   */
  fanList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加动态
   * @description 添加动态
   */
  addMoment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        text?: components["parameters"]["text"];
        /** @description 图片url列表， csv格式，逗号分开 */
        imgs?: string;
        /** @description 视频url列表， csv格式，逗号分开 */
        videos?: string;
        /** @description 视频封面url列表， csv格式，逗号分开 */
        videoImgs?: string;
        /** @description 话题id */
        topicId?: number;
        context?: components["parameters"]["context"];
        /** @description 可见范围 1-所有人可见 2-好友可见 3-自己可见 */
        visualRange?: 1 | 2 | 3;
        canCommentByAll?: components["parameters"]["canCommentByAll"];
        canForward?: components["parameters"]["canForward"];
        canCommentByStranger?: components["parameters"]["canCommentByStranger"];
        hotFactor?: components["parameters"]["hotFactor"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 删除动态
   * @description 删除动态
   */
  delMoment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        hotFactor?: components["parameters"]["hotFactor"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 活动话题列表
   * @description 活动话题列表
   */
  topicList: {
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["SuccRes"] & {
            data?: components["schemas"]["Topic"][];
          };
        };
      };
    };
  };
  /**
   * 获取荣誉墙荣誉
   * @description 获取荣誉墙荣誉
   */
  getHonors: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code?: number;
            data?: components["schemas"]["GetHonorList"];
          };
        };
      };
    };
  };
  /**
   * 获取荣誉墙荣誉数量
   * @description 获取荣誉墙荣誉数量
   */
  getHonorNum: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 查看玩家好友id列表
   * @description 查看玩家好友id列表
   */
  activityFriendIds: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        sameServer?: components["parameters"]["sameServer"];
      };
    };
    responses: {
      200: components["responses"]["ActivityFriendIdsRes"];
    };
  };
  /**
   * 获取玩家侠侣信息
   * @description 获取玩家侠侣信息
   */
  activityGetCoupleInfo: {
    parameters: {
      query?: {
        roleId?: components["parameters"]["roleId"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      200: components["responses"]["GetCoupleInfoRes"];
    };
  };
  /**
   * 活动分享到梦岛
   * @description 话题link特殊格式
   * text1<link action={Color="ff8c00",Text="＃我要上花火节＃",Name="OnDoCircleOfFriendsTopicAction",Params={TopicId=39}}>text2
   */
  activityAddMoment: {
    /** @description add moment body */
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 玩家id
           * @example 100100001
           */
          roleId?: number;
          /**
           * @description 心情文本
           * @example test to add
           */
          text?: string;
          /** @description 分享图片列表 */
          imgs?: string[];
          /**
           * @description 是否跳过图片审核
           * @enum {number}
           */
          skipImgAudit?: 0 | 1;
          /** @description 分享视频列表 */
          videos?: string[];
          /**
           * @description 话题id
           * @example 39
           */
          topicId?: number;
          /**
           * @description 是否跳过视频审核
           * @enum {number}
           */
          skipVideoAudit?: 0 | 1;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  gmPing: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 更新俱乐部和玩家荣誉
   * @description 更新俱乐部和玩家荣誉
   */
  gmClubHonorExcelAction: {
    parameters: {
      path: {
        /** @description 操作 */
        action: "parse" | "calHonor" | "update";
      };
    };
    requestBody?: {
      content: {
        "multipart/form-data": {
          /** Format: binary */
          honorExcel?: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 更新俱乐部比赛定义表
   * @description 更新俱乐部比赛定义表
   */
  gmClubMatchTableAction: {
    parameters: {
      path: {
        /** @description 操作 */
        action: "parse" | "update";
      };
    };
    requestBody?: {
      content: {
        "multipart/form-data": {
          /** Format: binary */
          matchExcel?: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 清理俱乐部荣誉
   * @description 清理俱乐部荣誉
   */
  gmClubCleanHonors: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加标签认证后台管理员
   * @description 添加标签认证后台管理员
   */
  gmIdentityAddAdmin: {
    parameters: {
      query?: {
        /** @description 邮箱账号 */
        urs?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 刷新朋友圈热门动态
   * @description 刷新朋友圈热门动态
   */
  gmHotMomentsRefresh: {
    parameters: {
      query?: {
        /** @description 服务器ID */
        serverId?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 清除拷贝动态的记录
   * @description 清除拷贝动态的记录
   */
  gmCopyMomentsClean: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 旧号RoleId */
        copyRoleId?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取动态的热度
   * @description 获取动态的热度
   */
  gmMomentGetHot: {
    parameters: {
      query: {
        id: components["parameters"]["momentid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 设置侠侣信息
   * @description 设置侠侣信息
   */
  gmPlayerSetCoupleInfo: {
    parameters: {
      query?: {
        roleId?: components["parameters"]["roleId"];
        coupleId?: components["parameters"]["coupleId"];
        coupleName?: components["parameters"]["coupleName"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 获取玩家侠侣信息
   * @description 获取玩家侠侣信息
   */
  gmPlayerGetCoupleInfo: {
    parameters: {
      query?: {
        roleId?: components["parameters"]["roleId"];
      };
    };
    responses: {
      200: components["responses"]["GetCoupleInfoRes"];
    };
  };
  /**
   * 根据服务器id批量清理俱乐部相关数据
   * @description 根据服务器id批量清理俱乐部相关数据
   */
  gmClubCleanByServer: {
    parameters: {
      query?: {
        serverId?: components["parameters"]["serverId"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /**
   * 删除一个心愿
   * @deprecated
   * @description 删除一个心愿
   */
  wishlistWishesRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wishId: components["parameters"]["wishId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 删除全部心愿
   * @deprecated
   * @description 删除全部心愿
   */
  wishlistWishesRemoveAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 添加商品到心愿单
   * @description 添加商品到心愿单
   */
  wishlistAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        commodityId?: components["parameters"]["commodityId"];
        itemId?: components["parameters"]["itemId"];
        shopId?: components["parameters"]["shopId"];
        commodityIndex?: components["parameters"]["commodityIndex"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加时装到心愿单
   * @description 添加时装到心愿单
   */
  wishlistAddByFashion: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        fashionId?: components["parameters"]["fashionId"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 从心愿单移除商品
   * @description 从心愿单移除商品
   */
  wishlistRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        commodityId?: components["parameters"]["commodityId"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 从心愿单移除时装
   * @description 从心愿单移除时装
   */
  wishlistRemoveByFashion: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        fashionId?: components["parameters"]["fashionId"];
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 通知购买行为, 检查商品ID并处理心愿单
   * @description 通知购买行为, 检查商品ID并处理心愿单
   */
  wishlistNotifyBuyAction: {
    parameters: {
      query?: {
        fashionId?: components["parameters"]["fashionId"];
        /** @description 购买者玩家Id */
        buyerId?: number;
        /** @description 获得着玩家Id */
        ownerId?: number;
        /** @description 购买时间戳(单位毫秒) */
        buyTime?: number;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 通知获取时装事件，检查时装id并处理心愿单
   * @description 通知获取时装事件，检查时装id并处理心愿单
   */
  wishlistNotifyFashionGetAction: {
    parameters: {
      query?: {
        fashionId?: components["parameters"]["fashionId"];
        /** @description 购买者玩家Id */
        buyerId?: number;
        /** @description 获得着玩家Id */
        ownerId?: number;
        /** @description 购买时间戳(单位毫秒) */
        buyTime?: number;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        token?: components["parameters"]["authToken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 查看玩家心愿单
   * @description 查看玩家心愿单
   */
  wishlistShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      200: components["responses"]["ShowWishList"];
    };
  };
  /**
   * 列出已经实现的心愿
   * @description 列出已经实现的心愿
   */
  wishlistFullFilled: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FullFillWishList"];
    };
  };
  /**
   * 删除一条心愿实现记录
   * @description 删除一条心愿实现记录
   */
  wishlistFullFilledRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wishId: components["parameters"]["wishId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 删除全部心愿实现记录
   * @description 删除全部心愿实现记录
   */
  wishlistFullFilledRemoveAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 服务器设置奖励
   * @description 服务器设置奖励
   */
  expansionTrialSceneSetReward: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 通行证账号，用英文逗号隔开，固定前面是邮箱后面是手机号
           * @example <EMAIL>,18888888888
           */
          urs: string;
          /**
           * @description 奖励内容
           * @example [0,0,0]
           */
          reward: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /**
               * @description 状态 -1失败 1新增成功 2覆盖
               * @example 1
               */
              status: number;
            };
          };
        };
      };
    };
  };
  /**
   * 服务器领取奖励
   * @description 服务器领取奖励
   */
  expansionTrialSceneGetReward: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 通行证账号，用英文逗号隔开，固定前面是邮箱后面是手机号
           * @example <EMAIL>,18888888888
           */
          urs: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /**
               * @description 状态 1成功 -1已领过 -2奖励不存在
               * @example 1
               */
              status: number;
              /**
               * @description 奖励内容
               * @example [0,0,0]
               */
              reward?: string;
            };
          };
        };
      };
    };
  };
  /**
   * 客户端查询奖励
   * @description 客户端查询奖励
   */
  expansionTrialSceneQueryReward: {
    parameters: {
      query: {
        /** @description 通行证账号，用英文逗号隔开，固定前面是邮箱后面是手机号 */
        urs: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /**
               * @description 状态 0未发 1已发 -2不存在
               * @example 1
               */
              status: number;
              /**
               * @description 奖励内容
               * @example [0,0,0]
               */
              reward?: string;
            };
          };
        };
      };
    };
  };
  /**
   * 上传照片
   * @description 上传照片
   */
  activityTakePhotoAddPhoto: {
    requestBody?: {
      content: {
        "application/json": {
          /**
           * @description 玩家id
           * @example 24017600001
           */
          roleId: number;
          /**
           * @description 位置ID
           * @example 1
           */
          locationId: number;
          /** @description 图片地址 */
          imgUrl: string;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查询自己的照片
   * @description 查询自己的照片
   */
  activityTakePhotoGetMyPhotos: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        locationId?: number;
      };
    };
    responses: {
      200: components["responses"]["ActivityTakePhotoGetMyPhotosRes"];
    };
  };
  /**
   * 选中照片
   * @description 选中照片
   */
  activityTakePhotoSelectPhoto: {
    requestBody?: {
      content: {
        "application/json": {
          /**
           * @description 玩家id
           * @example 24017600001
           */
          roleId: number;
          /**
           * @description 位置ID
           * @example 1
           */
          locationId: number;
          /** @description 图片ID */
          imgId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 锁定照片
   * @description 锁定照片
   */
  activityTakePhotoLockPhoto: {
    requestBody?: {
      content: {
        "application/json": {
          /**
           * @description 玩家id
           * @example 24017600001
           */
          roleId: number;
          /**
           * @description 位置ID
           * @example 1
           */
          locationId: number;
          /** @description 图片ID */
          imgId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 解锁照片
   * @description 解锁照片
   */
  activityTakePhotoUnlockPhoto: {
    requestBody?: {
      content: {
        "application/json": {
          /**
           * @description 玩家id
           * @example 24017600001
           */
          roleId: number;
          /**
           * @description 位置ID
           * @example 1
           */
          locationId: number;
          /** @description 图片ID */
          imgId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 查玩家某地点选中的照片
   * @description 查玩家某地点选中的照片
   */
  activityTakePhotoGetLocationSelectedPhoto: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        targetId: components["parameters"]["targetId"];
        locationId: number;
      };
    };
    responses: {
      200: components["responses"]["ActivityTakePhotoGetLocationSelectedPhotoRes"];
    };
  };
  /**
   * 同步庄园数据
   * @description 同步庄园数据
   */
  gardenSyncDataName: {
    parameters: {
      query?: {
        /** @description 同步类型对应payload数据(json字符串) */
        payload?: string;
        time?: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(name + nonce + payload +  time + auth_slat) */
        token?: string;
      };
      path: {
        /**
         * @description | name           | description        |
         * |----------------|--------------------|
         * | info       | 庄园信息         |
         */
        name: "info";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 庄园详情
   * @description 庄园详情
   */
  gardenDetail: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        ownerId: components["parameters"]["ownerId"];
      };
    };
    responses: {
      200: components["responses"]["GardenDetailRes"];
    };
  };
  /**
   * 庄园排行
   * @description 庄园排行
   */
  gardenRank: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GardenRankRes"];
    };
  };
  /**
   * 庄园评价
   * @description 庄园评价
   */
  gardenEvaluationAdd: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 庄园主角色id */
          ownerId: number;
          /** @description 评分 */
          score: number;
          /** @description 内容 */
          text: string;
          /** @description 图片 */
          imgList?: string[];
        };
      };
    };
    responses: {
      200: components["responses"]["idRes"];
    };
  };
  /**
   * 庄园删除评价
   * @description 庄园删除评价
   */
  gardenEvaluationDel: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评价id */
          evaluationId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 评价点赞
   * @description 评价点赞
   */
  gardenEvaluationLike: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评价id */
          evaluationId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 评价取消点赞
   * @description 评价取消点赞
   */
  gardenEvaluationCancelLike: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评价id */
          evaluationId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 庄园评价列表
   * @description 庄园评价列表
   */
  gardenEvaluationList: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        ownerId: components["parameters"]["ownerId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GardenEvalutionListRes"];
    };
  };
  /**
   * 评价评论
   * @description 评价评论
   */
  gardenEvaluationComment: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评价id */
          evaluationId: number;
          /** @description 回复的评论id，回复评论时传递 */
          replyCommentId?: number;
          /** @description 评论内容 */
          text: string;
        };
      };
    };
    responses: {
      200: components["responses"]["idRes"];
    };
  };
  /**
   * 评价评论删除
   * @description 评价评论删除
   */
  gardenEvaluationCommentDel: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评论id */
          commentId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 评论列表-一级评论
   * @description 评论列表-一级评论
   */
  gardenEvaluationCommentList: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        /** @description 评价id */
        evaluationId: number;
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GardenEvaluationCommentListRes"];
    };
  };
  /**
   * 评论列表-二级评论
   * @description 评论列表-二级评论
   */
  gardenEvaluationCommentSubList: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        /** @description 一级评论id */
        commentId: number;
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GardenEvaluationCommentListRes"];
    };
  };
  /**
   * 评论点赞
   * @description 评论点赞
   */
  gardenEvaluationCommentLike: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评论id */
          commentId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 评论取消点赞
   * @description 评论取消点赞
   */
  gardenEvaluationCommentCancelLike: {
    requestBody?: {
      content: {
        "application/json": {
          /** @description 角色id */
          roleId: number;
          /** @description 评论id */
          commentId: number;
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 庄园消息列表
   * @description ```
   * 返回字段中type为消息的类型
   * 1 - 新的打卡
   * 2 - 打卡点赞
   * 3 - 打卡评论
   * 4 - 评论点赞
   * 5 - 评论回复
   *
   * 返回字段中status为读取状态
   * 0 - 未读
   * 1 - 已读
   *
   * 查看该接口后,玩家的所有消息的读取状态都会变为已读
   * ```
   */
  gardenInformList: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["GardenInformListRes"];
    };
  };
  /**
   * 庄园消息红点
   * @description 庄园消息红点
   */
  gardenInformUnread: {
    parameters: {
      query: {
        roleId: components["parameters"]["roleIdIn"];
      };
    };
    responses: {
      200: components["responses"]["GardenInformUnreadRes"];
    };
  };
}
