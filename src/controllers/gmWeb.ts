import { corpAuthCfg } from "../common/config";
import { CorpAuthInfo, ctxCorpAuthInfoKey } from "../common/constants";
import { Context } from "../context";
import { errorHandler } from "../helper";
import OpenIdRoleModel, { ScopeType } from "../models/OpenIdRoleModel";

export const cloudGameDurationAuthMiddleware = gmWebLoginInfoHandlerGen(ScopeType.CLOUD_GAME_DURATION)

export function gmWebLoginInfoHandlerGen(scope: ScopeType) {
    async function middleware(req, res, next) {
        try {
            let ctx = Context.createWithRequest(req)
            const corpAuthInfo = ctx.get<CorpAuthInfo>(ctxCorpAuthInfoKey)
            const mail = corpAuthInfo.mail
            const permission = await OpenIdRoleModel.findRoleByOpenIdAndScope(mail, scope)
            if (!permission) {
                res.send(403, { code: 403, message: "Permission denied" })
                return
            }
            next()
        } catch (err) {
            errorHandler(err, req, res, next)
        }
    }
    return middleware
}

export async function gmWebLoginInfoHandler(req, res, next) {
    try {
        const ctx = Context.createWithRequest(req);
        const corpAuthInfo = ctx.get<CorpAuthInfo>(ctxCorpAuthInfoKey)
        res.send({ code: 0, data: corpAuthInfo })
    } catch (err) {
        errorHandler(err, req, res, next);
    }
}

export async function gmWebLogoutHandler(req, res, next) {
    try {
        res.clearCookie(corpAuthCfg.infoKey, corpAuthCfg.cookieCfg)
        res.clearCookie(corpAuthCfg.tokenKey, corpAuthCfg.cookieCfg)
        res.send({ code: 0, data: null })
    } catch (err) {
        errorHandler(err, req, res, next);
    }
}