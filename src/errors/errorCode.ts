export const errorCodesV2 = {
    // 新的规范, 业务通用错误码
    UnknownError: { code: 10000, msg: "未知错误" },
    ThirdServiceError: { code: 10001, msg: "三方服务错误" },
    DatabaseError: { code: 10002, msg: "数据库错误" },
    RedisError: { code: 10003, msg: "Redis错误" },
    MiddlewareServiceError: { code: 10004, msg: "中间件服务错误" },
    ServerError: { code: 10005, msg: "服务内部错误" },
    ActivityConfigErr: { code: 10006, msg: "活动配置出错，请检查配置" },

    NotLoggedIn: { code: 20000, msg: "用户未登录" },
    PermissionDenied: { code: 20001, msg: "用户无权限" },
    ParameterInvalid: { code: 20002, msg: "参数非法" },
    DataParseError: { code: 20003, msg: "数据解析异常" },
    DataNotFound: { code: 20004, msg: "数据不存在" },
    DataConflict: { code: 20005, msg: "数据冲突" },
    BizNotStarted: { code: 20006, msg: "活动未开始" },
    BizFinished: { code: 20007, msg: "活动已结束" },
    ServiceMaintenance: { code: 20008, msg: "系统维护中" },
    TooFrequently: { code: 20009, msg: "操作过于频繁/未获得锁" },
    ContentSensitive: { code: 20010, msg: "内容包含敏感信息" },
    LoginExpired: { code: 20011, msg: "登录过期" },
    RoleNotFound: { code: 20012, msg: "该账号还未创建角色，请先前往游戏内完成创角" },
    TextIsEmpty: { code: 20013, msg: "文本为空" },
    TextIsTooLong: { code: 20014, msg: "文本过长" },

    // 业务定制错误码，请从30000开始
    AstrologyUserNotFound: { code: 30000, msg: "您需要先填写用户信息注册星巫" },
    BazaarRatingAlreadyExists: { code: 30001, msg: "您已评价过该解惑" },
    BazaarCommentNotFound: { code: 30002, msg: "该解惑评论不存在" },
    BazaarCommentAlreadyExists: { code: 30003, msg: "您已经对该贴完成了解惑" },
    AstrologyUserAlreadyRegistered: { code: 30004, msg: "该角色已经注册过星巫" },
    BirthTimeFormatInvalid: { code: 30005, msg: "出生日期格式错误" },
    BazaarRatingNotSelf: { code: 30006, msg: "您不能评价他人的解惑" },
};

export const DBErrorCodes = {
    DuplicatedEntry: "ER_DUP_ENTRY",
}