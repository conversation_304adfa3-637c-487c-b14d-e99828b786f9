import * as httpLib from "../../common/request";
import { clazzLogger } from "../../logger";
import { Context } from "../../context";
import { AstrologyBazaarDiceTuple } from "../astrology/astrologyConstant";
import { astrologyCfg } from "../../common/config";
import { HoroscopeFortuneType } from "../../constants/astrologyConstant";
import * as fs from "fs";
import * as path from "path";

enum CommandType {
    USER_HOROSCOPE = 1,  // 星盘测算
    PLANETARY_ASPECTS = 2,   // 星象提醒
    DICE = 3                // 占星骰子
}


export const enum HoroScopeTimeInterval {
    TODAY = 1,    // 今日
    THIS_WEEK = 2 // 本周
}


export interface HoroscopeUserInfo {
    user_gender: string;
    birthdate_year: number;
    birthdate_month: number;
    birthdate_day: number;
    birthdate_hour: number;
    birthdate_minute: number;
    birth_province: string;
    birth_city: string;
    birth_district: string;
    now_province: string;
    now_city: string;
    now_district: string;
}

interface HoroscopeApiResponse<T> {
    code: number;
    message: string;
    data: T;
}

interface HoroscopeResult {
    fortune: string;
    time: string;
    user_constellation: string;
    in_water_reversal: boolean;
    day_for_water_reversal_remain?: number;
    day_for_next_water_reversal?: number;
    title: string;
    content: Array<{
        title: string;
        content: string;
    }>;
    score: number;
    /**幸运色 */
    lucky_color: string;
    /**幸运色具体色值 */
    lucky_color_code: string;
}

interface PlanetaryAspects {
    title: string;
    content: string[];
}

interface DiceResult {
    title: string;
    content: Array<{
        title: string;
        content: string;
    }>;
}

interface AstrologyFallbackConfig {
    id: number;
    name: string;
    type: number;
    sub_type: number | null;
    condition: string;
    default_response: {
        title?: string;
        content: Array<{
            title: string;
            content: string;
        }> | string[];
        score?: number;
        lucky_color?: string;
    };
}

export class HoroscopeService {
    private readonly logger = clazzLogger("service/horoscopeApi");
    private fallbackConfigs: AstrologyFallbackConfig[] = [];

    private static instance: HoroscopeService;

    private constructor() {
        this.loadFallbackConfigs();
    }

    public static getInstance(): HoroscopeService {
        if (!HoroscopeService.instance) {
            HoroscopeService.instance = new HoroscopeService();
        }
        return HoroscopeService.instance;
    }

    private loadFallbackConfigs(): void {
        try {
            const configPath = path.join(__dirname, "../../../asserts/json/astrology_ai_default_fallback.json");
            const configData = fs.readFileSync(configPath, "utf-8");
            this.fallbackConfigs = JSON.parse(configData);
            this.logger.info({ configCount: this.fallbackConfigs.length }, "FallbackConfigsLoaded");
        } catch (error) {
            this.logger.error({ error }, "LoadFallbackConfigsError");
            this.fallbackConfigs = [];
        }
    }

    private getFallbackByCommandType(commandType: CommandType): AstrologyFallbackConfig | null {
        return this.fallbackConfigs.find(config =>
            config.condition === `req.command_type = ${commandType}`
        ) || null;
    }

    private createHoroscopeFallback(fallbackConfig: AstrologyFallbackConfig): HoroscopeResult {
        const fallbackData = fallbackConfig.default_response;
        return {
            fortune: "",
            time: "",
            user_constellation: "",
            in_water_reversal: false,
            title: fallbackData.title || "运势解读",
            content: Array.isArray(fallbackData.content) && fallbackData.content.length > 0 && typeof fallbackData.content[0] === 'object'
                ? fallbackData.content as Array<{title: string; content: string}>
                : [{ title: "运势分析", content: "暂时无法获取运势信息，请稍后再试" }],
            score: fallbackData.score || 0,
            lucky_color: fallbackData.lucky_color || "",
            lucky_color_code: ""
        };
    }

    private createPlanetaryAspectsFallback(fallbackConfig: AstrologyFallbackConfig): PlanetaryAspects {
        const fallbackData = fallbackConfig.default_response;
        return {
            title: fallbackData.title || "今日星象",
            content: Array.isArray(fallbackData.content) && typeof fallbackData.content[0] === 'string'
                ? fallbackData.content as string[]
                : ["暂时无法获取星象信息，请稍后再试"]
        };
    }

    private createDiceResultFallback(fallbackConfig: AstrologyFallbackConfig): DiceResult {
        const fallbackData = fallbackConfig.default_response;
        return {
            title: fallbackData.title || "占星骰子",
            content: Array.isArray(fallbackData.content) && fallbackData.content.length > 0 && typeof fallbackData.content[0] === 'object'
                ? fallbackData.content as Array<{title: string; content: string}>
                : [{ title: "占卜结果", content: "暂时无法获取占卜信息，请稍后再试" }]
        };
    }

    private async withFallback<T>(
        ctx: Context,
        commandType: CommandType,
        apiCall: () => Promise<T>,
        fallbackCreator: (config: AstrologyFallbackConfig) => T,
        apiName: string
    ): Promise<T> {
        try {
            return await apiCall();
        } catch (err) {
            this.logger.error({ ctx, err }, `${apiName}Error`);

            const fallbackConfig = this.getFallbackByCommandType(commandType);
            if (fallbackConfig) {
                this.logger.info({ ctx, fallbackConfigId: fallbackConfig.id }, `Using${apiName}Fallback`);
                return fallbackCreator(fallbackConfig);
            }

            throw err;
        }
    }

    /**
     * 星盘测算
     * @param userInfo 用户信息
     * @param timeInterval 时间区间：TODAY-今日，THIS_WEEK-本周
     * @param fortune 测运类型：基本运势/财运/事业与学业/情感
     */
    public async getUserHoroscope(ctx: Context,
        userInfo: HoroscopeUserInfo,
        timestamp: number,
        timeInterval: HoroScopeTimeInterval,
        fortune: HoroscopeFortuneType
    ): Promise<HoroscopeResult> {
        return this.withFallback(
            ctx,
            CommandType.USER_HOROSCOPE,
            async () => {
                const url = astrologyCfg.horoscopeApiEndPoint;
                const body = {
                    command_type: CommandType.USER_HOROSCOPE,
                    timestamp: timestamp,
                    user_list: [userInfo],
                    time_interval: timeInterval,
                    fortune: fortune
                }

                const res: HoroscopeApiResponse<HoroscopeResult> = await httpLib.request({
                    method: "POST",
                    url: url,
                    timeout: astrologyCfg.horoscopeApiTimeout,
                    body: body
                });

                this.logger.info({ ctx, url, body, res }, "GetUserHoroscopeApiRes");

                if (res && res.code === 200) {
                    return res.data;
                } else {
                    throw new Error(res?.message || "GetUserHoroscopeApiError");
                }
            },
            this.createHoroscopeFallback.bind(this),
            "UserHoroscope"
        );
    }

    /**
     * 星象提醒
     */
    public async getPlanetaryAspects(ctx: Context, timestamp: number): Promise<PlanetaryAspects> {
        return this.withFallback(
            ctx,
            CommandType.PLANETARY_ASPECTS,
            async () => {
                const url = astrologyCfg.horoscopeApiEndPoint;
                const body = {
                    command_type: CommandType.PLANETARY_ASPECTS,
                    timestamp: timestamp
                }

                const res: HoroscopeApiResponse<PlanetaryAspects> = await httpLib.request({
                    method: "POST",
                    url: url,
                    timeout: astrologyCfg.horoscopeApiTimeout,
                    body: body
                });

                this.logger.info({ ctx, url, body, res }, "GetPlanetaryAspectsApiRes");

                if (res && res.code === 200) {
                    return res.data;
                } else {
                    throw new Error(res?.message || "Get planetary aspects failed");
                }
            },
            this.createPlanetaryAspectsFallback.bind(this),
            "PlanetaryAspects"
        );
    }

    /**
     * 占星骰子
     * @param fortuneQuery 占卜问题
     * @param diceResult 骰子结果，如：["水星", "白羊座", "11宫"]
     */
    public async getDiceResult(ctx: Context, fortuneQuery: string, diceResult: AstrologyBazaarDiceTuple): Promise<DiceResult> {
        return this.withFallback(
            ctx,
            CommandType.DICE,
            async () => {
                const url = astrologyCfg.horoscopeApiEndPoint;
                let duration = 0;
                let startTime = Date.now();

                const body = {
                    command_type: CommandType.DICE,
                    timestamp: Date.now(),
                    fortune_query: fortuneQuery,
                    dice_result: diceResult
                }

                const res: HoroscopeApiResponse<DiceResult> = await httpLib.request({
                    method: "POST",
                    url: url,
                    timeout: astrologyCfg.horoscopeApiTimeout,
                    body: body
                });

                let endTime = Date.now();
                duration = endTime - startTime;

                this.logger.info({ ctx, url, body, res, duration }, "GetDiceResultApiRes");

                if (res && res.code === 200) {
                    return res.data;
                } else {
                    throw new Error(res?.message || "Get dice result failed");
                }
            },
            this.createDiceResultFallback.bind(this),
            "DiceResult"
        );
    }
}