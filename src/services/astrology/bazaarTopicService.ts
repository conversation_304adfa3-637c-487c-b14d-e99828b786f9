import { getDayStr } from "../../common/util";
import { Context } from "../../context";
import { errorCode } from "../../errorCodes";
import { clazzLogger } from "../../logger";
import { BazaarTopicDailyHotModel, BazaarTopicDailyHotRecord } from "../../models/astrology/bazaarTopicDailyHotModel";
import { BazaarTopicModel } from "../../models/astrology/bazaarTopicModel";
import { schemas } from "../../types/type";
const logger = clazzLogger("astrology.bazaarTopicService");

export class BazaarTopicService {
    private static instance: BazaarTopicService;
    private bazaarTopicModel: BazaarTopicModel;
    private bazaarTopicDailyHotModel: BazaarTopicDailyHotModel;
    constructor() {
        this.bazaarTopicModel = BazaarTopicModel.getInstance();
        this.bazaarTopicDailyHotModel = BazaarTopicDailyHotModel.getInstance();
    }

    static getInstance() {
        if (!BazaarTopicService.instance) {
            BazaarTopicService.instance = new BazaarTopicService();
        }
        return BazaarTopicService.instance;
    }

    async saveTopic(ctx: Context, topicId: number, topicText: string): Promise<number> {
        const now = Date.now();
        try {
            const id = await this.bazaarTopicModel.createOrUpdate({
                ID: topicId,
                Text: topicText,
                CreateTime: now,
                UpdateTime: now
            }, {
                UpdateTime: now,
                Text: topicText
            });
            logger.info({ ctx, id, topicId, topicText }, "saveTopicOk");
            return topicId;
        } catch (error) {
            logger.error({ ctx, error }, "saveTopicError");
            throw errorCode.DatabaseError;
        }
    }

    async incrDailyTopicHot(ctx: Context, topicId: number, now: number): Promise<void> {
        const ds = getDayStr(now);
        try {
            const rawQuery = this.bazaarTopicDailyHotModel.raw(`
            INSERT INTO nsh_astrology_post_topic_daily_hot (DS, TopicId, Hot, CreateTime, UpdateTime)
            VALUES (?, ?, 1, ?, ?)
            ON DUPLICATE KEY UPDATE
            Hot = Hot + 1,
            UpdateTime = ?
        `, [ds, topicId, now, now, now]);
            const upRet = await this.bazaarTopicDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, topicId }, "incrDailyTopicHotOk");
        } catch (err) {
            logger.error({ ctx, err, ds, topicId }, "incrDailyTopicHotError");
            throw errorCode.DatabaseError;
        }
    }

    async decrDailyTopicHot(ctx: Context, topicId: number, now: number): Promise<void> {
        const ds = getDayStr(now);
        try {
            const rawQuery = this.bazaarTopicDailyHotModel.raw(`
            UPDATE nsh_astrology_post_topic_daily_hot
            SET Hot = Hot - 1,
            UpdateTime = ?
            WHERE DS = ? AND TopicId = ? AND Hot > 0
        `, [now, ds, topicId]);
            const upRet = await this.bazaarTopicDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, topicId }, "decrDailyTopicHotOk");
        } catch (err) {
            logger.error({ ctx, err, ds, topicId }, "decrDailyTopicHotError");
            throw errorCode.DatabaseError;
        }
    }

    async getTopicIdToTextMap(ctx: Context, topicIds: number[]): Promise<Record<number, string>> {
        try {
            const ret: Record<number, string> = {};
            if (topicIds.length === 0) {
                return ret;
            }
            const list = await this.bazaarTopicModel.findMany({
                where: {
                    ID: topicIds
                },
                select: ['ID', 'Text'],
                pagination: {
                    page: 1,
                    pageSize: topicIds.length
                }
            });
            for (const item of list) {
                ret[item.ID] = item.Text;
            }
            return ret;
        } catch (err) {
            logger.error({ ctx, err }, "getTopicIdToTextMapError");
            throw errorCode.DatabaseError;
        }
    }

    async getDailyTopicHotList(ctx: Context, ds: string, fetchSize: number): Promise<Pick<BazaarTopicDailyHotRecord, 'TopicId' | 'Hot'>[]> {
        try {
            const list = await this.bazaarTopicDailyHotModel.findMany({
                where: { DS: ds },
                orderBy: [['Hot', 'desc'], ['UpdateTime', 'desc']],
                select: ['TopicId', 'Hot'],
                pagination: {
                    page: 1,
                    pageSize: fetchSize
                }
            });
            logger.info({ ctx, list, ds, fetchSize }, "getDailyTopicHotListOk");
            return list;
        } catch (err) {
            logger.error({ ctx, err, ds, fetchSize }, "getDailyTopicHotListError");
            throw errorCode.DatabaseError;
        }
    }

    convertToTopicShowList(list: Pick<BazaarTopicDailyHotRecord, 'TopicId' | 'Hot'>[], topicIdToTextMap: Record<number, string>): schemas['bazaar_hot_topic_resp']['list'] {
        const listRet: schemas['bazaar_hot_topic_resp']['list'] = [];
        for (const item of list) {
            const text = topicIdToTextMap[item.TopicId] || ""
            listRet.push({
                id: item.TopicId,
                text,
                hot: item.Hot
            });
        }
        return listRet;
    }

    async getMostFocusedTopicId(ctx: Context, ds: string): Promise<schemas['bazaar_topic_show_item']> {
        let resp: schemas['bazaar_topic_show_item'] = null
        try {
            const list = await this.getDailyTopicHotList(ctx, ds, 1);
            if (list.length === 0) {
                return resp;
            }
            const topicIds = list.map(item => item.TopicId);
            const topicIdToTextMap = await this.getTopicIdToTextMap(ctx, topicIds);
            resp = {
                id: list[0].TopicId,
                text: topicIdToTextMap[list[0].TopicId]
            }
            return resp;
        } catch (err) {
            logger.error({ ctx, err }, "getMostFocusedTopicIdError");
            throw errorCode.DatabaseError;
        }
    }
}
