export const enum AstrologyPlanet {
    /** 太阳 */
    SUN = "sun",
    /** 月亮 */
    MOON = "moon",
    /** 水星 */
    MERCURY = "mercury",
    /** 金星 */
    VENUS = "venus",
    /** 火星 */
    MARS = "mars",
    /** 木星 */
    JUPITER = "jupiter",
    /** 土星 */
    SATURN = "saturn",
    /** 天王星 */
    URANUS = "uranus",
    /** 海王星 */
    NEPTUNE = "neptune",
    /** 冥王星 */
    PLUTO = "pluto",
    /** 南郊点 */
    SOUTH_NODE = "southNode",
    /** 北郊点 */
    NORTH_NODE = "northNode"
}

export const AstrologyPlanetMap = {
    [AstrologyPlanet.SUN]: "太阳",
    [AstrologyPlanet.MOON]: "月亮",
    [AstrologyPlanet.MERCURY]: "水星",
    [AstrologyPlanet.VENUS]: "金星",
    [AstrologyPlanet.MARS]: "火星",
    [AstrologyPlanet.JUPITER]: "木星",
    [AstrologyPlanet.SATURN]: "土星",
    [AstrologyPlanet.URANUS]: "天王星",
    [AstrologyPlanet.NEPTUNE]: "海王星",
    [AstrologyPlanet.PLUTO]: "冥王星",
    [AstrologyPlanet.SOUTH_NODE]: "南郊点",
    [AstrologyPlanet.NORTH_NODE]: "北郊点"
}

export const enum AstrologyConstellation {
    /** 白羊座 */
    ARIES = "aries",
    /** 金牛座 */
    TAURUS = "taurus",
    /** 双子座 */
    GEMINI = "gemini",
    /** 巨蟹座 */
    CANCER = "cancer",
    /** 狮子座 */
    LEO = "leo",
    /** 处女座 */
    VIRGO = "virgo",
    /** 天秤座 */
    LIBRA = "libra",
    /** 天蝎座 */
    SCORPIO = "scorpio",
    /** 射手座 */
    SAGITTARIUS = "sagittarius",
    /** 摩羯座 */
    CAPRICORN = "capricorn",
    /** 水瓶座 */
    AQUARIUS = "aquarius",
    /** 双鱼座 */
    PISCES = "pisces"
}

export const AstrologyConstellationMap = {
    [AstrologyConstellation.ARIES]: "白羊座",
    [AstrologyConstellation.TAURUS]: "金牛座",
    [AstrologyConstellation.GEMINI]: "双子座",
    [AstrologyConstellation.CANCER]: "巨蟹座",
    [AstrologyConstellation.LEO]: "狮子座",
    [AstrologyConstellation.VIRGO]: "处女座",
    [AstrologyConstellation.LIBRA]: "天秤座",
    [AstrologyConstellation.SCORPIO]: "天蝎座",
    [AstrologyConstellation.SAGITTARIUS]: "射手座",
    [AstrologyConstellation.CAPRICORN]: "摩羯座",
    [AstrologyConstellation.AQUARIUS]: "水瓶座",
    [AstrologyConstellation.PISCES]: "双鱼座"
}

export const enum AstrologyHouse {
    /** 第一宫命宫 */
    HOUSE_1 = 1,
    /** 第二宫财运宫 */
    HOUSE_2 = 2,
    /** 第三宫兄弟宫 */
    HOUSE_3 = 3,
    /** 第四宫家庭宫 */
    HOUSE_4 = 4,
    /** 第五宫子女宫 */
    HOUSE_5 = 5,
    /** 第六宫健康宫 */
    HOUSE_6 = 6,
    /** 第七宫夫妻宫 */
    HOUSE_7 = 7,
    /** 第八宫疾厄宫 */
    HOUSE_8 = 8,
    /** 第九宫迁移宫 */
    HOUSE_9 = 9,
    /** 第十宫事业宫 */
    HOUSE_10 = 10,
    /** 第十一宫人际宫 */
    HOUSE_11 = 11,
    /** 第十二宫玄秘宫 */
    HOUSE_12 = 12
}

export const AstrologyHouseMap = {
    [AstrologyHouse.HOUSE_1]: "第一宫命宫",
    [AstrologyHouse.HOUSE_2]: "第二宫财运宫",
    [AstrologyHouse.HOUSE_3]: "第三宫兄弟宫",
    [AstrologyHouse.HOUSE_4]: "第四宫家庭宫",
    [AstrologyHouse.HOUSE_5]: "第五宫子女宫",
    [AstrologyHouse.HOUSE_6]: "第六宫健康宫",
    [AstrologyHouse.HOUSE_7]: "第七宫夫妻宫",
    [AstrologyHouse.HOUSE_8]: "第八宫疾厄宫",
    [AstrologyHouse.HOUSE_9]: "第九宫迁移宫",
    [AstrologyHouse.HOUSE_10]: "第十宫事业宫",
    [AstrologyHouse.HOUSE_11]: "第十一宫人际宫",
    [AstrologyHouse.HOUSE_12]: "第十二宫玄秘宫"
}

export interface AstrologyBazaarDice {
    planet: AstrologyPlanet;
    constellation: AstrologyConstellation;
    house: AstrologyHouse;
}


// 骰子结果，如：["水星", "白羊座", "11宫"]
export type AstrologyBazaarDiceTuple = [string, string, string];

export function fromAstrologyBazaarDiceTuple(diceResultTuple: AstrologyBazaarDiceTuple): AstrologyBazaarDice {
    return {
        planet: AstrologyPlanetMap[diceResultTuple[0]],
        constellation: AstrologyConstellationMap[diceResultTuple[1]],
        house: AstrologyHouseMap[diceResultTuple[2]]
    }
}

export function toAstrologyBazaarDiceTuple(diceResult: AstrologyBazaarDice): AstrologyBazaarDiceTuple {
    return [AstrologyPlanetMap[diceResult.planet], AstrologyConstellationMap[diceResult.constellation], AstrologyHouseMap[diceResult.house]];
}
