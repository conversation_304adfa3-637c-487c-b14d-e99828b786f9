import { Statues } from "../../common/constants";
import { getDayStrForAstrology } from "../../components/astrology/operation";
import { Context } from "../../context";
import { errorCode } from "../../errorCodes";
import { clazzLogger } from "../../logger";
import { BazaarPostDailyHotModel } from "../../models/astrology/bazaarPostDailyHotModel";
import { BazaarPostModel, BazaarPostRecord } from "../../models/astrology/bazaarPostModel";
import { Pagination } from "../../types/req";
import { schemas } from "../../types/type";
const logger = clazzLogger("astrology.bazaarPostService");

export class BazaarPostService {
    private static instance: BazaarPostService;
    private bazaarPostModel: BazaarPostModel;
    private bazaarPostDailyHotModel: BazaarPostDailyHotModel;
    constructor() {
        this.bazaarPostModel = BazaarPostModel.getInstance();
        this.bazaarPostDailyHotModel = BazaarPostDailyHotModel.getInstance();
    }

    static getInstance() {
        if (!BazaarPostService.instance) {
            BazaarPostService.instance = new BazaarPostService();
        }
        return BazaarPostService.instance;
    }

    async addBazaarPost(ctx: Context, bazaarPost: Omit<BazaarPostRecord, "ID">): Promise<number> {
        try {
            const id = await this.bazaarPostModel.insert(bazaarPost);
            logger.info({ ctx, id, bazaarPost }, "addBazaarPostOk");
            return id
        } catch (error) {
            logger.error({ error, bazaarPost }, "addBazaarPostError");
            throw errorCode.DatabaseError;
        }
    }

    async getBazaarPost(ctx: Context, postId: number): Promise<BazaarPostRecord> {
        const bazaarPost = await this.bazaarPostModel.findOne({ ID: postId, Status: Statues.Normal });
        if (!bazaarPost) {
            logger.warn({ ctx, postId }, "getBazaarPostNotFound");
            throw errorCode.DataNotFound;
        }
        return bazaarPost;
    }

    async verifyBazaarPost(ctx: Context, postId: number): Promise<BazaarPostRecord> {
        const bazaarPost = await this.bazaarPostModel.findOne({ ID: postId, Status: Statues.Normal });
        if (!bazaarPost) {
            logger.warn({ ctx, postId }, "getBazaarPostNotFound");
            throw errorCode.DataNotFound;
        }
        return bazaarPost;
    }

    async incrCommentCount(ctx: Context, postId: number): Promise<void> {
        try {
            let query = this.bazaarPostModel.scope().where({ ID: postId, Status: Statues.Normal }).increment('CommentCount', 1);
            const ret = await this.bazaarPostModel.executeByQuery(query);
            logger.info({ ctx, ret, postId }, "incrPostCommentCountOk");
        } catch (error) {
            logger.error({ ctx, error, postId }, "incrPostCommentCountError");
            throw errorCode.DatabaseError;
        }
    }

    async incrPostDailyHot(ctx: Context, postId: number, now: number): Promise<void> {
        const ds = getDayStrForAstrology(new Date(now));
        try {
            const rawQuery = this.bazaarPostDailyHotModel.raw(`
            INSERT INTO nsh_astrology_post_daily_hot (DS, PostId, Hot, CreateTime, UpdateTime)
            VALUES (?, ?, 1, ?, ?)
            ON DUPLICATE KEY UPDATE
            Hot = Hot + 1,
            UpdateTime = ?
        `, [ds, postId, now, now, now]);
            const upRet = await this.bazaarPostDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, postId }, "incrPostDailyHotOk");
        } catch (err) {
            logger.error({ ctx, err, ds, postId }, "incrPostDailyHotError");
            throw errorCode.DatabaseError;
        }
    }

    async decrPostDailyHot(ctx: Context, postId: number, now: number): Promise<void> {
        const ds = getDayStrForAstrology(new Date(now));
        try {
            const rawQuery = this.bazaarPostDailyHotModel.raw(`
            UPDATE nsh_astrology_post_daily_hot
            SET Hot = Hot - 1,
            UpdateTime = ?
            WHERE DS = ? AND PostId = ? AND Hot > 0
        `, [now, ds, postId]);
            const upRet = await this.bazaarPostDailyHotModel.executeByQuery(rawQuery);
            logger.info({ ctx, upRet, ds, postId }, "decrPostDailyHotOk");
        } catch (err) {
            logger.error({ ctx, err, ds, postId }, "decrPostDailyHotError");
            throw errorCode.DatabaseError;
        }
    }

    async decrCommentCount(ctx: Context, postId: number): Promise<void> {
        try {
            let query = this.bazaarPostModel.scope().where({ ID: postId, Status: Statues.Normal }).where('CommentCount', '>', 0).decrement('CommentCount', 1);
            const ret = await this.bazaarPostModel.executeByQuery(query);
            logger.info({ ctx, ret, postId }, "decrPostCommentCountOk");
        } catch (err) {
            logger.error({ ctx, err, postId }, "decrPostCommentCountError");
            throw errorCode.DatabaseError;
        }
    }

    convertToBazaarPostShowItemList(bazaarPostList: BazaarPostRecord[], roleInfoMap: Record<number, schemas['basic_role_info']>, userInfoMap: Record<number, schemas['bazaar_core_user_info']>, commentedPostIds: number[]): schemas['bazaar_post_show_item'][] {
        const itemList: schemas['bazaar_post_show_item'][] = []
        const commentedPostIdsSet = new Set(commentedPostIds)

        for (const bazaarPost of bazaarPostList) {
            const roleInfo = roleInfoMap[bazaarPost.RoleId]
            const userInfo = userInfoMap[bazaarPost.RoleId]
            // 如果评论过该帖子，则不能评论,一个帖子用户只能评论一次
            const canComment = !commentedPostIdsSet.has(bazaarPost.ID)
            const item: schemas['bazaar_post_show_item'] = {
                id: bazaarPost.ID,
                roleId: bazaarPost.RoleId,
                topicId: bazaarPost.TopicId,
                roleInfo,
                userInfo,
                question: bazaarPost.Question,
                dice: {
                    planet: bazaarPost.DicePlanet as any,
                    constellation: bazaarPost.DiceConstellation as any,
                    house: bazaarPost.DiceHouse
                },
                commentCount: bazaarPost.CommentCount,
                canComment,
                createTime: bazaarPost.CreateTime
            }
            itemList.push(item)
        }
        return itemList
    }

    async listBazaarUserSelfPost(ctx: Context, roleId: number, topicIds: number[], pagination: Pagination): Promise<BazaarPostRecord[]> {
        try {
            let initQuery = this.bazaarPostModel.normalScope().where({ RoleId: roleId })
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds)
            }
            const list = await this.bazaarPostModel.findMany({
                initQuery,
                orderBy: [['ID', 'desc']],
                pagination
            })
            logger.info({ ctx, listLen: list.length }, "listUserSelfBazaarPostOk");
            return list
        } catch (error) {
            logger.error({ ctx, error, roleId }, "listUserSelfBazaarPostError");
            throw errorCode.DatabaseError;
        }
    }

    async countBazaarUserSelfPost(ctx: Context, roleId: number, topicIds: number[]): Promise<number> {
        try {
            let initQuery = this.bazaarPostModel.normalScope().where({ RoleId: roleId })
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds)
            }
            const count = await this.bazaarPostModel.countByQuery(initQuery)
            logger.debug({ ctx, count, roleId, topicIds }, "countBazaarUserSelfPostOk");
            return count
        } catch (error) {
            logger.error({ ctx, error, roleId }, "countBazaarUserSelfPostError");
            throw errorCode.DatabaseError;
        }
    }

    async listBazaarPublicPost(ctx: Context, roleId: number, topicIds: number[], pagination: Pagination): Promise<BazaarPostRecord[]> {
        try {
            let initQuery = this.bazaarPostModel.normalScope()
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds)
            }
            const list = await this.bazaarPostModel.findMany({
                initQuery,
                orderBy: [['CommentCount', 'desc']],
                pagination
            })
            logger.info({ ctx, listLen: list.length, roleId, topicIds }, "listUserSelfBazaarPostOk");
            return list
        } catch (error) {
            logger.error({ ctx, error, roleId }, "listUserSelfBazaarPostError");
            throw errorCode.DatabaseError;
        }
    }

    async countBazaarPublicPost(ctx: Context, roleId: number, topicIds: number[]): Promise<number> {
        try {
            let initQuery = this.bazaarPostModel.normalScope()
            if (topicIds.length > 0) {
                initQuery = initQuery.whereIn('TopicId', topicIds)
            }
            const count = await this.bazaarPostModel.countByQuery(initQuery)
            logger.debug({ ctx, count, roleId, topicIds }, "countBazaarPublicPostOk");
            return count
        } catch (error) {
            logger.error({ ctx, error, roleId }, "countBazaarPublicPostError");
            throw errorCode.DatabaseError;
        }
    }

    async getMostCommentedPostQuestion(ctx: Context, ds: string): Promise<string> {
        try {
            const rows = await this.bazaarPostDailyHotModel.findMany({
                where: {
                    DS: ds
                },
                orderBy: [['Hot', 'desc'], ['ID', 'asc']],
                select: ['PostId'],
                pagination: {
                    page: 1,
                    pageSize: 1
                }
            })
            if (rows.length === 0) {
                logger.warn({ ctx, ds }, "getMostCommentedPostQuestionNotFound");
                return ""
            }
            const postId = rows[0].PostId
            const post = await this.bazaarPostModel.findOne({ ID: postId, Status: Statues.Normal }, ['Question'])
            if (!post) {
                logger.warn({ ctx, postId }, "getMostCommentedPostQuestionNotFound");
                return ""
            }
            return post.Question
        } catch (err) {
            logger.error({ ctx, err, ds }, "getMostCommentedPostQuestionError");
            throw errorCode.DatabaseError;
        }
    }
}