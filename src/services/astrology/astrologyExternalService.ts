import { clazz<PERSON>ogger } from "../../logger";
import { AstrologyReq } from "../../components/astrology/type";
import * as MomentService from "../../services/moment";
import { Context } from "../../context";
const logger = clazzLogger("astrology.astrologyExternalService");

export class AstrologyExternalService {
    private static instance: AstrologyExternalService;
    constructor() {
    }

    static getInstance() {
        if (!AstrologyExternalService.instance) {
            AstrologyExternalService.instance = new AstrologyExternalService();
        }
        return AstrologyExternalService.instance;
    }

    async addMoment(ctx: Context, params: AstrologyReq.BazaarPostAdd) {
        try {
            const ip = ctx.getIp()
            let momentText = params.question;
            
            // Add topicText with #topicText <originText> syntax if topicText exists
            if (params.topicText && params.topicText.trim()) {
                momentText = `#${params.topicText} ${params.question}`;
            }
            
            // Generate shareImage URL from dice results
            const shareImageUrl = this.generateShareImageUrl(params.dice);
            
            const addProps = {
                roleid: params.roleid,
                text: momentText,
                ip: ip,
                ...(shareImageUrl && { 
                    imgs: shareImageUrl,
                    preApprovedImgs: [shareImageUrl]
                }),
            }
            const addRet = await MomentService.addMomentWithPreApprovedImages(addProps)
            
            logger.info({ ctx, addRet, params, addProps }, "astrologyAddMomentOk")
        } catch (err) {
            logger.error({ ctx, err, params }, "astrologyAddMomentFail")
            return false
        }
        return true
    }

    private generateShareImageUrl(dice?: { planet: string; constellation: string; house: number }): string {
        if (!dice) {
            return "";
        }
        
        const { planet, constellation, house } = dice;
        return `https://hi-163-nsh.nosdn.127.net/assets/icons/astrology/planet_${planet}_zodiac_${constellation}_house_${house}.png`;
    }

}