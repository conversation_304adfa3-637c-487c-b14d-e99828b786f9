import { Statues } from "../../common/constants";
import { Context } from "../../context";
import { errorCode } from "../../errorCodes";
import { DBErrorCodes, errorCodesV2 } from "../../errors/errorCode";
import { clazzLogger } from "../../logger";
import { BazaarRatingModel, BazaarRatingRecord } from "../../models/astrology/bazaarRatingModel";
import { Pagination } from "../../types/req";
import { schemas } from "../../types/type";
const logger = clazzLogger("astrology.bazaarCommentService");

export interface RatingCore {
    commentId: number;
    star: number;
    text: string;
    createTime: number;
}

export class BazaarRatingService {
    private static instance: BazaarRatingService;
    private bazaarRatingModel: BazaarRatingModel;
    constructor() {
        this.bazaarRatingModel = BazaarRatingModel.getInstance();
    }

    static getInstance() {
        if (!BazaarRatingService.instance) {
            BazaarRatingService.instance = new BazaarRatingService();
        }
        return BazaarRatingService.instance;
    }

    async addBazaarRating(ctx: Context, bazaarRating: Omit<BazaarRatingRecord, "ID">): Promise<number> {
        try {
            const id = await this.bazaarRatingModel.insert(bazaarRating);
            logger.info({ ctx, id, bazaarRating }, "addBazaarRatingOk");
            return id
        } catch (err) {
            if (err && err.code === DBErrorCodes.DuplicatedEntry) {
                throw errorCodesV2.BazaarRatingAlreadyExists;
            }
            logger.error({ err, bazaarRating }, "addBazaarRatingError");
            throw errorCode.DatabaseError;
        }
    }

    async isUserRated(ctx: Context, roleId: number, commentId: number): Promise<boolean> {
        const r = await this.bazaarRatingModel.findOne({
            FromRoleId: roleId,
            CommentId: commentId,
            Status: Statues.Normal
        }, ['ID'])
        return r && r.ID > 0
    }

    async checkUserRated(ctx: Context, roleId: number, commentId: number): Promise<void> {
        const isRated = await this.isUserRated(ctx, roleId, commentId);
        if (isRated) {
            throw errorCodesV2.BazaarRatingAlreadyExists;
        }
    }


    convertToBazaarRatingShowItemList(bazaarRatingList: BazaarRatingRecord[], roleInfoMap: Record<number, schemas['basic_role_info']>, userInfoMap: Record<number, schemas['bazaar_core_user_info']>): schemas['bazaar_rating_show_item'][] {
        const itemList: schemas['bazaar_rating_show_item'][] = []

        for (const bazaarRating of bazaarRatingList) {
            const roleInfo = roleInfoMap[bazaarRating.FromRoleId]
            const userInfo = userInfoMap[bazaarRating.FromRoleId]
            const item: schemas['bazaar_rating_show_item'] = {
                id: bazaarRating.ID,
                roleId: bazaarRating.FromRoleId,
                postId: bazaarRating.PostId,
                commentId: bazaarRating.CommentId,
                star: bazaarRating.Star,
                roleInfo,
                userInfo,
                text: bazaarRating.Text,
                createTime: bazaarRating.CreateTime,
            }
            itemList.push(item)
        }
        return itemList
    }

    /**
     * 获取用户接收的评价列表
     * @param ctx
     * @param roleId
     * @param pagination
     * @returns
     */
    async listReceiveBazaarRatingList(ctx: Context, roleId: number, pagination: Pagination): Promise<BazaarRatingRecord[]> {
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    ToRoleId: roleId,
                    Status: Statues.Normal
                },
                orderBy: [['ID', 'desc']],
                pagination
            })
            logger.info({ ctx, commentListLen: list.length, roleId, pagination }, "listReceiveBazaarRatingListOk");
            return list
        } catch (err) {
            logger.error({ ctx, err, roleId, pagination }, "listReceiveBazaarRatingListError");
            throw errorCode.DatabaseError;
        }
    }

    async countPlayerReceiveRating(ctx: Context, roleId: number): Promise<number> {
        try {
            const count = await this.bazaarRatingModel.count({
                ToRoleId: roleId,
                Status: Statues.Normal
            })
            logger.debug({ ctx, count, roleId }, "countPlayerReceiveRatingOk");
            return count
        } catch (err) {
            logger.error({ ctx, err, roleId }, "countPlayerReceiveRatingError");
            throw errorCode.DatabaseError;
        }
    }

    /**
     * 获取用户发送的评价列表
     * @param ctx
     * @param roleId
     * @param pagination
     * @returns
     */
    async listSendBazaarRatingList(ctx: Context, roleId: number, pagination: Pagination): Promise<BazaarRatingRecord[]> {
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    RoleId: roleId,
                    Status: Statues.Normal
                },
                orderBy: [['ID', 'desc']],
                pagination
            })
            logger.info({ ctx, commentListLen: list.length, roleId, pagination }, "listSendBazaarRatingListOk");
            return list
        } catch (err) {
            logger.error({ ctx, err, roleId, pagination }, "listSendBazaarRatingListError");
            throw errorCode.DatabaseError;
        }
    }

    async getRatingByCommentIds(ctx: Context, commentIds: number[]): Promise<BazaarRatingRecord[]> {
        if (commentIds.length === 0) {
            return []
        }
        try {
            const list = await this.bazaarRatingModel.findMany({
                where: {
                    CommentId: commentIds,
                    Status: Statues.Normal
                },
                orderBy: [['ID', 'desc']],
                pagination: {
                    page: 1,
                    pageSize: commentIds.length
                }
            })
            return list
        } catch (err) {
            logger.error({ ctx, err, commentIds }, "getRatingByCommentIdsError");
            throw errorCode.DatabaseError;
        }
    }

    async getRatingCoreMap(ctx: Context, commentIds: number[]): Promise<Record<number, RatingCore>> {
        const list = await this.getRatingByCommentIds(ctx, commentIds);
        const map: Record<number, RatingCore> = {};
        for (const item of list) {
            map[item.CommentId] = {
                commentId: item.CommentId,
                star: item.Star,
                text: item.Text,
                createTime: item.CreateTime
            };
        }
        return map;
    }

    async getCommentBeRatingRoleGteMinNum(ctx: Context, roleId: number, minNum: number): Promise<boolean> {
        try {
            const query = this.bazaarRatingModel.raw(`
                SELECT COUNT(*) >= ? AS isGteMinNum
                FROM (
                    SELECT DISTINCT FromRoleId
                    FROM nsh_astrology_bazaar_rating
                    WHERE ToRoleId = ? AND Status = 0
                    LIMIT ?
                ) AS distinct_roles;
            `, [minNum, roleId, minNum])

            const result = await this.bazaarRatingModel.executeByQuery<{ isGteMinNum: number }>(query)
            const isGteMinNum = Boolean(result[0]?.isGteMinNum ?? 0)
            logger.debug({ ctx, roleId, minNum, isGteMinNum }, "getCommentBeRatingRoleGteMinNumOK")
            return isGteMinNum
        } catch (err) {
            logger.error({ ctx, err, roleId, minNum }, "getCommentBeRatingRoleGteMinNumError")
            throw errorCode.DatabaseError
        }
    }
}
