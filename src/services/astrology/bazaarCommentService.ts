import { Statues } from "../../common/constants";
import { Context } from "../../context";
import { errorCode } from "../../errorCodes";
import { DBErrorCodes, errorCodesV2 } from "../../errors/errorCode";
import { clazzLogger } from "../../logger";
import { BazaarCommentModel, BazaarCommentRecord } from "../../models/astrology/bazaarCommentModel";
import { Pagination } from "../../types/req";
import { schemas } from "../../types/type";
import { RatingCore } from "./bazaarRatingService";
const logger = clazzLogger("astrology.bazaarCommentService");

export class BazaarCommentService {
    private static instance: BazaarCommentService;
    private bazaarCommentModel: BazaarCommentModel;
    constructor() {
        this.bazaarCommentModel = BazaarCommentModel.getInstance();
    }

    static getInstance() {
        if (!BazaarCommentService.instance) {
            BazaarCommentService.instance = new BazaarCommentService();
        }
        return BazaarCommentService.instance;
    }

    async addBazaarComment(ctx: Context,  bazaarComment: Omit<BazaarCommentRecord, "ID">): Promise<number> {
        try {
            const id = await this.bazaarCommentModel.insert(bazaarComment);
            logger.info({ ctx, id, bazaarComment }, "addBazaarCommentOk");
            return id
        } catch (err) {
            if (err && err.code === DBErrorCodes.DuplicatedEntry) {
                throw errorCodesV2.BazaarCommentAlreadyExists;
            }
            logger.error({ err, bazaarComment }, "addBazaarCommentError");
            throw errorCode.DatabaseError;
        }
    }

    async isUserCommented(ctx: Context, roleId: number, postId: number): Promise<boolean> {
        const r = await this.bazaarCommentModel.findOne({
            RoleId: roleId,
            PostId: postId,
            Status: Statues.Normal
        }, ['ID'])
        const isCommented = r && r.ID > 0
        return isCommented
    }

    async checkUserCommented(ctx: Context, roleId: number, postId: number): Promise<void> {
        const isCommented = await this.isUserCommented(ctx, roleId, postId);
        if (isCommented) {
            logger.warn({ ctx, roleId, postId }, "checkUserCommentedOk");
            throw errorCodesV2.BazaarCommentAlreadyExists;
        }
    }

    async getBazaarUserComment(ctx: Context, roleId: number, commentId: number): Promise<BazaarCommentRecord> {
        const bazaarComment = await this.bazaarCommentModel.findOne({ ID: commentId, RoleId: roleId, Status: Statues.Normal });
        if (!bazaarComment) {
            logger.warn({ ctx, roleId, commentId }, "getBazaarCommentNotFound");
            throw errorCode.DataNotFound;
        }
        return bazaarComment;
    }

    async verifyBazaarComment(ctx: Context, commentId: number): Promise<BazaarCommentRecord> {
        const bazaarComment = await this.bazaarCommentModel.findOne({ ID: commentId, Status: Statues.Normal });
        if (!bazaarComment) {
            logger.warn({ ctx, commentId }, "getBazaarCommentNotFound");
            throw errorCodesV2.BazaarCommentNotFound;
        }
        return bazaarComment;
    }


    convertToBazaarCommentShowItemList(bazaarCommentList: BazaarCommentRecord[], showRoleInfoMap: Record<number, schemas['basic_role_info']>, userInfoMap: Record<number, schemas['bazaar_core_user_info']>, ratingCoreMap: Record<number, RatingCore>): schemas['bazaar_comment_show_item'][] {
        const itemList: schemas['bazaar_comment_show_item'][] = []

        for (const bazaarComment of bazaarCommentList) {
            const roleInfo = showRoleInfoMap[bazaarComment.RoleId]
            const userInfo = userInfoMap[bazaarComment.RoleId]
            const ratingCore = ratingCoreMap[bazaarComment.ID]
            const item: schemas['bazaar_comment_show_item'] = {
                id: bazaarComment.ID,
                roleId: bazaarComment.RoleId,
                postId: bazaarComment.PostId,
                roleInfo: roleInfo,
                userInfo: userInfo,
                text: bazaarComment.Text,
                createTime: bazaarComment.CreateTime,
                rating: {
                    commentId: bazaarComment.ID,
                    star: ratingCore?.star || 0,
                    text: ratingCore?.text || "",
                    createTime: ratingCore?.createTime || 0
                }
            }
            itemList.push(item)
        }
        return itemList
    }

    async listBazaarCommentList(ctx: Context, postId: number, pagination: Pagination): Promise<BazaarCommentRecord[]> {
        try {
            const list = await this.bazaarCommentModel.findMany({
                where: {
                    PostId: postId,
                    Status: Statues.Normal
                },
                orderBy: [['ID', 'desc']],
                pagination
            })
            logger.info({ ctx, commentListLen: list.length, postId, pagination }, "listBazaarCommentListOk");
            return list
        } catch (err) {
            logger.error({ ctx, err, postId, pagination }, "listBazaarCommentListError");
            throw errorCode.DatabaseError;
        }
    }

    async countBazaarCommentByPostId(ctx: Context, postId: number): Promise<number> {
        try {
            const count = await this.bazaarCommentModel.count({
                PostId: postId,
                Status: Statues.Normal
            })
            logger.debug({ ctx, count, postId }, "countBazaarCommentListOk");
            return count
        } catch (err) {
            logger.error({ ctx, err, postId }, "countBazaarCommentListError");
            throw errorCode.DatabaseError;
        }
    }

    /**
     * 过滤当前用户已评论的帖子id
     * @param ctx
     * @param roleId
     * @param postIds
     * @returns
     */
    async filterCommentedPostIds(ctx: Context, roleId: number, postIds: number[]): Promise<number[]> {
        try {
            const rows = await this.bazaarCommentModel.findMany({
                where: {
                    RoleId: roleId,
                    PostId: postIds
                },
                select: ['PostId'],
                pagination: {
                    page: 1,
                    pageSize: postIds.length
                }
            })
            const commentedPostIds = rows.map(item => item.PostId)
            logger.info({ ctx, commentedPostIds, roleId, postIds }, "filterCommentedPostIdsOk");
            return commentedPostIds
        } catch (err) {
            logger.error({ ctx, err, roleId, postIds }, "filterCommentedPostIdsError");
            throw errorCode.DatabaseError;
        }
    }
}