import { Context } from "../../context";
import { clazzLogger } from "../../logger";
import { AstrologyUserModel } from "../../models/astrology/astrologyUserModel";
import { errorCode } from "../../errorCodes";
import { getRedis, ExpireType } from "../../common/redis";
import { cacheKeyGen } from "../../common/util";
import { RedisDistributedLock } from "../../common/redisDistributedLock";
import { astrologyCfg } from "../../common/config";
import * as moment from "moment";
import { schemas } from "../../types/type";
import { CommentRankItem } from "./astrologyCommentRankService";

const logger = clazzLogger("services/astrology/astrologyWeeklyCommentRankService");


export type WeeklyCommentRankItem = CommentRankItem;

type WeeklyCommentRankResponse = schemas['comment_rank_resp'];

export interface WeeklyCommentRankCache {
    data: {
        header: ['rank', 'roleId', 'roleName', 'commentCount', 'jobId', 'gender', 'subGender', 'headPaintId', 'bodyPaintId', 'lastCommentTime'];
        values: [number, number, string, number, number, number, number, number, number, number][];
    };
    updateTime: number;
    weekStart: string;
    weekEnd: string;
}

export class AstrologyWeeklyCommentRankService {
    private static instance: AstrologyWeeklyCommentRankService;
    private astrologyUserModel: AstrologyUserModel;

    private static readonly TIME_MULTIPLIER = 10 ** 13; // 10万亿，用于复合分数编码

    constructor() {
        this.astrologyUserModel = AstrologyUserModel.getInstance();
    }

    static getInstance() {
        if (!AstrologyWeeklyCommentRankService.instance) {
            AstrologyWeeklyCommentRankService.instance = new AstrologyWeeklyCommentRankService();
        }
        return AstrologyWeeklyCommentRankService.instance;
    }

    /**
     * 获取周排行榜
     */
    async getWeeklyCommentRank(ctx: Context): Promise<WeeklyCommentRankResponse> {
        try {
            const cachedRank = await this.getCachedWeeklyRank();
            if (cachedRank) {
                return cachedRank;
            }

            // 如果缓存中没有数据，则生成新的排行榜
            return await this.refreshWeeklyRank(ctx);
        } catch (err) {
            logger.error({ ctx, err }, "getWeeklyCommentRankError");
            throw errorCode.DatabaseError;
        }
    }

    /**
     * 增加用户周解惑次数
     */
    async incrementUserWeeklyComment(ctx: Context, roleId: number): Promise<void> {
        try {
            const now = Date.now();
            const weeklyRankKey = this.getWeeklyRankKey();

            // 获取当前周解惑次数
            const currentScoreStr = await getRedis().zscoreAsync(weeklyRankKey, roleId.toString());
            const currentScore = currentScoreStr ? parseFloat(currentScoreStr) : 0;

            let commentCount = 0;
            if (currentScore > 0) {
                const parsed = this.parseCompositeScore(currentScore);
                commentCount = parsed.commentCount;
            }

            // 生成新的复合分数
            const newCompositeScore = this.generateCompositeScore(commentCount + 1, now);

            // 更新Redis排行榜
            await getRedis().zaddAsync(weeklyRankKey, newCompositeScore, roleId.toString());

            // 设置过期时间（保留2周数据）
            const ttl = await getRedis().ttlAsync(weeklyRankKey);
            if (ttl === -1) {
                await getRedis().expireAsync(weeklyRankKey, 60 * 60 * 24 * 14);
            }

            // 异步清除缓存
            this.invalidateWeeklyRankCache().catch(err => {
                logger.warn({ ctx, err }, "invalidateWeeklyRankCacheError");
            });

            logger.info({ ctx, roleId, newCommentCount: commentCount + 1 }, "incrementUserWeeklyCommentSuccess");
        } catch (err) {
            logger.error({ ctx, err, roleId }, "incrementUserWeeklyCommentError");
            throw errorCode.DatabaseError;
        }
    }

    /**
     * 刷新周排行榜数据
     */
    async refreshWeeklyRank(ctx: Context): Promise<WeeklyCommentRankResponse> {
        const distributedLock = new RedisDistributedLock(this.getWeeklyRefreshLockKey());
        let lockResult: { acquired: boolean; lockValue?: string } = { acquired: false };

        try {
            logger.info({ ctx }, "refreshWeeklyCommentRankStart");

            // 尝试获取分布式锁
            lockResult = await distributedLock.acquire({ timeoutSeconds: 10, retryCount: 3, retryIntervalMs: 500 });

            if (!lockResult.acquired) {
                // 获取锁失败，等待其他进程完成
                logger.info({ ctx }, "refreshWeeklyCommentRankLockFailed_WaitForOtherProcess");
                await this.waitForRefresh();

                const cachedResult = await this.getCachedWeeklyRank();
                if (cachedResult) {
                    logger.info({ ctx }, "refreshWeeklyCommentRankGetCachedAfterWait");
                    return cachedResult;
                }

                // 如果等待后仍然没有缓存，返回空结果
                logger.warn({ ctx }, "refreshWeeklyCommentRankNoCacheAfterWait");
                const { weekStart, weekEnd } = this.getCurrentWeekRange();
                return { list: [], updateTime: Date.now(), weekStart, weekEnd };
            }

            const startTime = Date.now();
            const weeklyRankKey = this.getWeeklyRankKey();

            // 从Redis获取周排行数据
            const weeklyRankData = await getRedis().zrevrangeAsync(
                weeklyRankKey,
                0,
                astrologyCfg.weeklyCommentRankSize - 1,
                "WITHSCORES"
            );

            if (weeklyRankData.length === 0) {
                const { weekStart, weekEnd } = this.getCurrentWeekRange();
                const emptyResult = { list: [], updateTime: Date.now(), weekStart, weekEnd };
                await this.setCachedWeeklyRank(emptyResult);
                return emptyResult;
            }

            // 解析Redis数据并获取用户信息
            const roleIds: number[] = [];
            const scoreData: Array<{ roleId: number; commentCount: number; lastCommentTime: number }> = [];

            for (let i = 0; i < weeklyRankData.length; i += 2) {
                const roleId = parseInt(weeklyRankData[i]);
                const compositeScore = parseFloat(weeklyRankData[i + 1]);
                const { commentCount, lastCommentTime } = this.parseCompositeScore(compositeScore);

                roleIds.push(roleId);
                scoreData.push({ roleId, commentCount, lastCommentTime });
            }

            // 批量查询用户角色信息
            const query = this.astrologyUserModel.scope()
                .select([
                    'nsh_astrology_user.RoleId',
                    'nsh_roleinfo.RoleName',
                    'nsh_roleinfo.JobId',
                    'nsh_roleinfo.Gender',
                    'nsh_roleinfo.SubGender',
                    'nsh_roleinfo.HeadPaintId',
                    'nsh_roleinfo.BodyPaintId'
                ])
                .innerJoin('nsh_roleinfo', 'nsh_astrology_user.RoleId', 'nsh_roleinfo.RoleId')
                .whereIn('nsh_astrology_user.RoleId', roleIds);

            const roleInfos = await this.astrologyUserModel.executeByQuery(query);

            // 创建角色信息映射
            const roleInfoMap = new Map();
            roleInfos.forEach(info => {
                roleInfoMap.set(info.RoleId, info);
            });

            // 构建排行榜数据
            const rankList: WeeklyCommentRankItem[] = [];
            let currentRank = 1;
            let lastCommentCount = -1;

            for (let i = 0; i < scoreData.length; i++) {
                const { roleId, commentCount, lastCommentTime } = scoreData[i];
                const roleInfo = roleInfoMap.get(roleId);

                if (!roleInfo) {
                    logger.warn({ ctx, roleId }, "roleInfoNotFoundInWeeklyRank");
                    continue;
                }

                // 处理排名逻辑：相同解惑次数的用户排名相同
                if (commentCount !== lastCommentCount) {
                    currentRank = i + 1;
                    lastCommentCount = commentCount;
                }

                rankList.push({
                    roleId: roleId,
                    roleName: roleInfo.RoleName,
                    commentCount: commentCount,
                    rank: currentRank,
                    jobId: roleInfo.JobId,
                    gender: roleInfo.Gender,
                    subGender: roleInfo.SubGender,
                    headPaintId: roleInfo.HeadPaintId,
                    bodyPaintId: roleInfo.BodyPaintId,
                    lastCommentTime: lastCommentTime
                });
            }

            const { weekStart, weekEnd } = this.getCurrentWeekRange();
            const result: WeeklyCommentRankResponse = {
                list: rankList,
                updateTime: Date.now(),
                weekStart,
                weekEnd
            };

            // 缓存结果
            await this.setCachedWeeklyRank(result);

            const endTime = Date.now();
            const duration = endTime - startTime;
            logger.info({ ctx, rankCount: rankList.length, duration }, "refreshWeeklyCommentRankEnd");

            return result;
        } catch (err) {
            logger.error({ ctx, err }, "refreshWeeklyCommentRankError");
            throw errorCode.DatabaseError;
        } finally {
            // 释放分布式锁
            if (lockResult.acquired && lockResult.lockValue) {
                await distributedLock.release(lockResult.lockValue);
            }
        }
    }

    /**
     * 生成复合分数：解惑次数(高位) + 时间戳(低位)
     */
    private generateCompositeScore(commentCount: number, lastCommentTime: number): number {
        return commentCount * AstrologyWeeklyCommentRankService.TIME_MULTIPLIER + lastCommentTime;
    }

    /**
     * 解析复合分数
     */
    private parseCompositeScore(score: number): { commentCount: number; lastCommentTime: number } {
        const commentCount = Math.floor(score / AstrologyWeeklyCommentRankService.TIME_MULTIPLIER);
        const lastCommentTime = score % AstrologyWeeklyCommentRankService.TIME_MULTIPLIER;
        return { commentCount, lastCommentTime };
    }

    /**
     * 获取本周的时间范围（周一到周日）
     */
    private getCurrentWeekRange(): { weekStart: string; weekEnd: string } {
        const now = moment();
        // moment默认周日为一周开始，设置周一为一周开始
        const weekStart = now.clone().startOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');
        const weekEnd = now.clone().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');
        return { weekStart, weekEnd };
    }

    /**
     * 获取周排行榜Redis键
     */
    private getWeeklyRankKey(): string {
        const weekMonday = this.getWeekMondayKey(new Date());
        return cacheKeyGen("astrology_weekly_comment_rank", { week: weekMonday });
    }

    /**
     * 获取周一日期作为周标识（格式：YYYY-MM-DD）
     */
    private getWeekMondayKey(date: Date): string {
        return moment(date).startOf('isoWeek').format("YYYY-MM-DD");
    }

    /**
     * 获取缓存的周排行榜数据
     */
    private async getCachedWeeklyRank(): Promise<WeeklyCommentRankResponse | null> {
        try {
            const cached = await getRedis().getAsync(this.getWeeklyRankCacheKey());
            if (!cached) {
                return null;
            }

            const cacheData: WeeklyCommentRankCache = JSON.parse(cached);
            return this.convertCacheToResponse(cacheData);
        } catch (err) {
            logger.error({ err }, "getCachedWeeklyRankError");
            return null;
        }
    }

    /**
     * 设置缓存的周排行榜数据
     */
    private async setCachedWeeklyRank(data: WeeklyCommentRankResponse): Promise<void> {
        try {
            const cacheData = this.convertResponseToCache(data);
            await getRedis().setAsync(
                this.getWeeklyRankCacheKey(),
                JSON.stringify(cacheData),
                ExpireType.EX,
                astrologyCfg.weeklyCommentRankCacheTime * 60
            );
        } catch (err) {
            logger.error({ err, data }, "setCachedWeeklyRankError");
        }
    }

    /**
     * 获取周排行榜缓存键
     */
    private getWeeklyRankCacheKey(): string {
        const weekMonday = this.getWeekMondayKey(new Date());
        return cacheKeyGen("astrology_weekly_comment_rank_cache", { week: weekMonday });
    }

    /**
     * 获取刷新周排行榜的分布式锁键
     */
    private getWeeklyRefreshLockKey(): string {
        const weekMonday = this.getWeekMondayKey(new Date());
        return cacheKeyGen("astrology_weekly_comment_rank_refresh_lock", { week: weekMonday });
    }

    /**
     * 等待其他进程完成刷新
     */
    private async waitForRefresh(): Promise<void> {
        const waitTime = 2000 + Math.random() * 3000;
        await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    /**
     * 异步清除周排行榜缓存
     */
    private async invalidateWeeklyRankCache(): Promise<void> {
        try {
            await getRedis().delAsync(this.getWeeklyRankCacheKey());
        } catch (err) {
            logger.error({ err }, "invalidateWeeklyRankCacheError");
        }
    }

    /**
     * 将WeeklyCommentRankResponse转换为WeeklyCommentRankCache格式
     */
    private convertResponseToCache(response: WeeklyCommentRankResponse): WeeklyCommentRankCache {
        const values: [number, number, string, number, number, number, number, number, number, number][] =
            response.list.map(item => [
                item.rank,
                item.roleId,
                item.roleName,
                item.commentCount,
                item.jobId,
                item.gender,
                item.subGender,
                item.headPaintId,
                item.bodyPaintId,
                item.lastCommentTime
            ]);

        return {
            data: {
                header: ['rank', 'roleId', 'roleName', 'commentCount', 'jobId', 'gender', 'subGender', 'headPaintId', 'bodyPaintId', 'lastCommentTime'],
                values
            },
            updateTime: response.updateTime,
            weekStart: response.weekStart,
            weekEnd: response.weekEnd
        };
    }

    /**
     * 将WeeklyCommentRankCache格式转换为WeeklyCommentRankResponse
     */
    private convertCacheToResponse(cache: WeeklyCommentRankCache): WeeklyCommentRankResponse {
        const list: WeeklyCommentRankItem[] = cache.data.values.map(values => ({
            rank: values[0],
            roleId: values[1],
            roleName: values[2],
            commentCount: values[3],
            jobId: values[4],
            gender: values[5],
            subGender: values[6],
            headPaintId: values[7],
            bodyPaintId: values[8],
            lastCommentTime: values[9]
        }));

        return {
            list,
            updateTime: cache.updateTime,
            weekStart: cache.weekStart,
            weekEnd: cache.weekEnd
        };
    }

    /**
     * 预热缓存
     */
    async warmupCache(ctx: Context): Promise<void> {
        try {
            logger.info({ ctx }, "warmupWeeklyRankCacheStart");
            await this.refreshWeeklyRank(ctx);
            logger.info({ ctx }, "warmupWeeklyRankCacheEnd");
        } catch (err) {
            logger.error({ ctx, err }, "warmupWeeklyRankCacheError");
        }
    }

    /**
     * 获取用户周排行信息
     */
    async getUserWeeklyRankInfo(ctx: Context, roleId: number): Promise<{ rank: number; commentCount: number } | null> {
        try {
            const weeklyRankKey = this.getWeeklyRankKey();

            // 获取用户分数
            const scoreStr = await getRedis().zscoreAsync(weeklyRankKey, roleId.toString());
            if (!scoreStr) {
                return null;
            }

            const { commentCount } = this.parseCompositeScore(parseFloat(scoreStr));

            // 获取用户排名
            const rank = await getRedis().zrevrankAsync(weeklyRankKey, roleId.toString());

            return {
                rank: rank !== null ? rank + 1 : 0, // Redis排名从0开始，转换为从1开始
                commentCount
            };
        } catch (err) {
            logger.error({ ctx, err, roleId }, "getUserWeeklyRankInfoError");
            return null;
        }
    }
}