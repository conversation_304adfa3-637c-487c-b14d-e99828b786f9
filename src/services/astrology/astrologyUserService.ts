import { AstrologyReq, AstrologyRes } from "../../components/astrology/type";
import { Context } from "../../context";
import { clazzLogger } from "../../logger";
import { AstrologyUserModel, AstrologyUserRecord } from "../../models/astrology/astrologyUserModel";
import * as moment from "moment";
import { schemas } from "../../types/type";
import { AstrologyRoleInfoModel } from "../../models/astrology/astrologyRoleInfoModel";
import { errorCode } from "../../errorCodes";
import { EAstrologyUserGender } from "../../constants/astrologyConstant";
import { HoroscopeUserInfo } from "../external/horoscopeService";
import { AstrologyUserDailyForecastModel } from "../../models/astrology/astrologyUserDailyForecastModel";
import { astrologyCfg } from "../../common/config.all";
import { errorCodesV2 } from "../../errors/errorCode";
import { BazaarRatingService } from "./bazaarRatingService";


type AstrologyUserUpdateProps = Partial<Omit<AstrologyUserRecord, 'RoleId' | 'CreateTime' | 'UpdateTime' | 'RatingSum' | 'RatingCount'>>

const logger = clazzLogger("astrology.userService");

export class AstrologyUserService {
    private static instance: AstrologyUserService;
    private astrologyUserModel: AstrologyUserModel;
    private astrologyRoleInfoModel: AstrologyRoleInfoModel;
    private astrologyUserDailyForecastModel: AstrologyUserDailyForecastModel;
    constructor() {
        this.astrologyUserModel = AstrologyUserModel.getInstance();
        this.astrologyRoleInfoModel = AstrologyRoleInfoModel.getInstance();
        this.astrologyUserDailyForecastModel = AstrologyUserDailyForecastModel.getInstance();
    }

    static getInstance() {
        if (!AstrologyUserService.instance) {
            AstrologyUserService.instance = new AstrologyUserService();
        }
        return AstrologyUserService.instance;
    }

    async getProfile(ctx: Context, roleId: number): Promise<AstrologyUserRecord> {
        const r = await this.astrologyUserModel.findOne({ RoleId: roleId })
        return r
    }

    convertLocationToPlaceStr(place: schemas['location']): string {
        return `${place.province}-${place.city}-${place.district}`
    }

    fromPlaceStrToLocation(placeStr: string): schemas['location'] {
        const [province, city, district] = placeStr.split('-')
        return {
            province,
            city,
            district,
        }
    }

    convertToUpdateProps(params: AstrologyReq.BazaarUserProfileUpdate): AstrologyUserUpdateProps {
        const updateProps: AstrologyUserUpdateProps = {
        }
        if (params.gender >= 0) {
            updateProps.Gender = params.gender
        }
        if (params.birthTime) {
            updateProps.BirthTime = this.convertToBirthTimeInt(params.birthTime)
        }
        if (params.birthPlace) {
            updateProps.BirthPlace = this.convertLocationToPlaceStr(params.birthPlace)
        }
        if (params.currentPlace) {
            updateProps.CurrentPlace = this.convertLocationToPlaceStr(params.currentPlace)
        }
        if (params.astroLevel >= 0) {
            updateProps.AstroLevel = params.astroLevel
        }
        if (params.astroType >= 0) {
            updateProps.AstroType = params.astroType
        }
        return updateProps
    }

    async updateProfile(ctx: Context, roleId: number, params: AstrologyReq.BazaarUserProfileUpdate): Promise<void> {
        const r = await this.astrologyUserModel.findOne({ RoleId: roleId }, ['RoleId'])
        const now = Date.now()
        const updateProps = this.convertToUpdateProps(params)
        if (Object.keys(updateProps).length === 0) {
            logger.warn({ ctx, roleId, params }, "updateProfileNoChange")
            return
        }
        if (r && r.RoleId) {
            const upRet = await this.astrologyUserModel.updateByCondition({ RoleId: roleId }, updateProps)
            logger.info({ ctx, roleId, params, updateProps, upRet }, "updateProfileOK")
        } else {
            const addRet = await this.astrologyUserModel.createOrUpdate({
                RoleId: roleId,
                ...updateProps,
                CreateTime: now,
                UpdateTime: now,
            }, {
                ...updateProps,
                UpdateTime: now,
            })
            logger.info({ ctx, roleId, params, updateProps, addRet }, "updateProfileOK")
        }
    }

    async registerUser(ctx: Context, roleId: number, params: AstrologyReq.BazaarUserRegister): Promise<AstrologyRes.BazaarUserRegister> {
        const now = Date.now()
        const updateProps = this.convertToUpdateProps(params)
        await this.astrologyUserModel.createOrUpdate({
            RoleId: params.roleid,
            ...updateProps,
            CreateTime: now,
            UpdateTime: now,
        }, {
            ...updateProps,
            UpdateTime: now,
        })
        logger.info({ ctx, roleId, params, updateProps }, "RegisterUserOK")
        const resp: AstrologyRes.BazaarUserRegister = {
            roleId: params.roleid,
            createTime: now,
        }
        return resp
    }

    convertToBirthTimeInt(birthTime: string): number {
        return moment(birthTime).valueOf()
    }

    convertToBirthTimeStr(birthTime: number): string {
        if (birthTime <= 0) {
            return ""
        }
        return moment(birthTime).format('YYYY-MM-DD HH:mm')
    }

    /**
     * 处理评分事件
     * @param ctx
     * @param roleId
     * @param rating
     * @returns
     */
    async updateRatingStat(ctx: Context, roleId: number, rating: number, ts: number): Promise<void> {
        try {
            const rawQuery = this.astrologyUserModel.raw(`
                INSERT INTO nsh_astrology_user (
                    RoleId,
                    Gender,
                    BirthTime,
                    BirthPlace,
                    CurrentPlace,
                    RatingSum,
                    RatingCount,
                    CreateTime,
                    UpdateTime
                ) VALUES (
                    ?, 0, 0, '', '', ?, 1, ?, ?
                )
                ON DUPLICATE KEY UPDATE
                    RatingSum = RatingSum + ?,
                    RatingCount = RatingCount + 1,
                    UpdateTime = ?
            `, [roleId, rating, ts, ts, rating, ts])
            const r = await this.astrologyUserModel.executeByQuery(rawQuery)
            logger.info({ ctx, roleId, rating, r }, "onRatingEventOK")
        } catch (err) {
            logger.error({ ctx, err, roleId, rating }, "onRatingEventError")
            throw errorCode.DatabaseError
        }
    }

    async getUserInfoMap(ctx: Context, roleIds: number[]): Promise<Record<number, schemas['bazaar_core_user_info']>> {
        try {
            const profiles = await this.astrologyUserModel.findMany({
                where: { RoleId: roleIds },
                select: ['RoleId', 'Gender', 'AstroLevel', 'AstroType'],
                pagination: {
                    page: 1,
                    pageSize: roleIds.length
                }
            })
            const resp: Record<number, schemas['bazaar_core_user_info']> = {}
            for (const profile of profiles) {
                resp[profile.RoleId] = {
                    roleId: profile.RoleId,
                    gender: profile.Gender as EAstrologyUserGender,
                    astroLevel: profile.AstroLevel || 0,
                    astroType: profile.AstroType || 0,
                }
            }
            return resp
        } catch (err) {
            logger.error({ ctx, err, roleIds }, "getUserInfoMapError")
            throw errorCode.DatabaseError
        }
    }

    async getShowRoleInfoMap(ctx: Context, roleIds: number[]): Promise<Record<number, schemas['basic_role_info']>> {
        try {
            const roleInfos = await this.astrologyRoleInfoModel.findMany({
                where: { RoleId: roleIds },
                select: ['RoleId', 'RoleName', 'Gender', 'SubGender', 'JobId', 'HeadPaintId', 'BodyPaintId'],
                pagination: {
                    page: 1,
                    pageSize: roleIds.length
                }
            })
            const resp: Record<number, schemas['basic_role_info']> = {}
            for (const roleInfo of roleInfos) {
                resp[roleInfo.RoleId] = {
                    roleId: roleInfo.RoleId,
                    roleName: roleInfo.RoleName,
                    jobId: roleInfo.JobId,
                    gender: roleInfo.Gender,
                    subGender: roleInfo.SubGender,
                    headPaintId: roleInfo.HeadPaintId,
                    bodyPaintId: roleInfo.BodyPaintId,
                }
            }
            return resp
        } catch (err) {
            logger.error({ ctx, err, roleIds }, "getShowRoleInfoMapError")
            throw errorCode.DatabaseError
        }
    }

    getAverageRating(ratingSum: number, ratingCount: number, isMatchMinRoleIdCount: boolean): number {
        if (isMatchMinRoleIdCount) {
            return Number((ratingSum / ratingCount).toFixed(astrologyCfg.avgRatingDecimalPlaces))
        }
        return 0
    }

    async getUserShowProfileForServer(ctx: Context, roleId: number): Promise<AstrologyRes.BazaarUserProfileForServer> {
        const profile = await this.astrologyUserModel.findOne({ RoleId: roleId })
        if (!profile) {
            throw errorCodesV2.AstrologyUserNotFound
        }
        const totalReviews = profile.RatingCount

        // 是否满足计算平均值的最小角色数
        const isMatchMinRoleIdCount = await BazaarRatingService.getInstance().getCommentBeRatingRoleGteMinNum(ctx, roleId, astrologyCfg.avgRatingMinRoleIdCount)
        const averageRating = this.getAverageRating(profile.RatingSum, totalReviews, isMatchMinRoleIdCount)

        const resp: AstrologyRes.BazaarUserProfileForServer = {
            roleId: roleId,
            averageRating,
            totalReviews,
        }
        logger.debug({ ctx, roleId, resp }, "getUserShowProfileForServerOK")
        return resp
    }

    async getUserShowProfile(ctx: Context, roleId: number): Promise<AstrologyRes.BazaarUserProfile> {
        let roleInfo = await this.astrologyRoleInfoModel.findOne({ RoleId: roleId }, ['RoleId', 'RoleName', 'Gender', 'SubGender', 'JobId', 'HeadPaintId', 'BodyPaintId'])
        if (!roleInfo) {
            logger.warn({ ctx, roleId }, "roleInfoNotFound")
            roleInfo = { RoleId: roleId, RoleName: "", Gender: 0, SubGender: 0, JobId: 0, HeadPaintId: 0, BodyPaintId: 0 }
        }
        const profile = await this.astrologyUserModel.findOne({ RoleId: roleId })
        if (!profile) {
            throw errorCodesV2.AstrologyUserNotFound
        }
        const totalReviews = profile.RatingCount

        // 是否满足计算平均值的最小角色数
        const isMatchMinRoleIdCount = await BazaarRatingService.getInstance().getCommentBeRatingRoleGteMinNum(ctx, roleId, astrologyCfg.avgRatingMinRoleIdCount)
        const averageRating = this.getAverageRating(profile.RatingSum, totalReviews, isMatchMinRoleIdCount)

        const resp: AstrologyRes.BazaarUserProfile = {
            roleId: roleId,
            roleInfo: {
                roleId: roleId,
                roleName: roleInfo.RoleName,
                jobId: roleInfo.JobId,
                gender: roleInfo.Gender,
                subGender: roleInfo.SubGender,
                headPaintId: roleInfo.HeadPaintId,
                bodyPaintId: roleInfo.BodyPaintId,
            },
            userInfo: {
                gender: profile.Gender as EAstrologyUserGender,
                birthTime: this.convertToBirthTimeStr(profile.BirthTime),
                birthPlace: this.fromPlaceStrToLocation(profile.BirthPlace),
                currentPlace: this.fromPlaceStrToLocation(profile.CurrentPlace),
                astroLevel: profile.AstroLevel || 0,
                astroType: profile.AstroType || 0,
            },
            averageRating,
            totalReviews,
        }
        logger.debug({ ctx, roleId, resp }, "getUserShowProfileOK")
        return resp
    }

    convertToUserHoroscopeInfo(profile: AstrologyUserRecord): HoroscopeUserInfo {
        const birthTime = moment(profile.BirthTime);
        const birthPlace = this.fromPlaceStrToLocation(profile.BirthPlace);
        const currentPlace = this.fromPlaceStrToLocation(profile.CurrentPlace);

        const userInfo: HoroscopeUserInfo = {
            user_gender: profile.Gender === EAstrologyUserGender.MALE ? "男" : "女",
            birthdate_year: birthTime.year(),
            // moment.js的month()方法返回0-11表示1-12月,所以需要+1转换为1-12月
            birthdate_month: birthTime.month() + 1,
            birthdate_day: birthTime.date(),
            birthdate_hour: birthTime.hour(),
            birthdate_minute: birthTime.minute(),
            birth_province: birthPlace.province,
            birth_city: birthPlace.city,
            birth_district: birthPlace.district,
            now_province: currentPlace.province,
            now_city: currentPlace.city,
            now_district: currentPlace.district,
        }
        return userInfo
    }

    async updateUserDailyForecast(ctx: Context, roleId: number, ds: string, fortuneScore: number): Promise<void> {
        try {
            const now = Date.now()
            const r = await this.astrologyUserDailyForecastModel.createOrUpdate({
                RoleId: roleId,
                DS: ds,
                FortuneScore: fortuneScore,
                CreateTime: now,
                UpdateTime: now,
            }, {
                RoleId: roleId,
                DS: ds,
                FortuneScore: fortuneScore,
                UpdateTime: now,
            })
            logger.info({ ctx, roleId, ds, fortuneScore, r }, "updateUserDailyForecastOK")
        } catch (err) {
            logger.error({ ctx, err, roleId, ds, fortuneScore }, "updateUserDailyForecastError")
            throw errorCode.DatabaseError
        }
    }

    async getForecastFortuneDailyAvgScore(ctx: Context, ds: string): Promise<number> {
        try {
            const query = this.astrologyUserDailyForecastModel.scope().where({ DS: ds }).avg('FortuneScore as avgScore')
            const r = await this.astrologyUserDailyForecastModel.executeByQuery<{ avgScore: number }[]>(query)
            if (r.length === 0) {
                return 0
            }
            return r[0].avgScore || 0
        } catch (err) {
            logger.error({ ctx, err, ds }, "getForecastFortuneDailyAvgScoreError")
            throw errorCode.DatabaseError
        }
    }

    async verifyUserNotRegister(ctx: Context, roleId: number): Promise<void> {
        const profile = await this.astrologyUserModel.findOne({ RoleId: roleId }, ['RoleId'])
        if (profile && profile.RoleId) {
            logger.warn({ ctx, roleId }, "curRoleIdAlreadyRegistered")
            throw errorCodesV2.AstrologyUserAlreadyRegistered
        }
    }
}