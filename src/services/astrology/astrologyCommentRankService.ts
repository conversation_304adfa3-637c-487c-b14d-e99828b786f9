import { Context } from "../../context";
import { clazzLogger } from "../../logger";
import { AstrologyUserModel } from "../../models/astrology/astrologyUserModel";

import { errorCode } from "../../errorCodes";
import { getRedis, ExpireType } from "../../common/redis";
import { cacheKeyGen } from "../../common/util";
import { RedisDistributedLock } from "../../common/redisDistributedLock";
import { astrologyCfg } from "../../common/config";
import { AstrologyWeeklyCommentRankService } from "./astrologyWeeklyCommentRankService";
import { schemas } from "../../types/type";

const logger = clazzLogger("services/astrology/astrologyCommentRankService");

export type CommentRankItem = schemas['comment_rank_resp']['list'][0];

export type CommentRankResponse = schemas['comment_rank_resp'];

export interface CommentRankCache {
    data: {
        header: ['rank', 'roleId', 'roleName', 'commentCount', 'jobId', 'gender', 'subGender', 'headPaintId', 'bodyPaintId'];
        values: [number, number, string, number, number, number, number, number, number][];
    };
    updateTime: number;
}

export class AstrologyCommentRankService {
    private static instance: AstrologyCommentRankService;
    private astrologyUserModel: AstrologyUserModel;
    private weeklyRankService: AstrologyWeeklyCommentRankService;

    constructor() {
        this.astrologyUserModel = AstrologyUserModel.getInstance();
        this.weeklyRankService = AstrologyWeeklyCommentRankService.getInstance();
    }

    static getInstance() {
        if (!AstrologyCommentRankService.instance) {
            AstrologyCommentRankService.instance = new AstrologyCommentRankService();
        }
        return AstrologyCommentRankService.instance;
    }

    /**
     * 获取解惑次数排行榜
     */
    async getCommentRank(ctx: Context): Promise<CommentRankResponse> {
        try {
            const cachedRank = await this.getCachedRank();
            if (cachedRank) {
                return cachedRank;
            }

            // 如果缓存中没有数据，则生成新的排行榜
            return await this.refreshRank(ctx);
        } catch (err) {
            logger.error({ ctx, err }, "getCommentRankError");
            throw errorCode.DatabaseError;
        }
    }

    /**
     * 刷新排行榜数据
     * 优化：使用JOIN查询减少数据库往返次数，优化排名计算逻辑，使用分布式锁防止并发刷新
     */
    async refreshRank(ctx: Context): Promise<CommentRankResponse> {
        const distributedLock = new RedisDistributedLock(this.getRefreshLockKey());
        let lockResult: { acquired: boolean; lockValue?: string } = { acquired: false };

        try {
            logger.info({ ctx }, "refreshCommentRankStart");

            // 尝试获取分布式锁
            lockResult = await distributedLock.acquire({ timeoutSeconds: 10, retryCount: 3, retryIntervalMs: 500 });

            if (!lockResult.acquired) {
                // 获取锁失败，说明有其他进程正在刷新，等待一下再从缓存获取
                logger.info({ ctx }, "refreshCommentRankLockFailed_WaitForOtherProcess");
                await this.waitForRefresh();

                const cachedResult = await this.getCachedRank();
                if (cachedResult) {
                    logger.info({ ctx }, "refreshCommentRankGetCachedAfterWait");
                    return cachedResult;
                }

                // 如果等待后仍然没有缓存，说明其他进程可能失败了，重新尝试获取锁
                logger.warn({ ctx }, "refreshCommentRankNoCacheAfterWait_RetryLock");
                const retryLockResult = await distributedLock.acquire({ timeoutSeconds: 10, retryCount: 3, retryIntervalMs: 500 });
                if (!retryLockResult.acquired) {
                    // 重试仍然失败，返回空结果避免阻塞
                    logger.error({ ctx }, "refreshCommentRankRetryLockFailed");
                    return { list: [], updateTime: Date.now() };
                }
                // 更新lockResult为重试成功的结果
                lockResult = retryLockResult;
            }

            const startTime = Date.now();

            // 优化：使用JOIN查询一次性获取用户和角色信息，减少数据库往返
            // 排序规则：按解惑次数降序，相同次数时按最后解惑时间降序（最近解惑的排前面）
            const query = this.astrologyUserModel.scope()
                .select([
                    'nsh_astrology_user.RoleId',
                    'nsh_astrology_user.CommentCount',
                    'nsh_astrology_user.LastCommentTime',
                    'nsh_roleinfo.RoleName',
                    'nsh_roleinfo.JobId',
                    'nsh_roleinfo.Gender',
                    'nsh_roleinfo.SubGender',
                    'nsh_roleinfo.HeadPaintId',
                    'nsh_roleinfo.BodyPaintId'
                ])
                .innerJoin('nsh_roleinfo', 'nsh_astrology_user.RoleId', 'nsh_roleinfo.RoleId')
                .where('nsh_astrology_user.CommentCount', '>', 0)
                .orderBy('nsh_astrology_user.CommentCount', 'desc')
                .orderBy('nsh_astrology_user.LastCommentTime', 'desc')
                .limit(astrologyCfg.commentRankSize);

            const results = await this.astrologyUserModel.executeByQuery(query);

            if (results.length === 0) {
                const emptyResult = { list: [], updateTime: Date.now() };
                await this.setCachedRank(emptyResult);
                return emptyResult;
            }

            // 优化：直接从JOIN结果构建排行榜，无需额外的Map查找
            const rankList: CommentRankItem[] = [];
            let currentRank = 1;
            let lastCommentCount = -1;

            for (let i = 0; i < results.length; i++) {
                const result = results[i];

                // 处理排名逻辑：相同解惑次数的用户排名相同
                if (result.CommentCount !== lastCommentCount) {
                    currentRank = i + 1;
                    lastCommentCount = result.CommentCount;
                }

                rankList.push({
                    roleId: result.RoleId,
                    roleName: result.RoleName,
                    commentCount: result.CommentCount,
                    rank: currentRank,
                    jobId: result.JobId,
                    gender: result.Gender,
                    subGender: result.SubGender,
                    headPaintId: result.HeadPaintId,
                    bodyPaintId: result.BodyPaintId,
                });
            }

            const result: CommentRankResponse = {
                list: rankList,
                updateTime: Date.now()
            };

            // 4. 缓存结果
            await this.setCachedRank(result);

            const endTime = Date.now();
            const duration = endTime - startTime;
            logger.info({ ctx, rankCount: rankList.length, duration }, "refreshCommentRankEnd");

            return result;
        } catch (err) {
            logger.error({ ctx, err }, "refreshCommentRankError");
            throw errorCode.DatabaseError;
        } finally {
            // 释放分布式锁
            if (lockResult.acquired && lockResult.lockValue) {
                await distributedLock.release(lockResult.lockValue);
            }
        }
    }



    /**
     * 获取缓存的排行榜数据
     */
    private async getCachedRank(): Promise<CommentRankResponse | null> {
        try {
            const cached = await getRedis().getAsync(this.getRankCacheKey());
            if (!cached) {
                return null;
            }

            const cacheData: CommentRankCache = JSON.parse(cached);
            return this.convertCacheToResponse(cacheData);
        } catch (err) {
            logger.error({ err }, "getCachedRankError");
            return null;
        }
    }

    /**
     * 设置缓存的排行榜数据
     */
    private async setCachedRank(data: CommentRankResponse): Promise<void> {
        try {
            const cacheData = this.convertResponseToCache(data);
            await getRedis().setAsync(
                this.getRankCacheKey(),
                JSON.stringify(cacheData),
                ExpireType.EX,
                astrologyCfg.commentRankCacheTime * 60
            );
        } catch (err) {
            logger.error({ err, data }, "setCachedRankError");
        }
    }

    /**
     * 获取排行榜缓存键
     */
    private getRankCacheKey(): string {
        return cacheKeyGen("astrology_comment_rank", {});
    }

    /**
     * 获取刷新排行榜的分布式锁键
     */
    private getRefreshLockKey(): string {
        return cacheKeyGen("astrology_comment_rank_refresh_lock", {});
    }

    /**
     * 等待其他进程完成刷新
     */
    private async waitForRefresh(): Promise<void> {
        // 等待2-5秒，给其他进程时间完成刷新
        const waitTime = 2000 + Math.random() * 3000; // 2-5秒随机等待，避免惊群效应
        await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    /**
     * 更新用户解惑次数（当用户发表解惑时调用）
     * 优化：使用原子操作避免并发问题，同时更新解惑次数和最后解惑时间
     */
    async incrementUserCommentCount(ctx: Context, roleId: number): Promise<void> {
        try {
            const now = Date.now();

            // 原子性更新用户的解惑次数和最后解惑时间
            const updateResult = await this.astrologyUserModel.updateByCondition(
                { RoleId: roleId },
                {
                    CommentCount: this.astrologyUserModel.raw('COALESCE(CommentCount, 0) + 1'),
                    LastCommentTime: now,
                    UpdateTime: now
                }
            );

            // 如果更新影响行数为0，说明用户不存在，记录警告但不抛出错误
            // 因为只有已注册的星巫用户才应该有解惑次数统计
            if (updateResult.affectedRows === 0) {
                logger.warn({ ctx, roleId }, "incrementUserCommentCountUserNotFound");
                return; // 用户不存在，直接返回，不影响主流程
            }

            // 异步清除缓存，避免阻塞主流程
            this.invalidateRankCache().catch(err => {
                logger.warn({ ctx, err }, "invalidateRankCacheError");
            });

            // 同步更新周排行榜
            this.weeklyRankService.incrementUserWeeklyComment(ctx, roleId).catch(err => {
                logger.warn({ ctx, err, roleId }, "incrementUserWeeklyCommentError");
            });

            logger.info({ ctx, roleId }, "incrementUserCommentCountSuccess");
        } catch (err) {
            logger.error({ ctx, err, roleId }, "incrementUserCommentCountError");
            throw errorCode.DatabaseError;
        }
    }

    /**
     * 异步清除排行榜缓存
     */
    private async invalidateRankCache(): Promise<void> {
        try {
            await getRedis().delAsync(this.getRankCacheKey());
        } catch (err) {
            logger.error({ err }, "invalidateRankCacheError");
        }
    }

    /**
     * 将CommentRankResponse转换为CommentRankCache格式
     * 游戏要求一次返回100个，所以这里需要把数据处理成更加紧凑的格式
     */
    private convertResponseToCache(response: CommentRankResponse): CommentRankCache {
        const values: [number, number, string, number, number, number, number, number, number][] =
            response.list.map(item => [
                item.rank,
                item.roleId,
                item.roleName,
                item.commentCount,
                item.jobId,
                item.gender,
                item.subGender,
                item.headPaintId,
                item.bodyPaintId
            ]);

        return {
            data: {
                header: ['rank', 'roleId', 'roleName', 'commentCount', 'jobId', 'gender', 'subGender', 'headPaintId', 'bodyPaintId'],
                values
            },
            updateTime: response.updateTime
        };
    }

    /**
     * 将CommentRankCache格式转换为CommentRankResponse
     */
    private convertCacheToResponse(cache: CommentRankCache): CommentRankResponse {
        const list: CommentRankItem[] = cache.data.values.map(values => ({
            rank: values[0],
            roleId: values[1],
            roleName: values[2],
            commentCount: values[3],
            jobId: values[4],
            gender: values[5],
            subGender: values[6],
            headPaintId: values[7],
            bodyPaintId: values[8]
        }));

        return {
            list,
            updateTime: cache.updateTime
        };
    }

    /**
     * 预热缓存 - 在系统启动时或定时任务中调用
     */
    async warmupCache(ctx: Context): Promise<void> {
        try {
            logger.info({ ctx }, "warmupCacheStart");
            await this.refreshRank(ctx);
            logger.info({ ctx }, "warmupCacheEnd");
        } catch (err) {
            logger.error({ ctx, err }, "warmupCacheError");
        }
    }

    /**
     * 检查是否需要刷新排行榜
     * 优化：避免频繁刷新，只在必要时刷新
     */
    async shouldRefreshRank(): Promise<boolean> {
        try {
            const cached = await this.getCachedRank();
            if (!cached) {
                return true; // 没有缓存，需要刷新
            }

            const cacheAge = Date.now() - cached.updateTime;
            const maxAge = astrologyCfg.commentRankCacheTime * 60 * 1000; // 转换为毫秒

            return cacheAge >= maxAge; // 缓存过期，需要刷新
        } catch (err) {
            logger.error({ err }, "shouldRefreshRankError");
            return true; // 出错时默认刷新
        }
    }

    /**
     * 智能获取排行榜 - 只在需要时刷新
     */
    async getCommentRankSmart(ctx: Context): Promise<CommentRankResponse> {
        try {
            const cached = await this.getCachedRank();
            if (cached && !(await this.shouldRefreshRank())) {
                return cached;
            }

            return await this.refreshRank(ctx);
        } catch (err) {
            logger.error({ ctx, err }, "getCommentRankSmartError");
            throw errorCode.DatabaseError;
        }
    }
}
