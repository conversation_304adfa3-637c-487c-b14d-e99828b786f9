import { corpAuthCfg } from "../../common/config";
import { CorpAuthInfo, ctxCorpAuthInfoKey, ONE_DAY_SECONDS } from "../../common/constants";
import { hexMd5 } from "../../common/util";
import { Context } from "../../context";

export function generateToken(mail: string, time: number, secret: string) {
    const payload = [mail, time, secret].join('');
    return hexMd5(payload);
}

export function skipCorpAuth(req, res, next) {
    req.skipCorpAuth = true;
    next();
}

export function corpAuthCookieAuth(req, res, next) {
    if (req.skipCorpAuth) {
        next();
        return;
    }
    const cookies = req.cookies;
    let info = cookies?.[corpAuthCfg.infoKey];
    let token = cookies?.[corpAuthCfg.tokenKey];
    if (!info || !token) {
        res.send(401, { code: 401, msg: "当前用户未登录" })
        return;
    }
    let [mailBuff, time, payloadEncoded] = info.split('.');
    let mail = Buffer.from(mailBuff, 'base64').toString();

    let payload: { fullname: string, nickname: string } = JSON.parse(
        Buffer.from(decodeURIComponent(payloadEncoded), "base64").toString()
    );
    if (time) {
        if (Date.now() / 1000 - time > ONE_DAY_SECONDS) {
            res.send(401, { code: 401, msg: "登录状态过期,请重新登录" })
            return;
        }
    }
    let calToken = generateToken(mail, time, corpAuthCfg.secret)
    if (calToken !== token) {
        return { validate: false, msg: "登录token错误" };
    }
    let ctx = Context.createWithRequest(req);

    const corpAuthInfo: CorpAuthInfo = {
        mail,
        fullName: payload.fullname,
        nickname: payload.nickname
    }
    ctx.set(ctxCorpAuthInfoKey, corpAuthInfo)
    next();
}
