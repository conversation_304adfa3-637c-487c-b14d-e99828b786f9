﻿/* eslint-disable @typescript-eslint/no-explicit-any */
import * as bluebird from "bluebird";
import type * as knex from "knex";
import * as _ from "lodash";
import { Features, copyMomentCfg, testCfg } from "../common/config";
import { AuditStatues, EAuditStatus, EPicMediaType, Statues, TABLE_NAMES } from "../common/constants";
import { Constants, pyq as pyqData } from "../common/data";
import * as db from "../common/db";
import { getRedis } from "../common/redis";
import * as util from "../common/util";
import { getIsPickerFetcher } from "../components/adminMomentPick/service";
import { MdErrors, PyqErrors } from "../errorCodes";
import { EventBus, Events } from "../eventBus";
import { ADD_COMMENT_PAYLOAD, CANCEL_LIKE_PAYLOAD, DEL_COMMENT_PAYLOAD, LIKE_PAYLOAD } from "../eventTypes";
import { asyncRun, formatResult } from "../helper";
import { clazzLogger } from "../logger";
import { CopyMomentModel, CopyMomentRecord } from "../models/CopyMoment";
import { OfficialAccount } from "../models/OfficialAccount";
import { OfficialAccountMoment } from "../models/OfficialAccountMoment";
import { Comment as PyqComment } from "../models/PyqComment";
import { Follow } from "../models/PyqFollow";
import { MomentForwardModel, MomentForward as PyqMomentForward } from "../models/PyqMomentForward";
import { MomentLike as PyqMomentLike } from "../models/PyqMomentLike";
import { EMomentVisualRange, Moment, MomentModel, MomentRecord, Moment as PyqMoments } from "../models/PyqMoments";
import { Profile, Profile as PyqProfile } from "../models/PyqProfile";
import { MomentTopic as PyqTopicMoment } from "../models/PyqTopicMoment";
import { BasicParams, Pagination, PlayerReq } from "../types/req";
import { MomentRes, PlayerRes } from "../types/res";
import { UpdateResult } from "../types/type";
import * as HotMomentCache from "./HotMomentsCache";
import { processTopicMoment } from "./activityTopic";
import { processAtPlayerWhenAddMoment } from "./atPlayer";
import { QueryTopCommentParams, getMomentTopComments, tryToDelMomentTopComments } from "./comment";
import * as FollowService from "./follow";
import { getGuildPolemicIdFromLink, sharePolemic } from "./guildPolemic";
import { fillIdentity } from "./identity";
import { genPicIdFromInfo } from "./imageAudit";
import * as inform from "./inform";
import {
  getPlayerHomeMomentsCount,
  getPlayerMomentsCount,
  refreshPlayerHomeMomentsCount,
  refreshPlayerMomentsCount,
} from "./momentCounter";
import { ForwardCountService, PreviousForwardService } from "./momentForward";
import { addWeekRenQiForComment, addWeekRenQiForLike } from "./momentWeekRenqi";
import { getFollowOfficialIds } from "./official_accounts/follow";
import * as profile from "./profile";
import { getAddMomentImageLimit } from "./profile";
import { addVisitorId } from "./recentVisitor";
import { viewFriendsMoments } from "./redDot";
import { sendPic } from "./sendImageAudit";
import { addCommentLog, addMomentActionLog, addMomentLog } from "./yunyinLog";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const dataUtil = require("../common/data");
import Bluebird = require("bluebird");
import { isValidResourceUrl } from "./baseService";
const logger = clazzLogger("service/moment");

const BASIC_MOMENT_COLS: (keyof MomentRecord)[] = [
  "ID",
  "RoleId",
  "Text",
  "ImgList",
  "ImgAudit",
  "ZanList",
  "CreateTime",
  "HotState",
  "VideoList",
  "VideoAudit",
  "VideoCoverList",
  "VideoCoverAudit",
  "VisualRange",
  "CanCommentByAll",
  "CanForward",
  "CanCommentByStranger",
];

export interface IGetMomentParams extends BasicParams, Pagination {
  targetid?: number;
  isViewFriendMoments: boolean;
  justFollow: boolean;
}

async function getPyqPlayerIds(roleId: number): Promise<number[]> {
  const ids = await FollowService.getFollowIds(roleId);
  return [roleId, ...ids];
}

export async function get(params: IGetMomentParams) {
  let queryRoleIds = [];
  const targetId = params.targetid;
  if (targetId) {
    queryRoleIds = [targetId];
    params.justFollow = false;
    // 查看指定玩家动态, 添加最近访问记录
    addVisitorId(targetId, params.roleid);
    if (params.roleid !== params.targetid) {
      const isFollowEachOther = await Follow.isFollowEachOther(params.roleid, params.targetid);
      params.justFollow = !isFollowEachOther;
    }
  } else {
    params.isViewFriendMoments = true;
    queryRoleIds = await getPyqPlayerIds(params.roleid);
  }
  return getMoments(queryRoleIds, params);
}

export async function refreshHomeMomentCount(roleId: number): Promise<void> {
  const queryRoleIds = await getPyqPlayerIds(roleId);
  await refreshPlayerHomeMomentsCount(roleId, queryRoleIds);
}

export async function getCount(params): Promise<number> {
  const targetId = params.targetid;
  const roleId = params.roleId;
  let justFollow = false;
  if (roleId !== targetId) {
    const isFollowEachOther = await Follow.isFollowEachOther(roleId, targetId);
    justFollow = !isFollowEachOther;
  }
  if (targetId) {
    const count = await getPlayerMomentsCount(targetId, justFollow, roleId);
    return count;
  } else {
    throw { code: -1, msg: "必须有targetid" };
  }
}

export function getOne(params) {
  const momentId = params.id;
  return db
    .query({
      table: TABLE_NAMES.moment,
      cols: BASIC_MOMENT_COLS,
      filter: { ID: momentId, Status: dataUtil.Constants.STATUS_NORMAL },
    })
    .then(function (rows) {
      if (_.isEmpty(rows)) {
        throw { code: -1, msg: "该心情不存在" };
      } else {
        return rows;
      }
    })
    .then(util.currying(fillInfo, params))
    .then(function (list) {
      return fillIdentity(list);
    })
    .then(function (list) {
      return list[0];
    });
}

export function getHot(params) {
  const serverId = params.serverid || params.server;
  return getHotMoments(params, serverId);
}

export function getAllHot(params) {
  return getHotMoments(params, "all");
}

exports.hide = function (req, callback) {
  hideMoment(req.params).then(callback).catch(callback);
};

exports.like = function (params, callback) {
  likeMoment(params).then(callback).catch(callback);
};

exports.comment = function (params, callback) {
  addComment(params).then(callback).catch(callback);
};

exports.uncomment = function (params, callback) {
  delComment(params).then(callback).catch(callback);
};

export async function getMoments(roleList: number[], params: IGetMomentParams) {
  let totalCount = 0;
  if (!roleList || !roleList.length) {
    return { list: [], totalCount: 0 };
  }

  const roleId = params.roleid;
  let rows = [];
  if (roleList.length === 1 && OfficialAccount.isOfficialAccount(roleList[0])) {
    const isPublicOfficial = await OfficialAccount.isPublic(roleList[0]);
    if (isPublicOfficial) {
      rows = await getOfficialMoments(roleList[0], params);
    } else {
      return { list: [], totalCount: 0 };
    }
  } else {
    rows = await getBasicMoments(roleList, roleId, params);
  }

  if (params.isViewFriendMoments) {
    totalCount = await getPlayerHomeMomentsCount(params.roleid, roleList);
  } else {
    totalCount = await getPlayerMomentsCount(params.targetid, params.justFollow, roleId);
  }
  const list = await fillInfo(params, rows);

  if (params.isViewFriendMoments) {
    viewFriendsMoments(roleId);
  }
  const result = formatResult({ list: list, count: totalCount });
  return result;
}

async function getBasicMoments(roleList: number[], roleId: number, params: IGetMomentParams) {
  const query = Moment.scope();

  if (params.isViewFriendMoments) {
    const followEachOthers = await Follow.filterFollowingMe(roleList, roleId);
    followEachOthers.push(roleId);

    const justFollow = _.difference(roleList, followEachOthers);

    query.where(function () {
      this.where(function () {
        this.whereIn("RoleId", justFollow).where("VisualRange", EMomentVisualRange.all);
      });
      this.orWhere(function () {
        this.whereIn("RoleId", followEachOthers).whereIn("VisualRange", [
          EMomentVisualRange.all,
          EMomentVisualRange.friend,
        ]);
      });
      this.orWhere("RoleId", roleId);
    });
  } else if (params.justFollow) {
    query.where("RoleId", roleList[0]).where("VisualRange", EMomentVisualRange.all);
  } else if (roleId != roleList[0]) {
    query.where("RoleId", roleList[0]).whereIn("VisualRange", [EMomentVisualRange.all, EMomentVisualRange.friend]);
  } else {
    query.where("RoleId", roleId);
  }

  query
    .where("Status", dataUtil.Constants.STATUS_NORMAL)
    .where(function () {
      this.whereRaw("Text!=''").orWhereRaw(`RoleId = ${roleId} or locate(',1', concat(',', ImgAudit))>0`);
    })
    .select(BASIC_MOMENT_COLS)
    .orderBy("CreateTime", "DESC")
    .limit(params.pageSize)
    .offset((params.page - 1) * params.pageSize);

  return await Moment.executeByQuery(query);
}

async function getOfficialMoments(officialRoleId: number, params: IGetMomentParams) {
  const cols = BASIC_MOMENT_COLS.map((col) => {
    return "m." + col;
  });
  const query = Moment.normalScope()
    .from(Moment.tableName + " as m ")
    .leftJoin(OfficialAccountMoment.tableName + " as o", "m.Id", "o.MomentId")
    .select(cols)
    .where("m.RoleId", officialRoleId)
    .orderBy("o.TopTime", "desc")
    .orderBy("m.CreateTime", "desc");
  const rows = await Moment.queryWithPagination(query, params);
  return rows;
}

export async function getMomentsCount(roleList) {
  if (!roleList || !roleList.length) {
    return { count: 0 };
  }
  const filter = {
    RoleId: roleList,
    Status: dataUtil.Constants.STATUS_NORMAL,
    // or: { "Text!=''": null, "locate(',1',concat(',',ImgAudit))>0": null }
  };
  return db
    .query({
      table: TABLE_NAMES.moment,
      cols: ["count(*) as mcount"],
      filter: filter,
    })
    .then(function (res) {
      return { count: res[0].mcount };
    });
}

function getForwardCountHash(momentIds) {
  const hash = {};
  return bluebird
    .mapSeries(momentIds, (mId) => {
      return ForwardCountService.getForwardCount(mId).then((count) => {
        // @ts-ignore
        hash[mId] = count;
      });
    })
    .then(() => {
      return hash;
    });
}

function getPreviousForwards(momentIds) {
  let validMomentIds;
  let result;
  return bluebird
    .map(momentIds, (mId) => {
      return PreviousForwardService.getPreviousForwards(mId).then((info) => {
        return { MomentId: mId, PreviousForwards: info };
      });
    })
    .then((rows) => {
      result = rows;
      const mIds = _.flatMap(rows, (row) => _.map(row.PreviousForwards, (x) => x.ID));
      //这里需要过滤下Status=0， 前面可能从缓存中读出来，保留了删除了的之前转发心情
      return PyqMoments.find({ ID: mIds, Status: Constants.STATUS_NORMAL }, { cols: ["ID"] });
    })
    .then((rows) => {
      validMomentIds = _.map(rows, "ID");
      _.forEach(result, (info) => {
        info.PreviousForwards = _.filter(info.PreviousForwards, (m) => _.includes(validMomentIds, m.ID));
      });
      return result;
    });
}

async function getIsFollowing(moments: { RoleId: number }[], curRoleId: number): Promise<boolean[]> {
  const roleIds = moments.map((x) => x.RoleId);
  const meFollowRoleIds = await Follow.filterMeFollow(roleIds, curRoleId);
  const meFollowOfficialIds = await getFollowOfficialIds(curRoleId);
  const followIdSet = new Set([].concat(meFollowRoleIds, meFollowOfficialIds));
  const result = moments.map((x) => {
    return followIdSet.has(x.RoleId);
  });
  return result;
}

interface FillMomentOpt {
  roleid: number | string;
  isFillFollow?: boolean;
  refreshPickInfo?: boolean;
}

export async function fillInfo(params: FillMomentOpt, moments: MomentRecord[]) {
  const TOP_ZAN_SIZE = 20;

  const curRoleId = parseInt(params.roleid as string, 10);

  if (_.isEmpty(moments)) {
    return [];
  }

  const momentIds = _.map(moments, "ID");

  const queryComments = moments.map((m) => {
    const commentCount = PyqMoments.getCommentsCountFromHotStateStr(m.HotState);
    const item: QueryTopCommentParams = { momentId: m.ID, commentCount: commentCount };
    return item;
  });

  function getForwardMomentsHash() {
    return PyqMomentForward.getForwardOriginMomentsHash(momentIds);
  }

  function getProfileRoleInfos(roleIds, curRoleId, queryFollowingRoleIds) {
    return profile.getRoleInfo(roleIds, curRoleId, {
      includeFollowingList: true,
      queryFollowingRoleIds: queryFollowingRoleIds || [],
      queryBlackList: true,
      formatPrivacy: true,
    });
  }

  function getRelatedRoleIds(moments, comments, forwardMoments, previousForwardsRoleIds) {
    const commentRoleIds = _.flatMap(comments, (c) => [c.RoleId, c.ReplyId]);
    const momentRoleIds = _.map(moments, "RoleId");
    const topZanMomentRoleIds = _.flatMap(moments, (m) => getTopZanRoleIds(m, TOP_ZAN_SIZE));
    const forwardMomentRoleIds = _.map(forwardMoments, "RoleId");
    return _.compact(
      _.union(momentRoleIds, topZanMomentRoleIds, commentRoleIds, forwardMomentRoleIds, previousForwardsRoleIds),
    );
  }

  function isUserCanComment(momentOwnerProfile, curRoleId) {
    return pyqData.canComment(momentOwnerProfile, curRoleId, pyqData.getAllowHash(momentOwnerProfile));
  }

  function getMixinRoleInfo(roleInfo: Partial<profile.FullRoleInfo>) {
    if (!roleInfo) return null;
    const frameId = PyqProfile.getFrameIdFromExpressionBase(roleInfo.ExpressionBase);
    const showPhoto = PyqProfile.getPhotoView(roleInfo.Photo, roleInfo.PhotoAudit);
    const record: any = {
      roleId: roleInfo.RoleId,
      roleName: roleInfo.RoleName,
      gender: roleInfo.Gender || 0,
      subGender: roleInfo.SubGender || 0,
      voiceGender: roleInfo.SnsGender || 0,
      jobId: roleInfo.JobId,
      level: roleInfo.Level,
      headPaintId: roleInfo.HeadPaintId,
      bodyPaintId: roleInfo.BodyPaintId,
      photo: showPhoto,
      photoAudit: roleInfo.PhotoAudit || EAuditStatus.Init,
      frameId: frameId,
      bInBlacklist: roleInfo.bInBlacklist,
      privacy: roleInfo.Privacy,
    };
    if (Features.lianghao) {
      record.lianghaoId = roleInfo.lianghaoId;
      record.lianghaoOutTimeStamp = roleInfo.lianghaoOutTimeStamp;
    }
    return record;
  }

  function getIsUserLikedHash(momentIds, curRoleId) {
    const query = PyqMomentLike.normalScope()
      .whereIn("MomentId", momentIds)
      .where("RoleId", curRoleId)
      .select("MomentId");
    return PyqMomentLike.executeByQuery(query).then((rows) => {
      const likedMIds = new Set(_.map(rows, "MomentId"));
      return _.reduce(
        momentIds,
        (hash, mId) => {
          hash[mId] = likedMIds.has(mId);
          return hash;
        },
        {},
      );
    });
  }

  function getTopZanRoleIds(moment, size) {
    const list = util.csvStrToIntArray(moment.ZanList);
    const officialLikes = list.filter((id) => OfficialAccount.isOfficialAccount(id));
    const normalLikes = list.filter((id) => !OfficialAccount.isOfficialAccount(id));
    const newList = _.concat(officialLikes, normalLikes);
    return _.take(newList, size);
  }

  const data = await bluebird.all([
    getMomentTopComments(queryComments),
    getForwardMomentsHash(),
    getForwardCountHash(momentIds),
    getPreviousForwards(momentIds),
    getIsUserLikedHash(momentIds, curRoleId),
    getIsFollowing(moments, curRoleId),
  ]);
  let [comments, forwardMomentsHash, forwardCountHash, previousForwardsList, isUserLikedHash, isFollowing] = data;
  const previousForwardsRoleIds = _.map(
    _.flatMap(previousForwardsList, (r) => _.get(r, "PreviousForwards")),
    "RoleId",
  );
  const queryRoleIds = getRelatedRoleIds(moments, comments, _.values(forwardMomentsHash), previousForwardsRoleIds);
  const queryFollowingRoleIds = _.map(moments, "RoleId");
  const roleIdToProfiles = await getProfileRoleInfos(queryRoleIds, curRoleId, queryFollowingRoleIds);
  const officialProfileMap = await profile.getOfficialProfileMap(queryRoleIds);

  _.mapValues(forwardMomentsHash, (moment) => {
    //转发引用的心情删除也需要被查询出来， 并且把图片置空，文本变成该心情已经删除
    const officialProfile = officialProfileMap.get(moment.RoleId);
    let isForwardDeleted = false;
    if (officialProfile && !officialProfile.isPublic) {
      isForwardDeleted = true;
    } else {
      isForwardDeleted = moment.Status !== Constants.STATUS_NORMAL;
    }

    moment.videos = getMomentVideoViews(moment, curRoleId);
    moment.imgPlayType = getMomentImgPlayType(moment.videos);

    if (isForwardDeleted) {
      moment.Text = "该心情已删除";
      moment.ImgList = "";
      moment.ImgAudit = "";
      moment.videos = [];
    }
    pyqData.setImgListView(moment, curRoleId); // 设置图片显示状态

    util.extend(moment, getMixinRoleInfo(roleIdToProfiles[moment.RoleId]));
    return moment;
  });

  _.forEach(previousForwardsList, (row) => {
    if (row.PreviousForwards) {
      _.forEach(row.PreviousForwards, (forward) => {
        util.extend(forward, { roleinfo: getMixinRoleInfo(roleIdToProfiles[forward.RoleId]) });
      });
    }
  });

  comments = _.map(comments, (c) => {
    util.extend(c, getMixinRoleInfo(roleIdToProfiles[c.RoleId]));
    if (c.ReplyId) {
      c.ReplyInfo = getMixinRoleInfo(roleIdToProfiles[c.ReplyId]);
    }
    return c;
  });

  const commentGroupByMomentId = _.groupBy(comments, "TargetId");
  const previousForwardsHash = util.keyToRecordHash(previousForwardsList, "MomentId");
  const isPickFetcher = await getIsPickerFetcher(params.refreshPickInfo);

  return _.map(moments, (moment, momentIndex) => {
    pyqData.setImgListView(moment, curRoleId); // 设置图片显示状态
    //@ts-ignore
    moment.videos = getMomentVideoViews(moment, curRoleId);
    //@ts-ignore
    moment.imgPlayType = getMomentImgPlayType(moment.videos);
    const hotState = PyqMoments.formatHotState(moment.HotState);

    const zanRoleInfoList = _.map(getTopZanRoleIds(moment, TOP_ZAN_SIZE), function (roleId) {
      if (roleIdToProfiles[roleId]) {
        return getMixinRoleInfo(roleIdToProfiles[roleId]);
      } else {
        logger.warn({ roleId }, "RoleInfoNotFound");
        return { roleId: roleId };
      }
    });

    const profileInfo = roleIdToProfiles[moment.RoleId] || {};
    const commentList = commentGroupByMomentId[moment.ID] || [];
    const forwardMoment = forwardMomentsHash[moment.ID] || {};
    const forwardCount = forwardCountHash[moment.ID] || 0;
    const previousForwards = previousForwardsHash[moment.ID].PreviousForwards || [];
    const isUserLiked = isUserLikedHash[moment.ID];
    const officialProfile = officialProfileMap.get(moment.RoleId);
    const bePicked = isPickFetcher(moment.ID);

    const mixinProps = {
      likeCount: hotState.like,
      likeUsers: zanRoleInfoList,
      CommentCount: hotState.comment + hotState.reply,
      commentList: commentList,
      bePicked,
      IsUserLiked: isUserLiked,
      canComment: isUserCanComment(profileInfo, curRoleId),
      ForwardCount: forwardCount,
      ForwardMoment: forwardMoment,
      PreviousForwards: previousForwards,
      isFollowing: isFollowing[momentIndex],
    };

    //@ts-ignore
    moment = _.omit(moment, ["HotState", "ZanList", "VideoList", "VideoAudit", "VideoCoverList", "VideoCoverAudit"]);

    return _.assign(moment, mixinProps, getMixinRoleInfo(profileInfo), officialProfile);
  });
}

interface VideoView {
  video: string;
  cover: string;
}

enum ImgPlayType {
  NO_VIDEO = 0,
  VIDEO = 1,
}

function getMomentImgPlayType(videos: string[]) {
  return videos.length > 0 ? ImgPlayType.VIDEO : ImgPlayType.NO_VIDEO;
}

function getMomentVideoViews(moment: MomentRecord, curRoleId: number): VideoView[] {
  const videos = util.csvStrToArray(moment.VideoList);
  const videoAudits = util.csvStrToIntArray(moment.VideoAudit);

  const videoCovers = util.csvStrToArray(moment.VideoCoverList);
  const videoCoverAudit = util.csvStrToIntArray(moment.VideoCoverAudit);

  const videoList: VideoView[] = [];
  const defaultCover = "http://hi-163-nsh.nosdn.127.net/dynamicPicture/auditing.png?imageView";

  for (let i = 0; i < videos.length; i++) {
    const isViewBySelf = moment.RoleId === curRoleId;
    // 未设置封面的情况下处理成封面审核通过
    if (!videoCovers[i]) {
      videoCoverAudit[i] = AuditStatues.Pass;
    }
    if (videoAudits[i] === AuditStatues.Reject || videoCoverAudit[i] === AuditStatues.Reject) {
      videoList.push({ video: "", cover: defaultCover });
    } else {
      if (isViewBySelf) {
        videoList.push({ video: videos[i], cover: videoCovers[i] || "" });
      } else {
        if (videoAudits[i] === AuditStatues.Pass && videoCoverAudit[i] === AuditStatues.Pass) {
          videoList.push({ video: videos[i], cover: videoCovers[i] || "" });
        } else {
          videoList.push({ video: "", cover: defaultCover });
        }
      }
    }

    if (testCfg.skip_audit) {
      videoList.push({ video: videos[i], cover: videoCovers[i] });
    }
  }
  return videoList;
}

async function getWeightFactorReg() {
  const str = await getRedis().getAsync("moment:weight_factor_regex");
  const obj = util.getJsonInfo(str, []) || [];
  const regexs: Array<any> = obj.map((item) => {
    return new RegExp(item);
  });
  return regexs;
}

async function getWeightFactor(): Promise<number> {
  const factor = await getRedis().getAsync("moment:weight_factor");
  if (!factor) {
    return 0.5;
  } else {
    return _.toNumber(factor);
  }
}

interface IAddMoment {
  roleid: number;
  text: string;
  imgs?: string[] | string;
  videos?: string[];
  videoImgs?: string[];
  topicId?: number;
  ip: string;
  context?: string;
  visualRange?: EMomentVisualRange;
  hotFactor?: number;
  canCommentByAll?: 0 | 1;
  canForward?: 0 | 1;
  canCommentByStranger?: 0 | 1;
}

export async function addMoment(params: IAddMoment): Promise<MomentRes.AddMoment> {
  return addMomentInternal(params);
}

interface IAddMomentWithPreApprovedImages extends IAddMoment {
  preApprovedImgs?: string[];
}

export async function addMomentWithPreApprovedImages(params: IAddMomentWithPreApprovedImages): Promise<MomentRes.AddMoment> {
  return addMomentInternal(params);
}

async function getInitialHotStateString(text: string, hotFactor = 1) {
  const reg = await getWeightFactorReg();
  const weightFactor = await getWeightFactor();
  let momentFactor = 1;

  if (reg.some((item) => item.test(text))) {
    momentFactor = weightFactor;
  }
  const hotState = JSON.stringify({ weight: momentFactor, hotFactor });
  return hotState;
}

export function addToTopic(text, momentId) {
  const subject = dataUtil.md.getTopic(text);
  if (!subject) {
    return null;
  }

  let topicId;
  const now = Date.now();
  return db
    .push(
      {
        table: TABLE_NAMES.topic,
        values: { MomentList: momentId, Hot: 1, UpdateTime: now },
        filter: { Subject: subject },
      },
      {
        hookVal: function (values, exist) {
          const oldList = exist && exist.MomentList;
          if (oldList) {
            topicId = exist.ID;
            values.Hot = (parseInt(exist.Hot, 10) || 0) + 1;
            values.MomentList = util.addToList(momentId, oldList);
          } else {
            values.CreateTime = Date.now();
          }
        },
      },
    )
    .then(function (results) {
      PyqTopicMoment.addToTopic(subject, momentId);
      return topicId || results.insertId;
    });
}

export function likeMoment(params: { roleid: number; id: number; action?: string; hotFactor?: number }) {
  const undo = params.action === "undo";
  const roleId = params.roleid;
  const momentId = params.id;
  const Constants = dataUtil.Constants;
  const ZAN_LIST_MAX_SIZE = 20; // zanList最多缓存的角色id数量
  const hotFactor = MomentModel.checkHotFactorParams(params.hotFactor);
  let ownerId;
  return PyqMoments.findOne({ ID: momentId, Status: Constants.STATUS_NORMAL }, ["ID", "RoleId", "ZanList", "HotState"])
    .then((moment) => {
      if (!moment) {
        throw { code: -1, msg: "该心情不存在" };
      } else {
        ownerId = moment.RoleId;
        return moment;
      }
    })
    .then((moment) => {
      const zanList = moment.ZanList;
      const isLiked = ("," + zanList + ",").indexOf("," + roleId) >= 0;
      const hotState = MomentModel.formatHotState(moment.HotState);
      if (isLiked || hotState.like < ZAN_LIST_MAX_SIZE) {
        return [moment, isLiked];
      } else {
        return PyqMomentLike.findOne({ RoleId: roleId, MomentId: momentId, Status: Constants.STATUS_NORMAL }, [
          "ID",
        ]).then((momentLike) => {
          return [moment, !!momentLike];
        });
      }
    })
    .then((result) => {
      const [moment, isLiked] = result;
      if (isLiked !== undo) {
        throw { code: -1, message: undo ? "您尚未点过赞" : "您已经点过赞了" };
      }
      const updatePyqMoment = () => {
        const values = { ZanList: undefined };
        if (moment.ZanList) {
          values.ZanList = undo
            ? util.rmvFromList(roleId, moment.ZanList)
            : util.addToList(roleId, moment.ZanList, ZAN_LIST_MAX_SIZE);
        } else {
          values.ZanList = `${roleId}`;
        }
        const updateHotStep = undo ? -1 : 1;
        util.extend(values, updateHot(moment, "like", updateHotStep, { keepDuration: true, hotFactor })); // 更新动态热度
        return PyqMoments.updateByCondition({ ID: moment.ID }, values);
      };
      const updatePyqMomentLike = () => {
        const status = undo ? Constants.STATUS_DELETE : Constants.STATUS_NORMAL;
        return PyqMomentLike.createOrUpdate(
          { RoleId: roleId, MomentId: momentId, TargetId: ownerId, CreateTime: Date.now(), Status: status },
          { Status: status },
        );
      };
      return bluebird.all([updatePyqMoment(), updatePyqMomentLike()]);
    })
    .then(function () {
      addWeekRenQiForLike(roleId, ownerId);

      const relateId = "like:" + momentId + "-" + roleId; // 唯一ID，用于删除?
      if (undo) {
        inform.del({ RelateId: relateId }); // 删除通知
        addMomentActionLog(roleId, momentId, "cancelLike");
        const payload: CANCEL_LIKE_PAYLOAD = { roleId: roleId, momentId: momentId };
        EventBus.emit(Events.CANCEL_LIKE_MOMENT, payload);
      } else {
        inform.add({ RoleId: roleId, TargetId: ownerId, ObjectId: momentId, RelateId: relateId, Type: "like" }); // 添加通知
        const payload: LIKE_PAYLOAD = { roleId: roleId, momentId: momentId };
        EventBus.emit(Events.LIKE_MOMENT, payload);
        addMomentActionLog(roleId, momentId, "like");
      }

      return null;
    });
}

export function addComment(params: { id: number; roleid: number; replyid?: number; text: string; hotFactor?: number }) {
  let momentId = params.id,
    roleId = params.roleid,
    replyId = params.replyid,
    ownerId,
    text = util.trim(params.text);

  const hotFactor = MomentModel.checkHotFactorParams(params.hotFactor);
  if (!text) {
    throw { code: -1, msg: "评论内容不能为空" };
  }
  let theMoment;
  return db
    .query({
      table: TABLE_NAMES.moment,
      filter: { ID: momentId, Status: dataUtil.Constants.STATUS_NORMAL },
    })
    .then(function (results) {
      theMoment = results[0];
      if (!theMoment) {
        throw { code: -1, msg: "评论的动态不存在" };
      }
      ownerId = theMoment.RoleId;

      return ownerId == roleId
        ? true
        : bluebird
            .all([
              PyqProfile.findOne({ RoleId: ownerId }, ["RoleId", "Privacy", "FriendList", "BlackList"]),
              FollowService.getFollowIds(ownerId),
            ])
            .spread(function (profile: any, followingIds) {
              if (!theMoment.CanCommentByAll) {
                throw { code: -1, msg: "该动态不允许评论" };
              }

              profile.FollowingList = followingIds;
              profile.Privacy = Profile.formatPrivacy(profile.Privacy);
              if (!theMoment.CanCommentByStranger) {
                profile.Privacy.limitComment = true;
              }
              if (!pyqData.canComment(profile, roleId)) {
                throw { code: -1, msg: "您没有评论该动态的权限" };
              }

              const profileRecord = profile;
              if (profileRecord && profileRecord.BlackList) {
                const blacklist = util.csvStrToIntArray(profileRecord.BlackList);
                const blSet = new Set(blacklist);
                if (blSet.has(roleId)) {
                  throw { code: -1, msg: "您已被拉黑" };
                }
              }
            });
    })
    .then(function () {
      const commentFilter = {
        RoleId: roleId,
        TargetId: momentId,
      };
      if (replyId) {
        commentFilter["ReplyId"] = replyId;
      }
      return db
        .query({
          table: TABLE_NAMES.comment,
          cols: ["Text"],
          filter: commentFilter,
        })
        .then(function (results) {
          results = _.map(results, "Text");
          if (_.indexOf(results, text) != -1) {
            throw { code: -1, msg: "您不能评论重复内容" };
          }
        });
    })
    .then(function () {
      return db
        .add({
          table: TABLE_NAMES.comment,
          values: {
            RoleId: roleId,
            TargetId: momentId,
            ReplyId: replyId,
            Text: text,
            CreateTime: Date.now(),
          },
        })
        .then(function (result) {
          // 更新动态热度
          const updateHotType: UpdateHotType = ownerId == roleId ? "reply" : "comment";
          const values = updateHot(theMoment, updateHotType, 1, { keepDuration: true, hotFactor: hotFactor });
          values &&
            db.update({
              table: TABLE_NAMES.moment,
              values: values,
              filter: { ID: momentId },
            });
          // 添加通知
          const commentId = result.insertId;
          const targetId = replyId || ownerId;
          addWeekRenQiForComment(roleId, targetId);
          inform.add({
            RoleId: roleId,
            TargetId: replyId || ownerId,
            ObjectId: momentId,
            RelateId: "comment:" + commentId,
            Text: text,
            Type: replyId ? "reply" : "comment",
          });
          const commentPayload: ADD_COMMENT_PAYLOAD = { roleId: roleId, commentId: commentId, momentId: momentId };
          EventBus.emit(Events.ADD_COMMENT, commentPayload);
          addCommentLog({ roleId: roleId, momentId: momentId, replayId: replyId, text: text, commentId: commentId });
          return { id: commentId };
        });
    });
}

export interface IDelMomentParams {
  id: number;
  roleid: number;
  hotFactor?: number;
}

// 官v删除心情
export async function delMomentByOfficial(params: IDelMomentParams) {
  const momentId = params.id;
  const roleId = params.roleid;

  const result = await PyqMoments.softDeleteByCondition({ ID: momentId });
  const isDeleted = result.affectedRows > 0;
  if (isDeleted) {
    await processDelMoment(roleId, momentId);
  }
  return result;
}

async function processDelMoment(momentId: number, roleId: number, hotFactor = 1) {
  await HotMomentCache.onDeleteMoments([momentId]);
  await PyqComment.clearMomentCommentsCache(momentId);
  EventBus.emit(Events.DELETE_MOMENT, { momentId: momentId, hotFactor });
  // 删除相关通知
  inform.del({
    ObjectId: momentId,
  });
  // 清除count缓存
  addMomentActionLog(roleId, momentId, "del");
  refreshPlayerMomentsCount(roleId);
}

export async function delMoment(params: IDelMomentParams) {
  const momentId = params.id;
  const roleId = params.roleid;

  const result = await PyqMoments.softDeleteByCondition({ ID: momentId, RoleId: params.roleid });
  const isDeleted = result.changedRows > 0;
  if (isDeleted) {
    const hotFactor = MomentModel.checkHotFactorParams(params.hotFactor);
    await processDelMoment(momentId, roleId, hotFactor);
  }
  return result;
}

function hideMoment(params) {
  const roleId = params.roleId;
  const rid = params.targetid;
  return db
    .update(
      {
        table: TABLE_NAMES.moment,
        values: { HideList: rid },
        filter: { RoleId: roleId },
      },
      {
        hookVal: function (values, exist) {
          if (exist && exist.HideList) {
            values.HideList = util.addToList(rid, exist.HideList);
          }
        },
      },
    )
    .then(function (result) {
      return result.affectedRows;
    });
}

enum DelCommentCode {
  PermissionDeny = -1,
  ItemDeleted = -2,
}

export function delComment(params: { id: number; roleid: number; hotFactor?: number }): Promise<UpdateResult> {
  let itemInfo;
  const commentId = params.id;
  const roleId = params.roleid;

  const hotFactor = MomentModel.checkHotFactorParams(params.hotFactor);

  return db
    .query({
      table: TABLE_NAMES.comment + " as c," + TABLE_NAMES.moment + " as m",
      cols: ["m.*", "c.RoleId as CommentRoleId", "c.Status as CommentStatus"],
      filter: { "c.TargetId=m.ID": null, "c.ID": commentId, IdOr: { "c.RoleId": roleId, "m.RoleId": roleId } },
    })
    .then(function (results) {
      itemInfo = results[0];
      if (!itemInfo) {
        return bluebird.reject({ code: DelCommentCode.PermissionDeny, msg: "您没有删除该条评论的权限" });
      }
      if (itemInfo.CommentStatus != dataUtil.Constants.STATUS_NORMAL) {
        return bluebird.reject({ code: DelCommentCode.ItemDeleted, msg: "该条评论已删除" });
      }

      return db.update({
        table: TABLE_NAMES.comment,
        values: { Status: dataUtil.Constants.STATUS_DELETE },
        filter: { ID: commentId },
      });
    })
    .then(function (result) {
      const commentCount = Moment.getCommentsCountFromHotStateStr(itemInfo.HotState);
      tryToDelMomentTopComments(commentId, { momentId: itemInfo.ID, commentCount: commentCount });

      const payload: DEL_COMMENT_PAYLOAD = { roleId: roleId, commentId: commentId };
      EventBus.emit(Events.DEL_COMMENT, payload);

      const values = updateHot(itemInfo, itemInfo.RoleId == itemInfo.CommentRoleId ? "reply" : "comment", -1, {
        keepDuration: true,
        hotFactor,
      });
      values &&
        db.update({
          table: TABLE_NAMES.moment,
          values: values,
          filter: { ID: itemInfo.ID },
        });

      // 删除相关通知
      inform.del({
        RelateId: "comment:" + commentId,
      });
      refreshCommentsCache([commentId]);
      return result;
    });
}

export function getHotMoments(params, serverId) {
  const curRoleId = params.roleid;
  params.isHotMoments = true;
  const MAX_PAGE_SIZE = 20;
  const DefaultPageSize = 10;
  const MAX_HOT_MOMENTS_SIZE = 200;
  const lastHotIndex = Math.max(0, Math.min(MAX_HOT_MOMENTS_SIZE, params.lastid || 0));
  const limit = Math.max(0, Math.min(MAX_PAGE_SIZE, params.page_size || DefaultPageSize));
  let pageSize = params.pageSize || DefaultPageSize;
  let page = params.page || 0;
  let totalCount = 0;

  let useLastId = true; // 默认使用原来的方式
  let usePage = false;
  if (params.page !== undefined) {
    useLastId = false;
    usePage = true;
  }

  if (typeof pageSize === "string") pageSize = parseInt(pageSize, 10);
  if (typeof page === "string") page = parseInt(page, 10);

  return bluebird
    .resolve()
    .then(function () {
      serverId = serverId || "all";
      return HotMomentCache.getMoments(serverId, curRoleId);
    })
    .then(function (ms) {
      totalCount = ms.length;
      const moments = [];
      _.forEach(ms, function (m, index) {
        if (useLastId) {
          m.hot_index = index + 1;
          if (m.hot_index > lastHotIndex && m.hot_index < lastHotIndex + limit + 1) {
            moments.push(m);
          }
        } else if (usePage) {
          // use page
          m.hot_index = index + 1;
          const lower = (page - 1) * pageSize;
          const upper = page * pageSize;
          if (index >= lower && index < upper) {
            moments.push(m);
          }
        } else {
          // error
        }
      });
      return moments;
    })
    .then(function (moments) {
      const mIds = _.map(moments, "ID");
      return PyqMoments.findByIds(mIds, BASIC_MOMENT_COLS).then(function (colsNotInCache) {
        const idToCols = util.keyToRecordHash(colsNotInCache, "ID");
        return _.map(moments, function (m) {
          return _.assign(m, idToCols[m.ID]);
        });
      });
    })
    .then(util.currying(fillInfo, params))
    .then(function (list) {
      return fillIdentity(list);
    })
    .then(function (list) {
      let result = { list: list, count: totalCount };
      result = formatResult(result);
      return result;
    });
}

type UpdateHotType = "like" | "comment" | "reply" | "forward" | "time";

interface UpdateHotOption {
  keepDuration?: boolean;
  now?: number;
  hotFactor?: number;
}

function updateHot(item: MomentRecord, type: UpdateHotType, step, option: UpdateHotOption) {
  const info: any = calHot(item, type, step, option);
  const hot = info.hot;
  const state = info.state;

  const updateVal = { HotState: "", Hot: 0 };
  updateVal.HotState = JSON.stringify(state);
  updateVal.Hot = hot;

  return updateVal;
}

export function calHot(item: MomentRecord, type: UpdateHotType, step: number, option: UpdateHotOption): any {
  option = _.defaults(option, { keepDuration: false });
  const hotFactor = option.hotFactor !== undefined ? option.hotFactor : 1;
  const hotState = item.HotState;
  let state = { publish_duration: undefined };
  if (hotState) {
    try {
      state = JSON.parse(hotState);
    } catch (err) {
      logger.warn("ParseHotStateError", { err: err, hotState: hotState, item, type, step });
    }
  }
  if (type && step) {
    const val = (state[type] || 0) + step;
    state[type] = val > 0 ? val : 0;

    const hotKey = `${type}Hot`;
    const hotVal: number = (state[hotKey] || 0) + step * hotFactor;
    state[hotKey] = hotVal > 0 ? Number(hotVal.toFixed(2)) : 0;
  }

  if (!(option.keepDuration && state.publish_duration)) {
    const now = option.now || Date.now();
    state.publish_duration = Math.round((now - item.CreateTime) / 1000); // 发表持续时间，单位秒
  }

  const hot = PyqMoments.getHotFromState(state);
  return type ? { hot: hot, state: state } : hot;
}

export function refreshCommentsCache(commentIds) {
  const query = PyqComment.scope().distinct("TargetId").whereIn("ID", commentIds);
  return PyqComment.executeByQuery(query).then(function (rows) {
    const momentIds = _.map(rows, "TargetId");
    return bluebird.each(momentIds, function (momentId) {
      return PyqComment.refreshMomentCommentsCache(momentId);
    });
  });
}

export const fillMomentsInfo = fillInfo;

interface IAddMomentInternal extends IAddMoment {
  preApprovedImgs?: string[];
}

async function addMomentInternal(params: IAddMomentInternal): Promise<MomentRes.AddMoment> {
  const roleId = params.roleid,
    text = util.trim(params.text);

  const imgUrls = util.trim(params.imgs as string);
  const preApprovedImgs = params.preApprovedImgs || [];
  let imgList: string[] = [];
  let newMomentId;

  const hotFactor = MomentModel.checkHotFactorParams(params.hotFactor);

  if (!text && !imgUrls) {
    await bluebird.reject({ code: -1, msg: "动态内容不能为空" });
  }
  if (imgUrls) {
    try {
      imgList = util.csvStrToArray(imgUrls);
    } catch (ex) {
      await bluebird.reject({ code: -1, msg: "发布图片失败" });
    }

    for (let i = 0, l = imgList.length; i < l; i++) {
      imgList[i] = util.trim(imgList[i]);
      if (!isValidResourceUrl(imgList[i])) {
        await bluebird.reject({ code: -1, msg: "非法的图片地址" });
      }
    }
  }
  const addImg = imgList && imgList.length;

  if (imgList.length >= 0) {
    const imageLimit = await getAddMomentImageLimit(roleId);
    if (imgList.length > imageLimit) {
      await bluebird.reject(MdErrors.AddMomentLimitExceed);
    }
  }

  let videoUrlList: string[] = [];
  let videoCoverList: string[] = [];

  if (params.videos) {
    //@ts-ignore
    videoUrlList = util.csvStrToArray(params.videos);
    //@ts-ignore
    videoCoverList = util.csvStrToArray(params.videoImgs);
  }

  const videoAudit = util.getRepeatCsv(videoUrlList.length, "0");
  const videoCoverAudit = util.getRepeatCsv(videoCoverList.length, "0");
  const hotState = await getInitialHotStateString(text, hotFactor);

  // Generate ImgAudit based on pre-approved images
  const imgAudit = preApprovedImgs.length > 0 
    ? imgList.map(img => {
        return preApprovedImgs.includes(img) ? EAuditStatus.PASS.toString() : dataUtil.Constants.STATUS_AUDIT_INIT.toString();
      }).join(',')
    : PyqMoments.getInitialImgAudit(imgList);

  const values: Partial<MomentRecord> = {
    RoleId: roleId,
    Text: text,
    ImgList: addImg ? imgList.join(",") : undefined,
    ImgAudit: imgAudit,
    VideoList: videoUrlList.join(","),
    VideoAudit: videoAudit,
    VideoCoverList: videoCoverList.join(","),
    VideoCoverAudit: videoCoverAudit,
    HotState: hotState,
    CreateTime: Date.now(),
    VisualRange: params.visualRange ? params.visualRange : EMomentVisualRange.all,
    CanCommentByAll: params.canCommentByAll !== undefined ? params.canCommentByAll : 1,
    CanForward: params.canForward !== undefined ? params.canForward : 1,
    CanCommentByStranger: params.canCommentByStranger !== undefined ? params.canCommentByStranger : 1,
  };
  const tableName = TABLE_NAMES.moment;
  const subject = dataUtil.md.getTopic(text);
  const values2 = util.extend({}, values);
  return db
    .add({
      table: tableName,
      values: values2,
    })
    .then(function (result) {
      newMomentId = result.insertId;
    })
    .then(async function () {
      const mid = newMomentId;
      addMomentLog({
        roleId: roleId,
        momentId: mid,
        text: text,
        imgList: imgList,
        videoList: videoUrlList,
        videoCoverList: videoCoverList,
        topic: subject,
      });

      // 清除count缓存
      refreshPlayerMomentsCount(roleId);

      addToTopic(text, mid); // [2016.9.6] 生成话题
      processTopicMoment(roleId, newMomentId, imgList, videoUrlList, params.topicId);

      const getFormattedImgList = function () {
        if (addImg) {
          const moment = _.clone(values);
          pyqData.setImgListView(moment, roleId);
          return moment.ImgList;
        } else {
          return [];
        }
      };

      // Only send images for audit that are not pre-approved
      const imagesToAudit = preApprovedImgs.length > 0 
        ? imgList.filter(img => !preApprovedImgs.includes(img))
        : imgList;
      
      if (imagesToAudit.length > 0) {
        const picId = genPicIdFromInfo({ id: newMomentId, type: "moment_image" });
        sendPic(imagesToAudit, { roleId: roleId, picId: picId, media: EPicMediaType.Image, ip: params.ip });
      }

      if (videoUrlList.length > 0) {
        const picId = genPicIdFromInfo({ id: newMomentId, type: "moment_video" });
        sendPic(videoUrlList, { roleId: roleId, picId: picId, media: EPicMediaType.Video, ip: params.ip });
      }

      if (videoCoverList.length > 0) {
        const picId = genPicIdFromInfo({ id: newMomentId, type: "moment_video_cover" });
        sendPic(videoCoverList, { roleId: roleId, picId: picId, media: EPicMediaType.Image, ip: params.ip });
      }

      refreshPlayerMomentsCount(roleId);

      processAtPlayerWhenAddMoment(roleId, mid, text);

      const context = params.context || "";

      const addRet: MomentRes.AddMoment = {
        id: mid,
        //@ts-ignore
        imglist: getFormattedImgList(),
        context: context,
      };
      const polemicId = getGuildPolemicIdFromLink(params.text);
      if (polemicId) {
        const share = await sharePolemic({ roleid: params.roleid, id: polemicId });
        addRet.guildPolemic = { id: polemicId, shareCount: share.shareCount };
      }

      return addRet;
    });
}

export function auditMomentImageList(roleId: number, imgList: string[], mId: number, ip: string) {
  return sendPic(imgList, {
    // 提交图片审核
    roleId: roleId,
    picId: genPicIdFromInfo({ type: "moment_image", id: "" + mId }),
    media: EPicMediaType.Image,
    ip: ip,
  });
}

export function auditMomentVideoList(roleId: number, videoList: string[], mId: number, ip: string) {
  return sendPic(videoList, {
    // 提交视频审核
    roleId: roleId,
    picId: genPicIdFromInfo({ type: "moment_video", id: "" + mId }),
    media: EPicMediaType.Image,
    ip: ip,
  });
}

function getCopyMoment(moment: MomentRecord, newRoleId: number) {
  const m: Omit<MomentRecord, "ID"> = {
    RoleId: newRoleId,
    Text: moment.Text,
    ImgList: moment.ImgList,
    ImgAudit: moment.ImgAudit,
    VideoList: moment.VideoList,
    VideoAudit: moment.VideoAudit,
    VideoCoverList: moment.VideoCoverList,
    VideoCoverAudit: moment.VideoCoverAudit,
    ZanList: "",
    HotState: "",
    CreateTime: moment.CreateTime,
    Status: moment.Status,
    VisualRange: moment.VisualRange,
    CanCommentByAll: moment.CanCommentByAll,
    CanForward: moment.CanForward,
    CanCommentByStranger: moment.CanForward,
  };
  return m;
}

export async function getLastFinishCopyId(params: PlayerReq.CopyMoments) {
  const query = CopyMomentModel.scope()
    .where({ toRoleId: params.roleid })
    .where({ FromRoleId: params.copyRoleId })
    .orderBy("FromMomentId", "desc")
    .limit(1);
  const rows: CopyMomentRecord[] = await CopyMomentModel.executeByQuery(query);
  if (rows && rows.length > 0) {
    return rows[0].FromMomentId;
  } else {
    return 0;
  }
}

async function copyMoments(lastId: number, fromRoleId: number, toRoleId: number) {
  let fromMoments: MomentRecord[] = await scanCopyMoment(fromRoleId, lastId);
  while (fromMoments.length > 0) {
    for (const m of fromMoments) {
      const newInsertProps = getCopyMoment(m, toRoleId);
      const newMomentId = await MomentModel.insert(newInsertProps);
      await bluebird.delay(copyMomentCfg.delayTime);
      await CopyMomentModel.insert({
        ToRoleId: toRoleId,
        FromRoleId: fromRoleId,
        FromMomentId: m.ID,
        CreateTime: Date.now(),
        ToMomentId: newMomentId,
      });

      const forwardRecord = await MomentForwardModel.findOne({ MomentId: m.ID });
      if (forwardRecord && forwardRecord.ID) {
        await MomentForwardModel.insert({
          RoleId: toRoleId,
          MomentId: newMomentId,
          ForwardId: forwardRecord.ForwardId,
          OriginId: forwardRecord.OriginId,
        });
      }

      lastId = m.ID;
    }
    fromMoments = await scanCopyMoment(fromRoleId, lastId);
  }
  // 复制后清除目标的动态缓存计数
  await refreshPlayerMomentsCount(toRoleId);
}

async function scanCopyMoment(fromRoleId: number, lastId: number) {
  const copyQuery = MomentModel.scope()
    .where({ RoleId: fromRoleId, Status: Statues.Normal })
    .where("ID", ">", lastId)
    .limit(copyMomentCfg.batchSize);
  const fromMoments: MomentRecord[] = await MomentModel.executeByQuery(copyQuery);
  return fromMoments;
}

export async function copyPlayerMoments(params: PlayerReq.CopyMoments): Promise<PlayerRes.CopyMoments> {
  const fromRoleId = params.copyRoleId;
  const toRoleId = params.roleid;
  const lastId = await getLastFinishCopyId(params);
  if (fromRoleId === toRoleId) {
    await Bluebird.reject(PyqErrors.CopyMomentSameRoleId);
  }

  const copyQuery = MomentModel.scope().where({ RoleId: fromRoleId, Status: Statues.Normal }).where("ID", ">", lastId);
  const fromMoments: MomentRecord[] = await MomentModel.executeByQuery(copyQuery);

  asyncRun(copyMoments(lastId, fromRoleId, toRoleId));

  return { count: fromMoments.length };
}

export function publicNormalMomentScope(): knex.QueryBuilder {
  const query = MomentModel.normalScope().where("VisualRange", EMomentVisualRange.all);
  return query;
}
