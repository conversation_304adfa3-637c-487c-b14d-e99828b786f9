export const enum EAstrologyUserGender {
    MALE = 0,
    FEMALE = 1,
}

// basic:基本运势、wealth:财运、career:事业与学业、love:情感

export const enum EAstrologyUserFortuneType {
    BASIC = "basic",
    WEALTH = "wealth",
    CAREER = "career",
    LOVE = "love",
}


export const enum HoroscopeFortuneType {
    BASIC = "基本运势",
    WEALTH = "财运",
    CAREER = "事业与学业",
    LOVE = "情感"
}

export function convertToEAstrologyUserFortuneTypeStr(fortuneType: EAstrologyUserFortuneType): HoroscopeFortuneType {
    switch (fortuneType) {
        case EAstrologyUserFortuneType.BASIC:
            return HoroscopeFortuneType.BASIC;
        case EAstrologyUserFortuneType.WEALTH:
            return HoroscopeFortuneType.WEALTH;
        case EAstrologyUserFortuneType.CAREER:
            return HoroscopeFortuneType.CAREER;
        case EAstrologyUserFortuneType.LOVE:
            return HoroscopeFortuneType.LOVE;
        default:
            throw new Error(`Invalid fortune type: ${fortuneType}`);
    }
}