import { AstrologyReq, AstrologyRes } from "./type";
import { Context } from "../../context";
import { BazaarPostService } from "../../services/astrology/bazaarPostService";
import { BazaarPostRecord } from "../../models/astrology/bazaarPostModel";
import { AstrologyConstellation, AstrologyHouse, AstrologyPlanet, toAstrologyBazaarDiceTuple } from "../../services/astrology/astrologyConstant";
import { HoroscopeService, HoroScopeTimeInterval } from "../../services/external/horoscopeService";
import { BazaarCommentRecord } from "../../models/astrology/bazaarCommentModel";
import { BazaarCommentService } from "../../services/astrology/bazaarCommentService";
import { Statues } from "../../common/constants";
import { AstrologyCommentRankService } from "../../services/astrology/astrologyCommentRankService";
import { AstrologyWeeklyCommentRankService } from "../../services/astrology/astrologyWeeklyCommentRankService";
import { BazaarRatingService } from "../../services/astrology/bazaarRatingService";
import { BazaarRatingRecord } from "../../models/astrology/bazaarRatingModel";
import { BazaarTopicService } from "../../services/astrology/bazaarTopicService";
import { astrologyCfg } from "../../common/config";
import { formatDate, getDayStrV2, validateBirthTimeFormat } from "../../common/dateUtil";
import { clazzLogger } from "../../logger";
import { AstrologyUserService } from "../../services/astrology/astrologyUserService";
import { errorCodesV2 } from "../../errors/errorCode";
import { convertToEAstrologyUserFortuneTypeStr, EAstrologyUserFortuneType } from "../../constants/astrologyConstant";
import { AstrologyExternalService } from "../../services/astrology/astrologyExternalService";
import { checkValidText, csvStrToIntArray, isEmptyStr } from "../../common/util";
import envsdkService from "../../services/external/envsdkService";
import * as _ from "lodash";
import * as moment from "moment";
import { totalmem } from "os";
const logger = clazzLogger("AstrologyOperation")

async function reviewTextSensitive(text: string): Promise<boolean> {
  const { validate } = await envsdkService.reviewWords(text);
  if (!validate) {
    throw errorCodesV2.ContentSensitive;
  }
  return validate;
}

export async function bazaarPostAdd(
  ctx: Context,
  params: AstrologyReq.BazaarPostAdd
): Promise<AstrologyRes.BazaarPostAdd> {
  const now = Date.now();
  checkValidText(params.question, astrologyCfg.questionMaxLen)
  const checkText = [params.question, params.topicText].join(" ");
  await reviewTextSensitive(checkText);

  const bazaarPost: Omit<BazaarPostRecord, "ID"> = {
    RoleId: params.roleid,
    TopicId: params.topicId,
    Question: params.question,
    DicePlanet: params.dice.planet as AstrologyPlanet,
    DiceConstellation: params.dice.constellation as AstrologyConstellation,
    DiceHouse: params.dice.house as AstrologyHouse,
    CommentCount: 0,
    CreateTime: now,
    UpdateTime: now,
    Status: Statues.Normal,
  }
  const id = await BazaarPostService.getInstance().addBazaarPost(ctx, bazaarPost);
  const bazaarTopicService = BazaarTopicService.getInstance();
  await bazaarTopicService.saveTopic(ctx, params.topicId, params.topicText);
  await bazaarTopicService.incrDailyTopicHot(ctx, params.topicId, now);
  if (params.isSyncMoment) {
    AstrologyExternalService.getInstance().addMoment(ctx, params)
  }
  const data: AstrologyRes.BazaarPostAdd = {
    id,
  };
  return data;
}

async function convertBazaarPostListToShowList(
  ctx: Context,
  roleId: number,
  bazaarPostList: BazaarPostRecord[]
): Promise<AstrologyRes.BazaarPostList['list']> {
  const postRoleIds = bazaarPostList.map(item => item.RoleId);
  const roleInfoMap = await AstrologyUserService.getInstance().getShowRoleInfoMap(ctx, postRoleIds);
  const userInfoMap = await AstrologyUserService.getInstance().getUserInfoMap(ctx, postRoleIds);
  const bazaarCommentService = BazaarCommentService.getInstance();
  const postIds = bazaarPostList.map(item => item.ID);
  const commentedPostIds = await bazaarCommentService.filterCommentedPostIds(ctx, roleId, postIds);
  return BazaarPostService.getInstance().convertToBazaarPostShowItemList(bazaarPostList, roleInfoMap, userInfoMap, commentedPostIds);
}

export async function bazaarPostList(
  ctx: Context,
  params: AstrologyReq.BazaarPostList
): Promise<AstrologyRes.BazaarPostList> {
  const bazaarPostService = BazaarPostService.getInstance();
  const topicIds = params.topicIds ? csvStrToIntArray(params.topicIds) : [];
  const bazaarPostList = await bazaarPostService.listBazaarPublicPost(ctx, params.roleid, topicIds, {
    page: params.page,
    pageSize: params.pageSize,
  });
  const list = await convertBazaarPostListToShowList(ctx, params.roleid, bazaarPostList);
  const total = await bazaarPostService.countBazaarPublicPost(ctx, params.roleid, topicIds);
  const resp: AstrologyRes.BazaarPostList = {
    list,
    total,
  };
  return resp;
}

export async function bazaarPostSelfList(
  ctx: Context,
  params: AstrologyReq.BazaarPostSelfList
): Promise<AstrologyRes.BazaarPostSelfList> {
  const bazaarPostService = BazaarPostService.getInstance();
  const topicIds = params.topicIds ? csvStrToIntArray(params.topicIds) : [];
  const bazaarPostList = await bazaarPostService.listBazaarUserSelfPost(ctx, params.roleid, topicIds, {
    page: params.page,
    pageSize: params.pageSize,
  });
  const list = await convertBazaarPostListToShowList(ctx, params.roleid, bazaarPostList);
  const total = await bazaarPostService.countBazaarUserSelfPost(ctx, params.roleid, topicIds);
  const resp: AstrologyRes.BazaarPostSelfList = {
    list,
    total,
  };
  return resp;
}

/**
 * 发起解读，把对应问题和占卜结果发送AI, 并返回解读结果
 * [伏羲提供的上游接口文档](https://docs.popo.netease.com/team/pc/npl5djx6/pageDetail/624e76a7c81c455ebbce4c14a9ebe794)
 */
export async function bazaarPostInterpret(
  ctx: Context,
  params: AstrologyReq.BazaarPostInterpret
): Promise<AstrologyRes.BazaarPostInterpret> {
  const bazaarPost = await BazaarPostService.getInstance().getBazaarPost(ctx, params.postId);
  const diceResult = toAstrologyBazaarDiceTuple({
    planet: bazaarPost.DicePlanet,
    constellation: bazaarPost.DiceConstellation,
    house: bazaarPost.DiceHouse,
  });
  const apiRes = await HoroscopeService.getInstance().getDiceResult(ctx, bazaarPost.Question, diceResult);
  const resp: AstrologyRes.BazaarPostInterpret = {
    title: apiRes.title || "",
    content: apiRes.content || [],
  };

  return resp;
}

export async function bazaarCommentAdd(
  ctx: Context,
  params: AstrologyReq.BazaarCommentAdd
): Promise<AstrologyRes.BazaarCommentAdd> {
  checkValidText(params.text, astrologyCfg.commentMaxLen)
  await reviewTextSensitive(params.text);
  const bazaarPostService = BazaarPostService.getInstance();
  const bazaarCommentService = BazaarCommentService.getInstance();
  const bazaarPost = await bazaarPostService.verifyBazaarPost(ctx, params.postId);
  const now = Date.now();
  const bazaarComment: Omit<BazaarCommentRecord, "ID"> = {
    PostId: params.postId,
    PostRoleId: bazaarPost.RoleId,
    RoleId: params.roleid,
    Text: params.text,
    CreateTime: now,
    UpdateTime: now,
    Status: Statues.Normal,
  }
  await bazaarCommentService.checkUserCommented(ctx, params.roleid, params.postId)
  const id = await bazaarCommentService.addBazaarComment(ctx, bazaarComment);
  await bazaarPostService.incrCommentCount(ctx, bazaarPost.ID);
  await bazaarPostService.incrPostDailyHot(ctx, bazaarPost.ID, now);

  // 增加用户解惑次数
  try {
    const rankService = AstrologyCommentRankService.getInstance();
    await rankService.incrementUserCommentCount(ctx, params.roleid);
  } catch (err) {
    // 解惑次数更新失败不影响主流程，只记录日志
    logger.error({ ctx, err, roleId: params.roleid }, "incrementUserCommentCountError");
  }

  const data: AstrologyRes.BazaarCommentAdd = {
    id,
  };
  return data;
}

export async function bazaarPostCommentList(
  ctx: Context,
  params: AstrologyReq.BazaarPostCommentList
): Promise<AstrologyRes.BazaarPostCommentList> {
  const bazaarPostService = BazaarPostService.getInstance();
  const bazaarCommentService = BazaarCommentService.getInstance();
  const bazaarPost = await bazaarPostService.verifyBazaarPost(ctx, params.postId);
  const bazaarCommentList = await bazaarCommentService.listBazaarCommentList(ctx, bazaarPost.ID, {
    page: params.page,
    pageSize: params.pageSize,
  });
  const commentRoleIds = bazaarCommentList.map(item => item.RoleId);
  const showRoleInfoMap = await AstrologyUserService.getInstance().getShowRoleInfoMap(ctx, commentRoleIds);
  const userInfoMap = await AstrologyUserService.getInstance().getUserInfoMap(ctx, commentRoleIds);
  const commentIds = bazaarCommentList.map(item => item.ID);
  const ratingCoreMap = await BazaarRatingService.getInstance().getRatingCoreMap(ctx, commentIds);
  const total = await bazaarCommentService.countBazaarCommentByPostId(ctx, bazaarPost.ID);
  const list = bazaarCommentService.convertToBazaarCommentShowItemList(bazaarCommentList, showRoleInfoMap, userInfoMap, ratingCoreMap);
  const resp: AstrologyRes.BazaarPostCommentList = {
    list,
    total,
  };
  return resp;
}

export async function bazaarRatingAdd(
  ctx: Context,
  params: AstrologyReq.BazaarRatingAdd
): Promise<AstrologyRes.BazaarRatingAdd> {
  if (params.text) {
    checkValidText(params.text, astrologyCfg.ratingTextMaxLen)
    await reviewTextSensitive(params.text);
  }
  const bazaarCommentService = BazaarCommentService.getInstance();
  const bazaarComment = await bazaarCommentService.verifyBazaarComment(ctx, params.commentId);
  if (bazaarComment.PostRoleId !== params.roleid) {
    throw errorCodesV2.BazaarRatingNotSelf;
  }
  const ratingService = BazaarRatingService.getInstance();
  const now = Date.now();

  const rating: Omit<BazaarRatingRecord, "ID"> = {
    CommentId: params.commentId,
    ToRoleId: bazaarComment.RoleId,
    PostId: bazaarComment.PostId,
    FromRoleId: params.roleid,
    Star: params.star,
    Text: params.text,
    CreateTime: now,
    UpdateTime: now,
    Status: Statues.Normal,
  }

  await ratingService.checkUserRated(ctx, params.roleid, params.commentId);
  const id = await ratingService.addBazaarRating(ctx, rating);
  AstrologyUserService.getInstance().updateRatingStat(ctx, bazaarComment.RoleId, params.star, now)

  const data: AstrologyRes.BazaarRatingAdd = {
    id,
    createTime: now,
  };

  return data;
}


export async function ratingAdd(
  ctx: Context,
  params: AstrologyReq.RatingAdd
): Promise<AstrologyRes.RatingAdd> {
  if (params.text) {
    checkValidText(params.text, astrologyCfg.ratingTextMaxLen)
    await reviewTextSensitive(params.text);
  }
  const ratingService = BazaarRatingService.getInstance();
  const now = Date.now();

  const rating: Omit<BazaarRatingRecord, "ID"> = {
    CommentId: 0,
    ToRoleId: params.toRoleId,
    PostId: 0,
    FromRoleId: params.fromRoleId,
    Star: params.star,
    Text: params.text,
    CreateTime: now,
    UpdateTime: now,
    Status: Statues.Normal,
  }

  const id = await ratingService.addBazaarRating(ctx, rating);
  AstrologyUserService.getInstance().updateRatingStat(ctx, params.toRoleId, params.star, now)

  const data: AstrologyRes.RatingAdd = {
    id,
    createTime: now,
  };

  return data;
}


/**
 * 今日热门话题
 * @param ctx
 * @param params
 * @returns
 */
export async function bazaarHotTopic(
  ctx: Context,
  params: AstrologyReq.BazaarHotTopic
): Promise<AstrologyRes.BazaarHotTopic> {
  const bazaarTopicService = BazaarTopicService.getInstance();
  const currentTime = Date.now();
  const ds = getDayStrForAstrology(new Date(currentTime));
  const list = await bazaarTopicService.getDailyTopicHotList(ctx, ds, astrologyCfg.hotTopicSize);
  const topicIds = list.map(item => item.TopicId);
  const topicIdToTextMap = await bazaarTopicService.getTopicIdToTextMap(ctx, topicIds);
  const showList = bazaarTopicService.convertToTopicShowList(list, topicIdToTextMap);
  const data: AstrologyRes.BazaarHotTopic = {
    list: showList,
    lastRefreshTime: currentTime,
  };
  return data;
}


/**
 *
 * @param timestamp
 * 每日时间范围是上一日的晚上8点到今天的晚上8点，所以计算yyyyMMdd的时候要特殊处理
 * 举个例子，如果timestamp是2025-06-16 20:00:00，那么返回的yyyyMMdd是20250617
 * 如果是2025-06-15 21:00:00，那么返回的yyyyMMdd是20250616
 */
export function getDayStrForAstrology(date: Date): string {
  const hour = date.getHours();
  // 如果时间在晚上8点(20:00)之后，返回下一天的日期
  if (hour >= 20) {
    date.setDate(date.getDate() + 1);
  }
  return getDayStrV2(date);
}

export async function bazaarGroupDivinationReport(
  ctx: Context,
  params: AstrologyReq.BazaarGroupDivinationReport
): Promise<AstrologyRes.BazaarGroupDivinationReport> {
  const bazaarTopicService = BazaarTopicService.getInstance();
  const currentTime = params.timestamp > 0 ? new Date(params.timestamp) : new Date();
  const yesterday = moment(currentTime).subtract(1, 'days').toDate();
  const ds = getDayStrForAstrology(yesterday);
  const generatedAt = formatDate(currentTime, "yyyy-MM-dd HH:mm:ss");
  const mostFocusedTopic = await bazaarTopicService.getMostFocusedTopicId(ctx, ds);
  const averageFortuneScore = await AstrologyUserService.getInstance().getForecastFortuneDailyAvgScore(ctx, ds);
  const mostCommentedQuestion = await BazaarPostService.getInstance().getMostCommentedPostQuestion(ctx, ds);
  const data: AstrologyRes.BazaarGroupDivinationReport = {
    reportDate: ds,
    mostFocusedTopic,
    averageFortuneScore,
    mostCommentedQuestion,
    generatedAt: generatedAt,
  };

  return data;
}

export async function bazaarUserProfile(
  ctx: Context,
  params: AstrologyReq.BazaarUserProfile
): Promise<AstrologyRes.BazaarUserProfile> {
  const resp = await AstrologyUserService.getInstance().getUserShowProfile(ctx, params.roleid)
  return resp;
}


export async function bazaarUserProfileForServer(
  ctx: Context,
  params: AstrologyReq.BazaarUserProfile
): Promise<AstrologyRes.BazaarUserProfileForServer> {
  const resp = await AstrologyUserService.getInstance().getUserShowProfileForServer(ctx, params.roleid)
  return resp;
}

export async function bazaarUserProfileUpdate(
  ctx: Context,
  params: AstrologyReq.BazaarUserProfileUpdate
): Promise<AstrologyRes.BazaarUserProfileUpdate> {
  if (params.birthTime && !validateBirthTimeFormat(params.birthTime)) {
    throw errorCodesV2.BirthTimeFormatInvalid;
  }
  await AstrologyUserService.getInstance().updateProfile(ctx, params.roleid, params)
  const resp: AstrologyRes.BazaarUserProfileUpdate = null
  return resp;
}


export async function bazaarUserRegister(
  ctx: Context,
  params: AstrologyReq.BazaarUserRegister
): Promise<AstrologyRes.BazaarUserRegister> {
  const astrologyUserService = AstrologyUserService.getInstance()

  if (!validateBirthTimeFormat(params.birthTime)) {
    throw errorCodesV2.BirthTimeFormatInvalid;
  }

  await astrologyUserService.verifyUserNotRegister(ctx, params.roleid)
  const resp = await astrologyUserService.registerUser(ctx, params.roleid, params)
  return resp;
}


export async function bazaarUserReceiveRatingList(
  ctx: Context,
  params: AstrologyReq.BazaarUserRatingReceiveList
): Promise<AstrologyRes.BazaarUserRatingReceiveList> {
  const bazaarRatingService = BazaarRatingService.getInstance();
  const list = await bazaarRatingService.listReceiveBazaarRatingList(ctx, params.roleid, {
    page: params.page,
    pageSize: params.pageSize,
  });
  const total = await bazaarRatingService.countPlayerReceiveRating(ctx, params.roleid);

  const ratingRoleIds = list.map(item => item.FromRoleId);
  const roleInfoMap = await AstrologyUserService.getInstance().getShowRoleInfoMap(ctx, ratingRoleIds);
  const userInfoMap = await AstrologyUserService.getInstance().getUserInfoMap(ctx, ratingRoleIds);
  const showList = bazaarRatingService.convertToBazaarRatingShowItemList(list, roleInfoMap, userInfoMap);
  const resp: AstrologyRes.BazaarUserRatingReceiveList = {
    list: showList,
    total,
  };
  return resp;
}

export async function HoroscopePlanetaryAspects(
  ctx: Context,
  params: AstrologyReq.HoroscopePlanetaryAspects
): Promise<AstrologyRes.HoroscopePlanetaryAspects> {
  const apiRes = await HoroscopeService.getInstance().getPlanetaryAspects(ctx, params.timestamp);

  const resp: AstrologyRes.HoroscopePlanetaryAspects = {
    title: apiRes.title,
    content: apiRes.content,
  };

  return resp;
}

export async function HoroscopeDailyForecast(
  ctx: Context,
  params: AstrologyReq.HoroscopeDailyForecast
): Promise<AstrologyRes.HoroscopeDailyForecast> {
  const ds = getDayStrForAstrology(new Date(params.timestamp))
  const astrologyUserService = AstrologyUserService.getInstance()
  const profile = await astrologyUserService.getProfile(ctx, params.roleid)
  if (!profile) {
    logger.warn({ ctx, roleId: params.roleid }, "HoroscopeDailyForecastUserNotFound")
    throw errorCodesV2.AstrologyUserNotFound
  }
  const fortuneType = convertToEAstrologyUserFortuneTypeStr(params.fortune as EAstrologyUserFortuneType)
  const timeInterval = params.timeInterval == "today" ? HoroScopeTimeInterval.TODAY : HoroScopeTimeInterval.THIS_WEEK;
  const userInfo = astrologyUserService.convertToUserHoroscopeInfo(profile)
  const apiRes = await HoroscopeService.getInstance().getUserHoroscope(ctx, userInfo, params.timestamp, timeInterval, fortuneType);

  const fortuneScore = apiRes.score

  const data: AstrologyRes.HoroscopeDailyForecast = {
    fortune: apiRes.fortune,
    time: apiRes.time,
    user_constellation: apiRes.user_constellation,
    in_water_reversal: apiRes.in_water_reversal,
    day_for_water_reversal_remain: apiRes.day_for_water_reversal_remain,
    day_for_next_water_reversal: apiRes.day_for_next_water_reversal,
    title: apiRes.title,
    content: apiRes.content,
    score: fortuneScore,
    lucky_color: apiRes.lucky_color,
    lucky_color_code: apiRes.lucky_color_code,
  };

  if (timeInterval === HoroScopeTimeInterval.TODAY) {
    AstrologyUserService.getInstance().updateUserDailyForecast(ctx, params.roleid, ds, fortuneScore)
  }

  return data;
}


export async function diceResultInterpret(
  ctx: Context,
  params: AstrologyReq.DiceResultInterpret
): Promise<AstrologyRes.DiceResultInterpret> {
  const diceResult = toAstrologyBazaarDiceTuple({
    planet: params.dice.planet as AstrologyPlanet,
    constellation: params.dice.constellation as AstrologyConstellation,
    house: params.dice.house as AstrologyHouse,
  });
  const apiRes = await HoroscopeService.getInstance().getDiceResult(ctx, params.question, diceResult);
  const resp: AstrologyRes.DiceResultInterpret = {
    title: apiRes.title,
    content: apiRes.content,
  };
  return resp
}

/**
 * 获取解惑次数排行榜
 */
export async function commentRank(
  ctx: Context,
  params: AstrologyReq.CommentRank
): Promise<AstrologyRes.CommentRank> {
  const rankService = AstrologyCommentRankService.getInstance();
  const rankData = await rankService.getCommentRank(ctx);

  return {
    list: rankData.list,
    updateTime: rankData.updateTime
  };
}

/**
 * 获取解惑次数周排行榜
 */
export async function commentRankWeekly(
  ctx: Context,
  params: AstrologyReq.CommentRankWeekly
): Promise<AstrologyRes.CommentRankWeekly> {
  const weeklyRankService = AstrologyWeeklyCommentRankService.getInstance();
  const rankData = await weeklyRankService.getWeeklyCommentRank(ctx);

  return {
    list: rankData.list,
    updateTime: rankData.updateTime,
    weekStart: rankData.weekStart,
    weekEnd: rankData.weekEnd
  };
}