import { operations } from "../../types/swagger";

export namespace AstrologyReq {
  export type BazaarPostAdd = operations["bazaarPostAdd"]["parameters"]["query"] &
    operations["bazaarPostAdd"]["requestBody"]["content"]["application/json"];
  export type BazaarPostList = operations["bazaarPostList"]["parameters"]["query"];
  export type BazaarPostSelfList = operations["bazaarPostSelfList"]["parameters"]["query"];
  export type BazaarPostInterpret = operations["bazaarPostInterpret"]["parameters"]["query"];
  export type BazaarCommentAdd = operations["bazaarCommentAdd"]["parameters"]["query"] &
    operations["bazaarCommentAdd"]["requestBody"]["content"]["application/json"];
  export type BazaarPostCommentList = operations["bazaarPostCommentList"]["parameters"]["query"];
  export type BazaarRatingAdd = operations["bazaarRatingAdd"]["parameters"]["query"] &
    operations["bazaarRatingAdd"]["requestBody"]["content"]["application/json"];
  export type RatingAdd = operations["ratingAdd"]["requestBody"]["content"]["application/json"];
  export type BazaarHotTopic = operations["bazaarHotTopic"]["parameters"]["query"];
  export type BazaarGroupDivinationReport = operations["bazaarGroupDivinationReport"]["parameters"]["query"];
  export type BazaarUserProfile = operations["bazaarUserProfile"]["parameters"]["query"];
  export type BazaarUserRegister = operations["bazaarUserRegister"]["parameters"]["query"] &
    operations["bazaarUserRegister"]["requestBody"]["content"]["application/json"];
  export type BazaarUserProfileUpdate = operations["bazaarUserProfile"]["parameters"]["query"] &
    operations["bazaarUserProfileUpdate"]["requestBody"]["content"]["application/json"];
  export type BazaarUserRatingReceiveList = operations["bazaarUserRatingReceiveList"]["parameters"]["query"];
  export type HoroscopePlanetaryAspects = operations["horoscopePlanetaryAspects"]["parameters"]["query"];
  export type HoroscopeDailyForecast = operations["horoscopeDailyForecast"]["parameters"]["query"]
  export type DiceResultInterpret = operations["diceResultInterpret"]["parameters"]["query"] &
    operations["diceResultInterpret"]["requestBody"]["content"]["application/json"];
  export type CommentRank = {
    roleid: number;
  };
  export type CommentRankWeekly = {
    roleid: number;
  };
}

export namespace AstrologyRes {
  export type BazaarPostAdd = operations["bazaarPostAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarPostList = operations["bazaarPostList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarPostSelfList =
    operations["bazaarPostSelfList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarPostInterpret =
    operations["bazaarPostInterpret"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarCommentAdd =
    operations["bazaarCommentAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarPostCommentList =
    operations["bazaarPostCommentList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarRatingAdd =
    operations["bazaarRatingAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type RatingAdd = operations["ratingAdd"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarHotTopic = operations["bazaarHotTopic"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarGroupDivinationReport =
    operations["bazaarGroupDivinationReport"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarUserRegister =
    operations["bazaarUserRegister"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarUserProfile =
    operations["bazaarUserProfile"]["responses"]["200"]["content"]["application/json"]["data"];

  export type BazaarUserProfileForServer =
    operations["bazaarUserProfileForServer"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarUserProfileUpdate = operations["bazaarUserProfileUpdate"]["responses"]["200"]["content"]["application/json"]["data"];
  export type BazaarUserRatingReceiveList =
    operations["bazaarUserRatingReceiveList"]["responses"]["200"]["content"]["application/json"]["data"];
  export type HoroscopePlanetaryAspects =
    operations["horoscopePlanetaryAspects"]["responses"]["200"]["content"]["application/json"]["data"];
  export type HoroscopeDailyForecast =
    operations["horoscopeDailyForecast"]["responses"]["200"]["content"]["application/json"]["data"];
  export type DiceResultInterpret =
    operations["diceResultInterpret"]["responses"]["200"]["content"]["application/json"]["data"];
  export type CommentRank = operations['commentRank']['responses']['200']['content']['application/json']['data'];
  export type CommentRankWeekly = operations['commentRankWeekly']['responses']['200']['content']['application/json']['data'];
}

export const diceSchema = {
  type: "object",
  properties: {
    planet: {
      type: "string",
      enum: [
        "sun",
        "moon",
        "mercury",
        "venus",
        "mars",
        "jupiter",
        "saturn",
        "uranus",
        "neptune",
        "pluto",
        "southNode",
        "northNode",
      ],
    },
    constellation: {
      type: "string",
      enum: [
        "aries",
        "taurus",
        "gemini",
        "cancer",
        "leo",
        "virgo",
        "libra",
        "scorpio",
        "sagittarius",
        "capricorn",
        "aquarius",
        "pisces",
      ],
    },
    house: { type: "integer", minimum: 1, maximum: 12 }
  }
}

export const ReqSchemas = {
  BazaarPostAdd: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      topicId: { type: "integer" },
      question: { type: "string" },
      dice: diceSchema,
      isSyncMoment: { type: "boolean" },
    },
    required: ["roleid", "topicId", "question", "dice", "isSyncMoment"],
  },
  BazaarPostList: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      page: { type: "number", minimum: 1, default: 1 },
      pageSize: { type: "number", maximum: 20, default: 10 },
      topicIds: { type: "string" },
    },
    required: ["roleid"],
  },
  BazaarPostSelfList: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      page: { type: "number", minimum: 1, default: 1 },
      pageSize: { type: "number", maximum: 20, default: 10 },
      topicIds: { type: "string" },
    },
    required: ["roleid"],
  },
  BazaarPostInterpret: {
    type: "object",
    properties: { roleid: { type: "number" }, postId: { type: "number" } },
    required: ["roleid", "postId"],
  },
  BazaarCommentAdd: {
    type: "object",
    properties: { roleid: { type: "number" }, postId: { type: "integer" }, text: { type: "string" } },
    required: ["roleid", "postId", "text"],
  },
  BazaarPostCommentList: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      postId: { type: "number" },
      page: { type: "number", minimum: 1, default: 1 },
      pageSize: { type: "number", maximum: 20, default: 10 },
    },
    required: ["roleid", "postId"],
  },
  BazaarRatingAdd: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      star: { type: "integer", minimum: 1, maximum: 5 },
      text: { type: "string" },
    },
    required: ["roleid"],
  },
  RatingAdd: {
    type: "object",
    properties: {
      star: { type: "integer", minimum: 1, maximum: 5 },
      text: { type: "string" },
    },
  },
  BazaarHotTopic: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
  BazaarGroupDivinationReport: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      timestamp: { type: "integer" }
    }, required: ["roleid", "timestamp"]
  },
  BazaarUserRegister: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      gender: { type: "number", enum: [0, 1] },
      birthTime: { type: "string" },
      birthPlace: {
        type: "object",
        required: ["province", "city", "district"],
        properties: {
          province: { type: "string" },
          city: { type: "string" },
          district: { type: "string" },
        },
      },
      currentPlace: {
        type: "object",
        required: ["province", "city", "district"],
        properties: {
          province: { type: "string" },
          city: { type: "string" },
          district: { type: "string" },
        },
      },
      astroLevel: { type: "integer" },
      astroType: { type: "integer" },
    },
    required: ["roleid", "gender", "birthTime", "birthPlace", "currentPlace", "astroLevel", "astroType"],
  },
  BazaarUserProfile: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
  BazaarUserProfileForServer: { type: "object", properties: { roleid: { type: "number" } }, required: ["roleid"] },
  BazaarUserProfileUpdate: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      gender: { type: "number", enum: [0, 1] },
      birthTime: { type: "string" },
      birthPlace: {
        type: "object",
        required: ["province", "city", "district"],
        properties: {
          province: { type: "string" },
          city: { type: "string" },
          district: { type: "string" },
        },
      },
      currentPlace: {
        type: "object",
        required: ["province", "city", "district"],
        properties: {
          province: { type: "string" },
          city: { type: "string" },
          district: { type: "string" },
        },
      },
    },
  },
  BazaarUserRatingList: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      page: { type: "number", minimum: 1, default: 1 },
      pageSize: { type: "number", maximum: 20, default: 10, minimum: 1 },
    },
    required: ["roleid", "page", "pageSize"],
  },
  HoroscopePlanetaryAspects: {
    type: "object",
    properties: { roleid: { type: "number" }, timestamp: { type: "number" } },
    required: ["roleid", "timestamp"],
  },
  HoroscopeDailyForecast: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      timestamp: { type: "integer" },
      timeInterval: { type: "string", enum: ["today", "week"] },
      fortune: { type: "string", enum: ["basic", "wealth", "career", "love"] },
    },
    required: ["roleid", "timestamp", "timeInterval", "fortune"],
  },
  DiceResultInterpret: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      question: { type: "string" },
      dice: diceSchema,
    },
    required: ["roleid", "question", "dice"],
  }
};
