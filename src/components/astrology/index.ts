import { gameIpLimit } from "../../auth/gameIpLimit";
import { Component } from "../../helper";
import { Path } from "../../types/type";
import {
  bazaarPostAdd,
  bazaarPostList,
  bazaarPostSelfList,
  bazaarPostInterpret,
  bazaarCommentAdd,
  bazaarPostCommentList,
  bazaarHotTopic,
  bazaarGroupDivinationReport,
  bazaarUserProfile,
  bazaarUserReceiveRatingList,
  HoroscopePlanetaryAspects,
  HoroscopeDailyForecast,
  bazaarRatingAdd,
  bazaarUserProfileUpdate,
  bazaarUserRegister,
  diceResultInterpret,
  ratingAdd,
  commentRank,
  commentRankWeekly,
  bazaarUserProfileForServer,
} from "./operation";
import { ReqSchemas } from "./type";

export const paths: Path[] = [
  {
    method: "post",
    url: "/astrology/bazaar/post/add",
    paramsSchema: ReqSchemas.BazaarPostAdd,
    operation: bazaarPostAdd,
  },
  {
    method: "get",
    url: "/astrology/bazaar/post/list",
    paramsSchema: ReqSchemas.BazaarPostList,
    operation: bazaarPostList,
  },
  {
    method: "get",
    url: "/astrology/bazaar/post/self_list",
    paramsSchema: ReqSchemas.BazaarPostSelfList,
    operation: bazaarPostSelfList,
  },
  {
    method: "get",
    url: "/astrology/bazaar/post/interpret",
    paramsSchema: ReqSchemas.BazaarPostInterpret,
    operation: bazaarPostInterpret,
  },
  {
    method: "post",
    url: "/astrology/bazaar/comment/add",
    paramsSchema: ReqSchemas.BazaarCommentAdd,
    operation: bazaarCommentAdd,
  },
  {
    method: "get",
    url: "/astrology/bazaar/comment/list",
    paramsSchema: ReqSchemas.BazaarPostCommentList,
    operation: bazaarPostCommentList,
  },
  {
    method: "get",
    url: "/astrology/bazaar/hot_topic",
    paramsSchema: ReqSchemas.BazaarHotTopic,
    operation: bazaarHotTopic,
  },
  {
    method: "get",
    url: "/astrology/bazaar/group_divination_report",
    paramsSchema: ReqSchemas.BazaarGroupDivinationReport,
    operation: bazaarGroupDivinationReport,
  },
  {
    method: "post",
    url: "/astrology/bazaar/user/register",
    paramsSchema: ReqSchemas.BazaarUserRegister,
    before: gameIpLimit,
    operation: bazaarUserRegister,
    option: {
      skipSkey: true,
    },
  },
  {
    method: "get",
    url: "/astrology/bazaar/user/profile",
    paramsSchema: ReqSchemas.BazaarUserProfile,
    operation: bazaarUserProfile,
  },
  {
    method: "get",
    url: "/astrology/server/bazaar/user/profile",
    paramsSchema: ReqSchemas.BazaarUserProfileForServer,
    before: gameIpLimit,
    operation: bazaarUserProfileForServer,
    option: {
      skipSkey: true,
    },
  },
  {
    method: "post",
    url: "/astrology/bazaar/user/profile/update",
    paramsSchema: ReqSchemas.BazaarUserProfileUpdate,
    before: gameIpLimit,
    operation: bazaarUserProfileUpdate,
    option: {
      skipSkey: true,
    },
  },
  {
    method: "post",
    url: "/astrology/bazaar/rating/add",
    paramsSchema: ReqSchemas.BazaarRatingAdd,
    operation: bazaarRatingAdd,
  },
  {
    method: "post",
    url: "/astrology/rating/add",
    before: gameIpLimit,
    paramsSchema: ReqSchemas.RatingAdd,
    operation: ratingAdd,
    option: {
      skipSkey: true,
    },
  },
  {
    method: "get",
    url: "/astrology/bazaar/user/rating/receive_list",
    paramsSchema: ReqSchemas.BazaarUserRatingList,
    operation: bazaarUserReceiveRatingList,
  },
  {
    method: "get",
    url: "/astrology/horoscope/planetary_aspects",
    paramsSchema: ReqSchemas.HoroscopePlanetaryAspects,
    operation: HoroscopePlanetaryAspects,
  },
  {
    method: "get",
    url: "/astrology/horoscope/daily_forecast",
    paramsSchema: ReqSchemas.HoroscopeDailyForecast,
    operation: HoroscopeDailyForecast,
  },
  {
    method: "post",
    url: "/astrology/dice_result/interpret",
    paramsSchema: ReqSchemas.DiceResultInterpret,
    operation: diceResultInterpret,
  },
  {
    method: "get",
    url: "/astrology/comment/rank",
    before: gameIpLimit,
    operation: commentRank,
    option: {
      skipSkey: true,
    },
  },
  {
    method: "get",
    url: "/astrology/comment/rank_weekly",
    before: gameIpLimit,
    operation: commentRankWeekly,
    option: {
      skipSkey: true,
    },
  },
];

export const AstrologyComponent: Component = {
  paths: paths,
  prefix: "/astrology/",
  version: "v2"
};
