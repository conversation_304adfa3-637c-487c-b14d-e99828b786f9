# 脚本使用说明

## 元宝充值数据修复脚本

### 功能说明

`fixYuanbaoCharge.ts` 脚本用于修复由于服务故障导致丢失的元宝充值数据。脚本会读取指定的日志文件，解析其中的元宝充值记录，并调用相应的接口将数据同步到数据库中。

### 使用方法

脚本可以直接使用 ts-node 执行：

```bash
# 直接执行脚本
./scripts/fixYuanbaoCharge.ts [选项] <日志文件路径>

# 或者使用 ts-node 执行
ts-node scripts/fixYuanbaoCharge.ts [选项] <日志文件路径>
```

#### 可用选项

- `-h, --help`: 显示帮助信息
- `-d, --dry-run`: 仅检查日志文件，不实际修复数据
- `-v, --verbose`: 显示详细日志
- `-t, --delay <ms>`: 每处理一行数据后的停留时间（毫秒），默认为50ms

#### 示例

```bash
# 修复指定文件中的数据
./scripts/fixYuanbaoCharge.ts tmp/yuanbaoChargesample.txt

# 仅检查文件，不实际修复数据
./scripts/fixYuanbaoCharge.ts --dry-run tmp/yuanbaoChargesample.txt

# 显示详细日志
./scripts/fixYuanbaoCharge.ts --verbose tmp/yuanbaoChargesample.txt

# 设置处理延迟为100毫秒
./scripts/fixYuanbaoCharge.ts --delay 100 tmp/yuanbaoChargesample.txt

# 同时使用多个选项
./scripts/fixYuanbaoCharge.ts --dry-run --verbose --delay 200 tmp/yuanbaoChargesample.txt

# 显示帮助信息
./scripts/fixYuanbaoCharge.ts --help
```

### 日志文件格式

日志文件应包含元宝充值记录，格式如下：

```
[时间戳] gas_服务器ID[进程ID]: [事件ID]INFO|PLAYER|[404135]玩家ID,,玩家URS,,元宝充值数量,,订单号,,是否云游戏
```

例如：

```
[2025-02-28 00:00:14] gas_1201[805652]: [2072507]INFO|PLAYER|[404135]33464901201,,<EMAIL>,,540,,33464901201z2,,false
```

### 功能特点

1. **自动跳过已处理订单**：脚本会检查订单ID是否已存在于数据库中，如果存在则自动跳过，避免重复处理
2. **实时进度显示**：脚本执行过程中会显示处理进度条和统计信息，方便监控处理状态
3. **详细日志记录**：所有处理过程都会记录到日志中，便于后续排查问题
4. **仅检查模式**：可以通过 `--dry-run` 选项仅检查日志文件，不实际修复数据，用于预览将要执行的操作
5. **详细输出模式**：可以通过 `--verbose` 选项显示详细的处理日志，便于调试和问题排查
6. **彩色输出**：脚本使用彩色输出，使信息更加清晰易读
7. **处理速率控制**：可以通过 `--delay` 选项设置每处理一行数据后的停留时间，控制处理速度，避免对数据库造成过大压力

### 输出说明

脚本执行过程中会显示实时进度条，包含以下信息：
- 处理进度百分比
- 已处理记录总数
- 成功修复记录数
- 修复失败记录数
- 跳过记录数（已存在的订单）

脚本执行完成后，会输出最终统计信息，包括：

- 总计处理：处理的记录总数
- 成功修复：成功修复的记录数
- 修复失败：修复失败的记录数
- 跳过记录：跳过的记录数（已存在的订单）
- 处理延迟：每行处理后的停留时间（如果设置了）

### 注意事项

1. 确保在执行脚本前，已经正确配置了数据库连接信息
2. 脚本会跳过空行和以 `#` 开头的注释行
3. 脚本会自动检查订单ID是否已存在于数据库中，如果存在则跳过处理
4. 脚本执行过程中的详细日志会记录到系统日志中
5. 对于大量数据的处理，建议分批进行，并使用 `--delay` 选项控制处理速度，避免对数据库造成过大压力
6. 建议先使用 `--dry-run` 选项检查日志文件，确认无误后再实际执行修复操作