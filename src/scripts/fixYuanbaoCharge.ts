#!/usr/bin/env node

import * as fs from 'fs';
import * as readline from 'readline';
import { parseGameRawLog } from '../components/cloudGameDuration/logConsumer/util';
import { parseAddYuanbaoLog } from '../components/cloudGameDuration/logConsumer/addYuanbaoLog';
import { cloudGameDurationNotifyChargeYuanbao } from '../components/cloudGameDuration/operation';
import { clazzLogger } from '../logger';
import { CloudGameYuanbaoChangeLogModel } from '../models';

const logger = clazzLogger('scripts.fixYuanbaoCharge');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

interface FixStats {
  total: number;
  success: number;
  failed: number;
  skipped: number;
}

interface ScriptOptions {
  dryRun: boolean;
  verbose: boolean;
  filePath: string;
  help: boolean;
  delay: number; // 每行处理后的延迟时间（毫秒）
}

// 显示帮助信息
function showHelp(): void {
  console.log(`${colors.blue}元宝充值数据修复脚本${colors.reset}`);
  console.log('');
  console.log('用法: ts-node fixYuanbaoCharge.ts [选项] <日志文件路径>');
  console.log('');
  console.log('选项:');
  console.log('  -h, --help          显示帮助信息');
  console.log('  -d, --dry-run       仅检查日志文件，不实际修复数据');
  console.log('  -v, --verbose       显示详细日志');
  console.log('  -t, --delay <ms>    每处理一行数据后的停留时间（毫秒），默认为50ms');
  console.log('');
  console.log('示例:');
  console.log('  ts-node fixYuanbaoCharge.ts tmp/yuanbaoChargesample.txt        # 修复指定文件中的数据');
  console.log('  ts-node fixYuanbaoCharge.ts --dry-run tmp/yuanbaoChargesample.txt  # 仅检查文件，不实际修复');
  console.log('  ts-node fixYuanbaoCharge.ts --delay 100 tmp/yuanbaoChargesample.txt  # 设置处理延迟为100ms');
  console.log('');
}

// 进度显示函数
function showProgress(current: number, total: number, stats: FixStats): void {
  const percent = Math.floor((current / total) * 100);
  const progressBar = '='.repeat(Math.floor(percent / 2)) + '>' + ' '.repeat(50 - Math.floor(percent / 2));

  process.stdout.write(`\r处理进度: [${progressBar}] ${percent}% | 总计: ${stats.total} | 成功: ${stats.success} | 失败: ${stats.failed} | 跳过: ${stats.skipped}`);
}

// 延迟函数
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 解析命令行参数
function parseCommandLineArgs(): ScriptOptions {
  const options: ScriptOptions = {
    dryRun: false,
    verbose: false,
    filePath: '',
    help: false,
    delay: 50 // 默认延迟50毫秒
  };

  const args = process.argv.slice(2);
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (arg === '--dry-run' || arg === '-d') {
      options.dryRun = true;
    } else if (arg === '--verbose' || arg === '-v') {
      options.verbose = true;
    } else if (arg === '--help' || arg === '-h') {
      options.help = true;
    } else if (arg === '--delay' || arg === '-t') {
      if (i + 1 < args.length) {
        const delayValue = parseInt(args[i + 1], 10);
        if (!isNaN(delayValue) && delayValue >= 0) {
          options.delay = delayValue;
          i++; // 跳过下一个参数，因为它是延迟值
        } else {
          console.error(`${colors.red}错误: 延迟时间必须是非负整数${colors.reset}`);
          process.exit(1);
        }
      } else {
        console.error(`${colors.red}错误: --delay 选项需要一个值${colors.reset}`);
        process.exit(1);
      }
    } else if (!arg.startsWith('-')) {
      options.filePath = arg;
    }
  }

  return options;
}

async function processLogFile(filePath: string, options: ScriptOptions): Promise<FixStats> {
  const stats: FixStats = {
    total: 0,
    success: 0,
    failed: 0,
    skipped: 0
  };

  if (!fs.existsSync(filePath)) {
    console.error(`${colors.red}错误: 文件 '${filePath}' 不存在${colors.reset}`);
    logger.error({ filePath }, '文件不存在');
    return stats;
  }

  // 首先计算总行数，用于显示进度
  const totalLines = (await fs.promises.readFile(filePath, 'utf8')).split('\n').filter(line => line.trim() !== '' && !line.startsWith('#')).length;
  console.log(`总计需要处理 ${totalLines} 条记录`);

  if (options.dryRun) {
    console.log(`${colors.yellow}注意: 仅检查模式，不会实际修复数据${colors.reset}`);
  }

  if (options.delay > 0) {
    console.log(`${colors.blue}处理延迟: ${options.delay}ms${colors.reset}`);
  }

  const fileStream = fs.createReadStream(filePath);
  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity
  });

  let processedLines = 0;

  for await (const line of rl) {
    // 跳过空行和注释行
    if (!line || line.trim() === '' || line.startsWith('#')) {
      continue;
    }

    processedLines++;
    stats.total++;

    try {
      // 解析日志行
      const gs = parseGameRawLog(line);
      if (!gs || gs.eventId === 0) {
        logger.warn({ line }, '解析日志失败');
        if (options.verbose) {
          console.log(`\n${colors.red}解析日志失败: ${line}${colors.reset}`);
        }
        stats.failed++;
        showProgress(processedLines, totalLines, stats);

        // 添加延迟
        if (options.delay > 0) {
          await sleep(options.delay);
        }

        continue;
      }

      // 解析元宝充值日志
      const parseRet = parseAddYuanbaoLog(gs);
      if (!parseRet.isOk) {
        logger.warn({ line, parseRet }, '解析元宝充值日志失败');
        if (options.verbose) {
          console.log(`\n${colors.red}解析元宝充值日志失败: ${line}${colors.reset}`);
        }
        stats.failed++;
        showProgress(processedLines, totalLines, stats);

        // 添加延迟
        if (options.delay > 0) {
          await sleep(options.delay);
        }

        continue;
      }

      const pd = parseRet.data;

      // 检查订单是否已经处理过
      const existingOrder = await CloudGameYuanbaoChangeLogModel.findOne({ urs: pd.accountName, orderId: pd.sn });
      if (existingOrder && existingOrder.id) {
        logger.info({ orderId: pd.sn }, '订单已存在，跳过处理');
        if (options.verbose) {
          console.log(`\n${colors.yellow}订单已存在，跳过处理: ${pd.sn}${colors.reset}`);
        }
        stats.skipped++;
        showProgress(processedLines, totalLines, stats);

        // 添加延迟
        if (options.delay > 0) {
          await sleep(options.delay);
        }

        continue;
      }

      const logInfo = {
        urs: pd.accountName,
        orderId: pd.sn,
        num: pd.yuanbao,
        userType: pd.bCloud ? 'cloud' : 'normal'
      };

      logger.info(logInfo, '准备修复元宝充值数据');
      if (options.verbose) {
        console.log(`\n${colors.blue}准备处理: ${JSON.stringify(logInfo)}${colors.reset}`);
      }

      // 如果是仅检查模式，不实际调用接口
      if (options.dryRun) {
        if (options.verbose) {
          console.log(`\n${colors.yellow}[仅检查] 将处理订单: ${pd.sn}${colors.reset}`);
        }
        stats.success++;
        showProgress(processedLines, totalLines, stats);

        // 添加延迟
        if (options.delay > 0) {
          await sleep(options.delay);
        }

        continue;
      }

      // 调用充值接口
      const data = await cloudGameDurationNotifyChargeYuanbao({
        urs: pd.accountName,
        orderId: pd.sn,
        num: pd.yuanbao,
        userType: pd.bCloud ? 'cloud' : 'normal',
      });

      if (data) {
        logger.info({ data }, '修复元宝充值数据成功');
        if (options.verbose) {
          console.log(`\n${colors.green}修复成功: ${pd.sn}${colors.reset}`);
        }
        stats.success++;
      } else {
        logger.warn({ pd }, '修复元宝充值数据失败');
        if (options.verbose) {
          console.log(`\n${colors.red}修复失败: ${pd.sn}${colors.reset}`);
        }
        stats.failed++;
      }
    } catch (err) {
      if (err && err.code === 'YUANBAO_ORDER_CONSUMED') {
        logger.info({ line, err }, '订单已处理过，跳过');
        if (options.verbose) {
          console.log(`\n${colors.yellow}订单已处理过，跳过: ${line}${colors.reset}`);
        }
        stats.skipped++;
      } else {
        logger.error({ line, err }, '处理日志行异常');
        if (options.verbose) {
          console.log(`\n${colors.red}处理异常: ${line}, 错误: ${err.message || err}${colors.reset}`);
        }
        stats.failed++;
      }
    }

    // 显示进度
    showProgress(processedLines, totalLines, stats);

    // 添加延迟
    if (options.delay > 0) {
      await sleep(options.delay);
    }
  }

  // 完成后换行
  console.log('\n');

  return stats;
}

async function main() {
  try {
    // 解析命令行参数
    const options = parseCommandLineArgs();

    // 显示帮助信息并退出
    if (options.help) {
      showHelp();
      return;
    }

    // 检查是否提供了文件路径
    if (!options.filePath) {
      console.error(`${colors.red}错误: 未提供日志文件路径${colors.reset}`);
      showHelp();
      process.exit(1);
    }

    const filePath = options.filePath;
    logger.info({ filePath, options }, '开始修复元宝充值数据');
    console.log(`${colors.green}开始处理文件: ${colors.blue}${filePath}${colors.reset}`);

    const stats = await processLogFile(filePath, options);
    logger.info({
      stats,
      filePath,
      options
    }, '元宝充值数据修复完成');

    console.log(`
${colors.green}修复统计:${colors.reset}
  总计处理: ${stats.total}
  成功修复: ${stats.success}
  修复失败: ${stats.failed}
  跳过记录: ${stats.skipped}
  ${options.dryRun ? `${colors.yellow}(仅检查模式，未实际修复数据)${colors.reset}` : ''}
  ${options.delay > 0 ? `${colors.blue}处理延迟: ${options.delay}ms${colors.reset}` : ''}
    `);

    console.log(`${colors.green}数据修复完成!${colors.reset}`);
  } catch (err) {
    logger.error({ err }, '修复元宝充值数据异常');
    console.error(`${colors.red}数据修复失败: ${err.message || err}${colors.reset}`);
    process.exit(1);
  }
}

// 执行主函数
main().catch(err => {
  logger.error({ err }, '脚本执行异常');
  console.error(`${colors.red}脚本执行异常: ${err.message || err}${colors.reset}`);
  process.exit(1);
});