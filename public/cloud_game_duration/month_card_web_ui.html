<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云游戏月卡查询管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Moment.js and Daterangepicker -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <script src="https://mg-file.nosdn.127.net/watermark/wm.js"></script>

    <style type="text/tailwindcss">
        @layer base {
            :root {
                --background: 0 0% 100%;
                --foreground: 222.2 84% 4.9%;
                --card: 0 0% 100%;
                --card-foreground: 222.2 84% 4.9%;
                --popover: 0 0% 100%;
                --popover-foreground: 222.2 84% 4.9%;
                --primary: 221.2 83.2% 53.3%;
                --primary-foreground: 210 40% 98%;
                --secondary: 210 40% 96.1%;
                --secondary-foreground: 222.2 47.4% 11.2%;
                --muted: 210 40% 96.1%;
                --muted-foreground: 215.4 16.3% 46.9%;
                --accent: 210 40% 96.1%;
                --accent-foreground: 222.2 47.4% 11.2%;
                --destructive: 0 84.2% 60.2%;
                --destructive-foreground: 210 40% 98%;
                --border: 214.3 31.8% 91.4%;
                --input: 214.3 31.8% 91.4%;
                --ring: 221.2 83.2% 53.3%;
                --radius: 0.5rem;
            }
        }

        .select2-container {
            width: 100% !important;
        }

        .select2-container .select2-selection--single {
            height: 40px;
            border-radius: 0.5rem;
            border-color: hsl(var(--input));
            background-color: hsl(var(--background));
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            padding-left: 12px;
            color: hsl(var(--foreground));
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 38px;
        }

        .select2-dropdown {
            border-color: hsl(var(--input));
            background-color: hsl(var(--background));
        }

        .select2-container--default .select2-search--dropdown .select2-search__field {
            border-color: hsl(var(--input));
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .tab-active {
            border-bottom: 2px solid hsl(var(--primary));
            color: hsl(var(--primary));
        }
        /* Style for daterangepicker input */
        #chargeDateRangePicker {
            /* Replicate shadcn input style */
            height: 40px;
            border-radius: 0.375rem; /* rounded-md */
            border: 1px solid hsl(var(--input));
            background-color: hsl(var(--background));
            padding-left: 0.75rem; /* px-3 */
            padding-right: 0.75rem; /* px-3 */
            font-size: 0.875rem; /* text-sm */
            color: hsl(var(--foreground));
        }
        #chargeDateRangePicker:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            border-color: hsl(var(--ring)); /* focus:ring-ring */
        }
    </style>
</head>
<body class="min-h-screen bg-background font-sans antialiased">
    <div class="container mx-auto py-10 space-y-8">
        <div class="flex justify-between items-start">
            <div class="space-y-2">
                <h1 class="text-3xl font-bold tracking-tight">云游戏月卡查询管理</h1>
                <p class="text-muted-foreground">查询玩家云游戏月卡状态和充值记录</p>
            </div>
            <div id="user-info" class="text-right space-y-1"></div>
        </div>

        <div class="rounded-lg border bg-card shadow-sm">
            <!-- 标签页导航 -->
            <div class="flex border-b">
                <button id="statusTab" class="px-4 py-2 tab-active">月卡状态</button>
                <button id="chargeTab" class="px-4 py-2">充值记录</button>
            </div>

            <!-- 月卡状态查询面板 -->
            <div id="statusPanel" class="p-8 space-y-6">
                <div class="flex gap-4">
                    <div class="flex-1">
                        <label for="ursInput" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            URS账号
                        </label>
                        <input
                            type="text"
                            id="ursInput"
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            placeholder="请输入URS账号"
                        >
                    </div>
                    <div class="w-32">
                        <label for="serverIdSelect" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            服务器ID
                        </label>
                        <select
                            id="serverIdSelect"
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            data-placeholder="选择服务器"
                        >
                            <option></option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button
                            id="searchBtn"
                            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 border border-border"
                        >
                            🔍 查询
                        </button>
                    </div>
                </div>

                <div id="loading" class="hidden">
                    <div class="flex items-center justify-center p-4">
                        <div class="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        <span class="ml-2 text-sm text-muted-foreground">查询中，请稍候...</span>
                    </div>
                </div>

                <div id="errorMessage" class="hidden rounded-md bg-destructive/15 p-4 text-sm text-destructive"></div>

                <div id="resultContainer" class="hidden space-y-4">
                    <h2 class="text-lg font-semibold">查询结果</h2>
                    <div class="rounded-md border">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b bg-muted/50">
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">URS账号</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">服务器ID</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">月卡状态</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">过期时间</th>
                                </tr>
                            </thead>
                            <tbody id="resultTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 充值记录查询面板 -->
            <div id="chargePanel" class="p-8 space-y-6 hidden">
                <div class="space-y-4"> <!-- Filter group -->
                    <div class="flex flex-col md:flex-row md:items-end gap-4"> <!-- URS, Date Range, and Button Row -->
                        <div class="flex-1 min-w-0"> <!-- URS Account Input -->
                            <label for="chargeUrsInput" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                URS账号
                            </label>
                            <input
                                type="text"
                                id="chargeUrsInput"
                                class="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="请输入URS账号"
                            >
                        </div>

                        <div class="flex-1 min-w-0"> <!-- Order ID Input -->
                            <label for="chargeOrderInput" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                订单号
                            </label>
                            <input
                                type="text"
                                id="chargeOrderInput"
                                class="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="请输入订单号"
                            >
                        </div>

                        <div class="flex-1 min-w-0 md:ml-4"> <!-- Date Range Picker -->
                            <label for="chargeDateRangePicker" class="text-sm font-medium leading-none">时间范围</label>
                            <input
                                type="text"
                                id="chargeDateRangePicker"
                                class="mt-1 flex w-full ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="选择时间范围"
                            />
                        </div>

                        <button
                            id="chargeSearchBtn"
                            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full md:w-auto border border-border"
                        >
                            🔍 查询
                        </button>
                    </div>
                </div>

                <div id="chargeLoading" class="hidden">
                    <div class="flex items-center justify-center p-4">
                        <div class="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        <span class="ml-2 text-sm text-muted-foreground">查询中，请稍候...</span>
                    </div>
                </div>

                <div id="chargeErrorMessage" class="hidden rounded-md bg-destructive/15 p-4 text-sm text-destructive"></div>

                <div id="chargeResultContainer" class="hidden space-y-4">
                    <h2 class="text-lg font-semibold">充值记录</h2>
                    <div class="rounded-md border">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b bg-muted/50">
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">URS账号</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">服务器ID</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">订单号</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">渠道来源</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">购买时间</th>
                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">新增时长</th>
                                </tr>
                            </thead>
                            <tbody id="chargeResultTable"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div id="permission-denied-view" class="hidden text-center p-8 rounded-lg border bg-card shadow-sm">
            <h2 class="text-2xl font-bold text-destructive mb-4">权限不足</h2>
            <p class="text-muted-foreground mb-6">抱歉，您没有足够的权限访问此页面。请联系管理员为您授权。</p>
            <a href="javascript:location.reload()" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                刷新页面
            </a>
        </div>
    </div>

    <script>
        const PERMISSION_DENIED_ERROR = 'Forbidden: Access denied.';
        // 添加工具函数
        const Utils = {
            async fetchWithErrorHandling(url, options) {
                const response = await fetch(url, options);
                if (!response.ok) {
                    if (response.status === 401) {
                        // 获取当前完整URL（包括hash）
                        const currentUrl = window.location.href;
                        // 构建重定向URL
                        const redirectUrl = `https://ccc-webpub.leihuo.netease.com/file_mg/public/share/common_auth/corpauth/login?redirect=${encodeURIComponent(currentUrl)}`;
                        // 重定向到登录页面
                        window.location.href = redirectUrl;
                        return; // Important to return to stop further execution
                    } else if (response.status === 403) {
                        // 权限不足
                        throw new Error(PERMISSION_DENIED_ERROR);
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const result = await response.json();
                // We need to check for code 0 for all requests now.
                if (result.code !== 0) {
                    throw new Error(result.msg || 'API request failed');
                }
                return result;
            }
        };

        function showPermissionDeniedView(message) {
            const mainContent = document.querySelector('.rounded-lg.border.bg-card');
            const permissionView = document.getElementById('permission-denied-view');
            const userInfoElement = document.getElementById('user-info');

            if (userInfoElement) userInfoElement.classList.add('hidden');
            if (mainContent) mainContent.classList.add('hidden');
            if (permissionView) {
                permissionView.classList.remove('hidden');
                const messageElement = permissionView.querySelector('p');
                if (messageElement && message) {
                    messageElement.textContent = message;
                }
            }
        }

        async function handleLogout(event) {
            event.preventDefault();
            if (!confirm('确定要退出登录吗？')) {
                return;
            }

            try {
                await Utils.fetchWithErrorHandling('/nsh/md/gm_web/api/cloud_game_duration/logout', {
                    method: 'POST'
                });
                // 登出成功后重定向到登录页面
                const currentUrl = window.location.href;
                const redirectUrl = `https://ccc-webpub.leihuo.netease.com/file_mg/public/share/common_auth/corpauth/login?redirect=${encodeURIComponent(currentUrl)}`;
                window.location.href = redirectUrl;
            } catch (error) {
                console.error('Logout failed:', error);
                alert('退出登录失败，请重试');
            }
        }

        async function fetchAndDisplayLoginInfo() {
            try {
                const result = await Utils.fetchWithErrorHandling('/nsh/md/gm_web/api/cloud_game_duration/login_info');
                const userInfo = result.data;
                const userInfoElement = document.getElementById('user-info');
                document.body.classList.remove('is-admin');

                if (userInfoElement && userInfo) {
                    let adminBadgeHtml = '';
                    if (userInfo.userType === 1) { // 假设1是管理员
                        adminBadgeHtml = `<span class="inline-flex items-center rounded-md bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">管理员</span>`;
                        document.body.classList.add('is-admin');
                    }
                    userInfoElement.innerHTML = `
                        <div class="text-sm font-medium">${userInfo.fullName} (${userInfo.mail}) ${adminBadgeHtml}</div>
                        <a href="#" onclick="handleLogout(event)" class="inline-block mt-2 rounded-md border border-input bg-background px-3 py-1.5 text-sm font-medium text-secondary-foreground hover:bg-accent">退出登录</a>
                    `;
                }
                if (userInfo && userInfo.mail) {
                    WaterMark.mark({ text: userInfo.mail });
                }

                return userInfo; // Return user info for further use if needed
            } catch (error) {
                if (error.message === PERMISSION_DENIED_ERROR) {
                    showPermissionDeniedView('抱歉，您没有足够的权限访问此页面。请联系管理员为您授权。');
                } else {
                    console.error('Failed to fetch login info:', error);
                    const container = document.querySelector('.container.mx-auto');
                    if (container) {
                        container.innerHTML = `<div class="p-8 space-y-6"><div class="rounded-md bg-destructive/15 p-4 text-sm text-destructive">${error.message}</div></div>`;
                    }
                }
                throw error; // Re-throw to stop execution in the caller
            }
        }

        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await fetchAndDisplayLoginInfo();
            } catch(e) {
                // Error is already handled inside fetchAndDisplayLoginInfo
                console.error("Page initialization failed:", e);
                return; // Stop execution
            }

            const searchBtn = document.getElementById('searchBtn');
            const ursInput = document.getElementById('ursInput');
            const serverIdSelect = $('#serverIdSelect');
            const resultContainer = document.getElementById('resultContainer');
            const resultTable = document.getElementById('resultTable');
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');

            // 月卡状态枚举
            const MonthCardStatus = {
                NOT_OPEN: 0,    // 未开通
                OPEN_VALID: 1,  // 有效
                OPEN_EXPIRED: 2 // 已过期
            };

            // 月卡状态对应的显示文本和样式
            const MonthCardStatusConfig = {
                [MonthCardStatus.NOT_OPEN]: {
                    text: '未开通',
                    class: 'bg-secondary text-secondary-foreground'
                },
                [MonthCardStatus.OPEN_VALID]: {
                    text: '已开通（有效）',
                    class: 'bg-green-100 text-green-700'
                },
                [MonthCardStatus.OPEN_EXPIRED]: {
                    text: '已开通（已过期）',
                    class: 'bg-orange-100 text-orange-700'
                }
            };

            // 根据过期时间计算月卡状态
            function calculateMonthCardStatus(expireAt) {
                if (!expireAt) return MonthCardStatus.NOT_OPEN;
                const now = Math.floor(Date.now() / 1000); // 转换为秒级时间戳
                return expireAt > now ? MonthCardStatus.OPEN_VALID : MonthCardStatus.OPEN_EXPIRED;
            }

            // 格式化时间戳为可读日期时间
            function formatTimestamp(timestamp) {
                if (!timestamp) return '无';
                // 检查时间戳是否为毫秒级
                const isMilliseconds = timestamp > 9999999999;
                const date = new Date(isMilliseconds ? timestamp : timestamp * 1000);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
            }

            // 初始化Select2
            serverIdSelect.select2({
                placeholder: '选择服务器',
                allowClear: true,
                minimumInputLength: 0
            });

            // 获取服务器列表
            Utils.fetchWithErrorHandling('/nsh/md/gm_web/api/cloud_game_duration/month_card/serverIds')
                .then(data => {
                    // 添加服务器选项
                    const options = data.data.map(serverId =>
                        new Option(serverId, serverId, false, false)
                    );
                    serverIdSelect.append(options).trigger('change');
                })
                .catch(error => {
                    if (error.message === PERMISSION_DENIED_ERROR) {
                        showPermissionDeniedView('抱歉，您的权限不足，无法获取服务器列表。');
                    } else {
                        showError(`获取服务器列表失败: ${error.message}`);
                    }
                });

            // 查询按钮点击事件
            searchBtn.addEventListener('click', function() {
                const urs = ursInput.value.trim();
                const serverId = serverIdSelect.val();

                if (!urs) {
                    showError('请输入URS账号');
                    return;
                }

                hideError();
                loading.classList.remove('hidden');
                resultContainer.classList.add('hidden');

                // 构建URL，只有在选择了服务器ID时才添加serverId参数
                let url = `/nsh/md/gm_web/api/cloud_game_duration/month_card/show?urs=${encodeURIComponent(urs)}`;
                if (serverId) {
                    url += `&serverId=${serverId}`;
                }

                fetchMonthCardInfo(url);
            });

            // 显示错误信息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.classList.remove('hidden');
            }

            // 隐藏错误信息
            function hideError() {
                errorMessage.classList.add('hidden');
            }

            // 查询月卡信息
            function fetchMonthCardInfo(url) {
                Utils.fetchWithErrorHandling(url)
                    .then(response => {
                        loading.classList.add('hidden');

                        if (response.code !== 0) {
                            showError(`查询失败: ${response.msg || '未知错误'}`);
                            return;
                        }

                        if (!response.data || response.data.length === 0) {
                            showError('未找到相关记录');
                            return;
                        }

                        displayResults(ursInput.value.trim(), serverIdSelect.val(), response.data);
                    })
                    .catch(error => {
                        loading.classList.add('hidden');
                        if (error.message === PERMISSION_DENIED_ERROR) {
                            showPermissionDeniedView('抱歉，您的权限已变更，无法执行此操作。请刷新页面或联系管理员。');
                        } else {
                            showError(`查询失败: ${error.message}`);
                        }
                    });
            }

            // 显示查询结果
            function displayResults(urs, serverId, dataList) {
                resultTable.innerHTML = '';

                dataList.forEach(data => {
                    const row = document.createElement('tr');
                    row.className = 'border-b transition-colors hover:bg-muted/50';

                    // URS账号
                    const ursCell = document.createElement('td');
                    ursCell.className = 'p-4 align-middle';
                    ursCell.textContent = urs;
                    row.appendChild(ursCell);

                    // 服务器ID - 使用后端返回的serverId
                    const serverIdCell = document.createElement('td');
                    serverIdCell.className = 'p-4 align-middle';
                    serverIdCell.textContent = data.serverId || '-';
                    row.appendChild(serverIdCell);

                    // 月卡状态
                    const status = calculateMonthCardStatus(data.expireAt);
                    const statusCell = document.createElement('td');
                    statusCell.className = 'p-4 align-middle';
                    const statusTag = document.createElement('span');
                    const statusConfig = MonthCardStatusConfig[status];
                    statusTag.className = `inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${statusConfig.class}`;
                    statusTag.textContent = statusConfig.text;
                    statusCell.appendChild(statusTag);
                    row.appendChild(statusCell);

                    // 过期时间
                    const expireAtCell = document.createElement('td');
                    expireAtCell.className = 'p-4 align-middle';
                    expireAtCell.textContent = formatTimestamp(data.expireAt);
                    row.appendChild(expireAtCell);

                    resultTable.appendChild(row);
                });

                resultContainer.classList.remove('hidden');
            }

            // 在输入框中按回车键也能触发查询
            ursInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchBtn.click();
                }
            });

            // 标签页切换
            const statusTab = document.getElementById('statusTab');
            const chargeTab = document.getElementById('chargeTab');
            const statusPanel = document.getElementById('statusPanel');
            const chargePanel = document.getElementById('chargePanel');

            statusTab.addEventListener('click', function() {
                statusTab.classList.add('tab-active');
                chargeTab.classList.remove('tab-active');
                statusPanel.classList.remove('hidden');
                chargePanel.classList.add('hidden');
                // Sync URS input from charge to status
                if (chargeUrsInput.value.trim() !== '') {
                    ursInput.value = chargeUrsInput.value;
                }
            });

            chargeTab.addEventListener('click', function() {
                chargeTab.classList.add('tab-active');
                statusTab.classList.remove('tab-active');
                chargePanel.classList.remove('hidden');
                statusPanel.classList.add('hidden');
                // Sync URS input from status to charge
                if (ursInput.value.trim() !== '') {
                    chargeUrsInput.value = ursInput.value;
                }
            });

            // 充值记录查询相关
            const chargeSearchBtn = document.getElementById('chargeSearchBtn');
            const chargeUrsInput = document.getElementById('chargeUrsInput');
            const chargeOrderInput = document.getElementById('chargeOrderInput');
            const chargeResultContainer = document.getElementById('chargeResultContainer');
            const chargeResultTable = document.getElementById('chargeResultTable');
            const chargeLoading = document.getElementById('chargeLoading');
            const chargeErrorMessage = document.getElementById('chargeErrorMessage');
            const chargeDateRangePicker = $('#chargeDateRangePicker');

            // Initialize Daterangepicker
            chargeDateRangePicker.daterangepicker({
                autoUpdateInput: false, // Important: Don't auto-update input, we'll do it on apply
                opens: 'left',
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: '确定',
                    cancelLabel: '清空',
                    fromLabel: '从',
                    toLabel: '到',
                    customRangeLabel: '自定义范围',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                ranges: {
                    '今天': [moment().startOf('day'), moment().endOf('day')],
                    '昨天': [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
                    '最近7天': [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
                    '最近30天': [moment().subtract(29, 'days').startOf('day'), moment().endOf('day')],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上个月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                },
                alwaysShowCalendars: true,
                showCustomRangeLabel: true,
                dateLimit: { months: 3 } // Limit custom range to 3 months
            });

            chargeDateRangePicker.on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            });

            chargeDateRangePicker.on('cancel.daterangepicker', function(ev, picker) {
                $(this).val(''); // Clear the input
            });

            function formatDuration(totalSeconds) {
                if (totalSeconds === null || typeof totalSeconds === 'undefined' || totalSeconds < 0) {
                    return '无效时长';
                }


                const days = Math.floor(totalSeconds / (24 * 3600));
                const remainingSecondsAfterDays = totalSeconds % (24 * 3600);
                const hours = Math.floor(remainingSecondsAfterDays / 3600);
                const remainingSecondsAfterHours = remainingSecondsAfterDays % 3600;
                const minutes = Math.floor(remainingSecondsAfterHours / 60);

                let parts = [];
                if (days > 0) {
                    parts.push(`${days}天`);
                }
                if (hours > 0) {
                    parts.push(`${hours}小时`);
                }
                if (minutes > 0 || (days === 0 && hours === 0)) {
                    parts.push(`${minutes}分钟`);
                }

                return parts.join('');
            }

            // 查询充值记录
            chargeSearchBtn.addEventListener('click', function() {
                const urs = chargeUrsInput.value.trim();
                const orderId = chargeOrderInput.value.trim();

                if (!urs && !orderId) {
                    showChargeError('请输入URS账号或订单号');
                    return;
                }

                const picker = chargeDateRangePicker.data('daterangepicker');
                let startTime = null;
                let endTime = null;

                if (picker && picker.startDate && picker.endDate && chargeDateRangePicker.val()) {
                    startTime = picker.startDate.startOf('day').unix();
                    endTime = picker.endDate.endOf('day').unix();

                    const diffTime = picker.endDate.diff(picker.startDate, 'days');
                    if (diffTime > 92) {
                         showChargeError('自定义时间范围不能超过3个月 (约92天)');
                         return;
                    }
                }

                hideChargeError();
                chargeLoading.classList.remove('hidden');
                chargeResultContainer.classList.add('hidden');

                let url = `/nsh/md/gm_web/api/cloud_game_duration/month_card/charge/list?`;

                if (urs) {
                    url += `urs=${encodeURIComponent(urs)}`;
                }

                if (orderId) {
                    if (urs) url += '&';
                    url += `orderId=${encodeURIComponent(orderId)}`;
                }

                if (startTime !== null && endTime !== null) {
                    url += `&startTime=${startTime}&endTime=${endTime}`;
                }

                fetchChargeRecords(url);
            });

            function showChargeError(message) {
                chargeErrorMessage.textContent = message;
                chargeErrorMessage.classList.remove('hidden');
            }

            function hideChargeError() {
                chargeErrorMessage.classList.add('hidden');
            }

            function fetchChargeRecords(url) {
                Utils.fetchWithErrorHandling(url)
                    .then(response => {
                        chargeLoading.classList.add('hidden');

                        if (response.code !== 0) {
                            showChargeError(`查询失败: ${response.msg || '未知错误'}`);
                            return;
                        }

                        if (!response.data || response.data.length === 0) {
                            showChargeError('未找到相关记录');
                            return;
                        }

                        displayChargeResults(response.data);
                    })
                    .catch(error => {
                        chargeLoading.classList.add('hidden');
                        if (error.message === PERMISSION_DENIED_ERROR) {
                            showPermissionDeniedView('抱歉，您的权限已变更，无法执行此操作。请刷新页面或联系管理员。');
                        } else {
                            showChargeError(`查询失败: ${error.message}`);
                        }
                    });
            }

            function displayChargeResults(dataList) {
                chargeResultTable.innerHTML = '';

                dataList.forEach(data => {
                    const row = document.createElement('tr');
                    row.className = 'border-b transition-colors hover:bg-muted/50';

                    // URS账号
                    const ursCell = document.createElement('td');
                    ursCell.className = 'p-4 align-middle';
                    ursCell.textContent = data.urs;
                    row.appendChild(ursCell);

                    // 服务器ID
                    const serverIdCell = document.createElement('td');
                    serverIdCell.className = 'p-4 align-middle';
                    serverIdCell.textContent = data.serverId;
                    row.appendChild(serverIdCell);

                    // 订单号
                    const orderIdCell = document.createElement('td');
                    orderIdCell.className = 'p-4 align-middle';
                    orderIdCell.textContent = data.orderId;
                    row.appendChild(orderIdCell);

                    // 渠道来源
                    const channelCell = document.createElement('td');
                    channelCell.className = 'p-4 align-middle';
                    channelCell.textContent = data.channel;
                    row.appendChild(channelCell);

                    // 购买时间
                    const buyTimeCell = document.createElement('td');
                    buyTimeCell.className = 'p-4 align-middle';
                    buyTimeCell.textContent = formatTimestamp(data.buyTime);
                    row.appendChild(buyTimeCell);

                    // 新增时长
                    const durationCell = document.createElement('td');
                    durationCell.className = 'p-4 align-middle';
                    durationCell.textContent = formatDuration(data.duration);
                    row.appendChild(durationCell);

                    chargeResultTable.appendChild(row);
                });

                chargeResultContainer.classList.remove('hidden');
            }

            // 在充值记录输入框中按回车键也能触发查询
            [chargeUrsInput, chargeOrderInput, chargeDateRangePicker.get(0)].forEach(input => {
                if (input) {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            chargeSearchBtn.click();
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
