<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>今日困惑</title>
    <style>
        /* General Styles */
        :root {
            --background-color: #f0f0f0;
            --container-background: #ffffff;
            --text-color: #333333;
            --border-color: #cccccc;
            --button-background: #e0e0e0;
            --button-hover-background: #d0d0d0;
            --primary-color: #4a90e2;
            --disabled-color: #aaaaaa;
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        }

        body {
            font-family: var(--font-family);
            background-color: var(--background-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            color: var(--text-color);
        }

        .modal-container {
            background-color: var(--container-background);
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            width: 400px;
            padding: 20px;
            position: relative;
            border: 1px solid var(--border-color);
        }

        .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 24px;
            font-weight: bold;
            color: var(--text-color);
            background: none;
            border: none;
            cursor: pointer;
        }
        .close-button:hover {
            color: #000;
        }

        .title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .question-list, .custom-question-area {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .refresh-button {
            align-self: flex-end; /* Aligns to the right if its parent is a flex container */
            background-color: var(--button-background);
            border: 1px solid var(--border-color);
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: -5px;
            margin-bottom: 10px;
            /* To make align-self work correctly if .question-list is not its direct flex parent */
            display: block;
            margin-left: auto;
        }
        .refresh-button:hover {
            background-color: var(--button-hover-background);
        }

        .dice-area {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 0;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .submit-action-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .sync-option {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--text-color);
        }
        .sync-option input[type="checkbox"] {
            margin-right: 5px;
            accent-color: var(--primary-color);
        }

    </style>
</head>
<body>

    <div class="modal-container">
        <button class="close-button" aria-label="关闭">&times;</button>
        <h2 class="title">今日困惑</h2>

        <div class="question-list">
            <question-option data-question="考研初试分数怎么样?"></question-option>
            <question-option data-question="今天打本掉出的装备怎么样?"></question-option>
            <question-option data-question="我现在去切玉佩情况如何?"></question-option>
        </div>

        <button class="refresh-button">换一换</button>

        <div class="custom-question-area">
             <custom-input placeholder="自定义疑问...... (30字)" maxlength="30"></custom-input>
        </div>

        <div class="dice-area">
            <div style="display: flex; flex-direction: column; align-items: center;">
                <dice-placeholder data-type="planet" data-value="☉"></dice-placeholder>
                <div class="dice-label" id="planet-label" style="font-size: 12px; color: #888; margin-top: 4px;"></div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center;">
                <dice-placeholder data-type="constellation" data-value="♍"></dice-placeholder>
                <div class="dice-label" id="constellation-label" style="font-size: 12px; color: #888; margin-top: 4px;"></div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center;">
                <dice-placeholder data-type="house" data-value="11"></dice-placeholder>
                <div class="dice-label" id="house-label" style="font-size: 12px; color: #888; margin-top: 4px;"></div>
            </div>
        </div>

        <div class="submit-action-area">
            <action-button text="问惑" subtext="(选择问题后亮起)" id="submitQueryButton" disabled></action-button>
            <div class="sync-option">
                <input type="checkbox" id="syncToFriends" checked>
                <label for="syncToFriends">同步到朋友圈</label>
            </div>
        </div>
    </div>

    <script>
        // Web Component: Question Option
        class QuestionOption extends HTMLElement {
            constructor() {
                super();
                this.attachShadow({ mode: 'open' });
                this.questionText = this.getAttribute('data-question') || '默认问题';
                this._selected = false;
            }

            static get observedAttributes() {
                return ['data-question', 'selected'];
            }

            attributeChangedCallback(name, oldValue, newValue) {
                if (name === 'data-question' && oldValue !== newValue) {
                    this.questionText = newValue;
                    this.render();
                }
                if (name === 'selected') {
                    this._selected = newValue !== null;
                    this.render(); // Re-render to apply/remove 'selected' class
                }
            }

            connectedCallback() {
                this.render();
                this.shadowRoot.querySelector('.question-item-component').addEventListener('click', () => {
                    this.dispatchEvent(new CustomEvent('question-selected', {
                        bubbles: true,
                        composed: true,
                        detail: { question: this.questionText, element: this }
                    }));
                });
            }

            set selected(value) {
                const isSelected = Boolean(value);
                if (this._selected !== isSelected) {
                    this._selected = isSelected;
                    if (isSelected) {
                        this.setAttribute('selected', '');
                    } else {
                        this.removeAttribute('selected');
                    }
                    // No need to call render here, attributeChangedCallback will handle it
                }
            }

            get selected() {
                return this._selected;
            }

            render() {
                this.shadowRoot.innerHTML = `
                    <style>
                        .question-item-component {
                            display: flex;
                            align-items: center;
                            border: 1px solid var(--border-color, #cccccc);
                            border-radius: 4px;
                            padding: 10px;
                            background-color: #fff;
                            cursor: pointer;
                            transition: background-color 0.2s, border-color 0.2s, box-shadow 0.2s;
                            font-size: 14px;
                            color: var(--text-color, #333333);
                        }
                        .question-item-component:hover {
                            background-color: #f9f9f9;
                        }
                        .question-item-component.selected { /* Style for selected state */
                            border-color: var(--primary-color, #4a90e2);
                            box-shadow: 0 0 5px color-mix(in srgb, var(--primary-color, #4a90e2) 50%, transparent);
                        }
                        .q-text {
                            flex-grow: 1;
                        }
                        .cost-badge-component {
                            background-color: var(--button-background, #e0e0e0);
                            color: var(--text-color, #333333);
                            font-size: 10px;
                            padding: 3px 6px;
                            border-radius: 3px;
                            margin-left: 10px;
                            border: 1px solid var(--border-color, #cccccc);
                            white-space: nowrap;
                        }
                    </style>
                    <div class="question-item-component ${this._selected ? 'selected' : ''}">
                        <span class="q-text">${this.questionText}</span>
                        <span class="cost-badge-component">消耗-10</span>
                    </div>
                `;
            }
        }
        customElements.define('question-option', QuestionOption);

        // Web Component: Custom Input
        class CustomInput extends HTMLElement {
            constructor() {
                super();
                this.attachShadow({ mode: 'open' });
                this.placeholder = this.getAttribute('placeholder') || '';
                this.maxlength = this.getAttribute('maxlength') || '';
                this._selected = false;
            }
             static get observedAttributes() {
                return ['selected'];
            }

            attributeChangedCallback(name, oldValue, newValue) {
                if (name === 'selected') {
                    this._selected = newValue !== null;
                    this.updateSelectedStyle();
                }
            }

            connectedCallback() {
                this.render();
                this.inputElement = this.shadowRoot.querySelector('input');
                this.wrapperElement = this.shadowRoot.querySelector('.custom-input-wrapper-component');

                this.inputElement.addEventListener('input', (e) => {
                     this.dispatchEvent(new CustomEvent('input-changed', {
                        bubbles: true,
                        composed: true,
                        detail: { value: e.target.value }
                    }));
                });
                 this.inputElement.addEventListener('focus', () => {
                     this.dispatchEvent(new CustomEvent('custom-input-focused', {
                        bubbles: true,
                        composed: true
                    }));
                });
            }

            get value() {
                return this.inputElement ? this.inputElement.value : '';
            }

            clear() {
                if (this.inputElement) {
                    this.inputElement.value = '';
                }
                 this.dispatchEvent(new CustomEvent('input-changed', { // Ensure state is updated
                    bubbles: true,
                    composed: true,
                    detail: { value: '' }
                }));
            }

            set selected(value) {
                const isSelected = Boolean(value);
                if (this._selected !== isSelected) {
                    this._selected = isSelected;
                    if (isSelected) {
                        this.setAttribute('selected', '');
                    } else {
                        this.removeAttribute('selected');
                    }
                }
            }

            get selected() {
                return this._selected;
            }

            updateSelectedStyle() {
                if(this.wrapperElement){
                    if(this._selected){
                        this.wrapperElement.classList.add('selected');
                    } else {
                        this.wrapperElement.classList.remove('selected');
                    }
                }
            }

            render() {
                this.shadowRoot.innerHTML = `
                    <style>
                        .custom-input-wrapper-component {
                            display: flex;
                            align-items: center;
                            border: 1px solid var(--border-color, #cccccc);
                            border-radius: 4px;
                            padding: 10px;
                            background-color: #fff;
                            transition: border-color 0.2s, box-shadow 0.2s;
                        }
                        .custom-input-component {
                            flex-grow: 1;
                            border: none;
                            outline: none;
                            font-size: 14px;
                            background-color: transparent;
                            color: var(--text-color, #333333);
                        }
                        .custom-input-component::placeholder {
                            color: #999999;
                        }
                        .cost-badge-component {
                            background-color: var(--button-background, #e0e0e0);
                            color: var(--text-color, #333333);
                            font-size: 10px;
                            padding: 3px 6px;
                            border-radius: 3px;
                            margin-left: 10px;
                            border: 1px solid var(--border-color, #cccccc);
                            white-space: nowrap;
                        }
                         .custom-input-wrapper-component.selected {
                            border-color: var(--primary-color, #4a90e2);
                            box-shadow: 0 0 5px color-mix(in srgb, var(--primary-color, #4a90e2) 50%, transparent);
                        }
                    </style>
                    <div class="custom-input-wrapper-component ${this._selected ? 'selected' : ''}">
                        <input type="text" class="custom-input-component" placeholder="${this.placeholder}" maxlength="${this.maxlength}">
                        <span class="cost-badge-component">消耗-10</span>
                    </div>
                `;
            }
        }
        customElements.define('custom-input', CustomInput);

        // Web Component: Dice Placeholder
        class DicePlaceholder extends HTMLElement {
            constructor() {
                super();
                this.attachShadow({ mode: 'open' });
            }

            static get observedAttributes() {
                return ['data-value', 'data-type'];
            }

            attributeChangedCallback(name, oldValue, newValue) {
                if ((name === 'data-value' || name === 'data-type') && oldValue !== newValue) {
                    this.render();
                }
            }

            connectedCallback() {
                this.render();
            }

            render() {
                const value = this.getAttribute('data-value') || '?';
                this.shadowRoot.innerHTML = `
                    <style>
                        .dice {
                            width: 60px; /* Match screenshot more closely */
                            height: 60px;
                            border: 1px solid var(--border-color, #cccccc);
                            /* Attempting a pentagon-like shape with border-radius, not perfect */
                            border-radius: 15px 15px 15px 15px / 20px 20px 12px 12px;
                            /* A simpler rounded rectangle as fallback: border-radius: 8px; */
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 24px;
                            font-weight: bold;
                            background-color: #fff;
                            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
                            color: var(--text-color, #333333);
                            /* clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%); /* True pentagon */
                        }
                    </style>
                    <div class="dice">${value}</div>
                `;
            }
        }
        customElements.define('dice-placeholder', DicePlaceholder);

        // Web Component: Action Button
        class ActionButton extends HTMLElement {
            constructor() {
                super();
                this.attachShadow({ mode: 'open' });
            }

            static get observedAttributes() {
                return ['text', 'subtext', 'disabled', 'primary'];
            }

            attributeChangedCallback(name, oldValue, newValue) {
                this.render();
            }

            connectedCallback() {
                this.render();
            }

            render() {
                const text = this.getAttribute('text') || 'Button';
                const subtext = this.getAttribute('subtext') || '';
                const isDisabled = this.hasAttribute('disabled');
                const isPrimary = this.hasAttribute('primary');

                this.shadowRoot.innerHTML = `
                    <style>
                        button {
                            width: 100%;
                            padding: 12px; /* Increased padding for better look */
                            font-size: 16px;
                            font-weight: bold;
                            border-radius: 4px;
                            border: 1px solid var(--border-color, #cccccc);
                            background-color: var(--button-background, #e0e0e0);
                            color: var(--text-color, #333333);
                            cursor: pointer;
                            transition: background-color 0.2s, color 0.2s, border-color 0.2s;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            line-height: 1.2;
                        }
                        button:disabled {
                            background-color: #e9e9e9 !important;
                            color: var(--disabled-color, #aaaaaa) !important;
                            cursor: not-allowed !important;
                            border-color: #d9d9d9 !important;
                        }
                        button:not(:disabled):hover {
                            background-color: var(--button-hover-background, #d0d0d0);
                        }
                        button.primary:not(:disabled) {
                            background-color: var(--primary-color, #4a90e2);
                            color: white;
                            border-color: var(--primary-color, #4a90e2);
                        }
                        button.primary:not(:disabled):hover {
                            background-color: color-mix(in srgb, var(--primary-color, #4a90e2) 90%, black);
                        }
                        .subtext {
                            font-size: 10px;
                            font-weight: normal;
                            color: ${isDisabled ? 'var(--disabled-color, #aaaaaa)' : 'inherit'};
                            opacity: 0.8;
                            margin-top: 2px; /* Small space between text and subtext */
                        }
                    </style>
                    <button class="${isPrimary ? 'primary' : ''}" ${isDisabled ? 'disabled' : ''}>
                        ${text}
                        ${subtext ? `<span class="subtext">${subtext}</span>` : ''}
                    </button>
                `;
            }
        }
        customElements.define('action-button', ActionButton);

        // Main script for interactions
        document.addEventListener('DOMContentLoaded', () => {
            const questionOptionElements = document.querySelectorAll('question-option');
            const customInputComponent = document.querySelector('custom-input');
            const submitQueryButton = document.getElementById('submitQueryButton'); // This is the <action-button>
            const refreshButton = document.querySelector('.refresh-button');
            const dicePlaceholderElements = document.querySelectorAll('dice-placeholder');
            const closeModalButton = document.querySelector('.close-button');

            let selectedQuestionComponent = null;
            let currentCustomInputValue = "";

            // Dice data (moved to a more global scope within DOMContentLoaded)
            const planetSymbols = ["☉", "☽", "☿", "♀", "♂", "♃", "♄", "♅", "♆", "♇", "☊", "☋"];
            const constellationSymbols = ["♈", "♉", "♊", "♋", "♌", "♍", "♎", "♏", "♐", "♑", "♒", "♓"];
            const houseNumbers = Array.from({length: 12}, (_, i) => i + 1);

            // 映射表
            const planetMap = {
                "☉": "太阳", "☽": "月亮", "☿": "水星", "♀": "金星", "♂": "火星",
                "♃": "木星", "♄": "土星", "♅": "天王星", "♆": "海王星", "♇": "冥王星",
                "☊": "北交点", "☋": "南交点"
            };
            const constellationMap = {
                "♈": "白羊座", "♉": "金牛座", "♊": "双子座", "♋": "巨蟹座",
                "♌": "狮子座", "♍": "处女座", "♎": "天秤座", "♏": "天蝎座",
                "♐": "射手座", "♑": "摩羯座", "♒": "水瓶座", "♓": "双鱼座"
            };
            const houseMap = {
                "1": "第一宫命宫", "2": "第二宫财运宫", "3": "第三宫兄弟宫", "4": "第四宫家庭宫",
                "5": "第五宫子女宫", "6": "第六宫健康宫", "7": "第七宫夫妻宫", "8": "第八宫疾厄宫",
                "9": "第九宫迁移宫", "10": "第十宫事业宫", "11": "第十一宫人际宫", "12": "第十二宫精神宫"
            };

            // 更新label的函数
            function updateDiceLabels() {
                if (dicePlaceholderElements.length < 3) return; // Guard clause
                const planetSymbol = dicePlaceholderElements[0].getAttribute('data-value');
                const constellationSymbol = dicePlaceholderElements[1].getAttribute('data-value');
                const houseNumber = dicePlaceholderElements[2].getAttribute('data-value');

                document.getElementById('planet-label').textContent = planetMap[planetSymbol] || '';
                document.getElementById('constellation-label').textContent = constellationMap[constellationSymbol] || '';
                document.getElementById('house-label').textContent = houseMap[houseNumber] || '';
            }

            // New function to randomize dice and update display
            function randomizeAndDisplayDice() {
                if (dicePlaceholderElements.length < 3) return; // Guard clause

                dicePlaceholderElements[0].setAttribute('data-value', planetSymbols[Math.floor(Math.random() * planetSymbols.length)]);
                dicePlaceholderElements[1].setAttribute('data-value', constellationSymbols[Math.floor(Math.random() * constellationSymbols.length)]);
                dicePlaceholderElements[2].setAttribute('data-value', houseNumbers[Math.floor(Math.random() * houseNumbers.length)].toString());

                updateDiceLabels();
            }

            function MOCK_getNewQuestions() {
                const allAvailableQuestions = [
                    "考研初试分数怎么样?", "今天打本掉出的装备怎么样?", "我现在去切玉佩情况如何?",
                    "未来一周的财运如何？", "我的暗恋对象也喜欢我吗？", "这个周末会加班吗？",
                    "新买的彩票会中奖吗？", "今天的会议能顺利结束吗？", "这次旅行会愉快吗？"
                ];
                const currentDisplayedQuestions = Array.from(questionOptionElements).map(el => el.getAttribute('data-question'));
                let uniqueNewQuestions = allAvailableQuestions.filter(q => !currentDisplayedQuestions.includes(q));

                const newQuestionsForDisplay = [];
                for (let i = 0; i < 3; i++) {
                    if (uniqueNewQuestions.length > 0) {
                        const randomIndex = Math.floor(Math.random() * uniqueNewQuestions.length);
                        newQuestionsForDisplay.push(uniqueNewQuestions.splice(randomIndex, 1)[0]);
                    } else {
                         // Fallback: if not enough unique, pick random from all (might include current ones but less likely)
                        newQuestionsForDisplay.push(allAvailableQuestions[Math.floor(Math.random() * allAvailableQuestions.length)]);
                    }
                }
                return newQuestionsForDisplay;
            }

            refreshButton.addEventListener('click', () => {
                const newQuestions = MOCK_getNewQuestions();
                questionOptionElements.forEach((option, index) => {
                    if (newQuestions[index]) {
                        if (option === selectedQuestionComponent) { // If selected question is being replaced
                           option.selected = false; // Deselect it first
                           selectedQuestionComponent = null; // Clear selection
                        }
                        option.setAttribute('data-question', newQuestions[index]);
                    }
                });
                updateSubmitButtonState();
                randomizeAndDisplayDice(); // Use the new function
            });

            function clearAllSelections() {
                questionOptionElements.forEach(q => q.selected = false);
                customInputComponent.selected = false;
                selectedQuestionComponent = null;
                // currentCustomInputValue is not cleared here, only selection state
            }

            questionOptionElements.forEach(optionElement => {
                optionElement.addEventListener('question-selected', (event) => {
                    const clickedOption = event.target; // This is the <question-option> element
                    const questionText = clickedOption.getAttribute('data-question');

                    // Set the clicked option as selected and deselect others
                    questionOptionElements.forEach(qOpt => {
                        qOpt.selected = (qOpt === clickedOption);
                    });
                    selectedQuestionComponent = clickedOption; // This is now the selected question component

                    // Populate the customInputComponent's text field
                    if (customInputComponent.inputElement) {
                        customInputComponent.inputElement.value = questionText;
                    }
                    // Update the state variable for the custom input's content
                    currentCustomInputValue = questionText.trim();

                    // Custom input is not the primary selection source now
                    customInputComponent.selected = false;

                    // Update the state of the submit button
                    updateSubmitButtonState();
                });
            });

            customInputComponent.addEventListener('input-changed', (e) => {
                currentCustomInputValue = e.detail.value.trim();
                 if (currentCustomInputValue) {
                    if(selectedQuestionComponent) selectedQuestionComponent.selected = false;
                    selectedQuestionComponent = null;
                    customInputComponent.selected = true;
                } else {
                    customInputComponent.selected = false; // Deselect if empty
                }
                updateSubmitButtonState();
            });

            customInputComponent.addEventListener('custom-input-focused', () => {
                clearAllSelections();
                customInputComponent.selected = true; // Select custom input on focus
                updateSubmitButtonState();
            });


            function updateSubmitButtonState() {
                const isQuestionPresetSelected = selectedQuestionComponent !== null && selectedQuestionComponent.selected;
                const isCustomInputFilledAndSelected = currentCustomInputValue.length > 0 && customInputComponent.selected;
                 // Allow submit if a preset is selected OR if custom input has text AND is the selected input method
                if (isQuestionPresetSelected || isCustomInputFilledAndSelected) {
                    submitQueryButton.removeAttribute('disabled');
                    submitQueryButton.setAttribute('primary', '');
                } else {
                    submitQueryButton.setAttribute('disabled', '');
                    submitQueryButton.removeAttribute('primary');
                }
            }

            customElements.whenDefined('action-button').then(() => {
                const actualButtonInShadowDom = submitQueryButton.shadowRoot.querySelector('button');
                if (actualButtonInShadowDom) {
                    actualButtonInShadowDom.addEventListener('click', () => {
                        if (submitQueryButton.hasAttribute('disabled')) return;

                        let questionToSubmit = "";
                        if (selectedQuestionComponent && selectedQuestionComponent.selected) {
                            questionToSubmit = selectedQuestionComponent.getAttribute('data-question');
                        } else if (currentCustomInputValue && customInputComponent.selected) {
                            questionToSubmit = currentCustomInputValue;
                        }

                        if (questionToSubmit) {
                            console.log("问惑提交:", questionToSubmit);

                            randomizeAndDisplayDice(); // Use the new function

                            const diceResults = {
                                planet: dicePlaceholderElements[0].getAttribute('data-value'),
                                constellation: dicePlaceholderElements[1].getAttribute('data-value'),
                                house: dicePlaceholderElements[2].getAttribute('data-value')
                            };
                            console.log("骰子参数:", diceResults);
                            // Here you would send questionToSubmit and diceResults to "有灵AI-DS版"
                            alert(`问题: ${questionToSubmit}\n行星: ${planetMap[diceResults.planet] || diceResults.planet}, 星座: ${constellationMap[diceResults.constellation] || diceResults.constellation}, 宫位: ${houseMap[diceResults.house] || diceResults.house}`);
                            // updateDiceLabels(); // No longer needed here as randomizeAndDisplayDice calls it
                        }
                    });
                }
            });

            closeModalButton.addEventListener('click', () => {
                // In a real app, this would hide or remove the modal.
                // For this standalone example, we can just log it or make it less visible.
                console.log("Close button clicked");
                document.querySelector('.modal-container').style.display = 'none';
                alert("弹窗已关闭 (模拟)。在实际应用中，这将通过JS控制模态框的可见性。");
            });

            updateSubmitButtonState(); // Initial state check
            updateDiceLabels(); // Call this to ensure initial labels for pre-set dice values in HTML are shown
        });

    </script>
</body>
</html>
