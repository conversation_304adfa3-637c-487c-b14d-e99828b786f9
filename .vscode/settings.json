{"files.exclude": {"dist/": true, "node_modules/": true}, "cSpell.words": ["checkin", "decr", "Downvote", "filepicker", "friendcircle", "gameid", "gethotestmoment", "GPLS", "<PERSON><PERSON><PERSON><PERSON>", "info", "KFMXYQS", "l", "leaveword", "netease", "playerid", "Proxied", "realname", "rebalancing", "<PERSON><PERSON>", "roleid", "roleinfo", "<PERSON><PERSON>", "smembers", "yuanbao", "<PERSON><PERSON><PERSON>"], "rest-client.environmentVariables": {"$shared": {"cheatSkey": "CHEAT_SKEY_ONLY_FOR_TEST", "baseUrl": "http://127.0.0.1:4001/nsh/md", "corpCookie": "common_auth_corp_info=****************************************.1740724731.********************************************************************************************************************************************************************; common_auth_corp_token=75c21e57a269111d70afe5d0f5652135;"}, "local": {"baseUrl": "http://127.0.0.1:4001/nsh/md"}, "test": {"baseUrl": "http://**************:88/nsh/md"}, "qa": {"baseUrl": "http://***************:80/nsh/md"}, "rc": {"baseUrl": "http://*************/nsh/md"}, "proc": {"baseUrl": "http://nshapi.hi.163.com/nsh/md"}}, "typescript.tsdk": "node_modules\\typescript\\lib", "mochaExplorer.env": {"NODE_ENV": "test"}}