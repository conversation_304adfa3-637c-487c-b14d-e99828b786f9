# 占星图片资源验证脚本

## 概述

此目录包含用于验证占星功能图片资源的脚本。占星shareToMoment功能需要确保所有行星/星座/宫位组合的图片都已正确上传到CDN。

## 脚本说明

### `verifyAstrologyImagesConcurrent.js`

**主要验证脚本** - 用于验证所有占星图片资源是否可访问

#### 功能
- 并发验证1,728张占星图片的可访问性
- 覆盖所有可能的占星组合：13个行星 × 12个星座 × 12个宫位
- 生成详细的验证报告

#### 运行方式
```bash
node scripts/verifyAstrologyImagesConcurrent.js
```

#### 验证范围
```
行星 (13个): sun, moon, mercury, venus, mars, jupiter, saturn, uranus, neptune, pluto, southNode, northNode
星座 (12个): aries, taurus, gemini, cancer, leo, virgo, libra, scorpio, sagittarius, capricorn, aquarius, pisces
宫位 (12个): 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12
```

#### 图片URL格式
```
https://hi-163-nsh.nosdn.127.net/assets/icons/astrology/planet_{planet}_zodiac_{constellation}_house_{house}.png
```

#### 输出文件
- **成功时**: `astrology_images_verification_success.json` - 包含验证统计信息
- **失败时**: `failed_astrology_images.json` - 包含缺失图片的详细信息

#### 示例输出
```
🎯 验证完成！
==========================================
⏱️  耗时: 12 秒
📊 总计: 1728
✅ 成功: 1728
❌ 失败: 0
📈 成功率: 100%
🚀 平均速度: 144 请求/秒

🎉 所有图片都可以正常访问！
```

## 使用场景

### 1. 上线前验证
在部署占星功能前，运行验证脚本确保所有图片资源就绪：
```bash
npm run verify-astrology-images  # 或直接运行 node scripts/verifyAstrologyImagesConcurrent.js
```

### 2. 图片资源更新后验证
当设计师更新或重新上传占星图片后，验证所有组合是否完整：
```bash
node scripts/verifyAstrologyImagesConcurrent.js
```

### 3. 生产环境健康检查
定期验证生产环境的图片资源完整性。

## 故障排除

### 如果发现缺失图片
1. 查看生成的 `failed_astrology_images.json` 文件
2. 按行星分组查看缺失的组合
3. 联系设计师补齐缺失的图片
4. 重新上传后再次运行验证

### 常见问题
- **网络超时**: 脚本会自动重试，如果仍然失败可能是网络问题
- **大量缺失**: 通常表示某个行星的图片批量缺失，需要重新上传
- **个别缺失**: 可能是特定组合的图片未正确命名或上传

## 技术细节

- **并发控制**: 每批50个请求，避免对服务器造成压力
- **超时设置**: 每个请求5秒超时
- **错误处理**: 网络错误会被标记为图片不可用
- **进度显示**: 实时显示验证进度

## 相关代码

占星图片功能的核心实现位于：
- `src/services/astrology/astrologyExternalService.ts` - 生成分享图片URL
- `src/services/moment.ts` - 处理预批准图片的动态发布