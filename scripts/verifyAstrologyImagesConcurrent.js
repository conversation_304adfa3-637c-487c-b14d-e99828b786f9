#!/usr/bin/env node

/**
 * 占星图片资源验证脚本
 * 
 * 功能：验证所有占星功能所需的图片资源是否可访问
 * 用途：在上线前确保所有行星/星座/宫位组合的图片都已正确上传
 * 
 * 运行方式：
 *   node scripts/verifyAstrologyImagesConcurrent.js
 * 
 * 验证范围：
 *   - 13个行星 × 12个星座 × 12个宫位 = 1,728张图片
 *   - 图片URL格式: https://hi-163-nsh.nosdn.127.net/assets/icons/astrology/planet_{planet}_zodiac_{constellation}_house_{house}.png
 * 
 * 输出：
 *   - 控制台显示详细进度和结果
 *   - 成功时生成: astrology_images_verification_success.json
 *   - 失败时生成: failed_astrology_images.json (包含缺失图片详情)
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 所有可能的组合
const planets = [
  "sun", "moon", "mercury", "venus", "mars", "jupiter", "saturn", 
  "uranus", "neptune", "pluto", "southNode", "northNode"
];

const constellations = [
  "aries", "taurus", "gemini", "cancer", "leo", "virgo",
  "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces"
];

const houses = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

const baseUrl = "https://hi-163-nsh.nosdn.127.net/assets/icons/astrology";

function generateImageUrl(planet, constellation, house) {
  return `${baseUrl}/planet_${planet}_zodiac_${constellation}_house_${house}.png`;
}

function checkImageExists(url) {
  return new Promise((resolve) => {
    const curl = spawn('curl', ['--head', '--silent', '--fail', '--max-time', '10', url]);
    
    curl.on('close', (code) => {
      resolve({
        url,
        exists: code === 0,
        combination: url.match(/planet_(.+)_zodiac_(.+)_house_(\d+)\.png$/)?.[0] || 'unknown'
      });
    });

    curl.on('error', () => {
      resolve({
        url,
        exists: false,
        combination: url.match(/planet_(.+)_zodiac_(.+)_house_(\d+)\.png$/)?.[0] || 'unknown'
      });
    });
  });
}

async function processBatch(urls, batchSize = 50) {
  const results = [];
  
  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    const batchPromises = batch.map(url => checkImageExists(url));
    const batchResults = await Promise.all(batchPromises);
    
    results.push(...batchResults);
    
    // 显示进度
    const progress = Math.min(i + batchSize, urls.length);
    process.stdout.write(`\r📈 进度: ${progress}/${urls.length} (${Math.round(progress/urls.length*100)}%)`);
  }
  
  return results;
}

async function main() {
  console.log('🔍 开始验证占星图片资源 (并发版本)...');
  
  // 生成所有URL
  const allUrls = [];
  for (const planet of planets) {
    for (const constellation of constellations) {
      for (const house of houses) {
        allUrls.push(generateImageUrl(planet, constellation, house));
      }
    }
  }
  
  console.log(`📊 总共需要验证: ${allUrls.length} 张图片`);
  console.log('🚀 使用并发请求，每批50个...');
  console.log('');

  const startTime = Date.now();
  
  const results = await processBatch(allUrls, 50);
  
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);

  // 统计结果
  const stats = {
    total: results.length,
    success: results.filter(r => r.exists).length,
    failed: results.filter(r => !r.exists).length,
    failedResults: results.filter(r => !r.exists)
  };

  console.log('\n');
  console.log('🎯 验证完成！');
  console.log('==========================================');
  console.log(`⏱️  耗时: ${duration} 秒`);
  console.log(`📊 总计: ${stats.total}`);
  console.log(`✅ 成功: ${stats.success}`);
  console.log(`❌ 失败: ${stats.failed}`);
  console.log(`📈 成功率: ${Math.round(stats.success/stats.total*100)}%`);
  console.log(`🚀 平均速度: ${Math.round(stats.total/duration)} 请求/秒`);

  if (stats.failed > 0) {
    console.log('\n❌ 失败的图片组合:');
    console.log('==========================================');
    
    // 按行星分组显示失败的组合
    const failedByPlanet = {};
    stats.failedResults.forEach(result => {
      const match = result.url.match(/planet_(.+)_zodiac_(.+)_house_(\d+)\.png$/);
      if (match) {
        const [, planet, constellation, house] = match;
        if (!failedByPlanet[planet]) {
          failedByPlanet[planet] = [];
        }
        failedByPlanet[planet].push(`${constellation}/house_${house}`);
      }
    });

    Object.keys(failedByPlanet).forEach(planet => {
      console.log(`\n🪐 ${planet.toUpperCase()}: ${failedByPlanet[planet].length} 个失败`);
      failedByPlanet[planet].slice(0, 5).forEach(combo => {
        console.log(`   - ${combo}`);
      });
      if (failedByPlanet[planet].length > 5) {
        console.log(`   ... 还有 ${failedByPlanet[planet].length - 5} 个`);
      }
    });

    // 保存详细结果
    const failedUrlsFile = path.join(__dirname, 'failed_astrology_images.json');
    const reportData = {
      timestamp: new Date().toISOString(),
      duration: duration,
      stats: stats,
      failedUrls: stats.failedResults.map(r => r.url),
      failedByPlanet: failedByPlanet
    };
    
    fs.writeFileSync(failedUrlsFile, JSON.stringify(reportData, null, 2));
    console.log(`\n📝 详细报告已保存到: ${failedUrlsFile}`);

    // 生成示例curl命令
    console.log('\n🔧 示例检查命令:');
    console.log('==========================================');
    stats.failedResults.slice(0, 3).forEach(result => {
      console.log(`curl --head "${result.url}"`);
    });

    process.exit(1);
  } else {
    console.log('\n🎉 所有图片都可以正常访问！');
    
    // 保存成功报告
    const successFile = path.join(__dirname, 'astrology_images_verification_success.json');
    const successReport = {
      timestamp: new Date().toISOString(),
      duration: duration,
      stats: stats,
      status: 'ALL_IMAGES_ACCESSIBLE'
    };
    fs.writeFileSync(successFile, JSON.stringify(successReport, null, 2));
    console.log(`📝 验证报告已保存到: ${successFile}`);
    
    process.exit(0);
  }
}

// 运行脚本
main().catch(error => {
  console.error('❌ 脚本执行失败:', error);
  process.exit(1);
});