const xlsx = require('node-xlsx');
const fs = require('fs');
const path = require('path');

const excelFilePath = path.join(__dirname, '../asserts/excels/astrology_ai_default_fallback.xlsx');
const outputDir = path.join(__dirname, '../asserts/json');

if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

try {
    const workSheetsFromFile = xlsx.parse(excelFilePath);
    
    workSheetsFromFile.forEach((worksheet, index) => {
        const sheetName = worksheet.name || `sheet${index + 1}`;
        const data = worksheet.data;
        
        if (data.length < 6) {
            console.log(`Skipping sheet ${sheetName}: insufficient data rows`);
            return;
        }
        
        // 根据分析，第5行是字段名称（表头），从第6行开始是数据
        const headers = data[4]; // 第5行 (索引4)
        const dataRows = data.slice(5); // 从第6行开始 (索引5)
        
        // 找到id列的位置，从id列开始处理数据
        const idIndex = headers.findIndex(header => header === 'id');
        if (idIndex === -1) {
            console.log(`Skipping sheet ${sheetName}: 'id' column not found`);
            return;
        }
        
        // 从id列开始，过滤有效的表头字段
        const validHeaders = [];
        const validIndexes = [];
        headers.slice(idIndex).forEach((header, index) => {
            if (header && typeof header === 'string' && header.trim() !== '') {
                validHeaders.push(header);
                validIndexes.push(idIndex + index);
            }
        });
        
        // 转换数据为JSON对象
        const jsonData = dataRows.map(row => {
            const obj = {};
            validHeaders.forEach((header, headerIndex) => {
                const cellIndex = validIndexes[headerIndex];
                let cellValue = row[cellIndex];
                
                // 处理JSON字符串
                if (header === 'default_response' && typeof cellValue === 'string') {
                    try {
                        cellValue = JSON.parse(cellValue);
                    } catch (e) {
                        // 如果解析失败，保持原字符串
                    }
                }
                
                obj[header] = cellValue !== undefined ? cellValue : null;
            });
            return obj;
        }).filter(obj => {
            // 过滤掉空行或无效行
            return Object.values(obj).some(val => val !== null && val !== undefined && val !== '');
        });
        
        const outputFile = path.join(outputDir, `astrology_ai_default_fallback.json`);
        fs.writeFileSync(outputFile, JSON.stringify(jsonData, null, 2));
        console.log(`Generated: ${outputFile}`);
        console.log(`Records processed: ${jsonData.length}`);
    });
    
    console.log('Excel parsing completed successfully!');
} catch (error) {
    console.error('Error parsing Excel file:', error);
    process.exit(1);
}