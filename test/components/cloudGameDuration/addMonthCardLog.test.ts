import { parseAddMonthCardLog } from "../../../src/components/cloudGameDuration/logConsumer/addMonthCardLog";
import { parseGameRawLog } from "../../../src/components/cloudGameDuration/logConsumer/util";
import * as assert from "power-assert";
import { GameLogStruct, LogEventId } from "../../../src/components/cloudGameDuration/logConsumer/type";

describe("cloudGameDuration.addMonthCardLog", () => {
  it("should parseGameRawLog for month card log correctly", () => {
    // Example log from documentation
    const rawLog =
      "/some/path/log [2025-02-13 14:14:29] gas[28056]:[2067273]INFO|PLAYER|[404353]#<EMAIL>,,101601001,,30,,01000000E903013000010000C58DAD67";
    const ret = parseGameRawLog(rawLog);
    assert.ok(ret, "parseGameRawLog should return a result");
    assert.equal(ret.eventId, LogEventId.CloudGameBuyMonthCard, "Event ID should match");
    assert.equal(ret.eventTime.getTime(), new Date("2025-02-13 14:14:29").getTime(), "Event time should match");
    assert.deepStrictEqual(ret.args, [
      "#<EMAIL>",
      "101601001", // Username (ignored in parsing logic)
      "30",
      "01000000E903013000010000C58DAD67",
    ], "Arguments should be parsed correctly");
  });

  it("should parseAddMonthCardLog valid log", () => {
    let eventTime = new Date("2025-02-13 14:14:29");
    const gs: GameLogStruct = {
      eventId: LogEventId.CloudGameBuyMonthCard,
      args: [
        "#<EMAIL>",
        "someUsername",
        "30", // daysAdded
        "orderSn12345",
        "219"
      ],
      payload: "", // Payload is not used in this parser
      eventTime,
    };
    const ret = parseAddMonthCardLog(gs);
    assert.equal(ret.isOk, true, "Parsing should be successful");
    assert.deepStrictEqual(ret.data, {
      urs: "<EMAIL>",
      daysAdded: 30,
      sn: "orderSn12345",
      serverId: 219,
      buyTime: Math.floor(eventTime.getTime() / 1000),
    }, "Parsed data should match expected values");
  });

  it("should return isOk: false for incorrect eventId", () => {
    const gs: GameLogStruct = {
      eventId: 123456, // Incorrect event ID
      args: ["#<EMAIL>", "user", "30", "sn1"],
      payload: "",
      eventTime: new Date(),
    };
    const ret = parseAddMonthCardLog(gs);
    assert.equal(ret.isOk, false, "Parsing should fail for wrong eventId");
    assert.equal(ret.data, null, "Data should be null on failure");
  });

  it("should return isOk: false for missing '#' prefix in urs", () => {
    const gs: GameLogStruct = {
      eventId: LogEventId.CloudGameBuyMonthCard,
      args: ["<EMAIL>", "user", "30", "sn1"], // Missing #
      payload: "",
      eventTime: new Date(),
    };
    const ret = parseAddMonthCardLog(gs);
    assert.equal(ret.isOk, false, "Parsing should fail for missing urs prefix");
    assert.equal(ret.data, null, "Data should be null on failure");
  });

  it("should return isOk: false for non-numeric daysAdded", () => {
    const gs: GameLogStruct = {
      eventId: LogEventId.CloudGameBuyMonthCard,
      args: ["#<EMAIL>", "user", "thirty", "sn1"], // Non-numeric days
      payload: "",
      eventTime: new Date(),
    };
    const ret = parseAddMonthCardLog(gs);
    assert.equal(ret.isOk, false, "Parsing should fail for non-numeric days");
    assert.equal(ret.data, null, "Data should be null on failure");
  });

  it("should return isOk: false for insufficient arguments", () => {
    const gs: GameLogStruct = {
      eventId: LogEventId.CloudGameBuyMonthCard,
      args: ["#<EMAIL>", "user", "30"], // Missing SN
      payload: "",
      eventTime: new Date(),
    };
    const ret = parseAddMonthCardLog(gs);
    assert.equal(ret.isOk, false, "Parsing should fail for insufficient args");
    assert.equal(ret.data, null, "Data should be null on failure");
  });
});