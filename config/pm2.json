{"apps": [{"name": "nsh-md", "script": "./dist/app.js", "watch": ["tmp/restart.txt"], "exec_mode": "cluster", "instances": 2, "env_production": {"NODE_ENV": "production"}, "error_file": "/srv/logs/sites/pm2_nsh_md_error.log", "out_file": "/srv/logs/sites/pm2_nsh_md_out.log", "combine_logs": true, "autorestart": true, "max_restarts": 10}, {"name": "nsh-crons", "script": "./dist/cron-jobs/pyqCrons.js", "watch": ["tmp/restart.txt"], "exec_mode": "fork", "instances": 1, "env_production": {"NODE_ENV": "production"}, "error_file": "/srv/logs/sites/pm2_nsh_crons_error.log", "out_file": "/srv/logs/sites/pm2_nsh_crons_out.log", "combine_logs": true, "autorestart": true, "max_restarts": 10}]}