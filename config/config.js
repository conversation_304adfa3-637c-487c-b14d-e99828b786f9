"use strict";
const dConfig = require("./config.all");
let config = Object.assign({}, dConfig);
config.db = {
    connectionLimit: 2,
    port: 3306,
    host: '***************',
    user: 'ngp-yxb',
    password: 'ngp.yxb.163',
    database: 'nodejs_yxb'
};
config.slaveDb = config.db;
config.testCfg = {
    skip_audit: false,
    skip_level: true,
    req_log: true,
    debug: false,
    test_env: true,
    skip_auth: true,
   skip_ip_auth: true,
    skip_skey_check: true,
    skip_token: false,
    db_debug: true,
    redis_debug: false,
    request_debug: false,
    skip_neDun_verify: true,
    no_forward_proxy: true,
    skip_openId_auth: true,
    skip_photo_wall_audit: true,
};
config.redis = {
    hosts: ['***************'],
    host: '***************',
    port: 19000,
   // port: 6379,
    db: 0,
   // password: 'sdjif934028ut',
    password: '',
    no_ready_check: false,
    prefix: 'nsh_md:'
};
config.redisSkeyCfg=config.redis;


config.MomentWeekRenqi = {
    maxAddDaily: 100,
    like: 1,
    comment: 1,
    forward: 1
}

config.MessageBoardWeekRenqi = {
    maxAddDaily: 100,
    signIn: 1,
}

config.log = {
    dir: '/srv/logs/',
    level: 'debug',
    prefix: 'nsh_md'
}

config.yinyunLog = {
    tag: 'nsh_md',
    level: 'info',
    facility: 'local0',
    host: '127.0.0.1',
    port: '514'
}

config.MessageBoardGift = {
    receiveLimitPerDay: 10
}

config.WeekRankCfg = {
    mockTime: false,
    mockDate: '2010-02-20 11:00'
}

config.geoRedis = config.redis

config.JWT_TOKEN_SECRET = '@#!!^&*()$#@~_($%^&##*$o' // jwt token 生成密钥

config.NOS_CFG.accessKey = '80daf02d6f7042b190ef3358091cc335'
config.NOS_CFG.secretKey = '2918bfd8aa064e2b8d7cc6429a9d45eb'

config.ursCookie = {
    hosts: ['https://cookiebj.reg.163.com/validate', 'https://cookiehz.reg.163.com/validate'],
    productId: 'f77072fb1b1a4664856363589c4127af'
}

config.officialAccount = {
    skipUrsCheck: true,
 adminUrs: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
}
config.IdentityAdminUrs = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
]

config.weekStat = {
  isMockDate: true,
  mockDate: '2019-06-03'
}

config.TopicAdminUrs = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
]

config.CHATBOTCFG.fuxi.url= 'http://dl.fuxi.netease.com:38621/virtualhuman'

config.GameItem.url = 'http://***************:23950/nsh_reward_http_service/claim_rewards'

config.corsOrigins = [
  /^https?:\/\/***************(:\d+)?/,
  /^https?:\/\/.*\.163\.com(:\d+)?/,
  /^https?:\/\/.*\.netease\.com(:\d+)?/
]

config.fakeAuditCfg = {
  auditTime: 5, // 5 seconds
  passRate: 1, // 100% 会通过
  returnPicUrl: 'http://127.0.0.1:88/nsh/md/audit/return_pic'
}

config.auditCfg.sendPicApi = 'http://127.0.0.1:88/nsh/md/fake_audit/send_pic'


config.guildPolemicCfg = {
  tokenExpire: 5 * 60, // 5分钟
  skipPubToken: true,
  hotSize: 200,
  hotExpireDays: 7,
  loseHotSeconds: 14400
};

config.Features.game_club = true
config.Features.lianghao = true
config.Features.wishlist = true

config.clubWebCfg.loginCookieCheck = false
config.clubWebCfg.recommendQualityCheck = true
config.clubWebCfg.smsCodeCheck = false


config.FcmSecureCfg = {
  appId: '76d4114af4b0409d98aad6164cd74758',
  bizId: '**********',
  secretKey: 'eca981397577631004ec96be3e308152'
}


config.FCM_WLC_HTTPS_HOST = "https://api.wlc.nppa.gov.cn"
config.FCM_WLC_HTTP_HOST = "http://api2.wlc.nppa.gov.cn"
config.URS_API_HOST = "http://reg.163.com";

module.exports = config;
