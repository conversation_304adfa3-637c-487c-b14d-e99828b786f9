#!/bin/bash

# 设置错误时退出
set -e

# 清理函数
cleanup() {
    echo "=== 执行清理操作 ==="
    if [ -f ".npmrc.bak" ]; then
        mv .npmrc.bak .npmrc
        echo "原始 .npmrc 文件已恢复"
    fi
}

# 设置陷阱，确保在脚本退出时执行清理
trap cleanup EXIT

echo "开始执行本地 CI 流程..."

# 设置仓库地址和项目ID
REPO_NSH="hub.fuxi.netease.com/leihuo-ccc-nsh/nsh-md"
REPO_STRESS="hub.fuxi.netease.com/leihuo-ccc-node-stress/nsh-md"
REPO_CSF="hub.fuxi.netease.com/leihuo-ccc-aliyun-d30/nsh-md"
TQ_PROJECT_ID_JD=38
TQ_PROJECT_ID_HJF=253
TQ_PROJECT_ID_CSF=1146
USER_EMAIL=$(git config user.email)

# 构建阶段
echo "=== 开始构建阶段 ==="
# 获取当前分支名
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD | tr "/" "-")
COMMIT_SHA=$(git rev-parse --short HEAD)
BUILD_ID=$(date +%Y%m%d%H%M)

# 构建标签
TAG_NSH="${REPO_NSH}:${BRANCH_NAME}.${BUILD_ID}.${COMMIT_SHA}"
TAG_STRESS="${REPO_STRESS}:${BRANCH_NAME}.${BUILD_ID}.${COMMIT_SHA}"
TAG_CSF="${REPO_CSF}:${BRANCH_NAME}.${BUILD_ID}.${COMMIT_SHA}"
LATEST_TAG_NSH="${REPO_NSH}:${BRANCH_NAME}.latest"
LATEST_TAG_STRESS="${REPO_STRESS}:${BRANCH_NAME}.latest"

echo "准备构建和推送以下标签："
echo "- ${TAG_NSH}"
echo "- ${TAG_STRESS}"
echo "- ${TAG_CSF}"
echo "- ${LATEST_TAG_NSH}"
echo "- ${LATEST_TAG_STRESS}"

# 注入 NPM Token 到 .npmrc
echo "=== 注入 NPM Token 到 .npmrc ==="
if [ -z "$CCC_NPM_TOKEN" ]; then
    echo "警告: CCC_NPM_TOKEN 环境变量未设置"
    exit 1
fi
# 备份原始 .npmrc 文件
cp .npmrc .npmrc.bak
# 替换环境变量
sed -i "s/\${CCC_NPM_TOKEN}/$CCC_NPM_TOKEN/g" .npmrc
echo "NPM Token 已注入到 .npmrc 文件"

# 构建 Docker 镜像
echo "=== 构建 Docker 镜像 ==="
docker build -t $TAG_NSH -t $LATEST_TAG_NSH -t $TAG_STRESS -t $LATEST_TAG_STRESS -t $TAG_CSF .

# 推送镜像
echo "=== 推送 Docker 镜像 ==="
docker push $TAG_NSH
docker push $LATEST_TAG_NSH
docker push $TAG_STRESS
docker push $LATEST_TAG_STRESS
docker push $TAG_CSF

# 发送构建成功通知
echo "=== 发送构建成功通知 ==="
curl -X POST "http://qa.leihuo.netease.com:3316/popo_qatool?receiver=1599911&msg=docker%20image%20build%20success!%20tag:%20$TAG_NSH"

# 部署阶段
echo "=== 开始部署阶段 ==="
# 获取最近一次提交信息
LAST_COMMIT_MSG=$(git log -1 --pretty=%s | head -c 100)

# 发送部署通知
echo "经典服测试环境部署"
curl -X POST -d "receiver=${USER_EMAIL}&project_id=$TQ_PROJECT_ID_JD&image=$TAG_NSH&msg=$LAST_COMMIT_MSG" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"

echo "怀旧服测试环境部署"
curl -X POST -d "receiver=${USER_EMAIL}&project_id=$TQ_PROJECT_ID_HJF&image=$TAG_NSH&msg=$LAST_COMMIT_MSG" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"

echo "重生服测试环境部署"
curl -X POST -d "receiver=${USER_EMAIL}&project_id=$TQ_PROJECT_ID_CSF&image=$TAG_CSF&msg=$LAST_COMMIT_MSG" "https://ccc-apipub.leihuo.netease.com/api/ci/pub"

echo "本地 CI 流程执行完成！"
echo "Docker 镜像已构建并推送："
echo "- ${TAG_NSH}"
echo "- ${TAG_STRESS}"
echo "- ${TAG_CSF}"
echo "- ${LATEST_TAG_NSH}"
echo "- ${LATEST_TAG_STRESS}"
echo "部署通知已发送到: ${USER_EMAIL}"