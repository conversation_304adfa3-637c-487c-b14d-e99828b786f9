-- +goose Up
-- +goose StatementBegin
CREATE TABLE nsh_astrology_bazaar_rating (
    ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    PostId BIGINT NOT NULL COMMENT '问题ID',
    CommentId BIGINT NOT NULL COMMENT '评论ID',
    FromRoleId BIGINT NOT NULL COMMENT '评分人ID',
    ToRoleId BIGINT NOT NULL COMMENT '被评分人ID',
    Star TINYINT NOT NULL COMMENT '解惑评分',
    Text TEXT NOT NULL COMMENT '评论内容',
    CreateTime BIGINT NOT NULL COMMENT '创建时间',
    UpdateTime BIGINT NOT NULL COMMENT '更新时间',
    Status INT NOT NULL DEFAULT 0 COMMENT '0: 正常, -1: 删除',
    UNIQUE KEY idx_comment_id (CommentId),
    KEY idx_post_id (PostId),
    KEY idx_from_role_id (FromRoleId),
    KEY idx_to_role_id (ToRoleId)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '星巫-问惑-评价';
-- +goose StatementEnd


-- +goose Down
-- +goose StatementBegin
DROP TABLE nsh_astrology_bazaar_rating;
-- +goose StatementEnd
