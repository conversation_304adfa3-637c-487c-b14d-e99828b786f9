-- +goose Up
-- +goose StatementBegin
CREATE TABLE nsh_astrology_bazaar_post (
    ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    RoleId BIGINT NOT NULL COMMENT '角色ID',
    TopicId INT NOT NULL COMMENT '话题ID',
    Question TEXT NOT NULL COMMENT '问题',
    DicePlanet VARCHAR(32) NOT NULL COMMENT '行星',
    DiceConstellation VARCHAR(32) NOT NULL COMMENT '星座',
    DiceHouse TINYINT NOT NULL COMMENT '星座宫位',
    CommentCount INT NOT NULL DEFAULT 0 COMMENT '评论数',
    CreateTime BIGINT NOT NULL COMMENT '创建时间',
    UpdateTime BIGINT NOT NULL COMMENT '更新时间',
    Status INT NOT NULL DEFAULT 0 COMMENT '0: 正常, -1: 删除',
    KEY idx_role_id (RoleId),
    KEY idx_topic_id (TopicId)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '星巫-问惑-帖子';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE nsh_astrology_bazaar_post;
-- +goose StatementEnd