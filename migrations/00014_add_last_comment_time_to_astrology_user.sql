-- +goose Up
-- +goose StatementBegin
-- 添加最后一次解惑时间字段，用于排行榜排序
ALTER TABLE `nsh_astrology_user` ADD COLUMN `LastCommentTime` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '最后一次解惑时间戳，用于相同解惑次数时的排序';
-- +goose StatementEnd

-- +goose StatementBegin
-- 添加索引优化排序查询性能
ALTER TABLE `nsh_astrology_user` ADD INDEX `idx_comment_count_time` (`CommentCount` DESC, `LastCommentTime` DESC);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- 删除索引和字段
ALTER TABLE `nsh_astrology_user` DROP INDEX `idx_comment_count_time`;
ALTER TABLE `nsh_astrology_user` DROP COLUMN `LastCommentTime`;
-- +goose StatementEnd
