-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `nsh_astrology_user_daily_forecast` (
    `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
    `RoleId` BIGINT(20) NOT NULL COMMENT '用户ID',
    `FortuneScore` int(11) NOT NULL COMMENT '运势分数',
    `CreateTime` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
    `UpdateTime` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `uk_role_id_ds` (`RoleId`, `DS`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '用户每日运势表';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `nsh_astrology_user_daily_forecast`;
-- +goose StatementEnd
