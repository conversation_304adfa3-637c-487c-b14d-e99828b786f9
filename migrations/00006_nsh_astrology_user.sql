-- +goose Up
-- +goose StatementBegin
CREATE TABLE `nsh_astrology_user` (
    `RoleId` BIGINT(20) NOT NULL COMMENT '角色ID',
    `Gender` TINYINT(1) NOT NULL COMMENT '性别 0 男 1 女',
    `BirthTime` BIGINT(20) NOT NULL COMMENT '出生时间戳',
    `BirthPlace` VARCHAR(255) NOT NULL COMMENT '出生地',
    `CurrentPlace` VARCHAR(255) NOT NULL COMMENT '当前地',
    `AstroLevel` INT(10) NOT NULL DEFAULT 0 COMMENT '星巫等级',
    `AstroType` INT(10) NOT NULL DEFAULT 0 COMMENT '星巫类型',
    `CreateTime` BIGINT(20) NOT NULL COMMENT '创建时间戳',
    `UpdateTime` BIGINT(20) NOT NULL COMMENT '更新时间戳',
    `RatingSum` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '解惑评分总和',
    `RatingCount` INT(10) NOT NULL DEFAULT 0 COMMENT '解惑评分次数',
    PRIMARY KEY (`RoleId`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '星巫用户资料';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `nsh_astrology_user`;
-- +goose StatementEnd