-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `nsh_astrology_post_topic_daily_hot` (
    `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `DS` varchar(8) NOT NULL COMMENT '日期, yyyyMMdd',
    `TopicId` bigint(20) NOT NULL COMMENT '话题ID',
    `Hot` int(11) NOT NULL COMMENT '热度',
    `CreateTime` bigint(20) NOT NULL COMMENT '创建时间',
    `UpdateTime` bigint(20) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `idx_topic_id_ds` (`TopicId`, `Ds`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '星座帖子每日热门表';

-- +goose StatementEnd
-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `nsh_astrology_post_topic_daily_hot`;
-- +goose StatementEnd