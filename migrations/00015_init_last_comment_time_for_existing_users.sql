-- +goose Up
-- +goose StatementBegin
-- 为现有有解惑记录的用户初始化LastCommentTime字段
-- 使用每个用户最新的解惑评论时间作为LastCommentTime
UPDATE `nsh_astrology_user` 
SET `LastCommentTime` = (
    SELECT MAX(`CreateTime`) 
    FROM `nsh_astrology_bazaar_comment` 
    WHERE `nsh_astrology_bazaar_comment`.`RoleId` = `nsh_astrology_user`.`RoleId`
    AND `nsh_astrology_bazaar_comment`.`Status` = 0
)
WHERE `CommentCount` > 0 
AND `LastCommentTime` = 0
AND EXISTS (
    SELECT 1 
    FROM `nsh_astrology_bazaar_comment` 
    WHERE `nsh_astrology_bazaar_comment`.`RoleId` = `nsh_astrology_user`.`RoleId`
    AND `nsh_astrology_bazaar_comment`.`Status` = 0
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- 回滚：将LastCommentTime重置为0
UPDATE `nsh_astrology_user` SET `LastCommentTime` = 0 WHERE `LastCommentTime` > 0;
-- +goose StatementEnd
