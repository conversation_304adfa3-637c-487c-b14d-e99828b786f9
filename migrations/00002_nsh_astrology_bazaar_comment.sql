-- +goose Up
-- +goose StatementBegin
CREATE TABLE nsh_astrology_bazaar_comment (
    ID BIGINT PRIMARY KEY AUTO_INCREMENT,
    PostId BIGINT NOT NULL COMMENT '帖子ID',
    PostRoleId BIGINT NOT NULL COMMENT '帖子角色ID',
    RoleId BIGINT NOT NULL COMMENT '评论的角色ID',
    Text TEXT NOT NULL COMMENT '评论内容',
    CreateTime BIGINT NOT NULL COMMENT '创建时间',
    UpdateTime BIGINT NOT NULL COMMENT '更新时间',
    Status INT NOT NULL DEFAULT 0 COMMENT '0: 正常, -1: 删除',
    UNIQUE KEY idx_uniq_post_role(PostId, RoleId),
    KEY idx_role_id (RoleId),
    KEY idx_post_role_id (PostRoleId)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '星巫-问惑-评论';
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE nsh_astrology_bazaar_comment;
-- +goose StatementEnd
